<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\Translation\Command\TranslationTrait;

class CmsPage extends Model
{
    use HasFactory, TranslationTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title', 'slug', 'content', 'status'
    ];

    protected $appends = [
        'created_date'
    ];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }
}
