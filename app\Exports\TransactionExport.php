<?php

namespace App\Exports;

use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class TransactionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    public $requestData;

    public function __construct(array $requestData)
    {
        $this->requestData = $requestData;
    }

    public function query()
    {
        $transaction = Transaction::with('patient', 'doctor', 'doctorAppointment', 'doctorSubscription');

        if(!empty($this->requestData['search'])){
            $keyword = $this->requestData['search'];
            $transaction = $transaction->whereHas('patient', function ($q) use ($keyword) {
                $q->searchByName($keyword);
            })->orWhereHas('doctor', function ($q) use ($keyword) {
                $q->searchByName($keyword);;
            })->orWhereHas('doctorAppointment', function ($que) use ($keyword) {
                $que->where('unique_id', 'LIKE', '%' . trim($keyword) . '%');
            });
        }


        if(!empty($this->requestData['payment_status'])){
            $transaction = $transaction->where('payment_status',$this->requestData['payment_status']);
        }

        if(!empty($this->requestData['transaction_type'])){
            $transaction = $transaction->where('transaction_type',$this->requestData['transaction_type']);
        }

        if(!empty($this->requestData['datepick'])){
            $date = explode(' - ', $this->requestData['datepick']);
            $start_date = date('Y/m/d', strtotime($date[0]));
            $end_date = date('Y/m/d', strtotime($date[1]));
            $transaction = $transaction->whereBetween(\DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$start_date, $end_date]);
        }

        return $transaction;
    }


    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        $heading =  ["ID","Patient Name", "Transaction ID","Doctor Name","Request Id","Amount", "Transaction Type", "Payment Status","Created At","Updated At"];

        if($this->requestData['transaction_type'] == 1){
            array_push($heading, "Appointment Id");
        }
        if($this->requestData['transaction_type'] == 3){
            array_push($heading, "Subscription Type");
        }

        return $heading;
    }

    public function map($transaction): array
    {
        if($transaction->transaction_type == 1 || $transaction->transaction_type == 0){
            $transaction_type = 'Consultation';
        }
        if($transaction->transaction_type == 2){
            $transaction_type = 'Pharmacy';
        }
        if($transaction->transaction_type == 3){
            $transaction_type = 'Subscription';
        }
        if($transaction->transaction_type == 4){
            $transaction_type = 'Doctor Payment';
        }

        if($transaction->payment_status == 1){
            $payment_status = 'Init';
        }
        if($transaction->payment_status == 2){
            $payment_status = 'Complete';
        }
        if($transaction->payment_status == 3){
            $payment_status = 'Fail';
        }
        if($transaction->payment_status == 4){
            $payment_status = 'Timeout';
        }
        if($transaction->payment_status == 5){
            $payment_status = 'Reward';
        }
        if($transaction->payment_status == 6){
            $payment_status = 'Reward Completed';
        }

        $value = [
            $transaction->id,
            $transaction->patient->name ?? '',
            $transaction->transaction_id,
            $transaction->doctor->name ?? '',
            $transaction->request_id ?? '',
            $transaction->amount.$transaction->currency,
            $transaction_type,
            $payment_status,
            $transaction->created_date,
            $transaction->updated_date
        ];

        if($this->requestData['transaction_type'] == 1){
            array_push($value, $transaction->doctorAppointment->unique_id);
        }
        if($this->requestData['transaction_type'] == 3){
            array_push($value, $transaction->doctorSubscription->subscription->slug);
        }

        return $value;
    }
}
