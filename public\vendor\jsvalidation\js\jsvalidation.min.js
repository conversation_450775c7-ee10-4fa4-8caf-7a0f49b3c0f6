var laravelValidation;!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(h){h.extend(h.fn,{validate:function(e){if(this.length){var i=h.data(this[0],"validator");return i?i:(this.attr("novalidate","novalidate"),i=new h.validator(e,this[0]),h.data(this[0],"validator",i),i.settings.onsubmit&&(this.on("click.validate",":submit",function(e){i.submitButton=e.currentTarget,h(this).hasClass("cancel")&&(i.cancelSubmit=!0),void 0!==h(this).attr("formnovalidate")&&(i.cancelSubmit=!0)}),this.on("submit.validate",function(a){function e(){var e,t;return i.submitButton&&(i.settings.submitHandler||i.formSubmitted)&&(e=h("<input type='hidden'/>").attr("name",i.submitButton.name).val(h(i.submitButton).val()).appendTo(i.currentForm)),!(i.settings.submitHandler&&!i.settings.debug)||(t=i.settings.submitHandler.call(i,i.currentForm,a),e&&e.remove(),void 0!==t&&t)}return i.settings.debug&&a.preventDefault(),i.cancelSubmit?(i.cancelSubmit=!1,e()):i.form()?i.pendingRequest?!(i.formSubmitted=!0):e():(i.focusInvalid(),!1)})),i)}e&&e.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing.")},valid:function(){var e,t,a;return h(this[0]).is("form")?e=this.validate().form():(a=[],e=!0,t=h(this[0].form).validate(),this.each(function(){(e=t.element(this)&&e)||(a=a.concat(t.errorList))}),t.errorList=a),e},rules:function(e,t){var a,i,r,n,s,o,l=this[0],u=void 0!==this.attr("contenteditable")&&"false"!==this.attr("contenteditable");if(null!=l&&(!l.form&&u&&(l.form=this.closest("form")[0],l.name=this.attr("name")),null!=l.form)){if(e)switch(i=(a=h.data(l.form,"validator").settings).rules,r=h.validator.staticRules(l),e){case"add":h.extend(r,h.validator.normalizeRule(t)),delete r.messages,i[l.name]=r,t.messages&&(a.messages[l.name]=h.extend(a.messages[l.name],t.messages));break;case"remove":return t?(o={},h.each(t.split(/\s/),function(e,t){o[t]=r[t],delete r[t]}),o):(delete i[l.name],r)}return(n=h.validator.normalizeRules(h.extend({},h.validator.classRules(l),h.validator.attributeRules(l),h.validator.dataRules(l),h.validator.staticRules(l)),l)).required&&(s=n.required,delete n.required,n=h.extend({required:s},n)),n.remote&&(s=n.remote,delete n.remote,n=h.extend(n,{remote:s})),n}}});function a(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var i;h.extend(h.expr.pseudos||h.expr[":"],{blank:function(e){return!a(""+h(e).val())},filled:function(e){var t=h(e).val();return null!==t&&!!a(""+t)},unchecked:function(e){return!h(e).prop("checked")}}),h.validator=function(e,t){this.settings=h.extend(!0,{},h.validator.defaults,e),this.currentForm=t,this.init()},h.validator.format=function(a,e){return 1===arguments.length?function(){var e=h.makeArray(arguments);return e.unshift(a),h.validator.format.apply(this,e)}:(void 0===e||(2<arguments.length&&e.constructor!==Array&&(e=h.makeArray(arguments).slice(1)),e.constructor!==Array&&(e=[e]),h.each(e,function(e,t){a=a.replace(new RegExp("\\{"+e+"\\}","g"),function(){return t})})),a)},h.extend(h.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",pendingClass:"pending",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:h([]),errorLabelContainer:h([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(e){this.lastActive=e,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,e,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(e)))},onfocusout:function(e){this.checkable(e)||!(e.name in this.submitted)&&this.optional(e)||this.element(e)},onkeyup:function(e,t){9===t.which&&""===this.elementValue(e)||-1!==h.inArray(t.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])||(e.name in this.submitted||e.name in this.invalid)&&this.element(e)},onclick:function(e){e.name in this.submitted?this.element(e):e.parentNode.name in this.submitted&&this.element(e.parentNode)},highlight:function(e,t,a){"radio"===e.type?this.findByName(e.name).addClass(t).removeClass(a):h(e).addClass(t).removeClass(a)},unhighlight:function(e,t,a){"radio"===e.type?this.findByName(e.name).removeClass(t).addClass(a):h(e).removeClass(t).addClass(a)}},setDefaults:function(e){h.extend(h.validator.defaults,e)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date (ISO).",number:"Please enter a valid number.",digits:"Please enter only digits.",equalTo:"Please enter the same value again.",maxlength:h.validator.format("Please enter no more than {0} characters."),minlength:h.validator.format("Please enter at least {0} characters."),rangelength:h.validator.format("Please enter a value between {0} and {1} characters long."),range:h.validator.format("Please enter a value between {0} and {1}."),max:h.validator.format("Please enter a value less than or equal to {0}."),min:h.validator.format("Please enter a value greater than or equal to {0}."),step:h.validator.format("Please enter a multiple of {0}.")},autoCreateRanges:!1,prototype:{init:function(){this.labelContainer=h(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||h(this.currentForm),this.containers=h(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var a,n=this.currentForm,i=this.groups={};function e(e){var t,a,i,r=void 0!==h(this).attr("contenteditable")&&"false"!==h(this).attr("contenteditable");!this.form&&r&&(this.form=h(this).closest("form")[0],this.name=h(this).attr("name")),n===this.form&&(t=h.data(this.form,"validator"),a="on"+e.type.replace(/^validate/,""),(i=t.settings)[a]&&!h(this).is(i.ignore)&&i[a].call(t,this,e))}h.each(this.settings.groups,function(a,e){"string"==typeof e&&(e=e.split(/\s/)),h.each(e,function(e,t){i[t]=a})}),a=this.settings.rules,h.each(a,function(e,t){a[e]=h.validator.normalizeRule(t)}),h(this.currentForm).on("focusin.validate focusout.validate keyup.validate",":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox'], [contenteditable], [type='button']",e).on("click.validate","select, option, [type='radio'], [type='checkbox']",e),this.settings.invalidHandler&&h(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler)},form:function(){return this.checkForm(),h.extend(this.submitted,this.errorMap),this.invalid=h.extend({},this.errorMap),this.valid()||h(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var e=0,t=this.currentElements=this.elements();t[e];e++)this.check(t[e]);return this.valid()},element:function(e){var t,a,i=this.clean(e),r=this.validationTargetFor(i),n=this,s=!0;return void 0===r?delete this.invalid[i.name]:(this.prepareElement(r),this.currentElements=h(r),(a=this.groups[r.name])&&h.each(this.groups,function(e,t){t===a&&e!==r.name&&(i=n.validationTargetFor(n.clean(n.findByName(e))))&&i.name in n.invalid&&(n.currentElements.push(i),s=n.check(i)&&s)}),t=!1!==this.check(r),s=s&&t,this.invalid[r.name]=!t,this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),h(e).attr("aria-invalid",!t)),s},showErrors:function(t){var a;t&&(a=this,h.extend(this.errorMap,t),this.errorList=h.map(this.errorMap,function(e,t){return{message:e,element:a.findByName(t)[0]}}),this.successList=h.grep(this.successList,function(e){return!(e.name in t)})),this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){h.fn.resetForm&&h(this.currentForm).resetForm(),this.invalid={},this.submitted={},this.prepareForm(),this.hideErrors();var e=this.elements().removeData("previousValue").removeAttr("aria-invalid");this.resetElements(e)},resetElements:function(e){var t;if(this.settings.unhighlight)for(t=0;e[t];t++)this.settings.unhighlight.call(this,e[t],this.settings.errorClass,""),this.findByName(e[t].name).removeClass(this.settings.validClass);else e.removeClass(this.settings.errorClass).removeClass(this.settings.validClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(e){var t,a=0;for(t in e)void 0!==e[t]&&null!==e[t]&&!1!==e[t]&&a++;return a},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(e){e.not(this.containers).text(""),this.addWrapper(e).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{h(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").trigger("focus").trigger("focusin")}catch(e){}},findLastActive:function(){var t=this.lastActive;return t&&1===h.grep(this.errorList,function(e){return e.element.name===t.name}).length&&t},elements:function(){var a=this,i={};return h(this.currentForm).find("input, select, textarea, [contenteditable]").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){var e=this.name||h(this).attr("name"),t=void 0!==h(this).attr("contenteditable")&&"false"!==h(this).attr("contenteditable");return!e&&a.settings.debug&&window.console&&console.error("%o has no name assigned",this),t&&(this.form=h(this).closest("form")[0],this.name=e),this.form===a.currentForm&&(!(e in i||!a.objectLength(h(this).rules()))&&(i[e]=!0))})},clean:function(e){return h(e)[0]},errors:function(){var e=this.settings.errorClass.split(" ").join(".");return h(this.settings.errorElement+"."+e,this.errorContext)},resetInternals:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=h([]),this.toHide=h([])},reset:function(){this.resetInternals(),this.currentElements=h([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(e){this.reset(),this.toHide=this.errorsFor(e)},elementValue:function(e){var t,a,i=h(e),r=e.type,n=void 0!==i.attr("contenteditable")&&"false"!==i.attr("contenteditable");return"radio"===r||"checkbox"===r?this.findByName(e.name).filter(":checked").val():"number"===r&&void 0!==e.validity?e.validity.badInput?"NaN":i.val():(t=n?i.text():i.val(),"file"===r?"C:\\fakepath\\"===t.substr(0,12)?t.substr(12):0<=(a=t.lastIndexOf("/"))||0<=(a=t.lastIndexOf("\\"))?t.substr(a+1):t:"string"==typeof t?t.replace(/\r/g,""):t)},check:function(t){t=this.validationTargetFor(this.clean(t));var e,a,i,r,n=h(t).rules(),s=h.map(n,function(e,t){return t}).length,o=!1,l=this.elementValue(t);for(a in"function"==typeof n.normalizer?r=n.normalizer:"function"==typeof this.settings.normalizer&&(r=this.settings.normalizer),r&&(l=r.call(t,l),delete n.normalizer),n){i={method:a,parameters:n[a]};try{if("dependency-mismatch"===(e=h.validator.methods[a].call(this,l,t,i.parameters))&&1===s){o=!0;continue}if(o=!1,"pending"===e)return void(this.toHide=this.toHide.not(this.errorsFor(t)));if(!e)return this.formatAndAdd(t,i),!1}catch(e){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+t.id+", check the '"+i.method+"' method.",e),e instanceof TypeError&&(e.message+=".  Exception occurred when checking element "+t.id+", check the '"+i.method+"' method."),e}}if(!o)return this.objectLength(n)&&this.successList.push(t),!0},customDataMessage:function(e,t){return h(e).data("msg"+t.charAt(0).toUpperCase()+t.substring(1).toLowerCase())||h(e).data("msg")},customMessage:function(e,t){var a=this.settings.messages[e];return a&&(a.constructor===String?a:a[t])},findDefined:function(){for(var e=0;e<arguments.length;e++)if(void 0!==arguments[e])return arguments[e]},defaultMessage:function(e,t){"string"==typeof t&&(t={method:t});var a=this.findDefined(this.customMessage(e.name,t.method),this.customDataMessage(e,t.method),!this.settings.ignoreTitle&&e.title||void 0,h.validator.messages[t.method],"<strong>Warning: No message defined for "+e.name+"</strong>"),i=/\$?\{(\d+)\}/g;return"function"==typeof a?a=a.call(this,t.parameters,e):i.test(a)&&(a=h.validator.format(a.replace(i,"{$1}"),t.parameters)),a},formatAndAdd:function(e,t){var a=this.defaultMessage(e,t);this.errorList.push({message:a,element:e,method:t.method}),this.errorMap[e.name]=a,this.submitted[e.name]=a},addWrapper:function(e){return this.settings.wrapper&&(e=e.add(e.parent(this.settings.wrapper))),e},defaultShowErrors:function(){for(var e,t,a=0;this.errorList[a];a++)t=this.errorList[a],this.settings.highlight&&this.settings.highlight.call(this,t.element,this.settings.errorClass,this.settings.validClass),this.showLabel(t.element,t.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(a=0;this.successList[a];a++)this.showLabel(this.successList[a]);if(this.settings.unhighlight)for(a=0,e=this.validElements();e[a];a++)this.settings.unhighlight.call(this,e[a],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return h(this.errorList).map(function(){return this.element})},showLabel:function(e,t){var a,i,r,n,s=this.errorsFor(e),o=this.idOrName(e),l=h(e).attr("aria-describedby");s.length?(s.removeClass(this.settings.validClass).addClass(this.settings.errorClass),s.html(t)):(a=s=h("<"+this.settings.errorElement+">").attr("id",o+"-error").addClass(this.settings.errorClass).html(t||""),this.settings.wrapper&&(a=s.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(a):this.settings.errorPlacement?this.settings.errorPlacement.call(this,a,h(e)):a.insertAfter(e),s.is("label")?s.attr("for",o):0===s.parents("label[for='"+this.escapeCssMeta(o)+"']").length&&(r=s.attr("id"),l?l.match(new RegExp("\\b"+this.escapeCssMeta(r)+"\\b"))||(l+=" "+r):l=r,h(e).attr("aria-describedby",l),(i=this.groups[e.name])&&(n=this,h.each(n.groups,function(e,t){t===i&&h("[name='"+n.escapeCssMeta(e)+"']",n.currentForm).attr("aria-describedby",s.attr("id"))})))),!t&&this.settings.success&&(s.text(""),"string"==typeof this.settings.success?s.addClass(this.settings.success):this.settings.success(s,e)),this.toShow=this.toShow.add(s)},errorsFor:function(e){var t=this.escapeCssMeta(this.idOrName(e)),a=h(e).attr("aria-describedby"),i="label[for='"+t+"'], label[for='"+t+"'] *";return a&&(i=i+", #"+this.escapeCssMeta(a).replace(/\s+/g,", #")),this.errors().filter(i)},escapeCssMeta:function(e){return void 0===e?"":e.replace(/([\\!"#$%&'()*+,./:;<=>?@\[\]^`{|}~])/g,"\\$1")},idOrName:function(e){return this.groups[e.name]||!this.checkable(e)&&e.id||e.name},validationTargetFor:function(e){return this.checkable(e)&&(e=this.findByName(e.name)),h(e).not(this.settings.ignore)[0]},checkable:function(e){return/radio|checkbox/i.test(e.type)},findByName:function(e){return h(this.currentForm).find("[name='"+this.escapeCssMeta(e)+"']")},getLength:function(e,t){switch(t.nodeName.toLowerCase()){case"select":return h("option:selected",t).length;case"input":if(this.checkable(t))return this.findByName(t.name).filter(":checked").length}return e.length},depend:function(e,t){return!this.dependTypes[typeof e]||this.dependTypes[typeof e](e,t)},dependTypes:{boolean:function(e){return e},string:function(e,t){return!!h(e,t.form).length},function:function(e,t){return e(t)}},optional:function(e){var t=this.elementValue(e);return!h.validator.methods.required.call(this,t,e)&&"dependency-mismatch"},startRequest:function(e){this.pending[e.name]||(this.pendingRequest++,h(e).addClass(this.settings.pendingClass),this.pending[e.name]=!0)},stopRequest:function(e,t){this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[e.name],h(e).removeClass(this.settings.pendingClass),t&&0===this.pendingRequest&&this.formSubmitted&&this.form()&&0===this.pendingRequest?(h(this.currentForm).trigger("submit"),this.submitButton&&h("input:hidden[name='"+this.submitButton.name+"']",this.currentForm).remove(),this.formSubmitted=!1):!t&&0===this.pendingRequest&&this.formSubmitted&&(h(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(e,t){return t="string"==typeof t&&t||"remote",h.data(e,"previousValue")||h.data(e,"previousValue",{old:null,valid:!0,message:this.defaultMessage(e,{method:t})})},destroy:function(){this.resetForm(),h(this.currentForm).off(".validate").removeData("validator").find(".validate-equalTo-blur").off(".validate-equalTo").removeClass("validate-equalTo-blur").find(".validate-lessThan-blur").off(".validate-lessThan").removeClass("validate-lessThan-blur").find(".validate-lessThanEqual-blur").off(".validate-lessThanEqual").removeClass("validate-lessThanEqual-blur").find(".validate-greaterThanEqual-blur").off(".validate-greaterThanEqual").removeClass("validate-greaterThanEqual-blur").find(".validate-greaterThan-blur").off(".validate-greaterThan").removeClass("validate-greaterThan-blur")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(e,t){e.constructor===String?this.classRuleSettings[e]=t:h.extend(this.classRuleSettings,e)},classRules:function(e){var t={},a=h(e).attr("class");return a&&h.each(a.split(" "),function(){this in h.validator.classRuleSettings&&h.extend(t,h.validator.classRuleSettings[this])}),t},normalizeAttributeRule:function(e,t,a,i){/min|max|step/.test(a)&&(null===t||/number|range|text/.test(t))&&(i=Number(i),isNaN(i)&&(i=void 0)),i||0===i?e[a]=i:t===a&&"range"!==t&&(e["date"===t?"dateISO":a]=!0)},attributeRules:function(e){var t,a,i={},r=h(e),n=e.getAttribute("type");for(t in h.validator.methods)a="required"===t?(""===(a=e.getAttribute(t))&&(a=!0),!!a):r.attr(t),this.normalizeAttributeRule(i,n,t,a);return i.maxlength&&/-1|2147483647|524288/.test(i.maxlength)&&delete i.maxlength,i},dataRules:function(e){var t,a,i={},r=h(e),n=e.getAttribute("type");for(t in h.validator.methods)""===(a=r.data("rule"+t.charAt(0).toUpperCase()+t.substring(1).toLowerCase()))&&(a=!0),this.normalizeAttributeRule(i,n,t,a);return i},staticRules:function(e){var t={},a=h.data(e.form,"validator");return a.settings.rules&&(t=h.validator.normalizeRule(a.settings.rules[e.name])||{}),t},normalizeRules:function(i,r){return h.each(i,function(e,t){if(!1!==t){if(t.param||t.depends){var a=!0;switch(typeof t.depends){case"string":a=!!h(t.depends,r.form).length;break;case"function":a=t.depends.call(r,r)}a?i[e]=void 0===t.param||t.param:(h.data(r.form,"validator").resetElements(h(r)),delete i[e])}}else delete i[e]}),h.each(i,function(e,t){i[e]="function"==typeof t&&"normalizer"!==e?t(r):t}),h.each(["minlength","maxlength"],function(){i[this]&&(i[this]=Number(i[this]))}),h.each(["rangelength","range"],function(){var e;i[this]&&(Array.isArray(i[this])?i[this]=[Number(i[this][0]),Number(i[this][1])]:"string"==typeof i[this]&&(e=i[this].replace(/[\[\]]/g,"").split(/[\s,]+/),i[this]=[Number(e[0]),Number(e[1])]))}),h.validator.autoCreateRanges&&(null!=i.min&&null!=i.max&&(i.range=[i.min,i.max],delete i.min,delete i.max),null!=i.minlength&&null!=i.maxlength&&(i.rangelength=[i.minlength,i.maxlength],delete i.minlength,delete i.maxlength)),i},normalizeRule:function(e){var t;return"string"==typeof e&&(t={},h.each(e.split(/\s/),function(){t[this]=!0}),e=t),e},addMethod:function(e,t,a){h.validator.methods[e]=t,h.validator.messages[e]=void 0!==a?a:h.validator.messages[e],t.length<3&&h.validator.addClassRules(e,h.validator.normalizeRule(e))},methods:{required:function(e,t,a){if(!this.depend(a,t))return"dependency-mismatch";if("select"!==t.nodeName.toLowerCase())return this.checkable(t)?0<this.getLength(e,t):null!=e&&0<e.length;var i=h(t).val();return i&&0<i.length},email:function(e,t){return this.optional(t)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)},url:function(e,t){return this.optional(t)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})+(?::(?:[^\]\[?\/<~#`!@$^&*()+=}|:";',>{ ]|%[0-9A-Fa-f]{2})*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(e)},date:(i=!1,function(e,t){return i||(i=!0,this.settings.debug&&window.console&&console.warn("The `date` method is deprecated and will be removed in version '2.0.0'.\nPlease don't use it, since it relies on the Date constructor, which\nbehaves very differently across browsers and locales. Use `dateISO`\ninstead or one of the locale specific methods in `localizations/`\nand `additional-methods.js`.")),this.optional(t)||!/Invalid|NaN/.test(new Date(e).toString())}),dateISO:function(e,t){return this.optional(t)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e,t){return this.optional(t)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e,t){return this.optional(t)||/^\d+$/.test(e)},minlength:function(e,t,a){var i=Array.isArray(e)?e.length:this.getLength(e,t);return this.optional(t)||a<=i},maxlength:function(e,t,a){var i=Array.isArray(e)?e.length:this.getLength(e,t);return this.optional(t)||i<=a},rangelength:function(e,t,a){var i=Array.isArray(e)?e.length:this.getLength(e,t);return this.optional(t)||i>=a[0]&&i<=a[1]},min:function(e,t,a){return this.optional(t)||a<=e},max:function(e,t,a){return this.optional(t)||e<=a},range:function(e,t,a){return this.optional(t)||e>=a[0]&&e<=a[1]},step:function(e,t,a){function i(e){var t=(""+e).match(/(?:\.(\d+))?$/);return t&&t[1]?t[1].length:0}function r(e){return Math.round(e*Math.pow(10,n))}var n,s=h(t).attr("type"),o="Step attribute on input type "+s+" is not supported.",l=new RegExp("\\b"+s+"\\b"),u=!0;if(s&&!l.test(["text","number","range"].join()))throw new Error(o);return n=i(a),(i(e)>n||r(e)%r(a)!=0)&&(u=!1),this.optional(t)||u},equalTo:function(e,t,a){var i=h(a);return this.settings.onfocusout&&i.not(".validate-equalTo-blur").length&&i.addClass("validate-equalTo-blur").on("blur.validate-equalTo",function(){h(t).valid()}),e===i.val()},remote:function(n,s,e,o){if(this.optional(s))return"dependency-mismatch";o="string"==typeof o&&o||"remote";var l,t,a,u=this.previousValue(s,o);return this.settings.messages[s.name]||(this.settings.messages[s.name]={}),u.originalMessage=u.originalMessage||this.settings.messages[s.name][o],this.settings.messages[s.name][o]=u.message,e="string"==typeof e?{url:e}:e,a=h.param(h.extend({data:n},e.data)),u.old===a?u.valid:(u.old=a,(l=this).startRequest(s),(t={})[s.name]=n,h.ajax(h.extend(!0,{mode:"abort",port:"validate"+s.name,dataType:"json",data:t,context:l.currentForm,success:function(e){var t,a,i,r=!0===e||"true"===e;l.settings.messages[s.name][o]=u.originalMessage,r?(i=l.formSubmitted,l.resetInternals(),l.toHide=l.errorsFor(s),l.formSubmitted=i,l.successList.push(s),l.invalid[s.name]=!1,l.showErrors()):(t={},a=e||l.defaultMessage(s,{method:o,parameters:n}),t[s.name]=u.message=a,l.invalid[s.name]=!0,l.showErrors(t)),u.valid=r,l.stopRequest(s,r)}},e)),"pending")}}});var r,n={};return h.ajaxPrefilter?h.ajaxPrefilter(function(e,t,a){var i=e.port;"abort"===e.mode&&(n[i]&&n[i].abort(),n[i]=a)}):(r=h.ajax,h.ajax=function(e){var t=("mode"in e?e:h.ajaxSettings).mode,a=("port"in e?e:h.ajaxSettings).port;return"abort"===t?(n[a]&&n[a].abort(),n[a]=r.apply(this,arguments),n[a]):r.apply(this,arguments)}),h}),function(e,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof module&&module.exports?module.exports=t():e.DateFormatter=t()}("undefined"!=typeof self?self:this,function(){var b={DAY:864e5,HOUR:3600,defaults:{dateSettings:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["AM","PM"],ordinal:function(e){var t=e%10,a={1:"st",2:"nd",3:"rd"};return 1!==Math.floor(e%100/10)&&a[t]?a[t]:"th"}},separators:/[ \-+\/.:@]/g,validParts:/[dDjlNSwzWFmMntLoYyaABgGhHisueTIOPZcrU]/g,intParts:/[djwNzmnyYhHgGis]/g,tzParts:/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,tzClip:/[^-+\dA-Z]/g},getInt:function(e,t){return parseInt(e,t||10)},compare:function(e,t){return"string"==typeof e&&"string"==typeof t&&e.toLowerCase()===t.toLowerCase()},lpad:function(e,t,a){var i=e.toString();return a=a||"0",i.length<t?b.lpad(a+i,t):i},merge:function(e){var t,a;for(e=e||{},t=1;t<arguments.length;t++)if(a=arguments[t])for(var i in a)a.hasOwnProperty(i)&&("object"==typeof a[i]?b.merge(e[i],a[i]):e[i]=a[i]);return e},getIndex:function(e,t){for(var a=0;a<t.length;a++)if(t[a].toLowerCase()===e.toLowerCase())return a;return-1}},e=function(e){var t=this,a=b.merge(b.defaults,e);t.dateSettings=a.dateSettings,t.separators=a.separators,t.validParts=a.validParts,t.intParts=a.intParts,t.tzParts=a.tzParts,t.tzClip=a.tzClip};return e.prototype={constructor:e,getMonth:function(e){var t=b.getIndex(e,this.dateSettings.monthsShort)+1;return 0===t&&(t=b.getIndex(e,this.dateSettings.months)+1),t},parseDate:function(e,t){var a,i,r,n,s,o,l,u,h,d,c=!1,m=!1,f=this.dateSettings,g={date:null,year:null,month:null,day:null,hour:0,min:0,sec:0};if(!e)return null;if(e instanceof Date)return e;if("U"===t)return(r=b.getInt(e))?new Date(1e3*r):e;switch(typeof e){case"number":return new Date(e);case"string":break;default:return null}if(!(a=t.match(this.validParts))||0===a.length)throw new Error("Invalid date format definition.");for(r=a.length-1;0<=r;r--)"S"===a[r]&&a.splice(r,1);for(i=e.replace(this.separators,"\0").split("\0"),r=0;r<i.length;r++)switch(n=i[r],s=b.getInt(n),a[r]){case"y":case"Y":if(!s)return null;h=n.length,g.year=2===h?b.getInt((s<70?"20":"19")+n):s,c=!0;break;case"m":case"n":case"M":case"F":if(isNaN(s)){if(!(0<(o=this.getMonth(n))))return null;g.month=o}else{if(!(1<=s&&s<=12))return null;g.month=s}c=!0;break;case"d":case"j":if(!(1<=s&&s<=31))return null;g.day=s,c=!0;break;case"g":case"h":if(d=i[l=-1<a.indexOf("a")?a.indexOf("a"):-1<a.indexOf("A")?a.indexOf("A"):-1],-1!==l)u=b.compare(d,f.meridiem[0])?0:b.compare(d,f.meridiem[1])?12:-1,1<=s&&s<=12&&-1!=u?g.hour=s%12==0?u:s+u:0<=s&&s<=23&&(g.hour=s);else{if(!(0<=s&&s<=23))return null;g.hour=s}m=!0;break;case"G":case"H":if(!(0<=s&&s<=23))return null;g.hour=s,m=!0;break;case"i":if(!(0<=s&&s<=59))return null;g.min=s,m=!0;break;case"s":if(!(0<=s&&s<=59))return null;g.sec=s,m=!0}if(!0===c){var p=g.year||0,v=g.month?g.month-1:0,y=g.day||1;g.date=new Date(p,v,y,g.hour,g.min,g.sec,0)}else{if(!0!==m)return null;g.date=new Date(0,0,0,g.hour,g.min,g.sec,0)}return g.date},guessDate:function(e,t){if("string"!=typeof e)return e;var a,i,r,n,s,o,l=e.replace(this.separators,"\0").split("\0"),u=t.match(this.validParts),h=new Date,d=0;if(!/^[djmn]/g.test(u[0]))return e;for(r=0;r<l.length;r++){if(d=2,s=l[r],o=b.getInt(s.substr(0,2)),isNaN(o))return null;switch(r){case 0:"m"===u[0]||"n"===u[0]?h.setMonth(o-1):h.setDate(o);break;case 1:"m"===u[0]||"n"===u[0]?h.setDate(o):h.setMonth(o-1);break;case 2:if(i=h.getFullYear(),d=(a=s.length)<4?a:4,!(i=b.getInt(a<4?i.toString().substr(0,4-a)+s:s.substr(0,4))))return null;h.setFullYear(i);break;case 3:h.setHours(o);break;case 4:h.setMinutes(o);break;case 5:h.setSeconds(o)}0<(n=s.substr(d)).length&&l.splice(r+1,0,n)}return h},parseFormat:function(e,i){function t(e,t){return s[e]?s[e]():t}var a=this,r=a.dateSettings,n=/\\?(.?)/gi,s={d:function(){return b.lpad(s.j(),2)},D:function(){return r.daysShort[s.w()]},j:function(){return i.getDate()},l:function(){return r.days[s.w()]},N:function(){return s.w()||7},w:function(){return i.getDay()},z:function(){var e=new Date(s.Y(),s.n()-1,s.j()),t=new Date(s.Y(),0,1);return Math.round((e-t)/b.DAY)},W:function(){var e=new Date(s.Y(),s.n()-1,s.j()-s.N()+3),t=new Date(e.getFullYear(),0,4);return b.lpad(1+Math.round((e-t)/b.DAY/7),2)},F:function(){return r.months[i.getMonth()]},m:function(){return b.lpad(s.n(),2)},M:function(){return r.monthsShort[i.getMonth()]},n:function(){return i.getMonth()+1},t:function(){return new Date(s.Y(),s.n(),0).getDate()},L:function(){var e=s.Y();return e%4==0&&e%100!=0||e%400==0?1:0},o:function(){var e=s.n(),t=s.W();return s.Y()+(12===e&&t<9?1:1===e&&9<t?-1:0)},Y:function(){return i.getFullYear()},y:function(){return s.Y().toString().slice(-2)},a:function(){return s.A().toLowerCase()},A:function(){var e=s.G()<12?0:1;return r.meridiem[e]},B:function(){var e=i.getUTCHours()*b.HOUR,t=60*i.getUTCMinutes(),a=i.getUTCSeconds();return b.lpad(Math.floor((e+t+a+b.HOUR)/86.4)%1e3,3)},g:function(){return s.G()%12||12},G:function(){return i.getHours()},h:function(){return b.lpad(s.g(),2)},H:function(){return b.lpad(s.G(),2)},i:function(){return b.lpad(i.getMinutes(),2)},s:function(){return b.lpad(i.getSeconds(),2)},u:function(){return b.lpad(1e3*i.getMilliseconds(),6)},e:function(){return/\((.*)\)/.exec(String(i))[1]||"Coordinated Universal Time"},I:function(){return new Date(s.Y(),0)-Date.UTC(s.Y(),0)!=new Date(s.Y(),6)-Date.UTC(s.Y(),6)?1:0},O:function(){var e=i.getTimezoneOffset(),t=Math.abs(e);return(0<e?"-":"+")+b.lpad(100*Math.floor(t/60)+t%60,4)},P:function(){var e=s.O();return e.substr(0,3)+":"+e.substr(3,2)},T:function(){return(String(i).match(a.tzParts)||[""]).pop().replace(a.tzClip,"")||"UTC"},Z:function(){return 60*-i.getTimezoneOffset()},c:function(){return"Y-m-d\\TH:i:sP".replace(n,t)},r:function(){return"D, d M Y H:i:s O".replace(n,t)},U:function(){return i.getTime()/1e3||0}};return t(e,e)},formatDate:function(e,t){var a,i,r,n,s,o="";if("string"==typeof e&&!(e=this.parseDate(e,t)))return null;if(e instanceof Date){for(r=t.length,a=0;a<r;a++)"S"!==(s=t.charAt(a))&&"\\"!==s&&(0<a&&"\\"===t.charAt(a-1)?o+=s:(n=this.parseFormat(s,e),a!==r-1&&this.intParts.test(s)&&"S"===t.charAt(a+1)&&(i=b.getInt(n)||0,n+=this.dateSettings.ordinal(i)),o+=n));return o}return""}},e}),laravelValidation={implicitRules:["Required","Confirmed"],init:function(){var a=$.fn.validate;$.fn.validate=function(e){var t="proengsoft_jsvalidation";return 0===$(this).find('input[name="'+t+'"]').length&&$("<input>").attr({type:"hidden",name:t}).appendTo(this),a.apply(this,[e])},$.validator.classRuleSettings={},$.validator.attributeRules=function(){},$.validator.dataRules=this.arrayRules,$.validator.prototype.arrayRulesCache={},this.setupValidations()},arrayRules:function(r){var n={},e=$.data(r.form,"validator"),s=e.arrayRulesCache;return-1===r.name.indexOf("[")||(r.name in s||(s[r.name]={}),$.each(e.settings.rules,function(e,t){var a,i;e in s[r.name]?n=laravelValidation.helpers.mergeRules(n,s[r.name][e]):(s[r.name][e]={},a=laravelValidation.helpers.regexFromWildcard(e),r.name.match(a)&&(i=$.validator.normalizeRule(t)||{},s[r.name][e]=i,n=laravelValidation.helpers.mergeRules(n,i)))})),n},setupValidations:function(){function r(e){var t=$(e.currentForm).attr("method");return $(e.currentForm).find('input[name="_method"]').length&&(t=$(e.currentForm).find('input[name="_method"]').val()),t}function h(a,e,i,t){return{mode:"abort",port:"validate"+e.name,dataType:"json",data:t,context:a.currentForm,url:$(a.currentForm).attr("action"),type:r(a),beforeSend:function(e){var t=i[0][1][1];if("get"!==r(a)&&t)return e.setRequestHeader("X-XSRF-TOKEN",t)}}}function l(s,i,o,e){var l=!0,u=s.previousValue(o);return $.each(e,function(e,t){var a=t[3]||-1!==laravelValidation.implicitRules.indexOf(t[0]),r=t[0],n=t[2];return!a&&s.optional(o)?!(l="dependency-mismatch"):(void 0!==laravelValidation.methods[r]?$.each(i,function(e,i){if(!1===(l=laravelValidation.methods[r].call(s,i,o,t[1],function(e){var t,a;s.settings.messages[o.name].laravelValidationRemote=u.originalMessage,e?(t=s.formSubmitted,s.prepareElement(o),s.formSubmitted=t,s.successList.push(o),delete s.invalid[o.name],s.showErrors()):((a={})[o.name]=u.message="function"==typeof n?n(i):n,s.invalid[o.name]=!0,s.showErrors(a)),s.showErrors(s.errorMap),u.valid=e})))return!1}):l=!1,!0!==l?(s.settings.messages[o.name]||(s.settings.messages[o.name]={}),s.settings.messages[o.name].laravelValidation=n,!1):void 0)}),l}$.validator.addMethod("laravelValidation",function(e,t,a){var i=[],r=[];$.each(a,function(e,t){var a=-1!==t[4].indexOf("[");t[3]||-1!==laravelValidation.implicitRules.indexOf(t[0])?a?r.unshift(t):i.unshift(t):a?r.push(t):i.push(t)});var n=l(this,[e],t,i),s=Array.isArray(e)?e:[e],o=l(this,s,t,r);return n&&o},""),$.validator.addMethod("laravelValidationRemote",function(s,o,e){if(t=e,a=!1,$.each(t,function(e,t){a=a||t[3]}),!a&&this.optional(o))return"dependency-mismatch";var t,a,l,i,u=this.previousValue(o);return this.settings.messages[o.name]||(this.settings.messages[o.name]={}),u.originalMessage=this.settings.messages[o.name].laravelValidationRemote,this.settings.messages[o.name].laravelValidationRemote=u.message,laravelValidation.helpers.arrayEquals(u.old,s)||u.old===s?u.valid:(u.old=s,(l=this).startRequest(o),(i=$(l.currentForm).serializeArray()).push({name:"_jsvalidation",value:o.name}),i.push({name:"_jsvalidation_validate_all",value:e[0][1][2]}),$.ajax(h(l,o,e,i)).always(function(e,t){var a,i,r,n;if("error"===t)n=!1,e=laravelValidation.helpers.parseErrorResponse(e);else{if("success"!==t)return;n=!0===e||"true"===e}l.settings.messages[o.name].laravelValidationRemote=u.originalMessage,n?(r=l.formSubmitted,l.prepareElement(o),l.formSubmitted=r,l.successList.push(o),delete l.invalid[o.name],l.showErrors()):(a={},i=e||l.defaultMessage(o,"remote"),a[o.name]=u.message="function"==typeof i?i(s):i[0],l.invalid[o.name]=!0,l.showErrors(a)),l.showErrors(l.errorMap),u.valid=n,l.stopRequest(o,n)}),"pending")},""),$.validator.addMethod("laravelValidationFormRequest",function(e,r,t){var n=this,s=n.previousValue(r),a=$(n.currentForm).serializeArray();return a.push({name:"__proengsoft_form_request",value:1}),JSON.stringify(s.old)===JSON.stringify(a)?(s.valid||n.showErrors(s.errors||{}),s.valid):(s.old=a,this.startRequest(r),$.ajax(h(n,r,t,a)).always(function(e,t){var i={},a="success"===t&&(!0===e||"true"===e);a?(n.resetInternals(),n.toHide=n.errorsFor(r)):($.each(e,function(e,t){var a=laravelValidation.helpers.findByName(n,e)[0];a&&(i[a.name]=laravelValidation.helpers.encode(t[0]||""))}),$.isEmptyObject(i)&&(a=!0)),s.valid=a,s.errors=i,n.showErrors(i),n.stopRequest(r,a)}),"pending")},"")}},$(function(){laravelValidation.init()}),function(){"use strict";var i={"./node_modules/locutus/php/array/array_diff.js":function(e){e.exports=function(e){var t,a={},i=arguments.length,r="",n=1,s="";e:for(r in e)for(n=1;n<i;n++){for(s in t=arguments[n])if(t[s]===e[r])continue e;a[r]=e[r]}return a}},"./node_modules/locutus/php/datetime/strtotime.js":function(e){var t="[ \\t]+",a="[ \\t]*",i="(?:([ap])\\.?m\\.?([\\t ]|$))",r="(2[0-4]|[01]?[0-9])",n="([01][0-9]|2[0-4])",s="(0?[1-9]|1[0-2])",o="([0-5]?[0-9])",l="([0-5][0-9])",u="(60|[0-5]?[0-9])",h="(60|[0-5][0-9])",d="(?:\\.([0-9]+))",c="sunday|monday|tuesday|wednesday|thursday|friday|saturday",m="sun|mon|tue|wed|thu|fri|sat",f=c+"|"+m+"|weekdays?",g="first|second|third|fourth|fifth|sixth|seventh|eighth?|ninth|tenth|eleventh|twelfth",p="next|last|previous|this",v="(?:second|sec|minute|min|hour|day|fortnight|forthnight|month|year)s?|weeks|"+f,y="([0-9]{1,4})",b="([0-9]{4})",w="(1[0-2]|0?[0-9])",x="(0[0-9]|1[0-2])",k="(?:(3[01]|[0-2]?[0-9])(?:st|nd|rd|th)?)",R="(0[0-9]|[1-2][0-9]|3[01])",F="january|february|march|april|may|june|july|august|september|october|november|december",E="jan|feb|mar|apr|may|jun|jul|aug|sept?|oct|nov|dec",S="("+F+"|"+E+"|i[vx]|vi{0,3}|xi{0,2}|i{1,3})",C="((?:GMT)?([+-])"+r+":?"+o+"?)",A=S+"[ .\\t-]*"+k+"[,.stndrh\\t ]*";function T(e,t){switch(t=t&&t.toLowerCase()){case"a":e+=12===e?-12:0;break;case"p":e+=12!==e?12:0}return e}function N(e){var t=+e;return e.length<4&&t<100&&(t+=t<70?2e3:1900),t}function j(e){return{jan:0,january:0,i:0,feb:1,february:1,ii:1,mar:2,march:2,iii:2,apr:3,april:3,iv:3,may:4,v:4,jun:5,june:5,vi:5,jul:6,july:6,vii:6,aug:7,august:7,viii:7,sep:8,sept:8,september:8,ix:8,oct:9,october:9,x:9,nov:10,november:10,xi:10,dec:11,december:11,xii:11}[e.toLowerCase()]}function _(e,t){var a=1<arguments.length&&void 0!==t?t:0;return{mon:1,monday:1,tue:2,tuesday:2,wed:3,wednesday:3,thu:4,thursday:4,fri:5,friday:5,sat:6,saturday:6,sun:0,sunday:0}[e.toLowerCase()]||a}function $(e,t){if(!(e=e&&e.match(/(?:GMT)?([+-])(\d+)(:?)(\d{0,2})/i)))return t;var a="-"===e[1]?-1:1,i=+e[2],r=+e[4];return e[4]||e[3]||(r=Math.floor(i%100),i=Math.floor(i/100)),a*(60*i+r)*60}var z={acdt:37800,acst:34200,addt:-7200,adt:-10800,aedt:39600,aest:36e3,ahdt:-32400,ahst:-36e3,akdt:-28800,akst:-32400,amt:-13840,apt:-10800,ast:-14400,awdt:32400,awst:28800,awt:-10800,bdst:7200,bdt:-36e3,bmt:-14309,bst:3600,cast:34200,cat:7200,cddt:-14400,cdt:-18e3,cemt:10800,cest:7200,cet:3600,cmt:-15408,cpt:-18e3,cst:-21600,cwt:-18e3,chst:36e3,dmt:-1521,eat:10800,eddt:-10800,edt:-14400,eest:10800,eet:7200,emt:-26248,ept:-14400,est:-18e3,ewt:-14400,ffmt:-14660,fmt:-4056,gdt:39600,gmt:0,gst:36e3,hdt:-34200,hkst:32400,hkt:28800,hmt:-19776,hpt:-34200,hst:-36e3,hwt:-34200,iddt:14400,idt:10800,imt:25025,ist:7200,jdt:36e3,jmt:8440,jst:32400,kdt:36e3,kmt:5736,kst:30600,lst:9394,mddt:-18e3,mdst:16279,mdt:-21600,mest:7200,met:3600,mmt:9017,mpt:-21600,msd:14400,msk:10800,mst:-25200,mwt:-21600,nddt:-5400,ndt:-9052,npt:-9e3,nst:-12600,nwt:-9e3,nzdt:46800,nzmt:41400,nzst:43200,pddt:-21600,pdt:-25200,pkst:21600,pkt:18e3,plmt:25590,pmt:-13236,ppmt:-17340,ppt:-25200,pst:-28800,pwt:-25200,qmt:-18840,rmt:5794,sast:7200,sdmt:-16800,sjmt:-20173,smt:-13884,sst:-39600,tbmt:10751,tmt:12344,uct:0,utc:0,wast:7200,wat:3600,wemt:7200,west:3600,wet:0,wib:25200,wita:28800,wit:32400,wmt:5040,yddt:-25200,ydt:-28800,ypt:-28800,yst:-32400,ywt:-28800,a:3600,b:7200,c:10800,d:14400,e:18e3,f:21600,g:25200,h:28800,i:32400,k:36e3,l:39600,m:43200,n:-3600,o:-7200,p:-10800,q:-14400,r:-18e3,s:-21600,t:-25200,u:-28800,v:-32400,w:-36e3,x:-39600,y:-43200,z:0},D={yesterday:{regex:/^yesterday/i,name:"yesterday",callback:function(){return--this.rd,this.resetTime()}},now:{regex:/^now/i,name:"now"},noon:{regex:/^noon/i,name:"noon",callback:function(){return this.resetTime()&&this.time(12,0,0,0)}},midnightOrToday:{regex:/^(midnight|today)/i,name:"midnight | today",callback:function(){return this.resetTime()}},tomorrow:{regex:/^tomorrow/i,name:"tomorrow",callback:function(){return this.rd+=1,this.resetTime()}},timestamp:{regex:/^@(-?\d+)/i,name:"timestamp",callback:function(e,t){return this.rs+=+t,this.y=1970,this.m=0,this.d=1,this.dates=0,this.resetTime()&&this.zone(0)}},firstOrLastDay:{regex:/^(first|last) day of/i,name:"firstdayof | lastdayof",callback:function(e,t){"first"===t.toLowerCase()?this.firstOrLastDayOfMonth=1:this.firstOrLastDayOfMonth=-1}},backOrFrontOf:{regex:RegExp("^(back|front) of "+r+a+i+"?","i"),name:"backof | frontof",callback:function(e,t,a,i){var r=+a,n=15;return"back"===t.toLowerCase()||(--r,n=45),r=T(r,i),this.resetTime()&&this.time(r,n,0,0)}},weekdayOf:{regex:RegExp("^("+g+"|"+p+")"+t+"("+c+"|"+m+")"+t+"of","i"),name:"weekdayof"},mssqltime:{regex:RegExp("^"+s+":"+l+":"+h+"[:.]([0-9]+)"+i,"i"),name:"mssqltime",callback:function(e,t,a,i,r,n){return this.time(T(+t,n),+a,+i,+r.substr(0,3))}},timeLong12:{regex:RegExp("^"+s+"[:.]"+o+"[:.]"+h+a+i,"i"),name:"timelong12",callback:function(e,t,a,i,r){return this.time(T(+t,r),+a,+i,0)}},timeShort12:{regex:RegExp("^"+s+"[:.]"+l+a+i,"i"),name:"timeshort12",callback:function(e,t,a,i){return this.time(T(+t,i),+a,0,0)}},timeTiny12:{regex:RegExp("^"+s+a+i,"i"),name:"timetiny12",callback:function(e,t,a){return this.time(T(+t,a),0,0,0)}},soap:{regex:RegExp("^"+b+"-"+x+"-"+R+"T"+n+":"+l+":"+h+d+C+"?","i"),name:"soap",callback:function(e,t,a,i,r,n,s,o,l){return this.ymd(+t,a-1,+i)&&this.time(+r,+n,+s,+o.substr(0,3))&&this.zone($(l))}},wddx:{regex:RegExp("^"+b+"-"+w+"-"+k+"T"+r+":"+o+":"+u),name:"wddx",callback:function(e,t,a,i,r,n,s){return this.ymd(+t,a-1,+i)&&this.time(+r,+n,+s,0)}},exif:{regex:RegExp("^"+b+":"+x+":"+R+" "+n+":"+l+":"+h,"i"),name:"exif",callback:function(e,t,a,i,r,n,s){return this.ymd(+t,a-1,+i)&&this.time(+r,+n,+s,0)}},xmlRpc:{regex:RegExp("^"+b+x+R+"T"+r+":"+l+":"+h),name:"xmlrpc",callback:function(e,t,a,i,r,n,s){return this.ymd(+t,a-1,+i)&&this.time(+r,+n,+s,0)}},xmlRpcNoColon:{regex:RegExp("^"+b+x+R+"[Tt]"+r+l+h),name:"xmlrpcnocolon",callback:function(e,t,a,i,r,n,s){return this.ymd(+t,a-1,+i)&&this.time(+r,+n,+s,0)}},clf:{regex:RegExp("^"+k+"/("+E+")/"+b+":"+n+":"+l+":"+h+t+C,"i"),name:"clf",callback:function(e,t,a,i,r,n,s,o){return this.ymd(+i,j(a),+t)&&this.time(+r,+n,+s,0)&&this.zone($(o))}},iso8601long:{regex:RegExp("^t?"+r+"[:.]"+o+"[:.]"+u+d,"i"),name:"iso8601long",callback:function(e,t,a,i,r){return this.time(+t,+a,+i,+r.substr(0,3))}},dateTextual:{regex:RegExp("^"+S+"[ .\\t-]*"+k+"[,.stndrh\\t ]+"+y,"i"),name:"datetextual",callback:function(e,t,a,i){return this.ymd(N(i),j(t),+a)}},pointedDate4:{regex:RegExp("^"+k+"[.\\t-]"+w+"[.-]"+b),name:"pointeddate4",callback:function(e,t,a,i){return this.ymd(+i,a-1,+t)}},pointedDate2:{regex:RegExp("^"+k+"[.\\t]"+w+"\\.([0-9]{2})"),name:"pointeddate2",callback:function(e,t,a,i){return this.ymd(N(i),a-1,+t)}},timeLong24:{regex:RegExp("^t?"+r+"[:.]"+o+"[:.]"+u),name:"timelong24",callback:function(e,t,a,i){return this.time(+t,+a,+i,0)}},dateNoColon:{regex:RegExp("^"+b+x+R),name:"datenocolon",callback:function(e,t,a,i){return this.ymd(+t,a-1,+i)}},pgydotd:{regex:RegExp("^"+b+"\\.?(00[1-9]|0[1-9][0-9]|[12][0-9][0-9]|3[0-5][0-9]|36[0-6])"),name:"pgydotd",callback:function(e,t,a){return this.ymd(+t,0,+a)}},timeShort24:{regex:RegExp("^t?"+r+"[:.]"+o,"i"),name:"timeshort24",callback:function(e,t,a){return this.time(+t,+a,0,0)}},iso8601noColon:{regex:RegExp("^t?"+n+l+h,"i"),name:"iso8601nocolon",callback:function(e,t,a,i){return this.time(+t,+a,+i,0)}},iso8601dateSlash:{regex:RegExp("^"+b+"/"+x+"/"+R+"/"),name:"iso8601dateslash",callback:function(e,t,a,i){return this.ymd(+t,a-1,+i)}},dateSlash:{regex:RegExp("^"+b+"/"+w+"/"+k),name:"dateslash",callback:function(e,t,a,i){return this.ymd(+t,a-1,+i)}},american:{regex:RegExp("^"+w+"/"+k+"/"+y),name:"american",callback:function(e,t,a,i){return this.ymd(N(i),t-1,+a)}},americanShort:{regex:RegExp("^"+w+"/"+k),name:"americanshort",callback:function(e,t,a){return this.ymd(this.y,t-1,+a)}},gnuDateShortOrIso8601date2:{regex:RegExp("^"+y+"-"+w+"-"+k),name:"gnudateshort | iso8601date2",callback:function(e,t,a,i){return this.ymd(N(t),a-1,+i)}},iso8601date4:{regex:RegExp("^([+-]?[0-9]{4})-"+x+"-"+R),name:"iso8601date4",callback:function(e,t,a,i){return this.ymd(+t,a-1,+i)}},gnuNoColon:{regex:RegExp("^t?"+n+l,"i"),name:"gnunocolon",callback:function(e,t,a){switch(this.times){case 0:return this.time(+t,+a,0,this.f);case 1:return this.y=100*t+ +a,this.times++,!0;default:return!1}}},gnuDateShorter:{regex:RegExp("^"+b+"-"+w),name:"gnudateshorter",callback:function(e,t,a){return this.ymd(+t,a-1,1)}},pgTextReverse:{regex:RegExp("^(\\d{3,4}|[4-9]\\d|3[2-9])-("+E+")-"+R,"i"),name:"pgtextreverse",callback:function(e,t,a,i){return this.ymd(N(t),j(a),+i)}},dateFull:{regex:RegExp("^"+k+"[ \\t.-]*"+S+"[ \\t.-]*"+y,"i"),name:"datefull",callback:function(e,t,a,i){return this.ymd(N(i),j(a),+t)}},dateNoDay:{regex:RegExp("^"+S+"[ .\\t-]*"+b,"i"),name:"datenoday",callback:function(e,t,a){return this.ymd(+a,j(t),1)}},dateNoDayRev:{regex:RegExp("^"+b+"[ .\\t-]*"+S,"i"),name:"datenodayrev",callback:function(e,t,a){return this.ymd(+t,j(a),1)}},pgTextShort:{regex:RegExp("^("+E+")-"+R+"-"+y,"i"),name:"pgtextshort",callback:function(e,t,a,i){return this.ymd(N(i),j(t),+a)}},dateNoYear:{regex:RegExp("^"+A,"i"),name:"datenoyear",callback:function(e,t,a){return this.ymd(this.y,j(t),+a)}},dateNoYearRev:{regex:RegExp("^"+k+"[ .\\t-]*"+S,"i"),name:"datenoyearrev",callback:function(e,t,a){return this.ymd(this.y,j(a),+t)}},isoWeekDay:{regex:RegExp("^"+b+"-?W(0[1-9]|[1-4][0-9]|5[0-3])(?:-?([0-7]))?"),name:"isoweekday | isoweek",callback:function(e,t,a,i){if(i=i?+i:1,!this.ymd(+t,0,1))return!1;var r=0-(4<(r=new Date(this.y,this.m,this.d).getDay())?r-7:r);this.rd+=r+7*(a-1)+i}},relativeText:{regex:RegExp("^("+g+"|"+p+")"+t+"("+v+")","i"),name:"relativetext",callback:function(e,t,a){var i,r={amount:{last:-1,previous:-1,this:0,first:1,next:1,second:2,third:3,fourth:4,fifth:5,sixth:6,seventh:7,eight:8,eighth:8,ninth:9,tenth:10,eleventh:11,twelfth:12}[i=t.toLowerCase()],behavior:{this:1}[i]||0},n=r.amount;switch(a.toLowerCase()){case"sec":case"secs":case"second":case"seconds":this.rs+=n;break;case"min":case"mins":case"minute":case"minutes":this.ri+=n;break;case"hour":case"hours":this.rh+=n;break;case"day":case"days":this.rd+=n;break;case"fortnight":case"fortnights":case"forthnight":case"forthnights":this.rd+=14*n;break;case"week":case"weeks":this.rd+=7*n;break;case"month":case"months":this.rm+=n;break;case"year":case"years":this.ry+=n;break;case"mon":case"monday":case"tue":case"tuesday":case"wed":case"wednesday":case"thu":case"thursday":case"fri":case"friday":case"sat":case"saturday":case"sun":case"sunday":this.resetTime(),this.weekday=_(a,7),this.weekdayBehavior=1,this.rd+=7*(0<n?n-1:n)}}},relative:{regex:RegExp("^([+-]*)[ \\t]*(\\d+)"+a+"("+v+"|week)","i"),name:"relative",callback:function(e,t,a,i){var r=t.replace(/[^-]/g,"").length,n=a*Math.pow(-1,r);switch(i.toLowerCase()){case"sec":case"secs":case"second":case"seconds":this.rs+=n;break;case"min":case"mins":case"minute":case"minutes":this.ri+=n;break;case"hour":case"hours":this.rh+=n;break;case"day":case"days":this.rd+=n;break;case"fortnight":case"fortnights":case"forthnight":case"forthnights":this.rd+=14*n;break;case"week":case"weeks":this.rd+=7*n;break;case"month":case"months":this.rm+=n;break;case"year":case"years":this.ry+=n;break;case"mon":case"monday":case"tue":case"tuesday":case"wed":case"wednesday":case"thu":case"thursday":case"fri":case"friday":case"sat":case"saturday":case"sun":case"sunday":this.resetTime(),this.weekday=_(i,7),this.weekdayBehavior=1,this.rd+=7*(0<n?n-1:n)}}},dayText:{regex:RegExp("^("+f+")","i"),name:"daytext",callback:function(e,t){this.resetTime(),this.weekday=_(t,0),2!==this.weekdayBehavior&&(this.weekdayBehavior=1)}},relativeTextWeek:{regex:RegExp("^("+p+")"+t+"week","i"),name:"relativetextweek",callback:function(e,t){switch(this.weekdayBehavior=2,t.toLowerCase()){case"this":this.rd+=0;break;case"next":this.rd+=7;break;case"last":case"previous":this.rd-=7}isNaN(this.weekday)&&(this.weekday=1)}},monthFullOrMonthAbbr:{regex:RegExp("^("+F+"|"+E+")","i"),name:"monthfull | monthabbr",callback:function(e,t){return this.ymd(this.y,j(t),this.d)}},tzCorrection:{regex:RegExp("^"+C,"i"),name:"tzcorrection",callback:function(e){return this.zone($(e))}},tzAbbr:{regex:RegExp("^\\(?([a-zA-Z]{1,6})\\)?"),name:"tzabbr",callback:function(e,t){var a=z[t.toLowerCase()];return!isNaN(a)&&this.zone(a)}},ago:{regex:/^ago/i,name:"ago",callback:function(){this.ry=-this.ry,this.rm=-this.rm,this.rd=-this.rd,this.rh=-this.rh,this.ri=-this.ri,this.rs=-this.rs,this.rf=-this.rf}},year4:{regex:RegExp("^"+b),name:"year4",callback:function(e,t){return this.y=+t,!0}},whitespace:{regex:/^[ .,\t]+/,name:"whitespace"},dateShortWithTimeLong:{regex:RegExp("^"+A+"t?"+r+"[:.]"+o+"[:.]"+u,"i"),name:"dateshortwithtimelong",callback:function(e,t,a,i,r,n){return this.ymd(this.y,j(t),+a)&&this.time(+i,+r,+n,0)}},dateShortWithTimeLong12:{regex:RegExp("^"+A+s+"[:.]"+o+"[:.]"+h+a+i,"i"),name:"dateshortwithtimelong12",callback:function(e,t,a,i,r,n,s){return this.ymd(this.y,j(t),+a)&&this.time(T(+i,s),+r,+n,0)}},dateShortWithTimeShort:{regex:RegExp("^"+A+"t?"+r+"[:.]"+o,"i"),name:"dateshortwithtimeshort",callback:function(e,t,a,i,r){return this.ymd(this.y,j(t),+a)&&this.time(+i,+r,0,0)}},dateShortWithTimeShort12:{regex:RegExp("^"+A+s+"[:.]"+l+a+i,"i"),name:"dateshortwithtimeshort12",callback:function(e,t,a,i,r,n){return this.ymd(this.y,j(t),+a)&&this.time(T(+i,n),+r,0,0)}}},M={y:NaN,m:NaN,d:NaN,h:NaN,i:NaN,s:NaN,f:NaN,ry:0,rm:0,rd:0,rh:0,ri:0,rs:0,rf:0,weekday:NaN,weekdayBehavior:0,firstOrLastDayOfMonth:0,z:NaN,dates:0,times:0,zones:0,ymd:function(e,t,a){return!(0<this.dates)&&(this.dates++,this.y=e,this.m=t,this.d=a,!0)},time:function(e,t,a,i){return!(0<this.times)&&(this.times++,this.h=e,this.i=t,this.s=a,this.f=i,!0)},resetTime:function(){return this.h=0,this.i=0,this.s=0,this.f=0,!(this.times=0)},zone:function(e){return this.zones<=1&&(this.zones++,this.z=e,!0)},toDate:function(e){switch(this.dates&&!this.times&&(this.h=this.i=this.s=this.f=0),isNaN(this.y)&&(this.y=e.getFullYear()),isNaN(this.m)&&(this.m=e.getMonth()),isNaN(this.d)&&(this.d=e.getDate()),isNaN(this.h)&&(this.h=e.getHours()),isNaN(this.i)&&(this.i=e.getMinutes()),isNaN(this.s)&&(this.s=e.getSeconds()),isNaN(this.f)&&(this.f=e.getMilliseconds()),this.firstOrLastDayOfMonth){case 1:this.d=1;break;case-1:this.d=0,this.m+=1}var t,a,i;isNaN(this.weekday)||((t=new Date(e.getTime())).setFullYear(this.y,this.m,this.d),t.setHours(this.h,this.i,this.s,this.f),a=t.getDay(),2===this.weekdayBehavior?(0===a&&0!==this.weekday&&(this.weekday=-6),0===this.weekday&&0!==a&&(this.weekday=7),this.d-=a,this.d+=this.weekday):(i=this.weekday-a,(this.rd<0&&i<0||0<=this.rd&&i<=-this.weekdayBehavior)&&(i+=7),0<=this.weekday?this.d+=i:this.d-=7-(Math.abs(this.weekday)-a),this.weekday=NaN)),this.y+=this.ry,this.m+=this.rm,this.d+=this.rd,this.h+=this.rh,this.i+=this.ri,this.s+=this.rs,this.f+=this.rf,this.ry=this.rm=this.rd=0,this.rh=this.ri=this.rs=this.rf=0;var r=new Date(e.getTime());switch(r.setFullYear(this.y,this.m,this.d),r.setHours(this.h,this.i,this.s,this.f),this.firstOrLastDayOfMonth){case 1:r.setDate(1);break;case-1:r.setMonth(r.getMonth()+1,0)}return isNaN(this.z)||r.getTimezoneOffset()===this.z||(r.setUTCFullYear(r.getFullYear(),r.getMonth(),r.getDate()),r.setUTCHours(r.getHours(),r.getMinutes(),r.getSeconds()-this.z,r.getMilliseconds())),r}};e.exports=function(e,t){null==t&&(t=Math.floor(Date.now()/1e3));for(var a=[D.yesterday,D.now,D.noon,D.midnightOrToday,D.tomorrow,D.timestamp,D.firstOrLastDay,D.backOrFrontOf,D.timeTiny12,D.timeShort12,D.timeLong12,D.mssqltime,D.timeShort24,D.timeLong24,D.iso8601long,D.gnuNoColon,D.iso8601noColon,D.americanShort,D.american,D.iso8601date4,D.iso8601dateSlash,D.dateSlash,D.gnuDateShortOrIso8601date2,D.gnuDateShorter,D.dateFull,D.pointedDate4,D.pointedDate2,D.dateNoDay,D.dateNoDayRev,D.dateTextual,D.dateNoYear,D.dateNoYearRev,D.dateNoColon,D.xmlRpc,D.xmlRpcNoColon,D.soap,D.wddx,D.exif,D.pgydotd,D.isoWeekDay,D.pgTextShort,D.pgTextReverse,D.clf,D.year4,D.ago,D.dayText,D.relativeTextWeek,D.relativeText,D.monthFullOrMonthAbbr,D.tzCorrection,D.tzAbbr,D.dateShortWithTimeShort12,D.dateShortWithTimeLong12,D.dateShortWithTimeShort,D.dateShortWithTimeLong,D.relative,D.whitespace],i=Object.create(M);e.length;){for(var r=null,n=null,s=0,o=a.length;s<o;s++){var l=a[s],u=e.match(l.regex);u&&(!r||u[0].length>r[0].length)&&(r=u,n=l)}if(!n||n.callback&&!1===n.callback.apply(i,r))return!1;e=e.substr(r[0].length),r=n=null}return Math.floor(i.toDate(new Date(1e3*t))/1e3)}},"./node_modules/locutus/php/info/ini_get.js":function(e,t,i){e.exports=function(e){var t="undefined"!=typeof window?window:i.g;t.$locutus=t.$locutus||{};var a=t.$locutus;return a.php=a.php||{},a.php.ini=a.php.ini||{},!a.php.ini[e]||void 0===a.php.ini[e].local_value||null===a.php.ini[e].local_value?"":a.php.ini[e].local_value}},"./node_modules/locutus/php/strings/strlen.js":function(e,t,r){e.exports=function(e){var t=e+"";if("off"===(r("./node_modules/locutus/php/info/ini_get.js")("unicode.semantics")||"off"))return t.length;for(var a=0,i=0,a=0,i=0;a<t.length;a++)!1!==function(e,t){var a,i,r=e.charCodeAt(t);if(55296<=r&&r<=56319){if(e.length<=t+1)throw new Error("High surrogate without following low surrogate");if((a=e.charCodeAt(t+1))<56320||57343<a)throw new Error("High surrogate without following low surrogate");return e.charAt(t)+e.charAt(t+1)}if(56320<=r&&r<=57343){if(0===t)throw new Error("Low surrogate without preceding high surrogate");if((i=e.charCodeAt(t-1))<55296||56319<i)throw new Error("Low surrogate without preceding high surrogate");return!1}return e.charAt(t)}(t,a)&&i++;return i}},"./node_modules/locutus/php/var/is_numeric.js":function(e){e.exports=function(e){var t=[" ","\n","\r","\t","\f","\v"," "," "," "," "," "," "," "," "," "," "," "," ","​","\u2028","\u2029","　"].join("");return("number"==typeof e||"string"==typeof e&&-1===t.indexOf(e.slice(-1)))&&""!==e&&!isNaN(e)}}},r={};function l(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return i[e](a,a.exports,l),a.exports}l.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return l.d(t,{a:t}),t},l.d=function(e,t){for(var a in t)l.o(t,a)&&!l.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},l.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),l.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},l.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var u={};!function(){l.r(u);var e=l("./node_modules/locutus/php/strings/strlen.js"),t=l.n(e),a=l("./node_modules/locutus/php/array/array_diff.js"),i=l.n(a),r=l("./node_modules/locutus/php/datetime/strtotime.js"),n=l.n(r),s=l("./node_modules/locutus/php/var/is_numeric.js"),o=l.n(s);$.extend(!0,laravelValidation,{helpers:{numericRules:["Integer","Numeric"],fileinfo:function(e,t){var a=e.value;return t=void 0!==t?t:0,null!==e.files&&void 0!==e.files[t]&&{file:a,extension:a.substr(a.lastIndexOf(".")+1),size:e.files[t].size/1024,type:e.files[t].type}},selector:function(e){var t=[];this.isArray(e)||(e=[e]);for(var a=0;a<e.length;a++)t.push("[name='"+e[a]+"']");return t.join()},hasNumericRules:function(e){return this.hasRules(e,this.numericRules)},hasRules:function(e,r){var n=!1;"string"==typeof r&&(r=[r]);var t=$.data(e.form,"validator"),a=[],i=t.arrayRulesCache;return e.name in i&&$.each(i[e.name],function(e,t){a.push(t)}),e.name in t.settings.rules&&a.push(t.settings.rules[e.name]),$.each(a,function(e,t){if("laravelValidation"in t)for(var a=t.laravelValidation,i=0;i<a.length;i++)if(-1!==$.inArray(a[i][0],r))return!(n=!0)}),n},strlen:function(e){return t()(e)},getSize:function(e,t,a){return this.hasNumericRules(t)&&this.is_numeric(a)?parseFloat(a):this.isArray(a)?parseFloat(a.length):"file"===t.type?parseFloat(Math.floor(this.fileinfo(t).size)):parseFloat(this.strlen(a))},getLaravelValidation:function(a,e){var i=void 0;return $.each($.validator.staticRules(e),function(e,t){"laravelValidation"===e&&$.each(t,function(e,t){t[0]===a&&(i=t)})}),i},parseTime:function(e,t){var a,i=!1,r=new DateFormatter;return"number"==typeof e&&void 0===t?e:("object"==typeof t&&(t=void 0!==(a=this.getLaravelValidation("DateFormat",t))?a[1][0]:null),i=null==t?this.strtotime(e):(i=r.parseDate(e,t))instanceof Date&&r.formatDate(i,t)===e&&Math.round(i.getTime()/1e3))},compareDates:function(e,t,a,i,r){var n=this.parseTime(i);if(!n){var s=this.dependentElement(e,a,i);if(void 0===s)return!1;n=this.parseTime(e.elementValue(s),s)}var o=this.parseTime(t,a);if(!1===o)return!1;switch(r){case"<":return o<n;case"<=":return o<=n;case"==":case"===":return o===n;case">":return n<o;case">=":return n<=o;default:throw new Error("Unsupported operator.")}},guessDate:function(e,t){return(new DateFormatter).guessDate(e,t)},strtotime:function(e,t){return n()(e,t)},is_numeric:function(e){return o()(e)},isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},arrayDiff:function(e,t){return i()(e,t)},arrayEquals:function(e,t){return!(!this.isArray(e)||!this.isArray(t))&&(e.length===t.length&&$.isEmptyObject(this.arrayDiff(e,t)))},dependentElement:function(e,t,a){var i,r,n=e.findByName(a);return void 0!==n[0]&&e.settings.onfocusout&&(i="blur","SELECT"!==n[0].tagName&&"OPTION"!==n[0].tagName&&"checkbox"!==n[0].type&&"radio"!==n[0].type||(i="click"),r=".validate-laravelValidation",n.off(r).off(i+r+"-"+t.name).on(i+r+"-"+t.name,function(){$(t).valid()})),n[0]},parseErrorResponse:function(e){var t,a=["Whoops, looks like something went wrong."];return"responseText"in e&&(t=e.responseText.match(/<h1\s*>(.*)<\/h1\s*>/i),this.isArray(t)&&(a=[t[1]])),a},escapeRegExp:function(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},regexFromWildcard:function(e){var t=e.split("[*]");return 1===t.length&&t.push(""),new RegExp("^"+t.map(function(e){return laravelValidation.helpers.escapeRegExp(e)}).join("\\[[^\\]]*\\]")+"$")},mergeRules:function(e,t){var a={laravelValidation:t.laravelValidation||[],laravelValidationRemote:t.laravelValidationRemote||[]};for(var i in a)0!==a[i].length&&(void 0===e[i]&&(e[i]=[]),e[i]=e[i].concat(a[i]));return e},encode:function(e){return $("<div/>").text(e).html()},findByArrayName:function(e,t){for(var a=t.replace(/\.([^\.]+)/g,"[$1]"),i=[a,a+"[]",a.replace(/(.*)\[(.*)\]$/g,"$1[]")],r=0;r<i.length;r++){var n=e.findByName(i[r]);if(0<n.length)return n}return $(null)},findByName:function(e,t){var a=e.findByName(t);if(0<a.length)return a;for(var i=t.split("."),r=i.length;0<r;r--){for(var n=[],s=0;s<r;s++)n.push(i[s]);if(0<(a=this.findByArrayName(e,n.join("."))).length)return a}return $(null)},allElementValues:function(a,e){return-1!==e.name.indexOf("[]")?a.findByName(e.name).map(function(e,t){return a.elementValue(t)}).get():a.elementValue(e)}}})}()}(),$.extend(!0,laravelValidation,{helpers:{isTimezone:function(e){var t={africa:["abidjan","accra","addis_ababa","algiers","asmara","bamako","bangui","banjul","bissau","blantyre","brazzaville","bujumbura","cairo","casablanca","ceuta","conakry","dakar","dar_es_salaam","djibouti","douala","el_aaiun","freetown","gaborone","harare","johannesburg","juba","kampala","khartoum","kigali","kinshasa","lagos","libreville","lome","luanda","lubumbashi","lusaka","malabo","maputo","maseru","mbabane","mogadishu","monrovia","nairobi","ndjamena","niamey","nouakchott","ouagadougou","porto-novo","sao_tome","tripoli","tunis","windhoek"],america:["adak","anchorage","anguilla","antigua","araguaina","argentina/buenos_aires","argentina/catamarca","argentina/cordoba","argentina/jujuy","argentina/la_rioja","argentina/mendoza","argentina/rio_gallegos","argentina/salta","argentina/san_juan","argentina/san_luis","argentina/tucuman","argentina/ushuaia","aruba","asuncion","atikokan","bahia","bahia_banderas","barbados","belem","belize","blanc-sablon","boa_vista","bogota","boise","cambridge_bay","campo_grande","cancun","caracas","cayenne","cayman","chicago","chihuahua","costa_rica","creston","cuiaba","curacao","danmarkshavn","dawson","dawson_creek","denver","detroit","dominica","edmonton","eirunepe","el_salvador","fortaleza","glace_bay","godthab","goose_bay","grand_turk","grenada","guadeloupe","guatemala","guayaquil","guyana","halifax","havana","hermosillo","indiana/indianapolis","indiana/knox","indiana/marengo","indiana/petersburg","indiana/tell_city","indiana/vevay","indiana/vincennes","indiana/winamac","inuvik","iqaluit","jamaica","juneau","kentucky/louisville","kentucky/monticello","kralendijk","la_paz","lima","los_angeles","lower_princes","maceio","managua","manaus","marigot","martinique","matamoros","mazatlan","menominee","merida","metlakatla","mexico_city","miquelon","moncton","monterrey","montevideo","montreal","montserrat","nassau","new_york","nipigon","nome","noronha","north_dakota/beulah","north_dakota/center","north_dakota/new_salem","ojinaga","panama","pangnirtung","paramaribo","phoenix","port-au-prince","port_of_spain","porto_velho","puerto_rico","rainy_river","rankin_inlet","recife","regina","resolute","rio_branco","santa_isabel","santarem","santiago","santo_domingo","sao_paulo","scoresbysund","shiprock","sitka","st_barthelemy","st_johns","st_kitts","st_lucia","st_thomas","st_vincent","swift_current","tegucigalpa","thule","thunder_bay","tijuana","toronto","tortola","vancouver","whitehorse","winnipeg","yakutat","yellowknife"],antarctica:["casey","davis","dumontdurville","macquarie","mawson","mcmurdo","palmer","rothera","south_pole","syowa","vostok"],arctic:["longyearbyen"],asia:["aden","almaty","amman","anadyr","aqtau","aqtobe","ashgabat","baghdad","bahrain","baku","bangkok","beirut","bishkek","brunei","choibalsan","chongqing","colombo","damascus","dhaka","dili","dubai","dushanbe","gaza","harbin","hebron","ho_chi_minh","hong_kong","hovd","irkutsk","jakarta","jayapura","jerusalem","kabul","kamchatka","karachi","kashgar","kathmandu","khandyga","kolkata","krasnoyarsk","kuala_lumpur","kuching","kuwait","macau","magadan","makassar","manila","muscat","nicosia","novokuznetsk","novosibirsk","omsk","oral","phnom_penh","pontianak","pyongyang","qatar","qyzylorda","rangoon","riyadh","sakhalin","samarkand","seoul","shanghai","singapore","taipei","tashkent","tbilisi","tehran","thimphu","tokyo","ulaanbaatar","urumqi","ust-nera","vientiane","vladivostok","yakutsk","yekaterinburg","yerevan"],atlantic:["azores","bermuda","canary","cape_verde","faroe","madeira","reykjavik","south_georgia","st_helena","stanley"],australia:["adelaide","brisbane","broken_hill","currie","darwin","eucla","hobart","lindeman","lord_howe","melbourne","perth","sydney"],europe:["amsterdam","andorra","athens","belgrade","berlin","bratislava","brussels","bucharest","budapest","busingen","chisinau","copenhagen","dublin","gibraltar","guernsey","helsinki","isle_of_man","istanbul","jersey","kaliningrad","kiev","lisbon","ljubljana","london","luxembourg","madrid","malta","mariehamn","minsk","monaco","moscow","oslo","paris","podgorica","prague","riga","rome","samara","san_marino","sarajevo","simferopol","skopje","sofia","stockholm","tallinn","tirane","uzhgorod","vaduz","vatican","vienna","vilnius","volgograd","warsaw","zagreb","zaporozhye","zurich"],indian:["antananarivo","chagos","christmas","cocos","comoro","kerguelen","mahe","maldives","mauritius","mayotte","reunion"],pacific:["apia","auckland","chatham","chuuk","easter","efate","enderbury","fakaofo","fiji","funafuti","galapagos","gambier","guadalcanal","guam","honolulu","johnston","kiritimati","kosrae","kwajalein","majuro","marquesas","midway","nauru","niue","norfolk","noumea","pago_pago","palau","pitcairn","pohnpei","port_moresby","rarotonga","saipan","tahiti","tarawa","tongatapu","wake","wallis"],utc:[""]},a=e.split("/",2),i=a[0].toLowerCase(),r="";return a[1]&&(r=a[1].toLowerCase()),i in t&&(0===t[i].length||-1!==t[i].indexOf(r))}}}),$.extend(!0,laravelValidation,{methods:{helpers:laravelValidation.helpers,jsRemoteTimer:0,Sometimes:function(){return!0},Bail:function(){return!0},Nullable:function(){return!0},Filled:function(e,t){return $.validator.methods.required.call(this,e,t,!0)},Required:function(e,t){return $.validator.methods.required.call(this,e,t)},RequiredWith:function(e,i,t){var r=this,n=!1,s=this;return $.each(t,function(e,t){var a=laravelValidation.helpers.dependentElement(s,i,t);n=n||void 0!==a&&$.validator.methods.required.call(r,s.elementValue(a),a,!0)}),!n||$.validator.methods.required.call(this,e,i,!0)},RequiredWithAll:function(e,i,t){var r=this,n=!0,s=this;return $.each(t,function(e,t){var a=laravelValidation.helpers.dependentElement(s,i,t);n=n&&void 0!==a&&$.validator.methods.required.call(r,s.elementValue(a),a,!0)}),!n||$.validator.methods.required.call(this,e,i,!0)},RequiredWithout:function(e,i,t){var r=this,n=!1,s=this;return $.each(t,function(e,t){var a=laravelValidation.helpers.dependentElement(s,i,t);n=n||void 0===a||!$.validator.methods.required.call(r,s.elementValue(a),a,!0)}),!n||$.validator.methods.required.call(this,e,i,!0)},RequiredWithoutAll:function(e,i,t){var r=this,n=!0,s=this;return $.each(t,function(e,t){var a=laravelValidation.helpers.dependentElement(s,i,t);n=n&&(void 0===a||!$.validator.methods.required.call(r,s.elementValue(a),a,!0))}),!n||$.validator.methods.required.call(this,e,i,!0)},RequiredIf:function(e,t,a){var i=laravelValidation.helpers.dependentElement(this,t,a[0]);if(void 0!==i){var r=String(this.elementValue(i));if(void 0!==r){var n=a.slice(1);if(-1!==$.inArray(r,n))return $.validator.methods.required.call(this,e,t,!0)}}return!0},RequiredUnless:function(e,t,a){var i=laravelValidation.helpers.dependentElement(this,t,a[0]);if(void 0!==i){var r=String(this.elementValue(i));if(void 0!==r){var n=a.slice(1);if(-1!==$.inArray(r,n))return!0}}return $.validator.methods.required.call(this,e,t,!0)},Confirmed:function(e,t,a){return laravelValidation.methods.Same.call(this,e,t,a)},Same:function(e,t,a){var i=laravelValidation.helpers.dependentElement(this,t,a[0]);return void 0!==i&&String(e)===String(this.elementValue(i))},InArray:function(e,t,a){if(void 0===a[0])return!1;for(var i=this.elements(),r=!1,n=laravelValidation.helpers.regexFromWildcard(a[0]),s=0;s<i.length;s++){var o,l=i[s].name;l.match(n)&&(o=laravelValidation.methods.Same.call(this,e,t,[l]),r=r||o)}return r},Distinct:function(e,t,a){if(void 0===a[0])return!1;for(var i=this.elements(),r=!1,n=laravelValidation.helpers.regexFromWildcard(a[0]),s=0;s<i.length;s++){var o,l=i[s].name;l!==t.name&&l.match(n)&&(o=laravelValidation.methods.Same.call(this,e,t,[l]),r=r||o)}return!r},Different:function(e,t,a){return!laravelValidation.methods.Same.call(this,e,t,a)},Accepted:function(e){return new RegExp("^(?:(yes|on|1|true))$","i").test(e)},Array:function(e,t){return-1!==t.name.indexOf("[")&&-1!==t.name.indexOf("]")||laravelValidation.helpers.isArray(e)},Boolean:function(e){return new RegExp("^(?:(true|false|1|0))$","i").test(e)},Integer:function(e){return new RegExp("^(?:-?\\d+)$","i").test(e)},Numeric:function(e,t){return $.validator.methods.number.call(this,e,t,!0)},String:function(e){return"string"==typeof e},Digits:function(e,t,a){return $.validator.methods.number.call(this,e,t,!0)&&e.length===parseInt(a,10)},DigitsBetween:function(e,t,a){return $.validator.methods.number.call(this,e,t,!0)&&e.length>=parseFloat(a[0])&&e.length<=parseFloat(a[1])},Size:function(e,t,a){return laravelValidation.helpers.getSize(this,t,e)===parseFloat(a[0])},Between:function(e,t,a){return laravelValidation.helpers.getSize(this,t,e)>=parseFloat(a[0])&&laravelValidation.helpers.getSize(this,t,e)<=parseFloat(a[1])},Min:function(e,t,a){return e=laravelValidation.helpers.allElementValues(this,t),laravelValidation.helpers.getSize(this,t,e)>=parseFloat(a[0])},Max:function(e,t,a){return e=laravelValidation.helpers.allElementValues(this,t),laravelValidation.helpers.getSize(this,t,e)<=parseFloat(a[0])},In:function(e,t,a){if(laravelValidation.helpers.isArray(e)&&laravelValidation.helpers.hasRules(t,"Array")){var i=laravelValidation.helpers.arrayDiff(e,a);return 0===Object.keys(i).length}return-1!==a.indexOf(e.toString())},NotIn:function(e,t,a){return-1===a.indexOf(e.toString())},Ip:function(e){return/^(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)$/i.test(e)||/^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(e)},Email:function(e,t){return $.validator.methods.email.call(this,e,t,!0)},Url:function(e,t){return $.validator.methods.url.call(this,e,t,!0)},File:function(e,t){return!(window.File&&window.FileReader&&window.FileList&&window.Blob)||"files"in t&&0<t.files.length},Mimes:function(e,t,a){if(!(window.File&&window.FileReader&&window.FileList&&window.Blob))return!0;var i=$.map(a,function(e){return e.toLowerCase()}),r=laravelValidation.helpers.fileinfo(t);return!1!==r&&-1!==i.indexOf(r.extension.toLowerCase())},Mimetypes:function(e,t,a){if(!(window.File&&window.FileReader&&window.FileList&&window.Blob))return!0;var i=$.map(a,function(e){return e.toLowerCase()}),r=laravelValidation.helpers.fileinfo(t);return!1!==r&&-1!==i.indexOf(r.type.toLowerCase())},Image:function(e,t){return laravelValidation.methods.Mimes.call(this,e,t,["jpg","png","gif","bmp","svg","jpeg"])},Dimensions:function(value,element,params,callback){if(!(window.File&&window.FileReader&&window.FileList&&window.Blob))return!0;if(null===element.files||void 0===element.files[0])return!1;var fr=new FileReader;return fr.onload=function(){var img=new Image;img.onload=function(){var height=parseFloat(img.naturalHeight),width=parseFloat(img.naturalWidth),ratio=width/height,notValid=params.width&&parseFloat(params.width!==width)||params.min_width&&parseFloat(params.min_width)>width||params.max_width&&parseFloat(params.max_width)<width||params.height&&parseFloat(params.height)!==height||params.min_height&&parseFloat(params.min_height)>height||params.max_height&&parseFloat(params.max_height)<height||params.ratio&&ratio!==parseFloat(eval(params.ratio));callback(!notValid)},img.onerror=function(){callback(!1)},img.src=fr.result},fr.readAsDataURL(element.files[0]),"pending"},Alpha:function(e){return"string"==typeof e&&new RegExp("^(?:^[a-zà-ü]+$)$","i").test(e)},AlphaNum:function(e){return"string"==typeof e&&new RegExp("^(?:^[a-z0-9à-ü]+$)$","i").test(e)},AlphaDash:function(e){return"string"==typeof e&&new RegExp("^(?:^[a-z0-9à-ü_-]+$)$","i").test(e)},Regex:function(e,t,a){var i=["x","s","u","X","U","A"],r=new RegExp("^(?:/)(.*\\/?[^/]*|[^/]*)(?:/)([gmixXsuUAJ]*)?$"),n=a[0].match(r);if(null===n)return!1;var s=[];if(void 0!==n[2]){s=n[2].split("");for(var o=0;o<s.length<o;o++)if(-1!==i.indexOf(s[o]))return!0}return new RegExp("^(?:"+n[1]+")$",s.join()).test(e)},Date:function(e){return!1!==laravelValidation.helpers.strtotime(e)},DateFormat:function(e,t,a){return!1!==laravelValidation.helpers.parseTime(e,a[0])},Before:function(e,t,a){return laravelValidation.helpers.compareDates(this,e,t,a[0],"<")},BeforeOrEqual:function(e,t,a){return laravelValidation.helpers.compareDates(this,e,t,a[0],"<=")},After:function(e,t,a){return laravelValidation.helpers.compareDates(this,e,t,a[0],">")},AfterOrEqual:function(e,t,a){return laravelValidation.helpers.compareDates(this,e,t,a[0],">=")},Timezone:function(e){return laravelValidation.helpers.isTimezone(e)},Json:function(e){var t=!0;try{JSON.parse(e)}catch(e){t=!1}return t},ProengsoftNoop:function(e){return!0}}});
//# sourceMappingURL=jsvalidation.min.js.map
