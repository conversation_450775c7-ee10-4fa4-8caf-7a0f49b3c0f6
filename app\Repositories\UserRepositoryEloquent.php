<?php

namespace App\Repositories;

use App\Enums\NotificationType;
use App\Enums\ReferStatus;
use App\Models\DoctorDetail;
use App\Models\OtpCode;
use App\Models\PatientDetail;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\UserRepository;
use App\Models\User;
use App\Models\UserDevice;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Http\Responses\Transformers\V1\UserAllDetailTransformers;
use App\Jobs\Notification;
use App\Jobs\PushNotifications;
use App\Jobs\SendEmails​;
use App\Models\DoctorClinic;
use App\Models\DoctorDocument;
use App\Models\DoctorInvitation;
use App\Models\DoctorSpeciality;
use App\Models\EmailTemplate;
use App\Models\Setting;
use App\Transformers\DoctorAllDetailTransformer;
use App\Transformers\PatientAllDetailTransformer;
use App\Transformers\UserAllDetailTransformer;
use App\Transformers\UserRegisterDetailTransformer;
use App\Validators\UserValidator;
use Exception;
use Illuminate\Support\Facades\Auth;
use Twilio\Rest\Client;
use Illuminate\Support\Facades\Log;

/**
 * Class UserRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class UserRepositoryEloquent extends BaseRepository implements UserRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return User::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    /**
     * Check User details
     *
     * @param $request
     * @return object
     */
    public function checkUser($request)
    {

        $user = User::when($request->type == 'email', function ($query) use ($request) {
            $query->where('email', $request->email);
        })->when($request->type == 'mobile', function ($query) use ($request) {
            $query->where('mobile', $request->mobile)
                ->where('dial_code', $request->dial_code);
        })->whereNotNull('email_verified_at')->firstOrFail();

        return $user;
    }

    /**
     * Social Auth
     *
     * @param $request
     * @return object
     */
    public function socialAuth($request)
    {
        $user = User::where('provider_name', $request->provider_name)
            ->where('provider_id', $request->provider_id)->firstOrFail();

        Auth::guard('web')->loginUsingId($user->id);

        $users = fractal(Auth::guard('web')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        return array('user' => $users, 'access_token' => Auth::guard('web')->user()->createToken('Molema')->accessToken);
    }

    /**
     * Social Auth
     *
     * @param $request
     * @return object
     */
    public function mobileAuth($request)
    {
        $user = User::where('dial_code', $request->dial_code)
            ->where('mobile', $request->mobile)->firstOrFail();

        Auth::guard('web')->loginUsingId($user->id);
        $users = fractal(Auth::guard('web')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        return array('user' => $users, 'access_token' => Auth::guard('web')->user()->createToken('Molema')->accessToken);
    }

    /**
     * Register User
     *
     * @param $request
     * @return object
     */
    public function register($request)
    {
        $input = $request->all();
        if (!empty($input['password'])) {
            $input['password'] = bcrypt($input['password']);
        }
        if (($request->user_type == UserType::Patient)) {
            $input['user_type'] = UserType::Patient;
        } else {
            $input['user_type'] = UserType::Doctor;
        }
        $input['username'] = generateUserName($request->user_type);
        if ($request->has('avatar')) {
            $input['avatar'] = self::avatarUpload($request, 'molema/user');
        }
        if ($request->email_already_verified == 1) {
            $input['email_verified_at'] = now();
        }

        $userCheck = User::where(function ($query) use ($request) {
            $query->where('email', $request->email)
                ->orWhere('mobile', $request->mobile);
        })->where(function ($query) use ($request) {
            $query->whereNull('email_verified_at')->orWhereNull('mobile_verified_at');
        })
            ->first();

        if (!empty($userCheck)) {
            $input['email_verified_at'] = null;
            $input['mobile_verified_at'] = null;
            $userCheck->update($input);
            $user = clone ($userCheck);
        } else {
            $user = User::create($input)->fresh();
        }

        if (!empty($input['invitation_code'])) {
            $doctorInvitation = DoctorInvitation::where('invitation_code', $input['invitation_code'])->first();
            $doctorInvitation->update(['status' => ReferStatus::Completed]);
        }

        if (empty($user->email_verified_at)) {
            self::sendOtpVerificationCode($user, $request);
        }
        if (env('APP_ENV') == 'Production') {
            self::sendTwilioMobileOtp($user, $request);
        }

        return fractal($user, new UserRegisterDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Send email otp verification code.
     *
     * @param $request
     */
    public function sendOtpVerificationCode($user, $request)
    {
        $code = generateUniqueOtpCode();

        OtpCode::create([
            'code' => $code,
            'user_id' => $user->id,
            'type' => 'email',
            'expiry_date' => now()->addMinutes(5)
        ]);

        $emailTemplate = EmailTemplate::where('slug', 'email_verification')->first();

        $emailBody = $emailTemplate->getTranslation('description', app()->getLocale());

        $email_content = str_replace("[[USERNAME]]", $user->name, $emailBody);
        $email_content = str_replace("[[CODE]]", $code, $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
            'email' => $user->email,
            'email_body' => $email_content,
            'subject' => $emailTemplate->getTranslation('title', app()->getLocale())
        ];

        dispatch((new SendEmails​($user)));
    }

    /**
     * Verify email otp verification code.
     *
     * @param $request
     * @return object
     */
    public function verifyOtpCode($request)
    {
        $user = User::where('email', $request->email)->firstOrFail();

        $otpCode = OtpCode::where('code', $request->code)
            ->where('user_id', $user->id)
            ->firstOrFail();

        if (now()->subMinutes(5) <= $otpCode->expiry_date) {
            if ($otpCode->code == $request->code) {
                $user->email_verified_at = now();
                $user->save();
                $otpCode->delete();
                if ($user->user_type == UserType::Patient) {
                    Auth::guard('web')->loginUsingId($user->id);
                    $user = fractal(Auth::guard('web')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());
                    return array('user' => $user, 'access_token' => Auth::guard('web')->user()->createToken('Molema')->accessToken);
                } else {
                    return array('user' => $user);
                }
            }
        } else {
            $otpCode->delete();
            return array('user' => $user);
        }
    }

    /**
     * Send mobile otp verification code.
     *
     * @param $request
     */
    public function sendTwilioMobileOtp($user, $request)
    {
        $mobile = $request->dial_code . $request->mobile;

        $token = getenv("TWILIO_AUTH_TOKEN");
        $twilio_sid = getenv("TWILIO_SID");
        $twilio_verify_sid = getenv("TWILIO_VERIFY_SID");
        $twilio = new Client($twilio_sid, $token);
        $twilio->verify->v2->services($twilio_verify_sid)
            ->verifications
            ->create($mobile, "sms");
    }

    /**
     * Verify twilio otp
     *
     * @param Request $request
     * @return \Twilio\Rest\Verify\V2\Service\VerificationCheckInstance
     */
    public function verifyTwilioOtp($request)
    {
        $mobile = $request->dial_code . $request->mobile;

        $verification_code = $request->code;

        $token = getenv("TWILIO_AUTH_TOKEN");
        $twilio_sid = getenv("TWILIO_SID");
        $twilio_verify_sid = getenv("TWILIO_VERIFY_SID");
        $twilio = new Client($twilio_sid, $token);
        $verification = $twilio->verify->v2->services($twilio_verify_sid)
            ->verificationChecks
            ->create(['code' => $verification_code, 'to' => $mobile]);
        return $verification;
    }

    /**
     * Update Mobile verify status
     *
     * @param $request
     * @return array
     */
    public function updateVerifyStatus($request)
    {
        $user = User::where('mobile', $request->mobile)->firstOrFail();
        $user->mobile_verified_at = now();
        return $user->save();
    }

    /**
     * Login with token
     *
     * @param $request
     * @return array
     */
    public function login()
    {
        $user = fractal(Auth::guard('web')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());

        return array('user' => $user, 'access_token' => Auth::guard('web')->user()->createToken('Molema')->accessToken);
    }

    /**
     * Store Device Info
     *
     * @param $request
     * @return mixed
     */
    public function storeDeviceInfo($request)
    {
        $data = $request->only('device_id', 'push_token', 'voip_token', 'device_type', 'os_version');
        if (!empty(Auth::guard('api')->user())) {
            $data['user_id'] = Auth::guard('api')->user()->id;
        } else {
            $data['user_id'] = Auth::guard('web')->user()->id;
        }

        return UserDevice::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    /**
     * Delete Device Info
     *
     * @param $request
     * @return mixed
     */
    public function deleteDeviceInfo($request)
    {
        return UserDevice::where('device_id', $request->device_id)->delete();
    }

    /**
     * Update Profile Info
     *
     * @param $request
     * @return mixed
     */
    public function updateProfile($request)
    {
        $user = Auth::guard('api')->user();

        $input = $request->only('first_name', 'last_name', 'avatar', 'language');

        if ($request->has('avatar')) {
            $input['avatar'] = self::avatarUpload($request, 'molema/user');
        }
        $user->update($input);

        if ($user->user_type == UserType::Patient) {
            return fractal($user, new PatientAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        }
        if ($user->user_type == UserType::Doctor) {
            return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        }
    }

    /**
     * @param $request
     * @return array
     * @throws \Prettus\Validator\Exceptions\ValidatorException
     */
    public function changePassword($request)
    {
        $user = Auth::guard('api')->user();
        $user->password = bcrypt($request->password);
        $user->save();
        return fractal(Auth::guard('api')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * @param $request
     * @return array
     * @throws \Prettus\Validator\Exceptions\ValidatorException
     */
    public function deleteAccount($request)
    {
        $user = Auth::guard('api')->user();
        $data['email'] = $user->email . time();
        $data['mobile'] = $user->mobile . time();
        $data['provider_id'] = null;
        $data['provider_name'] = null;
        if ($user->user_type == UserType::Patient) {
            PatientDetail::where('user_id', $user->id)->delete();
        }
        if ($user->user_type == UserType::Doctor) {
            DoctorSpeciality::where('user_id', $user->id)->delete();
            DoctorDetail::where('user_id', $user->id)->delete();
            DoctorDocument::where('user_id', $user->id)->delete();
            DoctorClinic::where('user_id', $user->id)->delete();
        }
        $user->update($data);
        return $user->delete();
    }

    /**
     * @param $request
     * @return array
     * @throws \Prettus\Validator\Exceptions\ValidatorException
     */
    public function uploadMedia($request)
    {
        $media = self::fileUpload($request->media, 'molema/chat');
        return ['media_url' => $media];
    }

    /**
     * @param $request
     * @return array
     * @throws \Prettus\Validator\Exceptions\ValidatorException
     */
    public function chatNotification($request)
    {
        //Send Notification to Patient
        $sender = User::findOrFail($request->senderId);
        $receiver = User::findOrFail($request->receiverId);
        if ($request->receiverId == $request->patientId) {
            $notificationType = NotificationType::PatientChat;
        }
        if ($request->receiverId == $request->doctorId) {
            $notificationType = NotificationType::DoctorChat;
        }
        $extra = [
            'notification_type' => $notificationType,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $request->patientId,
            'doctor_id' => $request->doctorId,
            'booking_id' => $request->bookingId,
            'title' => 'New Message',
            'message' => $sender->name . " has send you message.",
            'ref_id' => $request->appointmentId,
            'ref_type' => 'DoctorAppointment'
        ];

        dispatch(new PushNotifications($sender, $receiver, $extra));
    }
}
