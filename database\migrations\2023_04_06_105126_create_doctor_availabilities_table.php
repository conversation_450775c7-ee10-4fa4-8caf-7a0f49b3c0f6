<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('doctor_availabilities', function(Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('user_id');
            $table->string('day');
            $table->time('start_time');
            $table->time('end_time');
            $table->string('slot');
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('doctor_availabilities');
	}
};
