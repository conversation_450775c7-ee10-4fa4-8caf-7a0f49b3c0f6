/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!*************************************!*\
  !*** ./resources/assets/js/call.js ***!
  \*************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#call_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[5, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#call_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "appointment_id",
        name: "appointment_id"
      }, {
        data: "initiator_name",
        name: "initiator_name"
      }, {
        data: "channel_name",
        name: "channel_name"
      }, {
        data: "receiver_name",
        name: "receiver_name"
      }, {
        data: "call_date",
        name: "call_date"
      }, {
        data: "start_time",
        name: "start_time"
      }, {
        data: "calculated_seconds",
        name: "calculated_seconds"
      }],
      columnDefs: [{
        targets: 1,
        render: function render(data, type, row) {
          if (row.doctor_appointment != null) {
            var appointmentUrl = "".concat(appointmentRoute).replace('id', "".concat(row.doctor_appointment.id));
            return "<a href=" + appointmentUrl + ">".concat(row.doctor_appointment.unique_id, "</a>");
          } else {
            return '-';
          }
        }
      }, {
        targets: 2,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.initiator.avatar_url, "\" alt=\"").concat(row.initiator.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.initiator.name, "</a>\n                            <span>").concat(row.initiator.username, "</span>\n                        </div>");
        }
      }, {
        targets: 4,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.initiator.avatar_url, "\" alt=\"").concat(row.initiator.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.receiver.name, "</a>\n                            <span>").concat(row.receiver.username, "</span>\n                        </div>");
        }
      }]
    });
    table = datatable.$;
    $('#call_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
    var selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    filterButton.addEventListener("click", function () {
      var filterString = "";

      // Get filter values
      selectOptions.forEach(function (item, index) {
        if (item.value && item.value !== "") {
          if (index == 0) {
            datatable.column(3).search(item.value).draw();
          }
          if (index == 1) {
            datatable.column(2).search(item.value).draw();
          }
        }
      });
    });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

    // Reset datatable
    resetButton.addEventListener("click", function () {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
      selectOptions.forEach(function (select) {
        $(select).val("").trigger("change");
      });

      // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
      datatable.search("").columns().search("").draw();
    });
  };
  $('input[name="search"]').on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
    var start_date = picker.startDate.format("YYYY/MM/DD");
    var end_date = picker.endDate.format("YYYY/MM/DD");
    $("#call_table").data("dt_params", {
      start_date: start_date,
      end_date: end_date
    });
    datatable.draw();
  });
  $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#call_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });
  var initDaterangepicker = function initDaterangepicker() {
    var start = moment().startOf("year");
    var end = moment().endOf("year");
    var input = $('input[name="search"]');
    function cb(start, end) {
      input.val(start.format("YYYY/MM/DD") + " - " + end.format("YYYY/MM/DD"));
      var start_date = start.format("YYYY/MM/DD");
      var end_date = end.format("YYYY/MM/DD");
      $("#call_table").data("dt_params", {
        start_date: start_date,
        end_date: end_date
      });
      datatable.draw();
    }
    input.daterangepicker({
      startDate: start,
      endDate: end,
      ranges: {
        Today: [moment(), moment()],
        Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
        "Last 7 Days": [moment().subtract(6, "days"), moment()],
        "Last 30 Days": [moment().subtract(29, "days"), moment()],
        "This Month": [moment().startOf("month"), moment().endOf("month")],
        "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
        "This Year": [moment().startOf("year"), moment().endOf("year")],
        "Last Year": [moment().subtract(1, "year").startOf("year"), moment().subtract(1, "year").endOf("year")]
      }
    }, cb);
    cb(start, end);
  };

  // Public methods
  return {
    init: function init() {
      initDatatable();
      initDaterangepicker();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#call_table").DataTable().ajax.reload();
    }
  });
});
if ($("#export").length > 0) {
  var url = new URL($('#export').attr('href'));
  $('#datepick').on('change', function () {
    url.searchParams.set('datepick', this.value);
    $("a#export").attr("href", url);
  });
  $('#search').on("input", function () {
    url.searchParams.set('search', this.value);
    $("a#export").attr("href", url);
  });
}
/******/ })()
;