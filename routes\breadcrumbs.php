<?php

Breadcrumbs::for('dashboard', function ($trail) {
    $trail->push('Dashboard', route('dashboard'));
});

// Patient
Breadcrumbs::for('admin.patient', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Patients', route('admin.patient.index'));
});

Breadcrumbs::for('admin.patient.create', function ($trail) {
    $trail->parent('admin.patient');
    $trail->push('Add patient', route('admin.patient.index'));
});

Breadcrumbs::for('admin.patient.edit', function ($trail) {
    $trail->parent('admin.patient');
    $trail->push('Edit patient', route('admin.patient.index'));
});

Breadcrumbs::for('admin.patient.view', function ($trail) {
    $trail->parent('admin.patient');
    $trail->push('View patient', route('admin.patient.index'));
});

Breadcrumbs::for('admin.doctor.view', function ($trail) {
    $trail->parent('admin.doctor');
    $trail->push('View doctor', route('admin.doctor.index'));
});

Breadcrumbs::for('admin.bank_details.edit', function ($trail) {
    $trail->parent('admin.doctor');
    $trail->push('Edit Bank Detail', route('admin.doctor.index'));
});

// Appointment
Breadcrumbs::for('admin.appointment', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Appointments', route('admin.appointment.index'));
});

Breadcrumbs::for('admin.appointment.view', function ($trail) {
    $trail->parent('admin.appointment');
    $trail->push('View appointment', route('admin.appointment.index'));
});

// Doctor
Breadcrumbs::for('admin.doctor', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Doctors', route('admin.doctor.index'));
});

Breadcrumbs::for('admin.doctor.create', function ($trail) {
    $trail->parent('admin.doctor');
    $trail->push('Add doctor', route('admin.doctor.index'));
});

Breadcrumbs::for('admin.doctor.edit', function ($trail) {
    $trail->parent('admin.doctor');
    $trail->push('Edit doctor', route('admin.doctor.index'));
});

// Refer
Breadcrumbs::for('admin.doctor-invitation', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Reference', route('admin.doctor-invitation.index'));
});

Breadcrumbs::for('admin.doctor-invitation.create', function ($trail) {
    $trail->parent('admin.doctor-invitation');
    $trail->push('Add Reference', route('admin.doctor-invitation.index'));
});

Breadcrumbs::for('admin.doctor-invitation.edit', function ($trail) {
    $trail->parent('admin.doctor-invitation');
    $trail->push('Edit Reference', route('admin.doctor-invitation.index'));
});

// Specility
Breadcrumbs::for('admin.speciality', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Speciality', route('admin.speciality.index'));
});

Breadcrumbs::for('admin.speciality.create', function ($trail) {
    $trail->parent('admin.speciality');
    $trail->push('Add speciality', route('admin.speciality.index'));
});

Breadcrumbs::for('admin.speciality.edit', function ($trail) {
    $trail->parent('admin.speciality');
    $trail->push('Edit speciality', route('admin.speciality.index'));
});

// Symptoms
Breadcrumbs::for('admin.symptom', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Symptoms', route('admin.symptom.index'));
});

Breadcrumbs::for('admin.symptom.create', function ($trail) {
    $trail->parent('admin.symptom');
    $trail->push('Add symptom', route('admin.symptom.index'));
});

Breadcrumbs::for('admin.symptom.edit', function ($trail) {
    $trail->parent('admin.symptom');
    $trail->push('Edit symptom', route('admin.symptom.index'));
});

//Admin Bank Detail
Breadcrumbs::for('admin.settings.bank.detail', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Admin Eco Bank Detail', route('admin.bank-detail.index'));
});

// FAQ
Breadcrumbs::for('admin.settings.faq', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('FAQs', route('admin.faq.index'));
});

Breadcrumbs::for('admin.settings.faq.create', function ($trail) {
    $trail->parent('admin.settings.faq');
    $trail->push('Add FAQs', route('admin.faq.index'));
});

Breadcrumbs::for('admin.settings.faq.edit', function ($trail) {
    $trail->parent('admin.settings.faq');
    $trail->push('Edit FAQs', route('admin.faq.index'));
});

//CMS Page
Breadcrumbs::for('admin.settings.cmspages', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('CMS Pages', route('admin.cms.index'));
});

Breadcrumbs::for('admin.settings.cmspages.create', function ($trail) {
    $trail->parent('admin.settings.cmspages');
    $trail->push('Add CMS Pages', route('admin.cms.index'));
});

Breadcrumbs::for('admin.settings.cmspages.edit', function ($trail) {
    $trail->parent('admin.settings.cmspages');
    $trail->push('Edit CMS Pages', route('admin.cms.index'));
});

//Support Page
Breadcrumbs::for('admin.settings.support', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Support', route('admin.support.index'));
});

Breadcrumbs::for('admin.settings.support.create', function ($trail) {
    $trail->parent('admin.settings.support');
    $trail->push('Add CMS Pages', route('admin.cms.index'));
});

Breadcrumbs::for('admin.settings.support.edit', function ($trail) {
    $trail->parent('admin.settings.support');
    $trail->push('Edit CMS Pages', route('admin.cms.index'));
});

//Setting
Breadcrumbs::for('admin.settings.setting', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Setting', route('admin.setting.index'));
});

Breadcrumbs::for('admin.settings.setting.create', function ($trail) {
    $trail->parent('admin.settings.setting');
    $trail->push('Add Setting', route('admin.setting.index'));
});

Breadcrumbs::for('admin.settings.setting.edit', function ($trail) {
    $trail->parent('admin.settings.setting');
    $trail->push('Edit Setting', route('admin.setting.index'));
});


Breadcrumbs::for('admin.my-profile', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Edit Profile', route('dashboard'));
});

// Medicine
Breadcrumbs::for('admin.medicine', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Medicine', route('admin.medicine.index'));
});

Breadcrumbs::for('admin.medicine.create', function ($trail) {
    $trail->parent('admin.medicine');
    $trail->push('Add medicine', route('admin.medicine.index'));
});

Breadcrumbs::for('admin.medicine.edit', function ($trail) {
    $trail->parent('admin.medicine');
    $trail->push('Edit medicine', route('admin.medicine.index'));
});

// lab-test
Breadcrumbs::for('admin.lab-test', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Lab Test', route('admin.lab-test.index'));
});

Breadcrumbs::for('admin.lab-test.create', function ($trail) {
    $trail->parent('admin.lab-test');
    $trail->push('Add lab test', route('admin.lab-test.index'));
});

Breadcrumbs::for('admin.lab-test.edit', function ($trail) {
    $trail->parent('admin.lab-test');
    $trail->push('Edit lab test', route('admin.lab-test.index'));
});

// wallet transaction
Breadcrumbs::for('admin.wallet-transaction', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Doctor Wallet Transaction', route('admin.wallet-transaction.index'));
});

Breadcrumbs::for('admin.wallet-transaction.create', function ($trail) {
    $trail->parent('admin.wallet-transaction');
    $trail->push('Add lab test', route('admin.wallet-transaction.index'));
});

Breadcrumbs::for('admin.wallet-transaction.edit', function ($trail) {
    $trail->parent('admin.wallet-transaction');
    $trail->push('Edit lab test', route('admin.wallet-transaction.index'));
});

// transaction
Breadcrumbs::for('admin.transaction', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Transaction', route('admin.transactions.index'));
});

// Subscription
Breadcrumbs::for('admin.subscription', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Subscriptions', route('admin.subscription.index'));
});

Breadcrumbs::for('admin.subscription.create', function ($trail) {
    $trail->parent('admin.subscription');
    $trail->push('Add subscription', route('admin.subscription.index'));
});

Breadcrumbs::for('admin.subscription.edit', function ($trail) {
    $trail->parent('admin.subscription');
    $trail->push('Edit subscription', route('admin.subscription.index'));
});

Breadcrumbs::for('admin.subscription.view', function ($trail) {
    $trail->parent('admin.subscription');
    $trail->push('View subscription', route('admin.subscription.index'));
});

// Video
Breadcrumbs::for('admin.video', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Video', route('admin.video.index'));
});

Breadcrumbs::for('admin.video.create', function ($trail) {
    $trail->parent('admin.video');
    $trail->push('Add video', route('admin.video.index'));
});

Breadcrumbs::for('admin.video.edit', function ($trail) {
    $trail->parent('admin.video');
    $trail->push('Edit video', route('admin.video.index'));
});

// calls
Breadcrumbs::for('admin.call', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Appointment Calls', route('admin.call.index'));
});

Breadcrumbs::for('admin.call.view', function ($trail) {
    $trail->parent('admin.call');
    $trail->push('View appointment calls', route('admin.call.index'));
});

Breadcrumbs::for('admin.payout', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Payouts', route('admin.payout.index'));
});


// Email template
Breadcrumbs::for('admin.email-template', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Email Template', route('admin.email-template.index'));
});

Breadcrumbs::for('admin.email-template.view', function ($trail) {
    $trail->parent('admin.email-template');
    $trail->push('View email template', route('admin.email-template.index'));
});


// Notification
Breadcrumbs::for('admin.notification', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Notifications', route('admin.notification.index'));
});

// Logs
Breadcrumbs::for('admin.log', function ($trail) {
    $trail->parent('dashboard');
    $trail->push('Logs', route('admin.log.index'));
});
