<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\CardPaymentRequest;
use App\Http\Requests\TransactionRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\EcoBankRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;

class EcoBankController extends Controller
{
    /**
     * @var EcoBankRepositoryEloquent|string
     */
    public $ecoBankRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Doctor availability Controller constructor.
     * @param EcoBankRepositoryEloquent $ecoBankRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(EcoBankRepositoryEloquent $ecoBankRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->ecoBankRepository = $ecoBankRepository;
        $this->apiResponse = $apiFormattedResponse;
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function cardPayment(Request $request)
    {
        try {
            $cardPayment = $this->ecoBankRepository->cardPayment($request);
            return $this->apiResponse->respondWithMessageAndPayload($cardPayment, trans('messages.appointment_booked'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function ecoBankWebhook(Request $request)
    {
        try {
            $cardPayment = $this->ecoBankRepository->ecoBankWebhook($request);
            return $this->apiResponse->respondWithMessageAndPayload($cardPayment, trans('messages.unavailability_fetching'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
