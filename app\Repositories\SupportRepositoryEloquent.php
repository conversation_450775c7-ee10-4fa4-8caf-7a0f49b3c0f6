<?php

namespace App\Repositories;

use App\Enums\NotificationType;
use App\Enums\SenderType;
use App\Enums\SupportStatus;
use App\Enums\UserType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\Notification;
use App\Models\Admin;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\SupportRepository;
use App\Models\Support;
use App\Models\SupportChat;
use App\Models\User;
use App\Transformers\SupportChatTransformer;
use App\Transformers\SupportTransformer;
use App\Transformers\SupportWithChatTransformer;
use App\Validators\SupportValidator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\DataTables;

/**
 * Class SupportRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class SupportRepositoryEloquent extends BaseRepository implements SupportRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Support::class;
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $support = Support::select(['supports.*', 'users.first_name'])->with('user')
        ->join('users','supports.user_id','=', 'users.id');

        if (!empty($request->start_date)) {
            $support = $support->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($support)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="' . route('admin.support.change-status-active', ['id' => $row->id]) . '" class="menu-link px-3 changeStatus">
                        Open
                    </a>';
                } else {
                    $html .= '<a href="' . route('admin.support.change-status-deactive', ['id' => $row->id]) . '" class="menu-link px-3 changeStatus">
                        Closed
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.support.chat', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Chat
                        </a>
                    </div>
                </div>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->filterColumn('name', function ($query, $keyword) {
                return $query->whereHas('user', function ($q) use ($keyword) {
                    $q->searchByName($keyword);;
                });
            })
            ->orderColumn('name', function ($query, $order) {
                return $query->orderBy('first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request, $id)
    {
        $support = Support::findOrFail($id);

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($support->user_id);
        $extra = [
            'notification_type' => NotificationType::SupportResolve,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'sender_type' => SenderType::Admin,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Support Ticket Close',
            'message' => $sender->name." has mark your ticket as a completed.",
            'ref_id' => $support->id,
            'ref_type' => 'Support'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        $support->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request, $id)
    {
        $support = Support::findOrFail($id);

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($support->user_id);
        $extra = [
            'notification_type' => NotificationType::SupportResolve,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'sender_type' => SenderType::Admin,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Support Ticket Close',
            'message' => $sender->name." has mark your ticket as a completed.",
            'ref_id' => $support->id,
            'ref_type' => 'Support'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        $support->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function store($request)
    {
        $data = $request->all();
        $data['user_id'] = Auth::guard('api')->user()->id;
        $data['unique_id'] = generateUniqueSupportTicket();
        $help = Support::create($data);

        //Send Notification to Admin
        $sender = auth()->user();
        $receiver = Admin::first();
        $extra = [
            'notification_type' => NotificationType::Support,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->id,
            'receiver_type' => SenderType::Admin,
            'doctor_id' => $receiver->id,
            'title' => 'Support',
            'message' => $sender->name." has generated support.",
            'ref_id' => $help->id,
            'ref_type' => 'Support',
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        return $help;
    }

    /**
     * @param $request
     */
    public function patientDoctorSupportList($request)
    {
        $support = Support::whereHas('doctorAppointment', function($q){
            $q->where('doctor_id', auth()->user()->id);
        })->whereHas('user', function($q){
            $q->where('user_type', UserType::Patient);
        })->orderBy('created_at', 'DESC')->paginate(10);
        return fractal($support, new SupportTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * @param $request
     */
    public function doctorSupportList($request)
    {
        $support = Support::where('user_id', auth()->user()->id)->orderBy('created_at', 'DESC')->paginate(10);
        return fractal($support, new SupportTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * @param $request
     */
    public function patientSupportList($request)
    {
        $support = Support::where('user_id', auth()->user()->id)->orderBy('created_at', 'DESC')->paginate(10);
        return fractal($support, new SupportTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * @param $request
     */
    public function markResolve($request, $id)
    {
        $support = Support::findOrFail($id);
        $support->update(['status' => SupportStatus::Completed]);

         //Send Notification to Admin
         $sender = auth()->user();
         $receiver = User::findOrFail($support->user_id);
         $extra = [
             'notification_type' => NotificationType::SupportResolve,
             'sender_name' => $sender->name,
             'sender_avatar' => $sender->avatar_url,
             'receiver_type' => SenderType::User,
             'title' => 'Support',
             'message' => $sender->name." has marked your ticket has been closed.",
             'ref_id' => $support->id,
             'ref_type' => 'Support',
         ];

         dispatch(new Notification($sender, $receiver, $extra));

         $receiver->update(['notification_indicator' => true]);

        return fractal($support, new SupportTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * @param $request
     */
    public function createSupportChat($request){
        $supportChat = SupportChat::create($request->all());
        return fractal($supportChat, new SupportChatTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * @param $request
     */
    public function supportChatList($request, $id){
        $supportChat = Support::with('supportChat')->findOrFail($id);
        return fractal($supportChat, new SupportWithChatTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * @param $request
     */
    public function chat($request, $id){
        $supportChat = Support::with('supportChat', 'supportChat.senderUser', 'supportChat.senderAdmin')->findOrFail($id);
        return $supportChat;
    }

    /**
     * @param $request
     */
    public function createChat($request, $id){
        $data = $request->all();
        $data['sender_id'] = Auth::guard('admin')->user()->id;
        $data['sender_type'] = SenderType::Admin;
        $data['is_read'] = 0;
        if ($request->has('media')) {
            $data['media'] = self::fileUpload($request->media, 'molema/support/chat');
            $data['message_type'] = 'document';
            $data['message'] = $data['media'];
        }
        if(empty($request['media'])){
            $data['message_type'] = 'text';
        }
        $chat = SupportChat::create($data);

        $supportChat = Support::with('supportChat', 'supportChat.senderUser', 'supportChat.senderAdmin')->findOrFail($data['support_id']);

        //Send Notification to Admin
        $sender = auth()->user();
        $receiver = User::findOrFail($supportChat->user_id);
        $extra = [
            'notification_type' => NotificationType::SupportResolve,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'receiver_type' => SenderType::User,
            'title' => 'Support',
            'message' => "You've received a new message regarding your support ticket by ".$sender->name,
            'ref_id' => $supportChat->id,
            'ref_type' => 'Support',
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return $supportChat;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
