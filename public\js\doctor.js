/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!***************************************!*\
  !*** ./resources/assets/js/doctor.js ***!
  \***************************************/


if ($("#doctor_table").length > 0) {
  // Class definition
  var KTDatatablesServerSide = function () {
    // Shared variables
    var table;
    var datatable;
    var filterPayment;
    $('input[name="search"]').daterangepicker({
      autoUpdateInput: false,
      autoclose: false,
      locale: {
        cancelLabel: "Clear"
      }
    });

    // Private functions
    var initDatatable = function initDatatable() {
      datatable = $("#doctor_table").DataTable({
        searchDelay: 500,
        processing: true,
        serverSide: true,
        order: [[5, "desc"]],
        stateSave: false,
        select: {
          style: "multi",
          selector: 'td:first-child input[type="checkbox"]',
          className: "row-selected"
        },
        ajax: {
          url: getListRoute,
          data: function data(d) {
            var dt_params = $("#doctor_table").data("dt_params");
            console.log(dt_params);
            if (dt_params) {
              $.extend(d, dt_params);
            }
          },
          error: function error(jqXHR, exception) {
            if (jqXHR.status == 401 || jqXHR.status == 419) {
              window.location.href = login;
            }
          }
        },
        columns: [{
          data: "DT_RowIndex",
          name: "DT_RowIndex",
          orderable: false,
          searchable: false
        }, {
          data: "name",
          name: "name"
        }, {
          data: "email",
          name: "email"
        }, {
          data: "mobile",
          name: "mobile"
        }, {
          data: "account_verified",
          name: "account_verified",
          orderable: false
        }, {
          data: "status",
          name: "status"
        }, {
          data: "created_date",
          name: "created_date",
          searchable: false
        }, {
          data: "action",
          name: "action",
          searchable: false
        }],
        columnDefs: [{
          targets: 1,
          className: "d-flex align-items-center",
          render: function render(data, type, row) {
            var doctorUrl = "".concat(doctorRoute).replace('id', "".concat(row.id));
            return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.avatar_url, "\" alt=\"").concat(row.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=") + doctorUrl + " class=\"text-gray-800 text-hover-primary mb-1\">".concat(row.name, "</a>\n                            <span>").concat(row.username, "</span>\n                        </div>");
          }
        }, {
          targets: 3,
          render: function render(data, type, row) {
            return "<div>".concat(row.dial_code, " ").concat(row.mobile, "</div>");
          }
        }, {
          targets: 4,
          render: function render(data, type, row) {
            return "".concat(row.doctor_detail.account_verified_at) !== 'null' ? "<div class=\"badge badge-light-primary\">Verified</div>" : "<div class=\"badge badge-light-warning\"\">Unverified</div>";
          }
        }, {
          targets: 5,
          render: function render(data, type, row) {
            return "".concat(row.status) == true ? "<div class=\"badge badge-light-success\">Active</div>" : "<div class=\"badge badge-light-danger\">Inactive</div>";
          }
        }]
      });
      table = datatable.$;
      $('#doctor_table thead tr th').removeClass('d-flex align-items-center');
      // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
      datatable.on("draw", function () {
        KTMenu.createInstances();
      });
    };

    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
    var handleSearchDatatable = function handleSearchDatatable() {
      var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
      filterSearch.addEventListener("keyup", function (e) {
        datatable.search(e.target.value).draw();
      });
    };

    // Filter Datatable
    var handleFilterDatatable = function handleFilterDatatable() {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Filter datatable on submit
      filterButton.addEventListener("click", function () {
        var filterString = "";

        // Get filter values
        selectOptions.forEach(function (item, index) {
          if (item.value && item.value !== "") {
            if (index == 0) {
              datatable.column(5).search(item.value).draw();
            }
            if (index == 1) {
              datatable.column(4).search(item.value).draw();
            }
          } else {
            if (index == 0) {
              datatable.column(5).search("").draw();
            }
            if (index == 1) {
              datatable.column(4).search("").draw();
            }
          }
        });
      });
    };

    // Reset Filter
    var handleResetForm = function handleResetForm() {
      // Select reset button
      var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

      // Reset datatable
      resetButton.addEventListener("click", function () {
        // Select filter options
        var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
        var selectOptions = filterForm.querySelectorAll("select");

        // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
        selectOptions.forEach(function (select) {
          $(select).val("").trigger("change");
        });

        // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
        datatable.search("").columns().search("").draw();
      });
    };
    var initDaterangepicker = function initDaterangepicker() {
      var start = moment().startOf("year");
      var end = moment().endOf("year");
      var input = $('input[name="search"]');
      function cb(start, end) {
        input.val(start.format("YYYY/MM/DD") + " - " + end.format("YYYY/MM/DD"));
        var start_date = start.format("YYYY/MM/DD");
        var end_date = end.format("YYYY/MM/DD");
        $("#doctor_table").data("dt_params", {
          start_date: start_date,
          end_date: end_date
        });
        datatable.draw();
      }
      input.daterangepicker({
        startDate: start,
        endDate: end,
        ranges: {
          Today: [moment(), moment()],
          Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
          "Last 7 Days": [moment().subtract(6, "days"), moment()],
          "Last 30 Days": [moment().subtract(29, "days"), moment()],
          "This Month": [moment().startOf("month"), moment().endOf("month")],
          "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
          "This Year": [moment().startOf("year"), moment().endOf("year")],
          "Last Year": [moment().subtract(1, "year").startOf("year"), moment().subtract(1, "year").endOf("year")]
        }
      }, cb);
      cb(start, end);
    };
    $('input[name="search"]').on("apply.daterangepicker", function (ev, picker) {
      $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
      var start_date = picker.startDate.format("YYYY/MM/DD");
      var end_date = picker.endDate.format("YYYY/MM/DD");
      $("#doctor_table").data("dt_params", {
        start_date: start_date,
        end_date: end_date
      });
      datatable.draw();
    });
    $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
      $(this).val("");
      $("#doctor_table").data("dt_params", {
        start_date: null,
        end_date: null
      });
      datatable.draw();
    });

    // Public methods
    return {
      init: function init() {
        initDatatable();
        handleSearchDatatable();
        handleResetForm();
        handleFilterDatatable();
        initDaterangepicker();
      }
    };
  }();

  // On document ready
  KTUtil.onDOMContentLoaded(function () {
    KTDatatablesServerSide.init();
  });
}
$(document).on("click", ".changeStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#doctor_table").DataTable().ajax.reload();
    }
  });
});
$(document).on("click", ".accountVerify", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#doctor_table").DataTable().ajax.reload();
    }
  });
});
if ($("#export").length > 0) {
  var url = new URL($('#export').attr('href'));
  $('#patientstatus').on('change', function () {
    url.searchParams.set('patientstatus', this.value);
    $("a#export").attr("href", url);
  });
  $('#patientverification').on('change', function () {
    url.searchParams.set('patientverification', this.value);
    $("a#export").attr("href", url);
  });
  $('#search').on("input", function () {
    url.searchParams.set('search', this.value);
    $("a#export").attr("href", url);
  });
}
$('#virtual_consultation').change(function () {
  if (this.checked) {
    $('#virtual_consultation').val(1);
  } else {
    $('#virtual_consultation').val(0);
  }
});
$('#home_consultation').change(function () {
  if (this.checked) {
    $('#home_consultation').val(1);
  } else {
    $('#home_consultation').val(0);
  }
});
$('#physical_consultation').change(function () {
  if (this.checked) {
    $('#physical_consultation').val(1);
  } else {
    $('#physical_consultation').val(0);
  }
});
$(document).ready(function () {
  function show() {
    var p = document.getElementById('password');
    p.setAttribute('type', 'text');
  }
  function hide() {
    var p = document.getElementById('password');
    p.setAttribute('type', 'password');
  }
  var pwShown = 0;
  $('.uncheck-eye').hide();
  $('.cmfuncheck-eye').hide();
  $('.check-eye').click(function (e) {
    e.preventDefault();
    if (pwShown == 0) {
      pwShown = 1;
      show();
      $('.uncheck-eye').show();
      $('.check-eye').hide();
    } else {
      pwShown = 0;
      hide();
      $('.uncheck-eye').hide();
      $('.check-eye').show();
    }
  });
  $('.uncheck-eye').click(function (e) {
    e.preventDefault();
    if (pwShown == 0) {
      pwShown = 1;
      show();
      $('.uncheck-eye').show();
      $('.check-eye').hide();
    } else {
      pwShown = 0;
      hide();
      $('.uncheck-eye').hide();
      $('.check-eye').show();
    }
  });
});
$('#generate').on('click', function () {
  var pass = '';
  var str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz0123456789@#$';
  for (var i = 1; i <= 8; i++) {
    var _char = Math.floor(Math.random() * str.length + 1);
    pass += str.charAt(_char);
  }
  $('#password').val(pass);
});
/******/ })()
;