<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\RatingRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorRatingRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DoctorRatingController extends Controller
{
    /**
     * @var DoctorRatingRepositoryEloquent|string
     */
    public $doctorRatingRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Rating Page Controller constructor.
     * @param DoctorRatingRepositoryEloquent $doctorRatingRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorRatingRepositoryEloquent $doctorRatingRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->doctorRatingRepository = $doctorRatingRepository;
        $this->apiResponse = $apiFormattedResponse;
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function createRating(RatingRequest $request)
    {
        try {
            $rating = $this->doctorRatingRepository->createRating($request);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_created'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) { dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function editRating(Request $request, $id)
    {
        try {
            $rating = $this->doctorRatingRepository->editRating($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_updated'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

     /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function deleteRating(Request $request, $id)
    {
        try {
            $rating = $this->doctorRatingRepository->deleteRating($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_deleted'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewRating(Request $request, $id)
    {
        try {
            $rating = $this->doctorRatingRepository->viewRating($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function myRating(Request $request)
    {
        try {
            $rating = $this->doctorRatingRepository->myRating($request);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function doctorViewRating(Request $request)
    {
        try {
            $rating = $this->doctorRatingRepository->doctorViewRating($request);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function detailRating(Request $request)
    {
        try {
            $rating = $this->doctorRatingRepository->detailRating($request);
            return $this->apiResponse->respondWithMessageAndPayload($rating, trans('messages.rating_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
