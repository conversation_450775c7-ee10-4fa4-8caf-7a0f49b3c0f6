<?php

namespace App\Transformers;

use App\Models\DoctorRating;
use League\Fractal\TransformerAbstract;

class DoctorRatingTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorRating $doctorRating)
    {
        return [
            'id' => $doctorRating->id,
            'patient_id' => $doctorRating->patient_id,
            'patient_name' => $doctorRating->patient->name,
            'patient_avatar' => $doctorRating->patient->avatar_url,
            'doctor_id' => $doctorRating->doctor_id,
            'doctor_name' => $doctorRating->doctor->name,
            'doctor_avatar' => $doctorRating->doctor->avatar_url,
            'rating' => $doctorRating->rating,
            'notes' => $doctorRating->notes,
            'created_at' => $doctorRating->created_at
        ];
    }
}
