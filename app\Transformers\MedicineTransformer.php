<?php

namespace App\Transformers;

use App\Models\Medicine;
use League\Fractal\TransformerAbstract;

class MedicineTransformer extends TransformerAbstract
{
    
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Medicine $medicine)
    {
        return [
            'id' => $medicine->id,
            'name' => $medicine->name,
            'created_at' => $medicine->created_at
        ];
    }
}
