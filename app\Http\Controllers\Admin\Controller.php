<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Pagination\Paginator;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\View;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "";
    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * Instantiate a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->setupNavigation();
    }

    /**
     * Sets navigation data for sidebar
     *
     * @return void
     */
    protected function setupNavigation()
    {
        View::share('activeSidebarMenu', $this->activeSidebarMenu);
        View::share('activeSidebarSubMenu', $this->activeSidebarSubMenu);
    }
}
