@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">
    {{ Breadcrumbs::render('admin.payout') }}

    <div id="kt_app_content_container" class="app-container container-xxl">
        <div class="card mb-5 mb-xl-10">
            <div class="card-header border-0 pt-6">
                <!--begin::Card title-->
                <div class="card-title m-0">
                </div>
                <div class="card-toolbar">
                    <a href="{{ route('admin.payout.index') }}" class="btn btn-sm btn-light-primary">
                        <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-9 pb-0">
                <!--begin::Details-->
                <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
                    <!--begin: Pic-->
                    <div class="me-7 mb-4">
                        <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                            <img src="{{ $doctorDetail->avatar_url }}" alt="image" />
                            {{-- <div class="position-absolute translate-middle bottom-0 start-100 mb-6 bg-success rounded-circle border border-4 border-body h-20px w-20px"></div> --}}
                        </div>
                    </div>
                    <!--end::Pic-->
                    <!--begin::Info-->
                    <div class="flex-grow-1">
                        <!--begin::Title-->
                        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                            <!--begin::User-->
                            <div class="d-flex flex-column">
                                <!--begin::Name-->
                                <div class="d-flex align-items-center mb-2">
                                    <a href="#" class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">{{ $doctorDetail->name }}</a>
                                </div>
                                <!--end::Name-->
                                <!--begin::Info-->
                                <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                    <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2">
                                        <!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
                                        <span class="svg-icon svg-icon-4 me-1">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor" />
                                                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor" />
                                                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor" />
                                            </svg>
                                        </span>
                                        <!--end::Svg Icon-->
                                        {{ $doctorDetail->username }}
                                    </a>

                                    <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary mb-2">
                                        <!--begin::Svg Icon | path: icons/duotune/communication/com011.svg-->
                                        <span class="svg-icon svg-icon-4 me-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19Z" fill="currentColor" />
                                                <path d="M21 5H2.99999C2.69999 5 2.49999 5.10005 2.29999 5.30005L11.2 13.3C11.7 13.7 12.4 13.7 12.8 13.3L21.7 5.30005C21.5 5.10005 21.3 5 21 5Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                        <!--end::Svg Icon-->
                                        {{ $doctorDetail->email }}
                                    </a>
                                </div>
                                <!--end::Info-->
                            </div>
                            <!--end::User-->
                            <div class="d-flex flex-column ">
                                <!--begin::Stats-->
                                <div class="d-flex flex-wrap">
                                    <!--begin::Stat-->
                                    <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                        <!--begin::Number-->
                                        <div class="d-flex align-items-center">
                                            @if($walletBalance > 0)
                                                <!--begin::Svg Icon | path: icons/duotune/arrows/arr066.svg-->
                                                <span class="svg-icon svg-icon-3 svg-icon-success me-2">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect opacity="0.5" x="13" y="6" width="13" height="2" rx="1" transform="rotate(90 13 6)" fill="currentColor" />
                                                        <path d="M12.5657 8.56569L16.75 12.75C17.1642 13.1642 17.8358 13.1642 18.25 12.75C18.6642 12.3358 18.6642 11.6642 18.25 11.25L12.7071 5.70711C12.3166 5.31658 11.6834 5.31658 11.2929 5.70711L5.75 11.25C5.33579 11.6642 5.33579 12.3358 5.75 12.75C6.16421 13.1642 6.83579 13.1642 7.25 12.75L11.4343 8.56569C11.7467 8.25327 12.2533 8.25327 12.5657 8.56569Z" fill="currentColor" />
                                                    </svg>
                                                </span>
                                                <!--end::Svg Icon-->
                                            @endif
                                            <div class="fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="{{ $walletBalance }}" data-kt-countup-prefix="$">{{ $walletBalance }}</div>
                                        </div>
                                        <!--end::Number-->
                                        <!--begin::Label-->
                                        <div class="fw-semibold fs-6 text-gray-600">Earnings</div>
                                        <!--end::Label-->
                                    </div>
                                    <!--end::Stat-->
                                </div>
                                <!--end::Stats-->
                            </div>
                        </div>
                        <!--end::Title-->
                        @isset($doctorDetail->bankDetail[0])
                            <!--begin::Title-->
                            <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                                <!--begin::User-->
                                <div class="d-flex flex-column">
                                    <!--begin::Name-->
                                    <div class="d-flex align-items-center mb-2">
                                        <a href="#" class="text-gray-900 text-hover-primary fs-4 fw-bold me-1">Bank Details</a>
                                    </div>
                                    <!--end::Name-->
                                    <!--begin::Info-->
                                    <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                        @isset($doctorDetail->bankDetail[0]->account_number)
                                            <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
                                                <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M22 7H2V11H22V7Z" fill="currentColor"/>
                                                    <path opacity="0.3" d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19ZM14 14C14 13.4 13.6 13 13 13H5C4.4 13 4 13.4 4 14C4 14.6 4.4 15 5 15H13C13.6 15 14 14.6 14 14ZM16 15.5C16 16.3 16.7 17 17.5 17H18.5C19.3 17 20 16.3 20 15.5C20 14.7 19.3 14 18.5 14H17.5C16.7 14 16 14.7 16 15.5Z" fill="currentColor"/>
                                                    </svg>
                                                </span>
                                                <!--end::Svg Icon-->
                                                Acc Name : <span class="text-gray-800">{{ $doctorDetail->bankDetail[0]->account_name }}</span>
                                            </a>
                                        @endisset

                                        @isset($doctorDetail->bankDetail[0]->account_number)
                                            <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
                                                {{-- <span class="svg-icon svg-icon-4 me-1">
                                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor" />
                                                        <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor" />
                                                        <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor" />
                                                    </svg>
                                                </span> --}}
                                                <!--end::Svg Icon-->
                                                Acc No : <span class="text-gray-800">{{ $doctorDetail->bankDetail[0]->account_number }}</span>
                                            </a>
                                        @endisset

                                        @isset($doctorDetail->bankDetail[0]->account_type)
                                            <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com006.svg-->
                                                {{-- <span class="svg-icon svg-icon-4 me-1">
                                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor" />
                                                        <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor" />
                                                        <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor" />
                                                    </svg>
                                                </span> --}}
                                                <!--end::Svg Icon-->
                                                Type : <span class="text-gray-800"> {{ $doctorDetail->bankDetail[0]->account_type }}</span>
                                            </a>
                                        @endisset

                                        @isset($doctorDetail->bankDetail[0]->bank_code)
                                            <a href="#" class="d-flex align-items-center text-gray-600 text-hover-primary mb-2">
                                                <!--begin::Svg Icon | path: icons/duotune/communication/com011.svg-->
                                                <span class="svg-icon svg-icon-4 me-1">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path opacity="0.3" d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19Z" fill="currentColor" />
                                                        <path d="M21 5H2.99999C2.69999 5 2.49999 5.10005 2.29999 5.30005L11.2 13.3C11.7 13.7 12.4 13.7 12.8 13.3L21.7 5.30005C21.5 5.10005 21.3 5 21 5Z" fill="currentColor" />
                                                    </svg>
                                                </span>
                                                <!--end::Svg Icon-->
                                                Branch Code: <b class="text-gray-800">{{ $doctorDetail->bankDetail[0]->bank_code }}</b>
                                            </a>
                                        @endisset
                                    </div>
                                    <!--end::Info-->
                                </div>
                                <!--end::User-->
                            </div>
                            <!--end::Title-->
                        @endisset
                    </div>
                    <!--end::Info-->
                </div>
                <!--end::Details-->
            </div>
        </div>

        @if ($walletBalance > 0)
            <div class="card">
                <div class="card-header border-0 pt-6">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        {{__('Payout Release')}}
                    </div>
                </div>
                {{ Form::model(['route' => ['admin.payout.release', $id], 'method' => 'post','class'=>'form','id'=>'payout-release-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$id) !!}

                    <div class="card-body py-4">
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Payout Due', null, ['class' => 'col-lg-12 col-form-label required fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::number('payout_amount',$walletBalance, ['max' => $walletBalance, 'min'=> "1" ,'class' => 'form-control form-control-lg form-control-solid mb-3 mb-lg-0','id'=>'payout_amount','placeholder'=>'Enter Payout Due']) !!}
                                    @if($errors->has('payout_amount'))
                                        <div id="payout_amount-error" class="fv-plugins-message-container invalid-feedback" style="display: block;">
                                            {{ $errors->first('payout_amount') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Reference No', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('reference_no',"", ['class' => 'form-control form-control-lg form-control-solid mb-3 mb-lg-0','id'=>'reference_no','placeholder'=>'Enter Reference No/Transaction ID']) !!}
                                    @if($errors->has('reference_no'))
                                        <div id="reference_no-error" class="fv-plugins-message-container invalid-feedback" style="display: block;">
                                            {{ $errors->first('reference_no') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Notes', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('note',"", ['class' => 'form-control form-control-lg form-control-solid mb-3 mb-lg-0','id'=>'note','placeholder'=>'Enter Notes']) !!}
                                    @if($errors->has('note'))
                                        <div id="note-error" class="fv-plugins-message-container invalid-feedback" style="display: block;">
                                            {{ $errors->first('note') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-start py-6 px-9">
                        {!! Form::submit('Release',['id'=>'kt_account_profile_details_submit','class'=>'btn btn-primary']) !!}
                    </div>
                {!! Form::close() !!}
            </div>
        @endif
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
<script type="text/javascript" src="{{asset('js/payout.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\PayoutRequest', '#payout-release-form'); !!}
<script type="text/javascript" src="{{asset('assets/js/custom/apps/subscriptions/add/advanced.js')}}"></script>
@endsection
