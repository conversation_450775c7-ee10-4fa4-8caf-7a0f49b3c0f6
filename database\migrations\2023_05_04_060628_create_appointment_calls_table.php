<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('appointment_calls', function(Blueprint $table) {
            $table->increments('id');
            $table->string('channel_name');
            $table->bigInteger('initiator_id');
            $table->bigInteger('receiver_id');
            $table->date('call_date');
            $table->time('start_time');
            $table->integer('calculated_seconds')->nullable();
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('appointment_calls');
	}
};
