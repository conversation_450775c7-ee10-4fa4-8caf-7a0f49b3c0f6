<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('doctor_unavailabilities', function(Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('user_id');
            $table->date('unavailable_date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->boolean('leave_status')->comment('1 = fullday, 0 = halfday')->default(1);
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('doctor_unavailabilities');
	}
};
