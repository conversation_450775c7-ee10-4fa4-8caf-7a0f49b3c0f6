<?php

namespace App\Transformers;

use App\Models\PatientLabTest;
use League\Fractal\TransformerAbstract;

class PatientLabTestTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = ['patient_lab_test_description'];
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientLabTest $patientLabTest)
    {
        return [
            'id' => $patientLabTest->id,
            'appointment_id' => $patientLabTest->appointment_id,
            'unique_id' => $patientLabTest->unique_id,
            'lab_test_pdf' => $patientLabTest->lab_test_pdf,
            'created_at' => $patientLabTest->created_at
        ];
    }

    /**
     * @param PatientLabTest $patientLabTest
     * @return \League\Fractal\Resource\Collection
     */
    public function includePatientLabTestDescription(PatientLabTest $patientLabTest)
    {
        return $this->collection($patientLabTest->patientLabTestDescription, new PatientLabTestDescriptionTransformer());
    }
}
