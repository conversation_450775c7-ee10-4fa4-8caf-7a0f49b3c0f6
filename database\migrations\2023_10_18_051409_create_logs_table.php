<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('logs', function(Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('user_id');
            $table->integer('user_type');
            $table->string('transaction_id')->nullable();
            $table->string('req_log')->nullable();
            $table->string('res_log')->nullable();
            $table->string('log_type')->nullable();
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('logs');
	}
};
