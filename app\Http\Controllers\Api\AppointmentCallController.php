<?php

namespace App\Http\Controllers\Api;

use App\Exports\AppointmentCallExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\AppointCallRequest;
use App\Http\Requests\CallLogRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Jobs\silentNotification;
use App\Models\AgoraToken;
use App\Models\AppointmentCall;
use App\Models\CallLog;
use App\Repositories\AppointmentCallRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Log;

class AppointmentCallController extends Controller
{
    /**
     * @var AppointmentCallRepositoryEloquent|string
     */
    public $appointmentCallRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param AppointmentCallRepositoryEloquent $appointmentCallRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(AppointmentCallRepositoryEloquent $appointmentCallRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->appointmentCallRepository = $appointmentCallRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    public function getInitiatorToken()
    {
        try {
            $initiatorToken = $this->appointmentCallRepository->getInitiatorToken($channel_name = 'molema');
            return $this->apiResponse->respondWithMessageAndPayload($initiatorToken, trans('messages.get_faq_data'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    public function getReceiverToken()
    {
        try {
            $receiverToken = $this->appointmentCallRepository->getReceiverToken($channel_name = 'molema');
            return $this->apiResponse->respondWithMessageAndPayload($receiverToken, trans('messages.get_faq_data'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    public function callNotification(AppointCallRequest $request)
    {
        try {
            $notification = $this->appointmentCallRepository->callNotification($request);
            return $this->apiResponse->respondWithMessageAndPayload($notification, trans('messages.get_faq_data'));
        } catch (Exception $exception) {
            Log::error('||============Error in callNotification==========||');
            Log::error("|| Error => " . $exception->getMessage());
            Log::error("|| File => " . $exception->getFile());
            Log::error("|| Line => " . $exception->getLine());
            Log::error('||===============================================||');
            if ($exception->getCode() == '404') {
                return $this->apiResponse->respondNotFoundWithPayload($exception, $exception->getMessage());
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    public function updateStatus(CallLogRequest $request)
    {
        try {
            $notification = $this->appointmentCallRepository->updateStatus($request);
            return $this->apiResponse->respondWithMessageAndPayload($notification, trans('messages.get_faq_data'));
        } catch (Exception $exception) {
            $callLog = CallLog::where('appointment_call_id', $request->appointment_call_id)->orderBy('created_at', 'DESC')->first();
            if ($exception->getCode() == '403') {
                return $this->apiResponse->respondForbiddenWithPayload($callLog, $exception->getMessage());
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
