<?php

namespace App\Repositories;

use App\Enums\NotificationType;
use App\Enums\PaymentStatus;
use App\Enums\SenderType;
use App\Enums\TransactionPaymentStatus;
use App\Enums\ConsultationType;
use App\Enums\TransactionType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\Notification;
use App\Jobs\SendEmails​;
use App\Models\DoctorAppointment;
use App\Models\DoctorWalletTransaction;
use App\Models\Log as ModelLog;
use App\Models\Setting;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\TransactionRepository;
use App\Models\Transaction;
use App\Models\User;
use App\Transformers\TransactionTransformer;
use App\Transformers\WalletTransformer;
use App\Validators\TransactionValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use App\Models\EmailTemplate;
use Log;

/**
 * Class TransactionRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class TransactionRepositoryEloquent extends BaseRepository implements TransactionRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Transaction::class;
    }

    /**
     * List of call
     *
     * @return array
     */
    public function getList($request)
    {
        $transaction = Transaction::
        select(['transactions.*', 'patient.first_name as patient_first_name', 'doctor.first_name as doctor_first_name'])
        ->with('patient', 'doctor', 'doctorAppointment')
        ->leftJoin('users as doctor','transactions.doctor_id','=', 'doctor.id')
        ->leftJoin('users as patient','transactions.patient_id','=', 'patient.id');

        if (!empty($request->start_date)) {
            $transaction = $transaction->whereBetween(DB::raw("DATE_FORMAT(transactions.created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($transaction)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->payment_status != TransactionPaymentStatus::Complete) {
                    if ($row->payment_status === TransactionPaymentStatus::Reward) {
                        $html .= '<a href="' . $row->reward_file . '" class="menu-link px-3" target="_new">Reward Attachment</a>';
                    }else{
                        if($row->transaction_type == TransactionType::Consultation){

                            if(!empty($row->doctorAppointment)){
                                $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                                Rewards </a>';
                            }else{
                                $html.= '-';
                            }
                        }else{
                            $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                            Reward</a>';
                        }
                    }
                }else{
                    $html .= '-';
                }

                $html .= '</div>
                </div>';
                return $html;
            })
            ->filterColumn('patient_name', function ($query, $keyword) {
                return $query->whereHas('patient', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->filterColumn('doctor_name', function ($query, $keyword) {
                return $query->whereHas('doctor', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('patient_name', function ($query, $order) {
                $query->orderBy('patient_first_name', $order);
            })
            ->orderColumn('doctor_name', function ($query, $order) {
                $query->orderBy('doctor_first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * List of call
     *
     * @return array
     */
    public function appointmentTransactionGetList($request)
    {
        $transaction = Transaction::select(['transactions.*', 'patient.first_name as patient_first_name', 'doctor.first_name as doctor_first_name'])->with('patient', 'doctor', 'doctorAppointment')
        ->join('users as doctor','transactions.doctor_id','=', 'doctor.id')
        ->join('users as patient','transactions.patient_id','=', 'patient.id')
        ->where('transaction_type', TransactionType::Consultation);

        if (!empty($request->start_date)) {
            $transaction = $transaction->whereBetween(DB::raw("DATE_FORMAT(transactions.created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($transaction)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->payment_status != TransactionPaymentStatus::Complete) {
                    if ($row->payment_status === TransactionPaymentStatus::Reward) {
                        $html .= '<a href="' . $row->reward_file . '" class="menu-link px-3" target="_new">Reward Attachment</a>';
                    }else{
                        if($row->transaction_type == TransactionType::Consultation){

                            if(!empty($row->doctorAppointment)){
                                $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                                Rewards </a>';
                            }else{
                                $html.= '-';
                            }
                        }else{
                            $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                            Reward</a>';
                        }
                    }
                }

                $html .= '</div>
                </div>';
                return $html;
            })
            ->filterColumn('patient_name', function ($query, $keyword) {
                return $query->whereHas('patient', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->filterColumn('doctor_name', function ($query, $keyword) {
                return $query->whereHas('doctor', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->filterColumn('payment_id', function ($query, $keyword) {
                return $query->whereHas('doctorAppointment', function ($que) use ($keyword) {
                    $que->where('unique_id', 'LIKE', '%' . trim($keyword) . '%');
                });
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('patient_name', function ($query, $order) {
                $query->orderBy('patient_first_name', $order);
            })
            ->orderColumn('doctor_name', function ($query, $order) {
                $query->orderBy('doctor_first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * List of call
     *
     * @return array
     */
    public function pharmacyTransactionGetList($request)
    {
        $transaction = Transaction::select(['transactions.*', 'patient.first_name as patient_first_name'])->with('patient', 'doctorAppointment')
        ->join('users as patient','transactions.patient_id','=', 'patient.id')
        ->where('transaction_type', TransactionType::Pharmacy);

        if (!empty($request->start_date)) {
            $transaction = $transaction->whereBetween(DB::raw("DATE_FORMAT(transactions.created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($transaction)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->payment_status != TransactionPaymentStatus::Complete) {
                    if ($row->payment_status === TransactionPaymentStatus::Reward) {
                        $html .= '<a href="' . $row->reward_file . '" class="menu-link px-3" target="_new">Reward Attachment</a>';
                    }else{
                        $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                        Reward</a>';
                    }
                }

                $html .= '</div>
                </div>';
                return $html;
            })
            ->filterColumn('patient_name', function ($query, $keyword) {
                return $query->whereHas('patient', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('patient_name', function ($query, $order) {
                $query->orderBy('patient_first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * List of call
     *
     * @return array
     */
    public function subscriptionTransactionGetList($request)
    {
        $transaction = Transaction::select(['transactions.*', 'doctor.first_name as doctor_first_name'])->with('doctor', 'doctorSubscription', 'doctorSubscription.subscription')
        ->join('users as doctor','transactions.doctor_id','=', 'doctor.id')
        ->where('transaction_type', TransactionType::Subscription);

        if (!empty($request->start_date)) {
            $transaction = $transaction->whereBetween(DB::raw("DATE_FORMAT(transactions.created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($transaction)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->payment_status != TransactionPaymentStatus::Complete) {
                    if ($row->payment_status === TransactionPaymentStatus::Reward) {
                        $html .= '<a href="' . $row->reward_file . '" class="menu-link px-3" target="_new">Reward Attachment</a>';
                    }else{
                        $html .= '<a href="#" class="menu-link px-3 changeStatus" data-bs-target="#kt_modal_transaction" data-bs-toggle="modal" id="' . $row->id . '">
                        Reward</a>';
                    }
                }


                $html .= '</div>
                </div>';
                return $html;
            })
            ->filterColumn('doctor_name', function ($query, $keyword) {
                return $query->whereHas('doctor', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                });
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('doctor_name', function ($query, $order) {
                $query->orderBy('doctor_first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Patient Transaction history api
     *
     * @return array
     */
    public function transactionHistory($request)
    {
        $transactions = Transaction::with('patient:id,first_name,last_name', 'doctor:id,first_name,last_name', 'doctorAppointment:id,unique_id')->where('patient_id', auth()->user()->id);
        if($request->reward == true){
            $transactions = $transactions->where('payment_status', TransactionPaymentStatus::Reward);
        }
        $transactions = $transactions->orderBy('created_at', 'DESC')->paginate(10);

        return fractal($transactions, new TransactionTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Patient Transaction history api
     *
     * @return array
     */
    public function walletHistory($request)
    {
        $walletTransaction = DoctorWalletTransaction::with('transaction.patient:id,first_name,last_name', 'transaction.doctor:id,first_name,last_name', 'doctorAppointment:id,unique_id', 'transaction')
            ->whereHas('transaction', function ($query) {
                $query->where('doctor_id', auth()->user()->id);
            });

        $totalEarningWallet = clone ($walletTransaction);
        $totalEarningGet = clone ($walletTransaction);

        $totalEarning = $totalEarningWallet->where('wallet_type', 1)->sum('wallet_amount');

        $totalGet = $totalEarningGet->where('wallet_type', 0)->sum('wallet_amount');

        $wallet = $totalEarning - $totalGet;

        $walletTransaction = $walletTransaction->orderBy('created_at', 'DESC')->paginate(10);

        return ['total_earnings' => $totalEarning, 'wallet' => $wallet, 'wallet_transaction' => fractal($walletTransaction, new WalletTransformer())->serializeWith(new PaginationArraySerializer())];
    }

    /**
     * Check payment status
     *
     * @return array
     */
    public function checkPaymentStatus($request)
    {
        $transactions = Transaction::where('transaction_id', $request->transaction_id)->first();

        ModelLog::create([
            'user_id' => auth()->user()->id,
            'user_type' => SenderType::User,
            'transaction_id' => $transactions->transaction_id,
            'req_log' => json_encode($request->all()),
            'res_log' => json_encode($transactions),
            'log_type' => 'check-payment-status',
        ]);

        return fractal($transactions, new TransactionTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Patient Transaction history api
     *
     * @return array
     */
    public function updatePaymentStatus($request){
        try{
            Log::info("=====================updatePaymentStatus Starts=======================");
            Log::info('Payload => '.json_encode($request));

            $transactions = Transaction::where('transaction_id', $request->transaction_id)->first();

            $appointmentPayment = DoctorAppointment::findOrFail($transactions->payment_id);

            $transactions->update([
                'payment_status' => $request->payment_status,
            ]);

            if ($request->payment_status == TransactionPaymentStatus::Complete) {
                $appointmentPayment->update(['payment_status' => PaymentStatus::completed]);
                Log::info("Transaction ".$transactions->id." marked as completed");
                //Send appointment payment notification to Patient
                $sender = $appointmentPayment->patient;
                $receiver = $appointmentPayment->patient;
                $extra = [
                    'notification_type' => NotificationType::AppointmentPaymentSuccess,
                    'sender_name' => $sender->name,
                    'sender_avatar' => $sender->avatar_url,
                    'patient_id' => $sender->id,
                    'doctor_id' => $receiver->id,
                    'title' => 'Appointment Payment',
                    'message' => "Your appointment" . $appointmentPayment->unique_id . "payement has been completed.",
                    'ref_id' => $transactions->id,
                    'ref_type' => 'Transaction'
                ];

                dispatch(new Notification($sender, $receiver, $extra));
                Log::info("appointment payment notification to Patient => ".json_encode($extra));

                //Send book appointment notification to Patient
                $sender = User::findOrFail($transactions->doctor_id);
                $receiver = $appointmentPayment->patient;
                $extra = [
                    'notification_type' => NotificationType::AppointmentBooked,
                    'sender_name' => $sender->name,
                    'sender_avatar' => $sender->avatar_url,
                    'patient_id' => $sender->id,
                    'doctor_id' => $receiver->id,
                    'title' => 'Appointment Book',
                    'message' => "Your appointment of" . $sender->name . " has been booked successfully.",
                    'ref_id' => $appointmentPayment->id,
                    'ref_type' => 'DoctorAppointment'
                ];

                dispatch(new Notification($sender, $receiver, $extra));

                Log::info("book appointment notification to Patient => ".json_encode($extra));
                $receiver->update(['notification_indicator' => true]);

                //Send book appointment email notification to Patient
                $emailTemplatePatient = EmailTemplate::where('slug', 'appointment_booking_patient')->first();
                $emailBody = $emailTemplatePatient->getTranslation('description', $appointmentPayment->patient->language);
                $email_content = str_replace("[[USERNAME]]", ucwords($appointmentPayment->patient->name), $emailBody);
                $email_content = str_replace("[[APPOINTMENT_ID]]", $appointmentPayment->unique_id, $email_content);
                $email_content = str_replace("[[DOCTOR_NAME]]", ucwords($appointmentPayment->doctor->name), $email_content);
                $email_content = str_replace("[[PATIENT_NAME]]", ucwords($appointmentPayment->patient->name), $email_content);
                $email_content = str_replace("[[APPOINTMENT_DATE]]", $appointmentPayment->appointment_date, $email_content);
                $email_content = str_replace("[[START_TIME]]", date("h:i A", strtotime($appointmentPayment->start_time)), $email_content);
                $email_content = str_replace("[[END_TIME]]", date('h:i A', strtotime($appointmentPayment->end_time)), $email_content);
                $email_content = str_replace("[[CONSULTATION_TYPE]]", ConsultationType::getKey($appointmentPayment->consultation_type), $email_content);
                $email_content = str_replace("[[STATUS]]", AppointmentStatus::getKey($appointmentPayment->appointment_status), $email_content);
                $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);


                $patient = [
                    'email' => $appointmentPayment->patient->email,
                    'email_body' => $email_content,
                    'subject' => $emailTemplatePatient->getTranslation('title', $appointmentPayment->patient->language)
                ];
                dispatch((new SendEmails​($patient)));

                //Send Transaction email notification to Patient
                $emailTemplatePatient = EmailTemplate::where('slug', 'transaction_status_patient')->first();
                $emailBody = $emailTemplatePatient->getTranslation('description', $appointmentPayment->patient->language);
                $email_content = str_replace("[[USERNAME]]", ucwords($appointmentPayment->patient->name), $emailBody);
                $email_content = str_replace("[[TRANSACTION_ID]]", $transactions->transaction_id, $email_content);
                $email_content = str_replace("[[TRANSACTION_DATE]]", $transactions->created_at, $email_content);
                $email_content = str_replace("[[AMOUNT]]", $transactions->amount, $email_content);
                $email_content = str_replace("[[PAYMENT_TYPE]]", TransactionType::getKey($transactions->payment_type), $email_content);
                $email_content = str_replace("[[STATUS]]", TransactionPaymentStatus::getKey($transactions->payment_status), $email_content);
                $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);


                $patient = [
                    'email' => $appointmentPayment->patient->email,
                    'email_body' => $email_content,
                    'subject' => $emailTemplatePatient->getTranslation('title', $appointmentPayment->patient->language)
                ];
                dispatch((new SendEmails​($patient)));



                $data = [];
                $data['appointment_id'] = $transactions->appointment_id;
                $data['transaction_id'] = $transactions->id;
                $data['unique_id'] = generateRequestId($transactions->doctor_id);
                $data['doctor_type'] = $transactions->doctor->doctorDetail->doctor_type;
                if ($data['doctor_type'] == '1') {
                    $commission = Setting::where('slug', 'freelancer-commission')->first();
                } else {
                    $commission = Setting::where('slug', 'collaborator-commission')->first();
                }
                $data['commission'] = $commission->value;
                $walletAmount = $transactions->amount - ($transactions->amount * (int)$data['commission']) / 100;
                $data['wallet_amount'] = $walletAmount;
                $doctorWallet = DoctorWalletTransaction::create($data);

                ModelLog::create([
                    'user_id' => $appointmentPayment->patient->id,
                    'user_type' => SenderType::User,
                    'transaction_id' => $transactions->transaction_id,
                    'req_log' => json_encode($request->all()),
                    'res_log' => json_encode($doctorWallet),
                    'log_type' => 'update-payment-status',
                ]);

                // Appointment booked email notification to doctor
                $emailTemplateDoctor = EmailTemplate::where('slug', 'appointment_booking_doctor')->first();
                $emailBody = $emailTemplateDoctor->getTranslation('description', $appointmentPayment->doctor->language);
                $email_content_doctor = str_replace("[[USERNAME]]", ucwords($appointmentPayment->doctor->name), $emailBody);
                $email_content_doctor = str_replace("[[APPOINTMENT_ID]]", $appointmentPayment->unique_id, $email_content_doctor);
                $email_content_doctor = str_replace("[[DOCTOR_NAME]]", ucwords($appointmentPayment->doctor->name), $email_content_doctor);
                $email_content_doctor = str_replace("[[PATIENT_NAME]]", ucwords($appointmentPayment->patient->name), $email_content_doctor);
                $email_content_doctor = str_replace("[[APPOINTMENT_DATE]]", $appointmentPayment->appointment_date, $email_content_doctor);
                $email_content_doctor = str_replace("[[START_TIME]]", date("h:i A", strtotime($appointmentPayment->start_time)), $email_content_doctor);
                $email_content_doctor = str_replace("[[END_TIME]]", date('h:i A', strtotime($appointmentPayment->end_time)), $email_content_doctor);
                $email_content_doctor = str_replace("[[CONSULTATION_TYPE]]", ConsultationType::getKey($appointmentPayment->consultation_type), $email_content_doctor);
                $email_content_doctor = str_replace("[[STATUS]]", AppointmentStatus::getKey($appointmentPayment->appointment_status), $email_content_doctor);
                $email_content_doctor = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content_doctor);

                $doctor = [
                    'email' => $appointmentPayment->doctor->email,
                    'email_body' => $email_content_doctor,
                    'subject' => $emailTemplateDoctor->getTranslation('title', $appointmentPayment->doctor->language)
                ];

                dispatch((new SendEmails​($doctor)));


                //Send appointment wallet transaction to doctor
                $sender = $appointmentPayment->patient;
                $receiver = User::findOrFail($transactions->doctor_id);
                $extra = [
                    'notification_type' => NotificationType::DoctorWalletCredit,
                    'sender_name' => $sender->name,
                    'sender_avatar' => $sender->avatar_url,
                    'patient_id' => $sender->id,
                    'doctor_id' => $receiver->id,
                    'title' => 'Wallet Credit',
                    'message' => $sender->name . " appointment payment has been credited in your wallet.",
                    'ref_id' => $doctorWallet->id,
                    'ref_type' => 'DoctorWalletTransaction'
                ];

                dispatch(new Notification($sender, $receiver, $extra));

                $receiver->update(['notification_indicator' => true]);

            }

            return fractal($transactions, new TransactionTransformer())->serializeWith(new PaginationArraySerializer());
        }catch(\Exception $e){
            Log::error("===================================Error in updatePaymentStatus===============================");
            Log::error("Error => ".$e->getMessage());
            Log::error("Line => ".$e->getLine());
            Log::error("File => ".$e->getFile());
            Log::error("=====================================================================================");
        }
    }

    public function changeStatusReward($request){
        $transaction = Transaction::findOrFail($request->input('id'));
        $transaction->update(['payment_status' => TransactionPaymentStatus::Reward]);
    }



    public function importRewardFile($request){
        $transaction = Transaction::findOrFail($request->input('id'));
        if ($request->has('reward_file')) {
            $data['reward_file'] = self::fileUpload($request->reward_file, 'molema/reward');
        }
        $data['payment_status'] = TransactionPaymentStatus::Reward;
        return $transaction->update($data);
    }
    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
