<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\SettingRequest;
use App\Repositories\SettingRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "setting";

    /**
     * @var SettingRepositoryEloquent|string
     */
    public $settingRepository = "";

    public function __construct(SettingRepositoryEloquent $settingRepository)
    {
        parent::__construct();
        $this->settingRepository = $settingRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Setting']);
        return view('admin.settings.setting.index');
    }

    /**
     * Return setting list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->settingRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Speciality']);
        return view('admin.settings.setting.setting');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SettingRequest $request)
    {
        try {
            $setting = $this->settingRepository->store($request);
            $request->session()->flash('success', config("messages.setting_created"));
            return redirect()->route('admin.setting.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit setting list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Patient']);
        try {
            $setting = $this->settingRepository->edit($request, $id);
            return view('admin.settings.setting.setting', compact('setting'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.setting.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.setting.index');
        }
    }

    /**
     * Update setting list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(SettingRequest $request, $id)
    {
        try {
            $setting = $this->settingRepository->update($request, $id);
            $request->session()->flash('success', config("messages.setting_updated"));
            return redirect()->route('admin.setting.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.setting.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.setting.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->settingRepository->destroy($request);
            $request->session()->flash('success', config("messages.setting_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.setting.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.setting.index');
        }
    }

}
