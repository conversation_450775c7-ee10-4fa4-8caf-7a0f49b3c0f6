<?php

namespace App\Transformers;

use App\Models\DoctorAvailability;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use League\Fractal\TransformerAbstract;

class DoctorListTransformer extends TransformerAbstract
{

    protected array $defaultIncludes = [
        'doctor_detail', 'doctor_experience', 'doctor_rating', 'doctor_document'
    ];

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        return [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'dial_code' => $user->dial_code,
            'avatar' => $user->avatar_url ?? "",
            'avarage_rating' => !empty($user->doctorRating) ? $user->doctorRating->avg('rating') : 0,
            'total_rating' => !empty($user->doctorRating) ? $user->doctorRating->count() : 0,
            'experience' => !empty($user->doctorExperience) ? $user->doctorExperience->sum('years') : 0,
            'speciality' => $this->getDoctorSpeciality($user->speciality),
            'doctor_availability' => $this->getDoctorAVailability($user)
        ];
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function getDoctorSpeciality($speciality)
    {

        $specialityData = collect();
        $speciality->each(function ($special, $key) use ($specialityData) {
            $specialityData->push([
                'id' => $special->id,
                'specialist' => $special->specialist,
                'symptoms' => $special->symptom->symptoms_name
            ]);
        });
        return $specialityData;
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function getDoctorAVailability($user)
    {

        $doctorAvailability = DoctorAvailability::select('slot', DB::raw('count(*) as total'))->where('user_id', $user->id)->groupBy('slot', 'day')->orderBy('day')->get();
        $available = [];
        foreach ($doctorAvailability as $key => $availability) {
            $available[$key]['slot'] = $availability->slot;
            $doctorSlot = DoctorAvailability::where('user_id', $user->id)->where('slot', $availability->slot)->get();
            foreach ($doctorSlot as $key1 => $slot) {
                $available[$key]['day'][$key1]['day'] = $slot->day;
                $available[$key]['day'][$key1]['start_time'] = $slot->start_time;
                $available[$key]['day'][$key1]['end_time'] = $slot->end_time;
            }
        }
        return $available;
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Item
     */
    public function includeDoctorDetail(User $user)
    {
        if (!empty($user->doctorDetail)) {
            return $this->item($user->doctorDetail, new DoctorDetailTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function includeDoctorExperience(User $user)
    {
        if (!empty($user->doctorExperience)) {
            return $this->collection($user->doctorExperience, new DoctorExperienceTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function includeDoctorRating(User $user)
    {
        if (!empty($user->doctorRating)) {
            return $this->collection($user->doctorRating, new DoctorRatingTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function includeDoctorDocument(User $user)
    {
        if (!empty($user->doctorDocument)) {
            return $this->collection($user->doctorDocument, new DoctorDocumentTransformer());
        }
    }

}
