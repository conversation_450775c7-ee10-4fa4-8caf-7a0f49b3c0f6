@extends('admin.layouts.app')
@section('content')
		
	<div class="d-flex flex-column flex-column-fluid">
		{{ Breadcrumbs::render('admin.appointment.view') }}
		<!--begin::Content-->
		<div id="kt_app_content" class="app-content flex-column-fluid">
			<!--begin::Content container-->
			<div id="kt_app_content_container" class="app-container container-xxl">

				<!--begin::Layout-->
				<div class="d-flex flex-column flex-lg-row">
					<!--begin::Sidebar-->
					<div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
						<!--begin::Card-->
						<div class="card mb-5 mb-xl-8">
							<!--begin::Card body-->
							<div class="card-body">
								<!--begin::Details toggle-->
								<div class="d-flex flex-stack fs-4 py-3">
									<div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_user_view_details" role="button" aria-expanded="false" aria-controls="kt_user_view_details">Details
									<span class="ms-2 rotate-180">
										<!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
										<span class="svg-icon svg-icon-3">
											<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z" fill="currentColor" />
											</svg>
										</span>
										<!--end::Svg Icon-->
									</span></div>
								</div>
								<div class="separator"></div>
								<div id="kt_user_view_details" class="collapse show">
									<div class="pb-5 fs-6">
										@if(!empty($appointment->patient))
										<div class="fw-bold mt-5">Patient Name</div>
										<div class="text-gray-600">{{$appointment->patient->name}}</div>
										@endif
										@if(!empty($appointment->doctor))
										<div class="fw-bold mt-5">Doctor Name</div>
										<div class="text-gray-600">{{$appointment->doctor->name}}</div>
										@endif
										@if(!empty($appointment->transaction))
										<div class="fw-bold mt-5">Booking Date</div>
										<div class="text-gray-600">{{$appointment->transaction->created_date}}</div>
										@endif
										<div class="fw-bold mt-5">Payment Status</div>
										<div class="text-gray-600">
											@if($appointment->payment_status == 0)
											Incomplete
											@elseif($appointment->payment_status == 1)
											Completed
											@endif
										</div>
										<div class="fw-bold mt-5">Appointment Date</div>
										<div class="text-gray-600">{{$appointment->appointment_date}}</div>
										<div class="fw-bold mt-5">Appointment Time</div>
										<div class="text-gray-600">{{$appointment->start_time}}</div>
										
										<div class="fw-bold mt-5">Consultation Type</div>
										<div class="text-gray-600">
											@if($appointment->consultation_type == 1)
												Virtual Consultation
											@elseif($appointment->consultation_type == 2)
												Physical Consultation
											@elseif($appointment->consultation_type == 3)
												Home Consultation
											@endif
										</div>
										<div class="fw-bold mt-5">Appointment Status</div>
										<div class="text-gray-600">
											@if($appointment->appointment_status == 1)
											Scheduled
											@elseif($appointment->appointment_status == 2)
											Ongoing
											@elseif($appointment->appointment_status == 3)
											Completed
											@endif
										</div>
									</div>
								</div>

								<!--end::Details content-->
							</div>

							<!--end::Card body-->
						</div>
						<div class="card mb-5 mb-xl-8">
							<!--begin::Card header-->
							<div class="card-header border-0">
								<div class="card-title">
									<h3 class="fw-bold m-0">Patient Address</h3>
								</div>
							</div>
							<!--end::Card header-->
							<!--begin::Card body-->
							<div class="card-body pt-2">
								@if(!empty($appointment->patient) && !empty($appointment->patient->patientDetail))
									<div class="fw-bold mt-5">Patient Apartment No</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->apartment_no ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Address</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->address ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Landmark</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->landmark ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient City</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->city ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient State</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->state ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Country</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->country ?? "-"}}</div>
								@endif
							</div>
						</div>
						<!--end::Card-->
						<div class="card mb-5 mb-xl-8">
							<!--begin::Card header-->
							<div class="card-header border-0">
								<div class="card-title">
									<h3 class="fw-bold m-0">Clinical Details</h3>
								</div>
							</div>
							<!--end::Card header-->
							<!--begin::Card body-->
							<div class="card-body pt-2">
								@if(!empty($appointment->doctor) && !empty($appointment->doctor->doctorClinic))
									<div class="fw-bold mt-5">Clinic Name</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_name ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Moible</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->apartment_no ?? "-"}}</div>
									<div class="fw-bold mt-5">Apartment No</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_address ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Address</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_city ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Landmark</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_landmark ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic City</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_city ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic State</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_state ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Country</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->country ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Open Time</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_open_time ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Close Time</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_close_time ?? "-"}}</div>
								@endif
							</div>
						</div>
					</div>

					<div>
						<!--end::Sidebar-->
						<!--begin::Content-->
						<div class="flex-lg-row-fluid ms-lg-15 col-xl-12">

							<ul style="background-color:white;padding-left: 30px;" class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
								
								<!--begin::Nav item-->
								<li class="nav-item mt-2">
									<a id="prescriptionview" class="nav-link text-active-primary ms-0 me-10 py-5 active" href="{{route('admin.appointment.view',$appointment->id)}}">Prescriptions</a>
								</li>
								<!--end::Nav item-->
								<!--begin::Nav item-->
								<li class="nav-item mt-2">
									<a id="labview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.labview',$appointment->id)}}">Lab Tests</a>
								</li>
								<!--end::Nav item-->
								<!--begin::Nav item-->
								<li class="nav-item mt-2">
									<a id="notesview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.notesview',$appointment->id)}}">Notes</a>
								</li>
								<!--end::Nav item-->
							</ul>

							<!--begin:::Tab content-->
							<div class="tab-content" id="myTabContent">
								<!--begin:::Tab pane-->
								<div class="tab-pane fade show active" id="kt_user_view_overview_tab" role="tabpanel">
									<!--begin::Table Widget 4-->
									<div class="card card-flush">
										
										<!--begin::Card body-->
										<div class="card-body pt-2">
											<!--begin::Table-->
											<table class="table align-middle table-row-dashed fs-6 gy-3" id="kt_table_widget_4_table">
												<!--begin::Table head-->
												<thead>
													<!--begin::Table row-->
													<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
														<th class="min-w-100px">Id</th>
														<th class="min-w-100px">Unique Id</th>
														<th class="min-w-125px">Appointment</th>
														<th class="min-w-100px">Created</th>
														
														<th class="min-w-100px">PDF</th>
														<th></th>
													</tr>
													<!--end::Table row-->
												</thead>
												<!--end::Table head-->
												<!--begin::Table body-->
												<tbody class="fw-bold text-gray-600">
													@forelse($appointmentprescription as $key => $value)
							                        <?php
							                        $count = count($value->patientPrescriptionMedicine);
							                        ?>
													<tr>
														<td>
															{{$key+1}}
														</td>
														<td>{{$value->unique_id}}</td>
														<td>
															{{$value->appointment->unique_id}}
														</td>
														
														<td>
															{{$value->created_date}}
														</td>
														
														<td><a target="_blank" href="{{$value->prescription_pdf}}"><i class="fa fa-eye"></i></a></td>
														<td>
															<button type="button" class="btn btn-sm btn-icon btn-light btn-active-light-primary toggle h-25px w-25px subtable" id="subtable{{$value->id}}" data-kt-table-widget-4="expand_row">
																<!--begin::Svg Icon | path: icons/duotune/arrows/arr087.svg-->
																<span class="svg-icon svg-icon-3 m-0 toggle-off">
																	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																		<rect opacity="0.5" x="11" y="18" width="12" height="2" rx="1" transform="rotate(-90 11 18)" fill="currentColor" />
																		<rect x="6" y="11" width="12" height="2" rx="1" fill="currentColor" />
																	</svg>
																</span>
																<!--end::Svg Icon-->
																<!--begin::Svg Icon | path: icons/duotune/arrows/arr089.svg-->
																<span class="svg-icon svg-icon-3 m-0 toggle-on">
																	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																		<rect x="6" y="11" width="12" height="2" rx="1" fill="currentColor" />
																	</svg>
																</span>
																<!--end::Svg Icon-->
															</button>
														</td>
													</tr>
													@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
													<tr class="idsubtable{{$pvalue->patient_prescription_id}} d-none" data-kt-table-widget-4="subtable_template{{$pvalue->patient_prescription_id}}">
														<td>
															<div class="text-gray-800 fs-7">Name</div>
															<div class="text-muted fs-7 fw-bold">{{$pvalue->medicine_name}}</div>
														</td>
														<td>
															<div class="text-gray-800 fs-7">Type</div>
															<div class="text-muted fs-7 fw-bold">{{$pvalue->medicine_type}}</div>
														</td>
														<td>
															<div class="text-gray-800 fs-7">Course</div>
															<div class="text-muted fs-7 fw-bold" style="width:150px;">
																
									                            @php $stringBuffer = ''; @endphp
									                            @if(!empty($pvalue->medicine_course))
										                            @foreach(explode('_', $pvalue->medicine_course) as $key => $value)

											                            @php
											                            $val = explode('-', $value);
											                            $timingIdentifier = $val[0];
											                            $quantity = $val[1];
											                            $whenToTake = $val[2] == '1' ? 'Before Food' : 'After Food';
											                            @endphp

										                                @if ($quantity != '0')
										                                    @switch ($timingIdentifier)
										                                        @case('B')
										                                        {{ "Breakfast-".$quantity."-".$whenToTake }}<br>
										                                        @break

										                                        @case('L')
										                                        {{ "Lunch-".$quantity."-".$whenToTake }}<br>
										                                        @break

										                                        @case('D')
										                                        {{ "Dinner-".$quantity."-".$whenToTake }}
										                                        @break
										                                    @endswitch
										                                @endif

										                            @endforeach
									                        	@endif
															</div>
														</td>
														<td>
															<div class="text-gray-800 fs-7 me-3">Duration</div>
															<div class="text-muted fs-7 fw-bold">{{$pvalue->medicine_duration}}</div>
														</td>
														<td>
															<div class="text-gray-800 fs-7 me-3">Quantity</div>
															<div class="text-muted fs-7 fw-bold">{{$pvalue->medicine_quantity}}</div>
														</td>
														<td>
															<div class="text-gray-800 fs-7 me-3">Direction</div>
															<div class="text-muted fs-7 fw-bold">{{$pvalue->medicine_direction}}</div>
														</td>
														<td></td>
													</tr>
													@empty
									                @endforelse
													@empty
							                        <tbody class="text-gray-600 fw-semibold">
							                        	<td>No Prescription Found.</td>
							                        </tbody>
							                        @endforelse
												</tbody>
												<!--end::Table body-->
											</table>
											<div class="pagination-custom">
											@php
												echo $appointmentprescription->render();
											@endphp
											</div>
											<!--end::Table-->
										</div>
										<!--end::Card body-->
									</div>
									<!--end::Table Widget 4-->
									
								</div>
								<!--end:::Tab pane-->
								
							</div>
							<!--end:::Tab content-->
						</div>
						<!--end::Content-->

						<div class="flex-lg-row-fluid ms-lg-15 col-xl-12 mt-10">
	                        <div id="kt_app_content" class="app-content flex-column-fluid">
	                            <div id="kt_app_content_container" class="container-xxl p-0">
	                                <div class="card">
	                                    <div class="card-header border-0 pt-6">

	                                        <div class="card-title">
	                                            <div class="d-flex align-items-center position-relative my-1">
	                                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
	                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
	                                                        xmlns="http://www.w3.org/2000/svg">
	                                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
	                                                            transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
	                                                        <path
	                                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
	                                                            fill="currentColor" />
	                                                    </svg>
	                                                </span>
	                                                <input type="text" data-kt-user-table-filter="search"
	                                                    class="form-control form-control-solid w-250px ps-14" placeholder="Search ticket" />
	                                            </div>
	                                        </div>

	                                        <div class="card-toolbar">
	                                            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
	                                                <input id="datepick" class="form-control form-control-solid w-250px ps-14" placeholder="Pick date"
	                                                name="search" />
	                                            </div>

	                                        </div>

	                                    </div>

	                                    <div class="card-body py-4">

	                                        <table id="appointment_ticket_table" class="table align-middle table-row-dashed fs-6 gy-5">
	                                            <thead>
	                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
	                                                    <th>Id</th>
	                                                    <th>User Name</th>
	                                                    <th>Subject</th>
	                                                    <th>Description</th>
	                                                    <th>Status</th>
	                                                    <th>Unique Id</th>
	                                                    <th>Created Date</th>
	                                                </tr>
	                                            </thead>
	                                            <tbody class="text-gray-600 fw-semibold">
	                                            </tbody>
	                                        </table>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                    </div>

	                    <div class="flex-lg-row-fluid ms-lg-15 col-xl-12 mt-10">
	                        <div id="kt_app_content" class="app-content flex-column-fluid">
	                            <div id="kt_app_content_container" class="container-xxl p-0">
	                                <div class="card">
	                                    <div class="card-header border-0 pt-6">

	                                        <div class="card-title">
	                                            <div class="d-flex align-items-center position-relative my-1">
	                                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
	                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
	                                                        xmlns="http://www.w3.org/2000/svg">
	                                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
	                                                            transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
	                                                        <path
	                                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
	                                                            fill="currentColor" />
	                                                    </svg>
	                                                </span>
	                                                <input type="text" data-kt-user-table-filter="search1"
	                                                    class="form-control form-control-solid w-250px ps-14" placeholder="Search Log" />
	                                            </div>
	                                        </div>

	                                        <div class="card-toolbar">
	                                            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
	                                                <input id="datepick" class="form-control form-control-solid w-250px ps-14" placeholder="Pick date"
	                                                name="search1" />
	                                            </div>

	                                        </div>

	                                    </div>

	                                    <div class="card-body py-4">

	                                        <table id="call_logs_table" class="table align-middle table-row-dashed fs-6 gy-5">
	                                            <thead>
	                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
	                                                    <th>Id</th>
	                                                    <th>Channel Name</th>
	                                                    <th>Initiator</th>
	                                                    <th>Receiver</th>
	                                                    <th>Date time</th>
	                                                    <th>Duration</th>
	                                                    <th>Created Date</th>
	                                                </tr>
	                                            </thead>
	                                            <tbody class="text-gray-600 fw-semibold">
	                                            </tbody>
	                                        </table>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
					</div>
				</div>
				<!--end::Layout-->
			</div>
			<!--end::Content container-->
		</div>
		<!--end::Content-->
	</div>
			
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript">
	$(document).ready(function() {
	    var current = window.location.pathname.split("/")[4];
	    if(current == "view"){
	    	$('#view').addClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "labview"){
	    	$('#view').removeClass('active');
	    	$('#labview').addClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "view"){
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "notesview"){
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').addClass('active');
	    }
	});

	$(".subtable").click(function(){
		var id = $(this).attr('id');
	  	$("#"+id).toggleClass('active');
	  	$(".id"+id).toggleClass('d-none');
	});
</script>

@endsection

@section('css')
<link href="{{asset('assets/plugins/custom/datatables/datatables.bundle.css')}}" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css"
    href="{{ asset('assets/plugins/custom/daterangepicker/daterangepicker.css') }}" />
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('assets/plugins/custom/daterangepicker/moment.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/plugins/custom/daterangepicker/daterangepicker.min.js') }}">
</script>
<script type="text/javascript">
var appointment_id = {!! json_encode($appointment->id) !!};
var getListRoute = '{{ route('admin.appointment.supportview',"appointment_id") }}';
var getCallListRoute = '{{ route('admin.appointment.calllogsview',"appointment_id") }}';
getListRoute = getListRoute.replace('appointment_id', appointment_id);
getCallListRoute = getCallListRoute.replace('appointment_id', appointment_id);
</script>
<script src="{{asset('assets/plugins/custom/datatables/datatables.bundle.js')}}" type="text/javascript"></script>
<script type="text/javascript" src="{{asset('js/appointmentticket.js')}}"></script>
<script type="text/javascript" src="{{asset('js/calllogs.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/alert.js')}}"></script>
@endsection

