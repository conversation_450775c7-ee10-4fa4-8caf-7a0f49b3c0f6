/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!*****************************************************!*\
  !*** ./resources/assets/js/pharmacytransactions.js ***!
  \*****************************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#transaction_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[8, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#transaction_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "patient_name",
        name: "patient_name"
      }, {
        data: "pharmacy_id",
        name: "pharmacy_id",
        searchable: false,
        orderable: false
      }, {
        data: "request_id",
        name: "request_id"
      }, {
        data: "transaction_id",
        name: "transaction_id"
      }, {
        data: "amount",
        name: "amount"
      }, {
        data: "transaction_type",
        name: "transaction_type"
      }, {
        data: "payment_status",
        name: "payment_status"
      }, {
        data: "created_date",
        name: "created_date",
        searchable: false
      }, {
        data: "action",
        name: "action",
        searchable: false,
        orderable: false
      }],
      columnDefs: [{
        targets: 1,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          var patientUrl = "".concat(patientRoute).replace('id', "".concat(row.patient.id));
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.patient.avatar_url, "\" alt=\"").concat(row.patient.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=") + patientUrl + " class=\"text-gray-800 text-hover-primary mb-1\">".concat(row.patient.name, "</a>\n                            <span>").concat(row.patient.username, "</span>\n                        </div>");
        }
      }, {
        targets: 2,
        render: function render(data, type, row) {
          return "-";
        }
      }, {
        targets: 5,
        render: function render(data, type, row) {
          return "".concat(row.amount).concat(row.currency);
        }
      }, {
        targets: 6,
        render: function render(data, type, row) {
          if (row.transaction_type == 1 || row.transaction_type == 0) {
            return "<div class=\"badge badge-light-primary\">Consultation</div>";
          }
          if (row.transaction_type == 2) {
            return "<div class=\"badge badge-light-success\">Pharmacy</div>";
          }
          if (row.transaction_type == 3) {
            return "<div class=\"badge badge-light-info\">Subscription</div>";
          }
          if (row.transaction_type == 4) {
            return "<div class=\"badge badge-light-warning\">Doctor Payment</div>";
          }
        }
      }, {
        targets: 7,
        render: function render(data, type, row) {
          if (row.payment_status == 1) {
            return "<div class=\"badge badge-outline badge-primary\">Init</div>";
          }
          if (row.payment_status == 2) {
            return "<div class=\"badge badge-outline badge-success\">Complete</div>";
          }
          if (row.payment_status == 3) {
            return "<div class=\"badge badge-outline badge-info\">Fail</div>";
          }
          if (row.payment_status == 4) {
            return "<div class=\"badge badge-outline badge-warning\">Timeout</div>";
          }
          if (row.payment_status == 5) {
            return "<div class=\"badge badge-outline badge-danger\">Reward</div>";
          }
          if (row.payment_status == 6) {
            return "<div class=\"badge badge-outline badge-primary\">Reward Completed</div>";
          }
        }
      }]
    });
    table = datatable.$;
    $('#transaction_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
    var selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    filterButton.addEventListener("click", function () {
      var filterString = "";

      // Get filter values
      selectOptions.forEach(function (item, index) {
        if (item.value && item.value !== "") {
          if (index == 0) {
            datatable.column(7).search(item.value).draw();
          }
        }
      });
    });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

    // Reset datatable
    resetButton.addEventListener("click", function () {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
      selectOptions.forEach(function (select) {
        $(select).val("").trigger("change");
      });

      // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
      datatable.search("").columns().search("").draw();
    });
  };
  var initDaterangepicker = function initDaterangepicker() {
    var start = moment().startOf("year");
    var end = moment().endOf("year");
    var input = $('input[name="search"]');
    function cb(start, end) {
      input.val(start.format("YYYY/MM/DD") + " - " + end.format("YYYY/MM/DD"));
      var start_date = start.format("YYYY/MM/DD");
      var end_date = end.format("YYYY/MM/DD");
      $.ajax({
        url: countUrl,
        method: "GET",
        data: {
          start_date: start_date,
          end_date: end_date
        },
        success: function success(data) {
          $(".transaction-count").text(data.transactionCount);
          $(".transaction-appointment-count").text(data.transactionAppointmentCount);
          $(".transaction-pharmacy-count").text(data.transactionPharmacyCount);
          $(".transaction-subscription-count").text(data.transactionSubscriptionCount);
          $(".total-transaction").text(data.totalTransaction);
          $("#transaction_table").data("dt_params", {
            start_date: start_date,
            end_date: end_date
          });
          datatable.draw();
        }
      });
    }
    input.daterangepicker({
      startDate: start,
      endDate: end,
      ranges: {
        Today: [moment(), moment()],
        Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
        "Last 7 Days": [moment().subtract(6, "days"), moment()],
        "Last 30 Days": [moment().subtract(29, "days"), moment()],
        "This Month": [moment().startOf("month"), moment().endOf("month")],
        "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
        "This Year": [moment().startOf("year"), moment().endOf("year")],
        "Last Year": [moment().subtract(1, "year").startOf("year"), moment().subtract(1, "year").endOf("year")]
      }
    }, cb);
    cb(start, end);
  };
  $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#transaction_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });

  // Public methods
  return {
    init: function init() {
      initDatatable();
      initDaterangepicker();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeStatus", function () {
  var id = $(this).attr("id");
  $(".transaction_id").val(id);
});
if ($("#export").length > 0) {
  var url = new URL($('#export').attr('href'));
  url.searchParams.set('transaction_type', 2);
  $("a#export").attr("href", url);
  $('#payment_status').on('change', function () {
    url.searchParams.set('payment_status', this.value);
    $("a#export").attr("href", url);
  });
  $('#datepick').on('change', function () {
    url.searchParams.set('datepick', this.value);
    $("a#export").attr("href", url);
  });
  $('#search').on("input", function () {
    url.searchParams.set('search', this.value);
    $("a#export").attr("href", url);
  });
}
/******/ })()
;