<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                $table->string('response_message')->nullable()->default(null);
                $table->string('decision_code')->nullable()->default(null);
                $table->string('decision')->nullable()->default(null);
                $table->string('channel_transaction_id')->nullable()->default(null);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('response_message');
            $table->dropColumn('decision_code');
            $table->dropColumn('decision');
            $table->dropColumn('channel_transaction_id');
        });
    }
}
