<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SymptomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'symptoms_name_en' => 'required|unique:symptoms,symptoms_name->en'.request()->id,
            'symptoms_name_es' => 'unique:symptoms,symptoms_name->es,'.request()->id
        ];
    }
}
