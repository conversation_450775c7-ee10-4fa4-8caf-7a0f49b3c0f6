<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\LogRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class LogController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "logs";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var LogRepositoryEloquent|string
     */
    public $logRepositoryEloquent = "";

    public function __construct(LogRepositoryEloquent $logRepositoryEloquent)
    {
        parent::__construct();
        $this->logRepositoryEloquent = $logRepositoryEloquent;
    }

    /**
     * View logs
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Logs']);
        return view('admin.log.index');
    }

    /**
     * Return log list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->logRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }
}
