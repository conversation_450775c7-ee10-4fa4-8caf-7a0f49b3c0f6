# VoIP Notification Debugging and Fixes

## Problem
VoIP notifications are being initiated on the sender side but not received on the receiver side.

## Root Causes Identified

### 1. **Suppressed Error Handling**
- The original code used `ob_start()` and `ob_end_clean()` which suppressed all output including errors
- This made it impossible to see what was actually failing

### 2. **Missing Error Logging**
- No comprehensive error logging for notification failures
- No fallback mechanisms when VoIP notifications fail

### 3. **Device Token Issues**
- No validation of device tokens before sending notifications
- No debugging information about token availability

### 4. **Queue Configuration**
- VoIP notifications are queued but queue workers might not be running
- No error handling for failed queue jobs

## Fixes Implemented

### 1. **Enhanced Error Handling in AppointmentCallRepositoryEloquent.php**

**Changes Made:**
- Removed `ob_start()/ob_end_clean()` to see actual errors
- Added comprehensive logging for device types and token availability
- Added fallback mechanisms (iOS VoIP → iOS Push → Error)
- Added try-catch blocks with detailed error logging
- Added validation for userDevice relationship

**Key Improvements:**
```php
// Before: Errors were suppressed
ob_start();
try {
    Notification::send($receiver->userDevice, new VoipNotification(...));
} finally {
    ob_end_clean();
}

// After: Errors are logged and handled
try {
    $voipNotification = new VoipNotification(...);
    Notification::send($receiver->userDevice, $voipNotification);
    Log::info("|| VoIP notification sent successfully");
} catch (Exception $e) {
    Log::error("|| VoIP notification failed: " . $e->getMessage());
    // Fallback to silent notification
    if (!empty($receiver->userDevice->push_token)) {
        dispatch(new silentNotification($sender, $receiver, $extra));
    }
}
```

### 2. **Improved VoipNotification.php**

**Changes Made:**
- Added detailed logging in `toApn()` method
- Added `failed()` method to handle job failures
- Added Exception import for proper error handling

### 3. **Enhanced silentNotification.php Job**

**Changes Made:**
- Added comprehensive logging for debugging
- Added validation for token availability
- Added device record checking when tokens are missing
- Added `failed()` method for job failure handling
- Improved error messages and debugging information

### 4. **Fixed PushNotification Trait**

**Changes Made:**
- Fixed `getUsersSilentToken()` method (was wrapping array in array)
- Added token validation (not null, not empty)
- Added comprehensive logging
- Fixed Log facade import
- Added device debugging when no tokens found

### 5. **Added Diagnostic Tools**

**New Commands:**

1. **DiagnoseVoipNotifications.php**
   ```bash
   php artisan voip:diagnose [user_id]
   ```
   - Checks user device records
   - Validates tokens
   - Checks APN/FCM configuration
   - Checks queue status

2. **TestVoipNotification.php**
   ```bash
   php artisan voip:test {receiver_id} {sender_id}
   ```
   - Sends test notifications
   - Tests both iOS and Android paths
   - Provides detailed feedback

## Debugging Steps

### 1. **Check User Device Records**
```bash
php artisan voip:diagnose
```
This will show:
- Users without device records
- iOS users without VoIP tokens
- Users without push tokens
- Configuration issues

### 2. **Test Specific User**
```bash
php artisan voip:diagnose 123
```
Replace `123` with the actual user ID to get detailed information about that user's notification setup.

### 3. **Send Test Notification**
```bash
php artisan voip:test 123 456
```
Replace `123` with receiver ID and `456` with sender ID to send a test notification.

### 4. **Check Application Logs**
Monitor the Laravel logs for detailed information:
```bash
tail -f storage/logs/laravel.log
```

Look for log entries starting with `||` which contain the detailed debugging information.

### 5. **Verify Queue Workers**
If using database or redis queues, ensure workers are running:
```bash
php artisan queue:work
```

## Common Issues and Solutions

### Issue 1: "No device record found"
**Solution:** User needs to register their device through the mobile app

### Issue 2: "VoIP token not available" (iOS)
**Solution:** 
- App needs to register for VoIP notifications
- Check APN configuration
- Verify app has VoIP background capability

### Issue 3: "Push token not available"
**Solution:**
- App needs to register for push notifications
- Check FCM configuration
- Verify app permissions

### Issue 4: "Queue jobs not processing"
**Solution:**
- Start queue workers: `php artisan queue:work`
- Check queue configuration in `config/queue.php`
- Consider using supervisor for production

### Issue 5: "APN/FCM configuration errors"
**Solution:**
- Verify environment variables are set
- Check certificate/key files exist
- Validate Firebase credentials

## Configuration Checklist

### APN Configuration (iOS)
- [ ] `APN_KEY_ID` environment variable set
- [ ] `APN_TEAM_ID` environment variable set  
- [ ] `APN_APP_BUNDLE_ID` environment variable set
- [ ] AuthKey file exists at `storage/app/pem/AuthKey_W4LYFB48MX.p8`

### FCM Configuration (Android)
- [ ] `FCM_SERVER_KEY` environment variable set
- [ ] `FCM_SENDER_ID` environment variable set
- [ ] `firebase-credentials.json` file exists in project root

### Queue Configuration
- [ ] Queue connection configured (not 'sync' for production)
- [ ] Queue workers running
- [ ] Failed jobs table exists

## Monitoring

After implementing these fixes, monitor the logs for:
- Successful notification sends
- Token validation issues
- Configuration problems
- Queue job failures

The enhanced logging will provide clear information about what's happening at each step of the notification process.
