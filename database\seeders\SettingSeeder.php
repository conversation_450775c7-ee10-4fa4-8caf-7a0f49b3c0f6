<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('settings')->truncate();

        DB::table('settings')->insert([
            [
                'key'=>'Virtual Consultation Min Price',
                'value' => '20',
                'slug'=>'virtual-consultation-min-price',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'key'=>'Physical Consultation Min Price',
                'value' => '20',
                'slug'=>'physical-consultation-min-price',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'key'=>'Home Consultation Min Price',
                'value' => '20',
                'slug'=>'home-consultation-min-price',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'key'=>'Doctor Invitation SignUp Mode',
                'value' => '0',
                'slug'=>'molema-doctor-sign-up-mode',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'key'=>'Freelancer Commission',
                'value' => '10%',
                'slug'=>'freelancer-commission',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'key'=>'Collaborator Commission',
                'value' => '5%',
                'slug'=>'collaborator-commission',
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ]
        ]);
    }
}
