<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorWalletTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment_id',
        'transaction_id',
        'unique_id',
        'doctor_type',
        'commission',
        'wallet_amount',
        'wallet_type',
        'reference_no'
    ];

    protected $appends = [
        'created_date'
    ];

    /**
     * Get Created Date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    public function doctorAppointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'appointment_id', 'id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'transaction_id', 'id');
    }


}
