<?php

namespace App\Repositories;

use App\Http\Responses\Transformers\CustomArraySerializer;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\PatientNoteRepository;
use App\Models\PatientNote;
use App\Transformers\PatientNotesTransformer;
use App\Validators\PatientNoteValidator;

/**
 * Class PatientNoteRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class PatientNoteRepositoryEloquent extends BaseRepository implements PatientNoteRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return PatientNote::class;
    }

    /**
     * Add a new PatientNote
     *
     * @return array
     */
    public function addNotes($request)
    {

        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'primary_health_complaint'],
            ['notes' => $request->primary_health_complaint ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'current_medicine'],
            ['notes' => $request->current_medicine ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'allergy'],
            ['notes' => $request->allergy ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'respiratory_problem'],
            ['notes' => $request->respiratory_problem ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'digestion_problem'],
            ['notes' => $request->digestion_problem ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'sleep_problem'],
            ['notes' => $request->sleep_problem ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'family_health_background'],
            ['notes' => $request->family_health_background ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'lifestyle_factor'],
            ['notes' => $request->lifestyle_factor ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'weight_change'],
            ['notes' => $request->weight_change ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'physical_examination'],
            ['notes' => $request->physical_examination ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'diagnosis'],
            ['notes' => $request->diagnosis ?? null]
        );
        PatientNote::updateOrCreate(
            ['appointment_id' => $request->appointment_id, 'subject' => 'summary'],
            ['notes' => $request->summary ?? null]
        );

        $patientNote = PatientNote::where('appointment_id', $request->appointment_id)->get();
        return ['notes' => fractal($patientNote, new PatientNotesTransformer())->serializeWith(new CustomArraySerializer())];
    }

    /**
     * view patient notes
     *
     * @return array
     */
    public function viewNotes($request){
        $patientNote = PatientNote::where('appointment_id',$request->appointment_id)->get();
        return ['notes' => fractal($patientNote, new PatientNotesTransformer())->serializeWith(new CustomArraySerializer())];
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
