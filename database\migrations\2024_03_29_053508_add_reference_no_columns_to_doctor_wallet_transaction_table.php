<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddReferenceNoColumnsToDoctorWalletTransactionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('doctor_wallet_transactions', function (Blueprint $table) {
            $table->string('reference_no', 100)->nullable()->default(null);
            $table->string('note', 100)->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('doctor_wallet_transactions', function (Blueprint $table) {
            $table->dropColumn('reference_no');
            $table->dropColumn('note');
        });
    }
}
