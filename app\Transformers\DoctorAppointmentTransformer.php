<?php

namespace App\Transformers;

use App\Enums\ConsultationType;
use App\Models\DoctorAppointment;
use League\Fractal\TransformerAbstract;

class DoctorAppointmentTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorAppointment $doctorAppointment)
    {
        $consultationType = $doctorAppointment->consultation_type;
        if($consultationType == ConsultationType::VirtualConsultation){
            $price = $doctorAppointment->doctor->doctorDetail->virtual_consultation_price;
        }else if($consultationType == ConsultationType::PhysicalConsultation){
            $price = $doctorAppointment->doctor->doctorDetail->physical_consultation_price;
        }else{
            $price =  $doctorAppointment->doctor->doctorDetail->home_consultation_price;
        }


        return [
            'id' => $doctorAppointment->id,
            'booking_id' => $doctorAppointment->unique_id,
            'patient_id' => $doctorAppointment->patient_id,
            'patient_name' => $doctorAppointment->patient->name,
            'patient_avatar' => $doctorAppointment->patient->avatar_url,
            'doctor_id' => $doctorAppointment->doctor_id,
            'doctor_name' => $doctorAppointment->doctor->name,
            'doctor_avatar' => $doctorAppointment->doctor->avatar_url,
            'doctor_online_status' => $doctorAppointment->doctor->doctorDetail->online_status,
            'doctor_type' => $doctorAppointment->doctor->doctorDetail->doctor_type,
            'doctor_education_summary' => $doctorAppointment->doctor->doctorDetail->education_summary,
            'doctor_type' => $doctorAppointment->doctor->doctorDetail->doctor_type,
            'is_doctor_verified'=> $doctorAppointment->doctor->doctorDetail->doctor_type == 1 ? 'true' : 'false',
            'appointment_date' => $doctorAppointment->appointment_date,
            'start_time' => $doctorAppointment->start_time,
            'end_time' => $doctorAppointment->end_time,
            'consultation_type' => $doctorAppointment->consultation_type,
            'consultation_price' => $price,
            'appointment_status' => $doctorAppointment->appointment_status,
            'payment_status' => $doctorAppointment->payment_status,
            'created_at' => $doctorAppointment->created_at,
            'security_code' => $doctorAppointment->security_code,
            'booking_id' => $doctorAppointment->unique_id
        ];
    }
}
