<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\NotificationRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * @var notificationRepositoryEloquent|string
     */
    public $notificationRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param notificationRepositoryEloquent $notificationRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(NotificationRepositoryEloquent $notificationRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->notificationRepositoryEloquent = $notificationRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewNotifications(Request $request)
    {
        try {
            $notification = $this->notificationRepositoryEloquent->viewNotifications($request);
            return $this->apiResponse->respondWithMessageAndPayload($notification, trans('messages.notification_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
