<?php


namespace App\Http\Responses;

use HarishPatel\ApiResponse\ApiResponse;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as ResponseHTTP;

class ApiFormattedResponse extends ApiResponse
{

    /**
     * Custom response structure.
     *
     * @param bool $success
     * @param null $payload
     * @param string $message
     * @param null $debug
     * @return array|object
     */
    public function getResponseStructure($success = false, $payload = [], $message = '', $debug = null)
    {
        if ($success) {
            $data = [
                'success' => true,
                'statusCode' => $this->getStatusCode(),
                'message' => $message,
                'data' => $payload ?? json_decode('{}'),
            ];
        } else {
            $data = [
                'success' => false,
                'statusCode' => $this->getStatusCode(),
                'message' => $message,
                'error' => [
                    'code' => Response::$statusTexts[$this->getStatusCode()],
                ]
            ];
        }

        return $data;
    }

    /**
     * Use this when response validation error
     *
     * @param string $message
     * @param null $data
     *
     * @return mixed
     */
    public function respondMethodNotAllowed($message = "Validation Error", $data = null)
    {
        return $this->setStatusCode(ResponseHTTP::HTTP_METHOD_NOT_ALLOWED)
            ->respondWithError($message, null, $data);
    }

    /**
     * Use this for responding with messages and payload.
     *
     * @param null $payload
     * @param string $message
     *
     * @return JsonResponse
     */
    public function respondForbiddenWithPayload($payload = null, $message = "Forbidden")
    {
        return $this->setStatusCode(ResponseHTTP::HTTP_FORBIDDEN)
        ->respondWithMessageAndPayload($payload, $message);
    }

    /**
     * Use this when a resource is not found (404).
     *
     * @param null $payload
     * @param string $message
     *
     * @return mixed
     */
    public function respondNotFoundWithPayload($payload = null, $message = "Deleted")
    {
        $reponse = $this->setStatusCode(ResponseHTTP::HTTP_NOT_FOUND)
            ->respondWithMessageAndPayload($payload, $message);
        return $reponse;
    }


    /**
     * Custom error messages.
     *
     * @param string $message
     * @param null $statusCode
     * @param null $data
     * @return mixed
     */
    public function respondCustomExceptionError($message = "Validation Error", $statusCode = null, $data = null)
    {
        return $this->setStatusCode($statusCode)->respondWithError($message, null, $data);
    }

    /**
     * Use this for general server errors (500).
     *
     * @param string $message
     *
     * @return mixed
     */
    public function respondWithCustomError($message)
    {
        $message = $message ?: "Internal Error";
        return $this->setStatusCode(ResponseHTTP::HTTP_INTERNAL_SERVER_ERROR)
            ->respondWithError($message);
    }
}
