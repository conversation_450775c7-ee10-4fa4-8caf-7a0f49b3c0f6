<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\ReferRequest;
use App\Repositories\DoctorInvitationRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DoctorInvitationController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "doctor-invitation";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var DoctorInvitationRepositoryEloquent|string
     */
    public $doctorInvitationRepository = "";

    public function __construct(DoctorInvitationRepositoryEloquent $doctorInvitationRepository)
    {
        parent::__construct();
        $this->doctorInvitationRepository = $doctorInvitationRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Doctor Invitation']);
        return view('admin.user.refer.index');
    }

    /**
     * Return player list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function import(Request $request)
    {
        try {
            $this->doctorInvitationRepository->import($request);
            $request->session()->flash('success', config("messages.file_import_success"));
            return redirect()->route('admin.doctor-invitation.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return player list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->doctorInvitationRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Reference']);
        return view('admin.user.refer.refer');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(ReferRequest $request)
    {
        try {
            $doctor = $this->doctorInvitationRepository->store($request);
            $request->session()->flash('success', config("messages.doctor_created"));
            return redirect()->route('admin.doctor-invitation.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit doctor list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit doctor']);
        try {
            $doctor = $this->doctorInvitationRepository->edit($request, $id);
            return view('admin.user.doctor.doctor', compact('doctor'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * Update doctor list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $doctor = $this->doctorInvitationRepository->update($request, $id);
            $request->session()->flash('success', config("messages.doctor_updated"));
            return redirect()->route('admin.doctor.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusApproved(Request $request)
    {
        try {
            $this->doctorInvitationRepository->changeStatusApproved($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeclined(Request $request)
    {
        try {
            $this->doctorInvitationRepository->changeStatusDeclined($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }
}
