<?php

namespace App\Transformers;

use App\Models\DoctorDetail;
use League\Fractal\TransformerAbstract;

class DoctorDetailTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorDetail $doctorDetail)
    {
        return [
            'gender' => $doctorDetail->gender,
            'education_summary' => $doctorDetail->education_summary,
            'council_number' => $doctorDetail->council_number,
            'signature' => $doctorDetail->signature,
            'is_generalist' => $doctorDetail->is_generalist,
            'is_specialist' => $doctorDetail->is_specialist,
            'doctor_type' => $doctorDetail->doctor_type,
            'online_status' => $doctorDetail->online_status,
            'served_location' => explode(',', $doctorDetail->served_location),
            'virtual_consultation' => $doctorDetail->virtual_consultation,
            'physical_consultation' => $doctorDetail->physical_consultation,
            'home_consultation' => $doctorDetail->home_consultation,
            'virtual_consultation_price' => $doctorDetail->virtual_consultation_price,
            'physical_consultation_price' => $doctorDetail->physical_consultation_price,
            'home_consultation_price' => $doctorDetail->home_consultation_price,
            'timezone' => $doctorDetail->timezone,
            'service' => json_decode($doctorDetail->service),
            'account_verified_at' => $doctorDetail->account_verified_at
        ];
    }
}
