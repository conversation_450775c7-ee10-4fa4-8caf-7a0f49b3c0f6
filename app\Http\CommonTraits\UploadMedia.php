<?php


namespace App\Http\CommonTraits;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

trait UploadMedia
{
    public function fileUpload($request, $folder)
    {
        $S3Disk = Storage::disk('s3');
        $fileName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->getClientOriginalExtension();

        $S3Disk->put($fileName, file_get_contents($request), 'public');

        return $S3Disk->exists($fileName) ? $S3Disk->url($fileName) : '';
    }

    public function pdfUpload($request, $folder)
    {
        $S3Disk = Storage::disk('s3');
        $fileName = $folder . rand(1, 1000) . '/' . time() . '.pdf';
        $S3Disk->put($fileName, ($request), 'public');

        return $S3Disk->exists($fileName) ? $S3Disk->url($fileName) : '';
    }

    public function imageUpload($request, $folder)
    {
        $image = $this->getImageResized($request);
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->getClientOriginalExtension();
        $S3Disk->put($imageName, $image->stream(), 'public');

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }

    public function educationcertiUpload($request, $folder)
    {
        $image = $this->getImageResized($request->education_certi);
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->education_certi->getClientOriginalExtension();
        $S3Disk->put($imageName, $image->stream(), 'public');

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }

    public function otherdocumentUpload($request, $folder)
    {
        $image = $this->getImageResized($request->other_document);
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->other_document->getClientOriginalExtension();
        $S3Disk->put($imageName, $image->stream(), 'public');

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }

    public function avatarUpload($request, $folder)
    {
        $image = $this->getImageResized($request->avatar);
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->avatar->getClientOriginalExtension();
        $S3Disk->put($imageName, $image->stream(), 'public');

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }

    public function signatureUpload($request, $folder)
    {
        $image = $request->signature;
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->signature->getClientOriginalExtension();
        $mime = $image->getClientMimeType();
        $S3Disk->put($imageName, file_get_contents($image), ['ACL'=>'public-read', 'mimetype' => $mime]);

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }


    public function videoUpload($request, $folder)
    {
        $video = $request->video;
        $S3Disk = Storage::disk('s3');
        $videoName = $folder . rand(1, 1000) . '/' . time() . '.' . $request->video->getClientOriginalExtension();
        $S3Disk->put($videoName, file_get_contents($video), 'public');

        return $S3Disk->exists($videoName) ? $S3Disk->url($videoName) : '';
    }

    /**
     * Store media file in S3 Bucket.
     *
     * @param $request
     * @param $folder
     * @param $id
     * @return string
     */
    public function upload($request, $folder, $id)
    {
        // create an image
        $image = $request->avatar;
        //dd($image);
        $S3Disk = Storage::disk('s3');
        $imageName = $folder . "/{$id}-" . time() . '.' . $request->avatar->getClientOriginalExtension();
        $S3Disk->put($imageName, file_get_contents($image), 'public');

        return $S3Disk->exists($imageName) ? $S3Disk->url($imageName) : '';
    }

    /**
     * Delete file from the path.
     *
     * @param $url
     */
    public function deleteImg($url)
    {
        $bucketBaseUrl = 'https://' . config('filesystems.disks.s3.bucket') . '.s3.' . config('filesystems.disks.s3.region') . '.amazonaws.com';
        $path = str_replace($bucketBaseUrl, '', $url);
        if (Storage::disk('s3')->exists($path)) {
            Storage::disk('s3')->delete($path);
        }

    }

    /**
     * @param $image
     * @return \Intervention\Image\Image
     */
    public function getImageResized($image): \Intervention\Image\Image
    {
        $image = Image::make($image);
        $image->fit(300);
        return $image;
    }
}
