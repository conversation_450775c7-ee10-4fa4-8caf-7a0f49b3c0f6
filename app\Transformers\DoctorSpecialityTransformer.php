<?php

namespace App\Transformers;

use App\Models\DoctorSpeciality;
use App\Models\Speciality;
use League\Fractal\TransformerAbstract;

class DoctorSpecialityTransformer extends TransformerAbstract
{
    /**
     * List of resources to automatically include
     *
     * @var array
     */
    protected array $defaultIncludes = [
        'speciality'
    ];
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Speciality $doctorSpeciality)
    {
        return [
        ];
    }

    /**
     * @param Speciality $doctorSpeciality
     * @return \League\Fractal\Resource\Collection
     */
    public function includeSpeciality(Speciality $doctorSpeciality)
    {
        if(!empty($doctorSpeciality)) {
            return $this->item($doctorSpeciality, new SpecialityTransformer());
        }
    }
}
