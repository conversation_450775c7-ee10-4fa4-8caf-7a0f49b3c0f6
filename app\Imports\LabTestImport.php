<?php

namespace App\Imports;

use App\Enums\Status;
use App\Models\LabTest;
use Maatwebsite\Excel\Concerns\ToModel;

class LabTestImport implements ToModel
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new LabTest([
            'name' => $row[0],
            'code' => $row[1],
            'status' => Status::Active
        ]);
    }
}
