<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use App\Enums\UserStatus;
use App\Enums\UserType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Prettus\Repository\Contracts\Transformable;
use Prettus\Repository\Traits\TransformableTrait;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Transaction;
use App\Models\BankDetail;

class User extends Authenticatable
{
    use TransformableTrait, HasRoles;
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $guard = 'web';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_type',
        'first_name',
        'last_name',
        'email',
        'password',
        'username',
        'country_code',
        'dial_code',
        'mobile',
        'avatar',
        'is_first_time_login',
        'steps',
        'status',
        'language',
        'provider_id',
        'provider_name',
        'email_verified_at',
        'mobile_verified_at',
        'notification_indicator'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime'
    ];

    protected $appends = [
        'avatar_url', 'name', 'verified', 'created_date'
    ];


    /**
     * Get avatar link
     *
     * @return string
     */
    public function getAvatarUrlAttribute()
    {
        if (!empty($this->attributes['avatar'])) {
            return $this->attributes['avatar'];
        } else {
            return asset('assets/media/avatars/blank.png');
        }
    }

    /**
     * Get avatar link
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * @param $query
     * @param $keyword
     * @return mixed
     */
    public function scopeSearchByName($query, $keyword)
    {
        return $query->where('first_name', 'LIKE', "%{$keyword}%")->orWhere('last_name', 'LIKE', "%{$keyword}%");
    }

    /**
     * @param $query
     * @param $keyword
     * @return mixed
     */
    public function scopeSearchByMobile($query, $keyword)
    {
        return $query->where('dial_code', 'LIKE', "%{$keyword}%")->orWhere('mobile', 'LIKE', "%{$keyword}%");
    }

    /**
     * Get avatar link
     *
     * @return string
     */
    public function getVerifiedAttribute()
    {
        return (!empty($this->email_verified_at) && !empty($this->mobile_verified_at)) ? 'Verified' : 'Unverified';
    }

    /**
     * @param $query
     * @param $keyword
     * @return mixed
     */
    public function scopeSearchByVerified($query, $keyword)
    {
        if($keyword == 'Verified'){
            return $query->where('email_verified_at', '!=', null)->where('mobile_verified_at', '!=', null);
        }if($keyword == 'Unverified'){
            return $query->where('email_verified_at', null)->orWhere('mobile_verified_at', null);
        }
    }

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

     /**
     * Get created date
     *
     * @return string
     */
    public function getUpdatedDateAttribute()
    {
        return !empty($this->updated_at) ? $this->updated_at->format("Y-m-d"): '-';
    }

    /**
     * The patient has details.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function patientDetail()
    {
        return $this->hasOne(PatientDetail::class, 'user_id');
    }

    public function patientTransaction()
    {
        return $this->hasMany(Transaction::class, 'patient_id');
    }

    /**
     * The doctor has details.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function doctorDetail()
    {
        return $this->hasOne(DoctorDetail::class, 'user_id');
    }

    /**
     * The doctor has experience.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorExperience()
    {
        return $this->hasMany(DoctorExperience::class, 'user_id');
    }

    /**
     * The user has devices.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function userDevice()
    {
        return $this->hasOne(UserDevice::class, 'user_id');
    }

    /**
     * The doctor has documents.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorDocument()
    {
        return $this->hasMany(DoctorDocument::class, 'user_id');
    }

    /**
     * The User has bank accounts.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function bankDetail()
    {
        return $this->hasMany(BankDetail::class, 'user_id');
    }

    public function doctorDocumentEducation()
    {
        return $this->hasOne(DoctorDocument::class, 'user_id')->where('image_id',1);
    }

    public function doctorDocumentOther()
    {
        return $this->hasOne(DoctorDocument::class, 'user_id')->where('image_id',2);
    }

    /**
     * The doctor has clinic.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorClinic()
    {
        return $this->hasOne(DoctorClinic::class, 'user_id');
    }

    /**
     * The doctor has clinic.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorAvailability()
    {
        return $this->hasMany(DoctorAvailability::class, 'user_id');
    }

    /**
     * The doctor has clinic.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorAppointment()
    {
        return $this->hasMany(DoctorAppointment::class, 'doctor_id');
    }

    /**
     * The doctor has ratings.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorRating()
    {
        return $this->hasMany(DoctorRating::class, 'doctor_id');
    }

    public function patientBankDetail()
    {
        return $this->hasOne(BankDetail::class, 'user_id');
    }

    /**
     * The doctor has ratings.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function doctorSubscription()
    {
        return $this->hasMany(DoctorSubscription::class, 'user_id');
    }

    /**
     * The speciality that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function speciality()
    {
        return $this->belongsToMany(Speciality::class, 'doctor_specialities', 'user_id', 'speciality_id')->withTrashed();
    }

    /**
     * The symptom that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function symptom()
    {
        return $this->belongsToMany(Symptom::class, 'doctor_symptoms', 'user_id', 'symptom_id')->withTrashed();
    }

    /**
     * @param $query
     * @return mixed
     */
    public function scopeOnlyPatient($query)
    {
        return $query->where('user_type', UserType::Patient);
    }

    /**
     * @param $query
     * @return mixed
     */
    public function scopeOnlyDoctor($query)
    {
        return $query->where('user_type', UserType::Doctor);
    }
}
