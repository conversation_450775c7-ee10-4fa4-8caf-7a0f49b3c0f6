<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDoctorInvitationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('doctor_invitations', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->nullable();
            $table->bigInteger('user_type')->nullable();
            $table->string('email', 200);
            $table->string('invitation_code')->collation('latin1_general_cs');
            $table->tinyInteger('type')->comment('1 = Invited, 2 = Waiting')->default(1);
            $table->boolean('status')->comment('1 = Initiated, 2 = Approved, 3 = Completed, 4 = Expired, 5 = Declined')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('doctor_invitations');
    }
}
