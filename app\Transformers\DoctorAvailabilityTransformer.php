<?php

namespace App\Transformers;

use App\Models\DoctorAvailability;
use League\Fractal\TransformerAbstract;

class DoctorAvailabilityTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorAvailability $doctorAvailability)
    {
        return [
            'id' => $doctorAvailability->id,
            'day' => $doctorAvailability->day,
            'start_time' => $doctorAvailability->start_time,
            'end_time' => $doctorAvailability->end_time,
            'slot' => $doctorAvailability->slot
        ];
    }
}
