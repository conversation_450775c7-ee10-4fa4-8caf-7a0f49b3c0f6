@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($medicine))
    {{ Breadcrumbs::render('admin.medicine.edit') }}
    @else
    {{ Breadcrumbs::render('admin.medicine.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($medicine))
                        {{__('Edit Medicine Detail')}}
                        @else
                        {{__('Add Medicine Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.medicine.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="medicine_details" class="collapse show">
                    @if(!empty($medicine))
                    {{ Form::model($medicine, ['route' => ['admin.medicine.update', $medicine->id], 'method' =>
                    'post','class'=>'form','id'=>'medicine-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$medicine->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.medicine.store',
                    'method'=>'post','class'=>'form','id'=>'medicine-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Medicine Name', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('name',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'name','placeholder'=>'Enter medicines name']) !!}
                                        @if($errors->has('name'))
                                        <div id="name-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('name') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('status', statusArray(), $medicine->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\MedicineRequest', '#medicine-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\MedicineRequest', '#medicine-edit-form'); !!}
@endsection
