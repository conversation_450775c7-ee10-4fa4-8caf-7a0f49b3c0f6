<?php

namespace App\Http\Controllers\Api;

use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\CheckUserRequest;
use App\Http\Requests\CreateSocialAuthRequest;
use App\Http\Requests\UserEmailVerifyRequest;
use App\Http\Requests\UserMobileVerifyRequest;
use App\Http\Requests\UserRegisterRequest;
use App\Http\Requests\UserVerifyRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Models\Setting;
use App\Repositories\UserRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    /**
     * @var UserRepositoryEloquent|string
     */
    public $userRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * UserController constructor.
     * @param UserRepositoryEloquent $userRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(UserRepositoryEloquent $userRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->userRepository = $userRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param CheckUserRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkUser(CheckUserRequest $request)
    {
        try {
            $user = $this->userRepository->checkUser($request);
            if ($user->status == true) {
                return $this->apiResponse->respondWithMessage(trans('messages.user_exist'));
            } else {
                return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
            }
        } catch (ModelNotFoundException $exception) {
            $setting = Setting::where('slug', 'molema-doctor-sign-up-mode')->first();
            return $this->apiResponse->respondNotFoundWithPayload(['invitation' => $setting->value], trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function socialAuth(CreateSocialAuthRequest $request)
    {
        try {
            $user = $this->userRepository->socialAuth($request);

            // if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
            if (!empty(Auth::guard('web')->user()->email_verified_at)) {
                if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
                    if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
                        return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
                    }
                }
                if (Auth::guard('web')->user()->status == true) {
                    $userDevice = $this->userRepository->storeDeviceInfo($request);
                    if (Auth::guard('web')->user()->is_first_time_login) {
                        Auth::guard('web')->user()->update(['is_first_time_login' => false]);
                    }
                } else {
                    return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
                }
                return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.login_success'));
            } else {
                return $this->apiResponse->respondForbiddenWithPayload(['user' => Auth::guard('web')->user()->only(['id', 'user_type', 'dial_code', 'mobile', 'email', 'email_verified_at', 'mobile_verified_at'])], trans('messages.verify_otp_error'));
            }
        } catch (ModelNotFoundException $exception) {
            $setting = Setting::where('slug', 'molema-doctor-sign-up-mode')->first();
            return $this->apiResponse->respondNotFoundWithPayload(['invitation' => $setting->value], trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UserRegisterRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(UserRegisterRequest $request)
    {
        try {
            $user = $this->userRepository->register($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.register_success'));
        } catch (Exception $exception) {
            if ($exception->getCode() == 23000) {
                return $this->apiResponse->respondValidationError("The email or mobile has already been taken.");
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UserEmailVerifyRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyOtpCode(UserEmailVerifyRequest $request)
    {
        try {
            $user = $this->userRepository->verifyOtpCode($request);
            if (!empty(Auth::guard('web')->user())) {
                // if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
                if (!empty(Auth::guard('web')->user()->email_verified_at)) {
                    if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
                        if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
                            return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
                        }
                    }
                    if (Auth::guard('web')->user()->status == true) {
                        $userDevice = $this->userRepository->storeDeviceInfo($request);
                        if (Auth::guard('web')->user()->is_first_time_login) {
                            Auth::guard('web')->user()->update(['is_first_time_login' => false]);
                        }
                    } else {
                        return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
                    }
                    return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.verify_email_success'));
                } else {
                    return $this->apiResponse->respondForbidden(trans('messages.verify_email_error'));
                }
            } else {
                // if (!empty($user['user']['email_verified_at']) && !empty($user['user']['mobile_verified_at'])) {
                if (!empty($user['user']['email_verified_at'])) {
                    return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.verify_email_success'));
                } else {
                    return $this->apiResponse->respondForbidden(trans('messages.verify_email_error'));
                }
            }
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param CheckUserRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function sendOtpVerificationCode(CheckUserRequest $request)
    {
        try {
            $user = $this->userRepository->checkUser($request);
            $this->userRepository->sendOtpVerificationCode($user, $request);
            return $this->apiResponse->respondWithMessage(trans('messages.send_otp_email'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UserMobileVerifyRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function verifyTwilioOtp(UserMobileVerifyRequest $request)
    {
        try {

            if (env('APP_ENV') == 'Production') {
                try {
                    $verification = $this->userRepository->verifyTwilioOtp($request);

                    if ($verification->valid) {
                        $this->userRepository->updateVerifyStatus($request);
                        if ($request->email_already_verified == 1) {
                            $user = $this->userRepository->mobileAuth($request);
                            // if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
                            if (!empty(Auth::guard('web')->user()->email_verified_at)) {
                                if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
                                    if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
                                        return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
                                    }
                                }
                                if (Auth::guard('web')->user()->status == true) {
                                    $userDevice = $this->userRepository->storeDeviceInfo($request);
                                    if (Auth::guard('web')->user()->is_first_time_login) {
                                        Auth::guard('web')->user()->update(['is_first_time_login' => false]);
                                    }
                                } else {
                                    return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
                                }
                                return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.login_success'));
                            } else {
                                return $this->apiResponse->respondForbiddenWithPayload(['user' => Auth::guard('web')->user()->only(['id', 'user_type', 'dial_code', 'mobile', 'email', 'email_verified_at', 'mobile_verified_at'])], trans('messages.verify_otp_error'));
                            }
                        }
                        return $this->apiResponse->respondWithMessage(trans('messages.verify_mobile_success'));
                    } else {
                        return $this->apiResponse->respondNotFound(trans('messages.verify_mobile_error'));
                    }
                } catch (Exception $exception) {
                    return $this->apiResponse->respondUnauthorized(trans('messages.otp_not_valid'));
                }
            } else {
                if ($request->code == '111111') {
                    $this->userRepository->updateVerifyStatus($request);
                    if ($request->email_already_verified == 1) {
                        $user = $this->userRepository->mobileAuth($request);
                        // if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
                        if (!empty(Auth::guard('web')->user()->email_verified_at)) {
                            if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
                                if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
                                    return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
                                }
                            }
                            if (Auth::guard('web')->user()->status == true) {
                                $userDevice = $this->userRepository->storeDeviceInfo($request);
                                if (Auth::guard('web')->user()->is_first_time_login) {
                                    Auth::guard('web')->user()->update(['is_first_time_login' => false]);
                                }
                            } else {
                                return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
                            }
                            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.login_success'));
                        } else {
                            return $this->apiResponse->respondForbiddenWithPayload(['user' => Auth::guard('web')->user()->only(['id', 'user_type', 'dial_code', 'mobile', 'email', 'email_verified_at', 'mobile_verified_at'])], trans('messages.verify_otp_error'));
                        }
                    }
                    return $this->apiResponse->respondWithMessage(trans('messages.verify_mobile_success'));
                }
            }
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param CheckUserRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function sendTwilioMobileOtp(CheckUserRequest $request)
    {
        try {
            $user = $this->userRepository->checkUser($request);
            $this->userRepository->sendTwilioMobileOtp($user, $request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.send_otp_mobile'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UserLoginRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function login(Request $request)
    {
        Log::info('Current PHP Version: ' . phpversion());
        try {
            if (Auth::guard('web')->attempt(['email' => $request->email, 'password' => $request->password]) || Auth::guard('web')->attempt(['dial_code' => $request->dial_code, 'mobile' => $request->mobile, 'password' => $request->password])) {
                if (Auth::guard('web')->user()->status == true) {
                    // if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
                    if (!empty(Auth::guard('web')->user()->email_verified_at)) {

                        if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
                            if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
                                return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
                            }
                        }
                        $userDetails = $this->userRepository->login();

                        $this->userRepository->storeDeviceInfo($request);

                        if (Auth::guard('web')->user()->is_first_time_login) {
                            Auth::guard('web')->user()->update(['is_first_time_login' => false]);
                        }

                        return $this->apiResponse->respondWithMessageAndPayload($userDetails, trans('messages.login_success'));
                    } else {
                        return $this->apiResponse->respondForbiddenWithPayload(['user' => Auth::guard('web')->user()->only(['id', 'user_type', 'dial_code', 'mobile', 'email', 'email_verified_at', 'mobile_verified_at'])], trans('messages.verify_otp_error'));
                    }
                } else {
                    return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
                }
            } else {
                return $this->apiResponse->respondUnauthorized(trans('messages.invalid_credentials'));
            }
        } catch (Exception $exception) {
            Log::error("Error in Login ==> " . $exception->getMessage());
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateProfile(Request $request)
    {
        try {
            $userDetails = $this->userRepository->updateProfile($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $userDetails], trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function logout(Request $request)
    {
        try {
            Auth::guard('api')->user()->userDevice->update(['device_id' => null, 'push_token' => null, 'voip_token' => null]);
            Auth::guard('api')->user()->token()->delete();
            return $this->apiResponse->respondWithMessage(trans('messages.logout_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * Change Password.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            if (Hash::check($request->old_password, Auth::guard('api')->user()->password)) {
                $user = $this->userRepository->changePassword($request);
                return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.user_password_updated'));
            } else {
                return $this->apiResponse->respondValidationError('Old Password Not Match!');
            }
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * Delete Account
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function deleteAccount(Request $request)
    {
        try {
            $userDetails = $this->userRepository->deleteAccount($request);
            Auth::guard('api')->user()->token()->delete();
            return $this->apiResponse->respondWithMessage(trans('messages.update_profile_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * Delete Account
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function uploadMedia(Request $request)
    {
        try {
            $media = $this->userRepository->uploadMedia($request);
            return $this->apiResponse->respondWithMessageAndPayload($media, trans('messages.upload_media_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * Chat notification
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function chatNotification(Request $request)
    {
        try {
            $media = $this->userRepository->chatNotification($request);
            return $this->apiResponse->respondWithMessageAndPayload($media, trans('messages.chat_notification_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * forgot password
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function forgotPassword(Request $request)
    {
        try {
            $request->validate([
                'email' => ['required', 'email', 'exists:users,email'],
            ]);

            // We will send the password reset link to this user. Once we have attempted
            // to send the link, we will examine the response then see the message we
            // need to show to the user. Finally, we'll send out a proper response.
            $status = Password::sendResetLink(
                $request->only('email')
            );

            if ($status == Password::RESET_LINK_SENT)
                $request->session()->flash('success', trans("messages.reset_password_link_sent"));

            return $status == Password::RESET_LINK_SENT
                ? $this->apiResponse->respondWithMessage(trans('messages.reset_password_link_sent'))
                : $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), 'error');
        } catch (Exception $exception) {
            if ($exception->getCode() == 0) {
                return $this->apiResponse->respondWithMessage(trans('messages.account_invalid'));
            } else {
                return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
            }
        }
    }
}
