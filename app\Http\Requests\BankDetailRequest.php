<?php

namespace App\Http\Requests;

use App\Http\CommonTraits\ApiValidationMessageFormat;
use Illuminate\Foundation\Http\FormRequest;

class BankDetailRequest extends FormRequest
{
    use ApiValidationMessageFormat;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'account_number' => 'required',
            'account_name' => 'required',
            'account_phone' => 'required|min:10',
            'account_type' => 'required',
            'account_address' => 'required',
            'bank_code' => 'required'
        ];
    }
}
