/* Imported Css Url */

@import url('https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css');
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=League+Spartan:wght@100;200;300;400;500;600;700;800;900&display=swap');
/* font-family: 'League Spartan', sans-serif; */

/* Basic Css */

:root {
	/*Basics*/
	--site-Inter-font-family: 'Inter', sans-serif;
	--site-Outfit-font-family: 'Outfit', sans-serif;
	--site-LeagueSpartan-font-family: 'League Spartan', sans-serif;
	--site-font-size: 16px;
	--site-line-height: 24px;
	/* --site-bgcolor: #F7F7F7; */

	/*Typography*/
	--h1-font-size: 48px;
	--h2-font-size: 40px;
	--h3-font-size: 38px;
	--h4-font-size: 36px;
	--h5-font-size: 32px;
	--h6-font-size: 24px;


	/*Wrapper*/
	--wrapper: 1430px;

	/*Transition*/
	--transition: all 400ms ease;

	/*FontSizes*/
	--font-size-10: 10px;
	--font-size-11: 11px;
	--font-size-12: 12px;
	--font-size-13: 13px;
	--font-size-14: 14px;
	--font-size-15: 15px;
	--font-size-16: 16px;
	--font-size-17: 17px;
	--font-size-18: 18px;
	--font-size-19: 19px;
	--font-size-20: 20px;
	--font-size-21: 21px;
	--font-size-22: 22px;
	--font-size-23: 23px;
	--font-size-24: 24px;
	--font-size-25: 25px;
	--font-size-26: 26px;
	--font-size-27: 27px;
	--font-size-28: 28px;
	--font-size-29: 29px;
	--font-size-30: 30px;
	--font-size-40: 40px;
	--font-size-41: 41px;
	--font-size-42: 42px;
	--font-size-43: 43px;
	--font-size-44: 44px;
	--font-size-45: 45px;
	--font-size-55: 55px;

	/*FontWeights*/
	--light: 300;
	--regular: 400;
	--medium: 500;
	--semiBold: 600;
	--bold: 700;
	--extraBold: 900;


	/*Colours*/
	--primary-colour: #202424;
	--primary-text-colour: #333;
	--primary-error-colour: #FC3737;
	--primary-success-colour: #33AB1F;
	--primary-gray-colour: #D2D2D2;
	--primary-green-colour: #00adaa;

	--secondary-colour: #0071BC;
	--secondary-light-colour: #1C99D6;
	--secondary-white-colour: #fff;
	--secondary-gray-colour: #F8F8F8;
	--secondary-text-colour-red: #B00202;
	--link-colour: #05181E;
	--border-colour: #c3c3c3;
	--light-gray-colour: #F0F3F8;

	/*Modals*/
	--modal-title-colour: #191B1E;
	--modal-sub-text-colour: #646872;

	/*Buttons*/
	--primary-button-colour: #fff;
	--primary-button-border-colour: #fff;

	--secondary-button-colour: #fff;
	--secondary-button-border-colour: #0071BC;

	--disabled-button-text-colour: #75767C;
	--disabled-button-bg-colour: #E6E6E6;

	/*Effects*/
	--card-drop-shadow: 0px 10px 20px rgba(0, 0, 0, 0.01);
	--pills-drop-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
	--card-border-radius: 8px;
	--larger-button-radius: 8px;
	--small-button-radius: 4px;

	/*Forms & Inputs*/
	--search-field-bg-colour: #EDEEF2;
	--search-clear-button-colour: rgba(98, 108, 118, 0.5);
	--search-field-border-radius: 10px;
	--switch-off-colour: #777F89;
	--switch-on-colour: #33AB1F;
	--text-input-field-border-colour: #0F2D52;
	--select-placeholder-colour: #3C3E45;
	--select-input-field-border-colour: rgba(119, 127, 137, 0.2);
	--text-input-field-placeholder-colour: #535558;
	--search-field-placeholder-colour: #747F89;
	--text-input-field-focus-border-colour: #005D91;
	--text-input-field-error-border-colour: #B00202;

}

body {
	font-family: var(--site-Inter-font-family);
	font-size: var(--site-font-size);
	color: var(--primary-text-colour);
	line-height: var(--site-line-height);
	margin: 0;
	padding: 0px 0 0 0;
	overflow-x: hidden;
	letter-spacing: 0.5px;
}

h1 {
	font-size: var(--h1-font-size);
	font-family: var(--site-font-family);
	color: var(--primary-colour);
	line-height: 52px;
	margin: 0;
	padding: 0;
}

h2 {
	font-size: var(--h2-font-size);
	font-family: var(--site-Outfit-font-family);
	color: var(--primary-colour);
	line-height: 56px;
	margin: 0;
	padding: 0;
}

h3 {
	font-size: var(--h3-font-size);
	font-family: var(--site-Outfit-font-family);
	color: var(--primary-colour);
	line-height: normal;
	margin: 0;
	padding: 0;
}

h4 {
	font-size: var(--h4-font-size);
	font-family: var(--site-Outfit-font-family);
	color: var(--primary-colour);
	line-height: normal;
	margin: 0;
	padding: 0;
}

h5 {
	font-size: var(--h5-font-size);
	font-family: var(--site-Outfit-font-family);
	color: var(--primary-colour);
	line-height: 52px;
	margin: 0;
	padding: 0;
}

h6 {
	font-size: var(--h6-font-size);
	font-family: var(--site-Outfit-font-family);
	color: var(--primary-colour);
	line-height: 32px;
	margin: 0;
	padding: 0;
}

* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	list-style: none;
	outline: 0;
}

.wrapper {
	margin: 0 auto !important;
	width: var(--wrapper);
	max-width: 100%;
	float: none !important;
}

a {
	text-decoration: none;
	outline: none;
	display: inline-block;
	color: var(--link-colour);
	transition: var(--transition);
	-webkit-transition: var(--transition);
	-moz-transition: var(--transition);
	-o-transition: var(--transition);
}

a:hover {
	color: var(--link-hover-colour);
}

p {
	float: left;
	width: 100%;
	margin: 10px 0;
}

h1+p,
h2+p,
h3+p,
h4+p,
h5+p,
h6+p {
	margin-top: 0;
}

.left-auto {
	float: left;
	width: auto;
	margin: 0;
}

.right-auto {
	float: right;
	width: auto;
	margin: 0;
}

.full-width {
	float: left;
	width: 100%;
}

.child-triggerm,
.sub-menu {
	display: none;
}

.bgimg-main {
	background-size: cover !important;
	background-position: center center !important;
	background-repeat: no-repeat !important;
}

.bg-img {
	display: none;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.text-center {
	text-align: center;
}

.relative {
	position: relative;
}

ul {
	float: left;
	width: 100%;
	margin: 0;
	padding: 0;
}


.cta-btn {
	min-width: 150px;
	text-align: center;
	border: 1px solid var(--primary-button-border-colour);
	color: var(--primary-button-colour);
	font-weight: var(--bold);
	border-radius: 30px;
	font-size: var(--font-size-15);
	padding: 10px 20px;
}

.cta-btn:hover,
.cta-btn.hovered {
	background: var(--secondary-white-colour);
	color: var(--secondary-colour);
}

.cta-btn.with-bg {
	background: var(--secondary-colour);
	color: var(--secondary-white-colour);
	border-color: var(--secondary-colour);
}

.cta-btn.with-bg:hover {
	background: var(--secondary-white-colour);
	color: var(--secondary-colour);
}

.two-cta {
	display: flex;
}

.two-cta>a:last-child {
	margin-left: 15px;
}

/* Menu Css */

.sub-menu li {
	float: left;
	width: 100%;
	position: relative;
}

.sub-menu li a {
	padding: 10px 15px;
	display: block;
}

.mainmenu li a {
	color: #3c3c3c;
}

.common-spacing {
	margin: 120px 0 0;
}

.header-section {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 9;
	padding: 20px 0;
	transition: var(--transition);
}

.header-section.header-fixed {
	background: var(--secondary-colour);
	padding: 10px 0
}

.logo img {
	transition: var(--transition);
	max-width: 118px;
}

.header-section.header-fixed .logo img {
	max-width: 90px;
}

.top-right {
	display: flex;
	align-items: center;
}

.top-right .cta-btn {
	margin-left: 15px;
}

.banner-section {
	padding-top: 142px;
	position: relative;
}

.banner-section .wrapper {
	position: relative;
	z-index: 1;
}

.banner-shap {
	position: relative;
}

.banner-social { position: absolute; right: 0; bottom: 0; display: flex;
    flex-direction: column; align-items: center; z-index: 1;}

.banner-social a { color: var(--secondary-white-colour); cursor: pointer; margin:7px 0}

.banner-shap::after {
	position: absolute;
	left: 0;
	top: 0;
	background: url(../media/website-images/banner-bg.png);
	background-position: bottom center;
	background-repeat: no-repeat;
	background-size: cover;
	height: calc(100% + 20px);
	width: 100%;
	content: ''
}

.think-doctor-section {
	position: relative;
}

.think-doctor-section .wrapper {
	position: relative;
	z-index: 1;
}

.think-doctor-section::before {
	content: '';
	bottom: -20px;
	left: 0;
	width: 100%;
	height: 290px;
	position: absolute;

	background-image: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.5), rgba(255, 255, 255, .7), rgba(255, 255, 255, .9));
}

.banner-right>img {
	position: relative;
	z-index: 1;
}

.banner-left {
	float: left;
	width: 60%;
	color: var(--secondary-white-colour);
}

.banner-right {
	float: left;
	width: 40%;
	color: var(--secondary-white-colour);
	position: relative;
}

.banner-right .dimond-shap {
	position: absolute;
	left: -270px;
	bottom: 130px;
	width: 480px;
	height: 150px;
}

.banner-right img {
	max-width: 100%;
}

.banner-left p {
	font-size: var(--font-size-23);
	font-weight: var(--light);
	line-height: 34px;
	padding: 15px 0;
}

.banner-left h2 {
	color: var(--secondary-white-colour);
}

.banner-left .cta-btn.full-width {
	width: 559px;
	max-width: 100%;
}

.banner-left .two-cta {
	margin-top: 50px;
}

.banner-left .two-cta a:hover,
.footer-3 .two-cta a:hover {
	transform: translateY(-3px);
}

.think-doctor-section {
	position: relative;
	z-index: 1;
	margin-top: 65px;
}

.think-doctor-section h1 {
	font-weight: var(--semiBold);
	margin-bottom: 20px;
}

.think-doctor-content {
	box-shadow: 0px 7px 41px -26px #c3c3c3;
	background: var(--secondary-white-colour);
	border-radius: 16px;
	padding: 40px 50px;
	display: flex;
	align-items: center;
}

.think-doctor-right {
	display: flex;
	text-align: center;
}

.think-doctor-pattern {
	background: url(../images/tiles-pattern.svg);
	background-position: top center;
	background-repeat: no-repeat;
	background-size: cover;
	margin-left: 25px;
	padding: 40px 50px 30px;
    background-color: rgba(211, 211, 211, 0.22);
    border-radius: 10px;
}

.think-doctor-pattern p {
	font-size: var(--font-size-14);
	line-height: 18px;
}

.think-doctor-pattern h6 {
	padding: 14px 0 15px;
}

.popular-logo-slider {
	position: relative;
	z-index: 1;
	text-align: center;
	padding: 42px 0 53px;
	background: var(--light-gray-colour);
}

.popular-logo-slider h5 {
	font-weight: var(--semiBold);
	font-family: var(--site-LeagueSpartan-font-family);
	margin-bottom: 30px;
}

.popular-logos {
	text-align: center;
}

.popular-logo-slider a {
	cursor: pointer;
	font-family: var(--site-LeagueSpartan-font-family);
	font-size: var(--font-size-18);
	font-weight: var(--medium);
}

.popular-logo-slider .logoImage {
	height: 70px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 5px;
}

.popular-logos img {
	display: inline-block;
}

.health-care-section .wrapper {
	position: relative;
}

.health-care-left {
	float: left;
	width: 80%;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	height: 100%;
}

.health-care-left>img {
	max-width: 100%;
}

.health-care-icons {
	position: absolute;
	left: 0;
	top: 0
}

.health-care-icons img {
	position: absolute;
	left: 50px;
	top: 50px;
	animation: crescendo 1.5s alternate infinite ease-in;
}

.health-care-icons img.care-icon-3 {
	left: 180px;
	top: 0px
}

.health-care-icons img.care-icon-1 {
	top: 180px;
}

.health-care-right {
	float: right;
	width: 40%;
	border-radius: 10px;
	background: rgba(0, 113, 188, 0.80);
	color: var(--secondary-white-colour);
	padding: 38px 32px 20px 32px;
	min-width: 655px;
	max-width: 100%;
	position: relative;
	z-index: 1;
}

.health-care-right h2 {
	color: var(--secondary-white-colour);
	margin: 5px 0 26px;
	float: left;
	width: 100%;
}

.why-choose-us-title {
	text-transform: uppercase;
	font-weight: var(--medium);
	letter-spacing: 2px;
}

.trusted-doctor-main {
	display: flex;
	justify-content: space-between;
	text-align: center;
	font-size: var(--font-size-20);
	font-weight: var(--semiBold);
	font-family: var(--site-Outfit-font-family);
	line-height: 31px;
}

.molema-work-video {
	border-radius: 6px;
	overflow: hidden;
	line-height: 0;
	margin-top: 85px;
	width: 105%;
	left: -2.5%;
	position: relative;
}

.molema-work-video h6 {
	background: var(--secondary-white-colour);
	padding: 6px 20px;
	width: 100%;
	text-align: center;
}

.molema-work-video iframe {
	height: 275px !important;
	width: 100% !important;
}

.pharmacy-network-left {
	float: left;
	width: 766px;
	max-width: 100%;
}

.pharmacy-network-right {
	float: right;
	width: 575px;
	max-width: 100%;
	color: var(--secondary-white-colour);
	background: rgba(0, 113, 188, 0.80);
	border-radius: 16px;
	padding: 30px 35px
}

.pharmacy-network-left .why-choose-us-title,
.testimonial-right .why-choose-us-title {
	color: var(--secondary-colour);
}

.pharmacy-network-left h5 {
	font-family: var(--site-Inter-font-family);
	margin-top: 20px;
}

.mapImage {
	margin: 27px 0;
}

.mapImage img {
	max-width: 100%;
}

.own-pharmacy {
	border: 1px solid var(--secondary-colour);
	border-radius: 10px;
	padding: 40px 40px
}

.own-pharmacy h3 {
	font-family: var(--site-Inter-font-family);
	margin: 17px 0 15px
}

.own-pharmacy ul {
	margin: 30px 0 13px;
}

.own-pharmacy ul li {
	position: relative;
	padding-left: 25px;
	font-size: var(--font-size-20);
	margin-bottom: 17px;
}

.own-pharmacy ul li::after {
	position: absolute;
	left: 0;
	top: 0;
	font-family: 'fontAwesome';
	content: "\f058";
	color: var(--secondary-light-colour)
}


.pharmacy-network-right h4 {
	color: var(--secondary-white-colour);
	margin-bottom: 22px;
	font-family: var(--site-Inter-font-family);
	font-weight: var(--medium);
}

.talk-to-doc:first-child {
	margin-top: 26px;
}

.talk-to-doc {
	margin-top: 16px;
}

.talk-left {
	width: 60px;
	float: left;
}

.talk-right {
	width: calc(100% - 60px);
	padding-left: 15px;
	float: left;
}

.talk-right h6 {
	color: var(--secondary-white-colour);
	margin-bottom: 7px;
	font-family: var(--site-Inter-font-family);
	font-weight: var(--medium);
}

.doc-talk-video {
	border-radius: 10px;
	overflow: hidden;
	line-height: 0;
	margin-top: 45px;
}

.doc-talk-video iframe {
	height: 385px !important;
	width: 100% !important;
}

.need-help {
	background: url(../images/need-help-bg.jpg);
	border-radius: 10px;
	position: relative;
	overflow: hidden;
	padding: 110px 35px;
	margin-top: 50px;
}

.need-help::after {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	content: '';
	background: rgba(0, 0, 0, .5);
}

.need-help .talk-to-doc {
	margin-top: 0px;
	position: relative;
	z-index: 1;
}

.need-help h4 {
	position: relative;
	z-index: 1;
}

@keyframes crescendo {
	0% {
		transform: scale(.8);
	}

	100% {
		transform: scale(1);
	}
}

.testimonial-left {
	width: 490px;
	height: 640px;
	position: relative;
	float: left;
}

.testimonial-left img {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

.testimonial-right {
	width: calc(100% - 490px);
	padding-left: 75px;
	float: left;
}

.testimonial-right h2 {
	margin: 14px 0 15px;
}

.authBoxMain .talk-right h6 {
	color: var(--primary-colour);
	margin: 0;
	font-size: var(--font-size-18);
	font-family: var(--site-Inter-font-family);
	font-weight: var(--medium);
	line-height: normal;
}

.auth-star {
	margin: 1px 0 0;
}

.auth-star a {
	color: var(--primary-gray-colour);
	font-size: var(--font-size-14);
}

.auth-star+p {
	margin: 0;
	font-size: var(--font-size-14);
}

.authContent {
	font-size: var(--font-size-24);
	line-height: 32px;
	font-weight: var(--medium);
	margin-top: 12px;
}

.authBoxMain {
	margin-top: 10px;
}

.authBox {
	background: var(--light-gray-colour);
	padding: 30px 35px 25px;
	border-radius: 10px;
	margin-top: 15px;
}

.authBoxMain {
	max-height: calc(640px - 178px);
	overflow: hidden;
	position: relative;
}

.authBoxMain::after {
	content: '';
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100px;
	position: absolute;
	background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1));
}

.authBoxMain .talk-to-doc {
	margin: 0 0 0 0;
}

.google-review {
	display: flex;
	border-top-right-radius: 10px;
	border-top-left-radius: 10px;
	background: var(--secondary-white-colour);
	position: absolute;
	bottom: 0;
	right: 0;
	padding: 25px 35px;
}

.rating {
	font-size: var(--font-size-55);
	border-right: 1px solid var(--light-gray-colour);
	font-weight: var(--bold);
	color: var(--primary-green-colour);
	display: flex;
	align-items: center;
	padding-right: 12px;
	margin-right: 15px;
}

.rating-right {
	display: flex;
	align-items: center;
	font-size: var(--font-size-12);
}

.rating-right .auth-star {
	margin: 0 0 0 5px;
}

.google-review h6 {
	color: var(--primary-colour);
	font-weight: var(--bold);
	line-height: 26px;
}

.footer {
	background: var(--secondary-gray-colour);
	padding: 98px 0 0px;
}

.footer .wrapper>div {
	width: 33.3%;
	float: left;
	padding-right: 100px;
}

.footer-social {
	margin-top: 25px;
}

.footer-social a:hover {
	transform: translateY(-3px);
}

.footer-social a img {
	margin-bottom: 0;
}

.footer h6 {
	font-size: var(--font-size-18);
	font-family: var(--site-Inter-font-family);
	color: var(--secondary-light-colour);
	font-weight: var(--medium);
	margin-bottom: 15px;
}

.footer .footer-3 h6 {
	color: var(--primary-colour);
}

.footer-2,
.footer-3 {
	font-size: var(--font-size-14);
}

.footer-2 ul li {
	margin-bottom: 5px;
}

.footer-2 ul li a {
	display: flex;
	align-items: center;
}

.footer-2 ul li a:hover {
	color: var(--secondary-colour);
}

.footer-2 ul li img {
	display: inline-block;
	margin-right: 10px;
}

.footer-3 .two-cta {
	margin-top: 12px;
}

.footer-3 .two-cta img {
	max-width: 100%;
}

.footer-1 img {
	margin-bottom: 5px;
}

.footer-copyright {
	padding: 26px 0;
	margin-top: 87px;
	background: var(--secondary-colour);
	color: var(--secondary-white-colour);
	font-size: var(--font-size-13);
}

.footer-copyright .wrapper {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.footer-copyright a {
	margin-left: 10px;
	color: var(--secondary-white-colour);
}

.footer-copyright a:hover {
	opacity: .7;
}

.footer-copyright .wrapper>div {
	width: auto;
	padding-right: 0;
}

.back-to-top {
	text-align: right;
	margin-top: 50px;
}

.back-to-top a {
	cursor: pointer;
}


/* Media Screen */

@media (min-width:768px) {
	.trusted-doctor-main > div {
		width: 33.3%;
		padding: 0 9px;
	}
}


@media (max-width:1450px) {
	.wrapper {
		width: 100%;
		padding: 0 50px;
	}

	:root {
		--h1-font-size: 40px;
	}

	.think-doctor-pattern {
		padding: 30px 20px 15px;
	}

	.think-doctor-content {
		padding: 35px 40px;
	}

	.banner-left {
		width: 50%;
		padding-right: 20px;
	}

	.banner-right {
		width: 50%
	}

	.banner-left .cta-btn.full-width {
		width: 400px;
		max-width: 100%;
	}

	.banner-left h2 br {
		display: none;
	}

	.banner-left p {
		font-size: var(--font-size-18);
		line-height: 28px;
	}

	.banner-left .two-cta {
		margin-top: 30px;
	}

	.think-doctor-section {
		margin-top: 40px;
	}

	.health-care-right {
		min-width: 545px;
	}

	.molema-work-video {
		margin-top: 40px;
	}

	.health-care-left {
		width: 70%;
		margin-top: 30px;
	}

	.pharmacy-network-left {
		width: 50%;
		padding-right: 30px;
	}

	.pharmacy-network-left h5 {
		line-height: 42px;
	}

	.need-help {
		padding: 85px 35px;
	}

	.doc-talk-video iframe {
		height: 310px !important;
	}

	.footer .wrapper>div {
		padding-right: 70px;
	}
}

@media (max-width:1240px) {
	:root {
		--h1-font-size: 30px;

		--h6-font-size: 22px;
	}

	.wrapper {
		padding: 0 25px;
	}

	.think-doctor-pattern {
		padding: 25px 12px 15px;
		margin-left: 20px;
	}

	.think-doctor-content {
		padding: 30px 30px;
	}

	.think-doctor-section h1 {
		line-height: normal;
		margin-bottom: 12px;
	}

	.think-doctor-pattern h6 {
		padding: 10px 0 10px;
	}

	.health-care-left {
		width: 70%;
		margin-top: 30px;
	}

	.health-care-right {
		min-width: 500px;
	}

	.health-care-right h2 {
		line-height: 45px;
	}

	.trusted-doctor-main {
		line-height: 26px;
		font-size: var(--font-size-18);
	}

	.health-care-right {
		padding: 38px 25px 20px 25px;
	}

	.health-care-left {
		width: 80%;
		margin-top: 40px;
	}

	.molema-work-video {
		width: 100%;
		left: 0;
	}

	.pharmacy-network-left {
		width: 50%;
		padding-right: 30px;
	}

	.pharmacy-network-right {
		width: 50%;
	}

	.own-pharmacy {
		padding: 35px 30px;
	}

	.doc-talk-video iframe {
		height: 250px !important;
	}

	.need-help {
		padding: 45px 25px;
		margin-top: 30px;
	}

	.testimonial-right {
		padding-left: 45px;
	}

	.authContent {
		font-size: var(--font-size-19);
		line-height: 27px;
	}

	.footer .wrapper>div {
		padding-right: 20px;
	}

	.footer .wrapper>div:last-child {
		padding-right: 0;
	}

	.footer {
		padding: 80px 0 0px;
	}

	.footer-copyright {
		margin-top: 70px;
	}

	.common-spacing {
		margin: 100px 0px 0 0;
	}

}

@media (max-width:992px) {
	:root {
		--h1-font-size: 32px;
		--h2-font-size: 30px;
		--h3-font-size: 28px;
		--h4-font-size: 27px;
		--h5-font-size: 26px;
		--h6-font-size: 22px;

	}

	.pharmacy-network-left h5 {
		line-height: 34px;
	}

	.logo img {
		max-width: 80px;
	}

	.header-section.header-fixed .logo img {
		max-width: 70px;
	}

	.cta-btn {
		padding: 6px 12px;
		min-width: 125px;
	}

	.think-doctor-content {
		flex-direction: column;
	}

	.think-doctor-pattern {
		margin: 10px 15px 0 0;
		padding: 25px 20px 15px;
	}

	.banner-left h2 {
		line-height: 45px;
	}

	.banner-left .two-cta img {
		max-width: 100%;
	}

	.banner-left p {
		font-size: var(--font-size-16);
	}

	.health-care-left {
		width: 100%;
		margin-top: 30px;
	}

	.health-care-right {
		min-width: 100%;
		width: 100%;
		text-align: center;
	}

	.health-care-left {
		position: relative;
		top: 0;
		transform: none;
		text-align: center;
		margin: 0 0 40px 0
	}

	.health-care-left>img {
		width: 500px
	}

	.health-care-icons img.care-icon-3 {
		left: 130px;
	}

	.health-care-icons img.care-icon-1 {
		top: 145px;
	}

	.health-care-icons img {
		max-width: 60px;
	}

	.own-pharmacy {
		padding: 30px 25px;
	}

	h3 br {
		display: none;
	}

	.own-pharmacy ul li {
		font-size: var(--font-size-16);
		margin-bottom: 12px;
	}

	.own-pharmacy ul {
		margin: 20px 0 13px;
	}

	.pharmacy-network-right {
		padding: 28px 25px;
	}

	.talk-right h6 {
		font-size: var(--font-size-20);
	}

	.talk-right h6+p {
		font-size: var(--font-size-15);
	}

	.talk-left {
		width: 45px;
	}

	.talk-left img {
		max-width: 45px;
	}

	.doc-talk-video iframe {
		height: 210px !important;
	}

	.doc-talk-video {
		margin-top: 20px;
	}

	.need-help {
		padding: 30px 20px;
	}

	.talk-to-doc:first-child {
		margin-top: 15px;
	}

	.testimonial-left {
		width: 350px;
		height: 400px;
	}

	.testimonial-right {
		width: calc(100% - 350px);
		padding-left: 35px;
	}

	.testimonial-right h2 {
		line-height: 34px;
	}

	.authBox {
		padding: 15px 20px 15px;
	}

	.authContent {
		font-size: var(--font-size-16);
		line-height: 24px;
	}

	.rating {
		font-size: var(--font-size-25);
	}

	.google-review h6 {
		font-size: var(--font-size-18);
		line-height: 22px;
	}

	.google-review {
		padding: 17px 25px;
	}

	.authBoxMain {
		max-height: calc(640px - 458px);
	}

	.authBoxMain::after {
		bottom: -10px;
	}

	.footer {
		padding: 68px 0 0px;
	}

	.footer-copyright {
		margin-top: 60px;
	}

	.common-spacing {
		margin: 80px 0px 0 0;
	}

	.banner-right .dimond-shap {
		bottom: 70px;
	}
}

@media (max-width:767px) {
	.wrapper {
		padding: 0 20px;
	}

	:root {
		--site-font-size: 14px;
		--site-line-height: 24px;
		--section-padding: 50px 0px;
	}

	.logo img {
		max-width: 60px;
	}

	.think-doctor-left {
		text-align: center;
	}

	.header-section.header-fixed .logo img {
		max-width: 60px;
	}

	.think-doctor-right {
		flex-direction: column;
	}

	.think-doctor-pattern {
		margin: 20px 0px 0 0;
	}

	.cta-btn {
		padding: 5px 10px;
		min-width: 110px;
		font-size: var(--font-size-14);
	}

	.top-right .cta-btn {
		margin-left: 10px;
		min-width: 100px;
	}

	.health-care-right h2 br {
		display: none;
	}

	.banner-section .wrapper {
		flex-direction: column-reverse;
	}

	.banner-left,
	.banner-right {
		width: 100%;
		padding-right: 0px;
		text-align: center;
	}

        .banner-left { margin-top: 20px;}

	.banner-left .two-cta {
		justify-content: center;
	}

	.banner-left .cta-btn.full-width {
		width: 100%;
	}

	.banner-section {
		padding-top: 110px;
	}

	.health-care-left {
		margin: 0 0 30px 0;
	}

	.health-care-icons img {
		left: 0;
	}

	.health-care-icons img.care-icon-3 {
		left: 50px;
		top: 10px;
	}

	.health-care-icons img {
		max-width: 45px;
	}

	.health-care-icons img.care-icon-1 {
		top: 112px;
	}

	.health-care-icons {
		top: -30px;
	}

	.trusted-doctor-main {
		flex-direction: column;
	}

	.trusted-doctor-main>div {
		margin: 10px 0 20px;
	}

	.molema-work-video {
		margin-top: 20px;
	}

	.molema-work-video iframe {
		height: 240px !important;
	}

	.pharmacy-network-left {
		width: 100%;
		padding: 0 0 30px 0;
	}

	.pharmacy-network-right {
		width: 100%;
	}

	.talk-left {
		width: 35px;
	}

	.talk-left img {
		max-width: 35px;
	}

	.talk-right {
		width: calc(100% - 35px);
	}

	.talk-right h6 {
		line-height: 28px;
		font-size: var(--font-size-18);
	}

	.need-help {
		padding: 30px 20px 20px;
	}

	.testimonial-left {
		width: 100%;
	}

	.testimonial-right {
		width: 100%;
		padding: 40px 0 0 0;
	}

	.authBoxMain {
		max-height: initial;
	}

	.common-spacing {
		margin: 60px 0 0;
	}

	.footer .wrapper>div {
		width: 100%;
		padding: 0 0 30px 0;
	}

	.footer-social {
		margin-top: 10px;
	}

	.back-to-top {
		margin-top: 20px;
	}

	.footer-copyright {
		margin-top: 25px;
	}

	.footer {
		padding: 48px 0 0px;
	}

	.footer-copyright .wrapper {
		padding: 5px 20px;
		flex-direction: column-reverse;
		text-align: center;
	}

	.footer .wrapper>div {
		padding-right: 0;
	}

	.footer-copyright .wrapper>div {
		padding: 5px 0;
	}

	.footer-copyright a {
		margin: 0 5px;
	}

	.footer-copyright {
		padding: 18px 0;
	}

	.banner-right .dimond-shap {
		bottom: 70px;
		transform: scale(.5);
		left: -250px;
	}

}

  .select-menu .select-btn {
    display: flex;
    height: 5px;
    background: #fff;
    padding: 20px;
    font-size: 18px;
    font-weight: 400;
    border-radius: 8px;
    align-items: center;
    cursor: pointer;
    justify-content: space-between;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  }
  .select-menu .options {
    position: absolute;
    width: 150px;
    overflow-y: auto;
    max-height: 295px;
    padding: 10px;
    margin-top: 10px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    animation-name: fadeInDown;
    -webkit-animation-name: fadeInDown;
    animation-duration: 0.35s;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.35s;
    -webkit-animation-fill-mode: both;
  }
  .select-menu .options .option {
    display: flex;
    height: 40px;
    cursor: pointer;
    padding: 0 16px;
    border-radius: 8px;
    align-items: center;
    background: #fff;
  }
  .select-menu .options .option:hover {
    background: #f2f2f2;
  }
  .select-menu .options .option i {
    font-size: 25px;
    margin-right: 12px;
  }
  .select-menu .options .option .option-text {
    font-size: 18px;
    color: #333;
  }

  .select-btn i {
    font-size: 25px;
    transition: 0.3s;
  }

  .select-menu.active .select-btn i {
    transform: rotate(-180deg);
  }
  .select-menu.active .options {
    display: block;
    opacity: 0;
    z-index: 10;
    animation-name: fadeInUp;
    -webkit-animation-name: fadeInUp;
    animation-duration: 0.4s;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.4s;
    -webkit-animation-fill-mode: both;
  }

  @keyframes fadeInUp {
    from {
      transform: translate3d(0, 30px, 0);
    }
    to {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
  }
  @keyframes fadeInDown {
    from {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }
    to {
      transform: translate3d(0, 20px, 0);
      opacity: 0;
    }
  }
