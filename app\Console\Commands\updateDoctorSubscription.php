<?php

namespace App\Console\Commands;

use App\Enums\DoctorType;
use App\Models\DoctorDetail;
use App\Models\DoctorSubscription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class updateDoctorSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'doctor:subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Doctor subscription complete';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info("doctor:subscription");

        $doctorSubscription = DoctorSubscription::select('id', 'user_id', \DB::raw('MAX(subscription_end_date) as end_date'))
                            ->whereNotNull('transaction_id')
                            ->where('end_date', '<', date('d-m-Y'))
                            ->groupBy('user_id')->get();


        foreach($doctorSubscription as $key=>$subscription){
            $doctorDetail = DoctorDetail::where('user_id', $subscription->user_id)->update(['doctor_type' => DoctorType::Freelancer]);
        }

        return $doctorDetail;
    }
}
