<?php

return [
    'something_went_wrong' => 'Something went wrong with our system. Please try again later.',
    'user_exist' =>'User is already exist with this account.',
    'reset_password_link_sent' => 'Reset password link has been sent to your email. Please check your mail.',
    'login_success' => 'Logged in successfully.',
    'admin_reset_password' => 'Your password has been changed successfully.',
    'register_success' => 'We have sent you an verification email. Please complete the process and login.',
    'verify_email_first' => 'Please verify your email first and try again.',
    'verify_email_success' => 'Your email verification has been successfully completed.',
    'verify_email_error' => 'Your email otp is invalid.',
    'verify_mobile_success' => 'Your mobile verification has been successfully completed.',
    'verify_mobile_error' => 'Your mobile otp is invalid.',
    'verify_otp_error' => 'Your OTP verification is not completed yet.',
    'send_otp_email' => 'We have sent you an verification code in email.',
    'send_otp_mobile' => 'We have sent you an verification code in mobile.',
    'user_is_inactive' => 'You are currently inactive user. Please contact to our support system.',
    'invalid_credentials' => 'Your credentials does not match with our system. Please try again.',
    'profile_details' => 'Profile types has been retrieved successfully.',
    'medical_history_detail' => 'Medical history has been retrieved successfully',
    'logout_success' => 'You are logged out successfully.',
    'record_not_found' => 'Requested record not found.',
    'forgot_password_link' => 'Reset password link has been sent to your email. Please check your mail.',
    'forgot_password_link_not_sent' => 'Reset password link has been not sent to your email.',
    'update_profile_success' => 'Your Profile has been updated successfully.',
    'profile_retrieved_success' => 'Your Profile has been retrieved successfully.',
    'speciality_details' => 'Speciality details has been retrieved successfully.',
    'cmspage_created' => 'CMS Page has been created successfully.',
    'cmspage_updated' => 'CMS Page has been updated successfully.',
    'cmspage_fetching' => 'CMS Page retrieved successfully',
    'cmspage_delete' => 'CMS Page deleted successfully.',
    'get_faq_data' => 'Faqs retrieved successfully',
    'get_cms_data' => 'Cms retrieved successfully',
    'support_status_update' => 'Your status has been updated successfully.',
    'support_created' => 'Support has been sent to admin successfully.',
    'document_delete_success' => 'Document has been deleted successfully.',
    'faq_created' => 'Faq has been created successfully.',
    'faq_updated' => 'Faq has been updated successfully.',
    'faq_fetching' => 'Faq retrieved successfully',
    'faq_delete' => 'Faq deleted successfully.',
    'setting_created' => 'Setting has been created successfully.',
    'setting_updated' => 'Setting has been updated successfully.',
    'setting_fetching' => 'Setting retrieved successfully',
    'setting_delete' => 'Setting deleted successfully.',
    'availability_overlap' => 'Doctor availability has been overlapped Please select other time slot.',
    'rating_created' => 'Rating has been created successfully.',
    'rating_updated' => 'Rating has been updated successfully.',
    'rating_deleted' => 'Rating has been deleted successfully.',
    'rating_fetching' => 'Rating has been retrieved successfully',
    'appointment_created' => 'Appointment has been created successfully.',
    'time_slot_created' => 'Time Slot has been created successfully.',
    'availability_created' => 'Doctor Availability has been created successfully.',
    'unavailability_created' => 'Doctor Unavailability has been created successfully.',
    'appointment_overlap' => 'You already have appointment on that date and time.',
    'booking_fetching' => 'Your appointment booking list retrieved successfully.',
    'availability_fetching' => 'Doctor Availability has been retrieved successfully.',
    'unavailability_fetching' => 'Doctor Unavailability has been retrieved successfully.',
    'unavailability_delete' => 'Doctor Unavailability has been deleted successfully.',
    'patient_prescription_success' => 'Patient prescription has been retrieved successfully.',
    'user_unavailable' => 'User unavailable',
    'verify_security_code_error' => 'Your security code is invalid.',
    'verify_invitation_code_error' => 'Your Invitation code is invalid.',
    'call_ongoing_success' => 'Your call has been start successfully.',
    'call_completed_success' => 'Your call has been completed successfully.',
    'not_receive_call' => 'Can not receive the call.',
    'not_accept_call' => 'Can not accept the call.',
    'prescription_add_success' => 'Prescription has been added successfully.',
    'prescription_update_success' => 'Prescription has been updated successfully.',
    'notes_add_success' => 'Notes data has been added successfully.',
    'notes_get_success' => 'Notes has been retrieved successfully.',
    'patient_lab_test_add_success' => 'Patient Lab Test has been added successfully.',
    'patient_lab_test_get_success' => 'Patient Lab Test has been retrieved successfully.',
    'update_collabrator' => 'Your status has been updated to collabrator.',
    'appointment_detail_success' => 'Appointment Detail has been retrieved successfully.',
    'upload_media_success' => 'Media has been uploaded successfully.',
    'support_resolved' => 'Support ticket has been resolved successfully.',
    'invitation_sent' => 'Invitation to the doctor has been send successfully',
    'transaction_fetching' => 'Transaction has been retrieved successfully.',
    'bank_detail_add_success' => 'Bank Detail has been added successfully.',
    'bank_detail_fetch_success' => 'Bank Detail has been fetched successfully.',
    'invitation_already_sent' => 'You have already invited this Person.',
    'payment_status_update_success' => 'Payment status has been updated successfully.',
    'account_unverified' => 'Your account verification request has been pending.',
    'notification_get_success' => 'Your notification has been received successfully',
    'chat_notification_success' => 'Chat notification has been send successfully',
    'support_ticket_open' => 'Support ticket has been open successfully.',
    'support_ticket_close' => 'Support ticket has been closed successfully.',
    'bank_detail_updated' => 'Bank Detail has been updated successfully.',
    'file_import_success' => 'File has been imported successfully!',
    'subscription_view_success' => 'Subscription has been fetched successfully.',
    'app_video_max_limit' => 'You can upload max 5 video for patient application home screen.',
    'web_video_max_limit' => 'You can upload max 2 video for doctor subscription website.',
    'video_created' => 'video has been created successfully.',
    'video_updated' => 'video has been updated successfully.',
    'video_fetching' => 'video retrieved successfully',
    'video_delete' => 'video deleted successfully.',
    'availability_deleted' => 'availability has been deleted successfully.',
    'medicine_add_success' => 'Medicine has been added successfully.',
    'lab_test_add_success' => 'Lab Test has been added successfully.',
    'doctor_user_unavailable' => 'Currently doctor is unavailable.',
    'patient_user_unavailable' => 'Currently patient is unavailable.',
    'account_invalid' => 'We can`t find a user with that email address.',
    'video_change_status' => 'Video status has been changed successfully.',
    'appointment_booked' => 'Your consultation has been booked successfully.'
];
