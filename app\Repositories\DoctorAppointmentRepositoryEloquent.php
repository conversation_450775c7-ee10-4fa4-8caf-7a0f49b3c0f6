<?php

namespace App\Repositories;

use App\Enums\AppointmentStatus;
use App\Enums\ConsultationType;
use App\Enums\Day;
use App\Enums\NotificationType;
use App\Enums\PaymentStatus;
use App\Enums\SenderType;
use App\Enums\Status;
use App\Enums\TransactionPaymentStatus;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\Notification;
use App\Jobs\SendEmails;
use App\Models\EmailTemplate;
use App\Jobs\silentNotification;
use App\Models\AppointmentCall;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorAppointmentRepository;
use App\Models\DoctorAppointment;
use App\Models\DoctorAvailability;
use App\Models\DoctorRating;
use App\Models\DoctorUnavailability;
use App\Models\DoctorWalletTransaction;
use App\Models\Log as ModelLog;
use App\Models\PatientDetail;
use App\Models\User;
use App\Transformers\AppointmentDetailTransformer;
use App\Transformers\DoctorAppointmentTransformer;
use App\Validators\DoctorAppointmentValidator;
use DateTime;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Period\Boundaries;
use Spatie\Period\Period;
use Spatie\Period\Precision;
use Yajra\DataTables\DataTables;
use App\Models\PatientNote;
use App\Models\PatientLabTest;
use App\Models\PatientPrescription;
use App\Transformers\CallDetailTransformer;
use App\Models\Support;
use App\Models\Transaction;
use Carbon\Carbon;
use App\Jobs\SendEmails​;
use Log;

/**
 * Class DoctorAppointmentRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorAppointmentRepositoryEloquent extends BaseRepository implements DoctorAppointmentRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorAppointment::class;
    }

    /**
     * Get time slot according to doctor availability/unavailability
     *
     * @return array
     */
    public function getTimeSlot($request)
    {

        //check doctor unavailability date and time
        $unavailability = DoctorUnavailability::where('unavailable_date', $request->date)->where('user_id', $request->doctor_id)->get();
        $fullDayUnavailability = $unavailability;
        $halfDayUnavailability = $unavailability;

        if (!empty($unavailability)) {

            //check doctor take full day leave or half day leave
            $fullDayLeave = $fullDayUnavailability->where('leave_status', Status::Active)->first();
            if (!empty($fullDayLeave)) {
                return []; // Doctor is on full day leave
            } else {
                $timeSlotLeave = $halfDayUnavailability->where('leave_status', Status::Inactive)->toArray();
            }
        }

        $day = date('l', strtotime($request->date)); // convert date to day

        //check doctor availability as particular date convert to day
        $availability = DoctorAvailability::where('user_id', $request->doctor_id)->where('day', Day::getValues($day))->get()->toArray();

        $times = [];
        $slotsRange = [];
        $count = 0;

        foreach ($availability as $index => $available) {
            $a = Period::make('1970-01-01 ' . $available['start_time'], '1970-01-01 ' . $available['end_time'], Precision::HOUR);
            $slots = [];
            $yes = 0;
            if (!empty($timeSlotLeave)) {
                foreach ($timeSlotLeave as $index2 => $timeSlot) {
                    $b = Period::make('1970-01-01 ' . $timeSlot['start_time'], '1970-01-01 ' . $timeSlot['end_time'], Precision::HOUR);
                    $overlap = $a->overlapsWith($b);
                    if ($overlap) { //Doctor has take leave and it overlap with availability
                        $yes = 0;
                        if ($timeSlot['start_time'] > $available['start_time']) {
                            $slots['start_time'] = $available['start_time'];
                            $slots['end_time'] = $timeSlot['start_time'];
                            $slotsRange[$count] = $slots;

                            $count += 1;
                        }

                        if ($timeSlot['end_time'] < $available['end_time']) {
                            $slots['start_time'] = $timeSlot['end_time'];
                            $slots['end_time'] = $available['end_time'];
                            $slotsRange[$count] = $slots;

                            $count += 1;
                        }
                        break;
                    } else {
                        $yes += 1;
                    }
                }
            } else { // Doctor has not take leave on selected date
                $yes += 1;
            }
            if ($yes >= 1) {
                $slots['start_time'] = $available['start_time'];
                $slots['end_time'] = $available['end_time'];
                $slotsRange[$count] = $slots;
                $count += 1;
            }
        }

        //make slots of time frame
        foreach ($slotsRange as $key => $hel) {
            $times[$key] = $this->getPartTimeSlots($request, '15', $hel['start_time'], $hel['end_time']);
        }

        $timeSlots = Arr::collapse($times);

        ModelLog::create([
            'user_id' => auth()->user()->id,
            'user_type' => SenderType::User,
            'transaction_id' => null,
            'req_log' => json_encode($request->all()),
            'res_log' => json_encode($timeSlots),
            'log_type' => 'time-slot',
        ]);

        return $timeSlots;
    }

    /**
     * make slots of give time
     *
     * @return array
     */
    public function getPartTimeSlots($request, $interval, $start_time, $end_time)
    {
        $start = new DateTime($start_time);
        $end = new DateTime($end_time);
        $startTime = $start->format('H:i:s');
        $endTime = $end->format('H:i:s');

        $currentDateTime = Carbon::now('Asia/Kolkata')->toTimeString();
        $i = 0;
        $time = [];
        $bookedTimes = DoctorAppointment::where('appointment_date', $request->date)->where('doctor_id', $request->doctor_id)->orderBy('start_time', 'asc')->get();
        // check time slot already selected by other user.

        while (strtotime($startTime) <= strtotime($endTime)) {
            $start = $startTime;
            $end = date('H:i:s', strtotime('+' . $interval . ' minutes', strtotime($startTime)));
            $startTime = date('H:i:s', strtotime('+' . $interval . ' minutes', strtotime($startTime)));
            $i++;
            if (strtotime($startTime) <= strtotime($endTime)) {
                if (($start > $currentDateTime && $request->date == Carbon::now('Asia/Kolkata')->toDateString()) || $request->date > Carbon::now('Asia/Kolkata')) {
                    $time[$i]['slot_start_time'] = $start;
                    $time[$i]['slot_end_time'] = $end;
                    $time[$i]['is_booked'] = 0;
                }
            }
        }

        if (!$bookedTimes->isEmpty()) {
            foreach ($bookedTimes as $booked) {
                foreach ($time as $key => $slot) {
                    $start = $slot['slot_start_time'];
                    $end = $slot['slot_end_time'];
                    $startTime = $slot['slot_start_time'];

                    $a = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::HOUR);
                    $b = Period::make($request->date . " " . $start, $request->date . " " . $end, Precision::HOUR);

                    $overlap = $a->overlapsWith($b);

                    if ($overlap) {
                        $c = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::MINUTE, Boundaries::EXCLUDE_END);
                        $d = Period::make($request->date . " " . $start, $request->date . " " . $end, Precision::MINUTE, Boundaries::EXCLUDE_END);

                        $overlapped = $c->overlapsWith($d);

                        if ($overlapped) {
                            if (strtotime($startTime) <= strtotime($endTime)) {
                                if (($start > $currentDateTime && $request->date == Carbon::now('Asia/Kolkata')->toDateString()) || $request->date > Carbon::now('Asia/Kolkata')) {
                                    $time[$key]['slot_start_time'] = $start;
                                    $time[$key]['slot_end_time'] = $end;
                                    $time[$key]['is_booked'] = 1;
                                    break;
                                }
                            }
                        } else {
                            if (strtotime($startTime) <= strtotime($endTime)) {
                                if (($start > $currentDateTime && $request->date == Carbon::now('Asia/Kolkata')->toDateString()) || $request->date > Carbon::now('Asia/Kolkata')) {
                                    $time[$key]['slot_start_time'] = $start;
                                    $time[$key]['slot_end_time'] = $end;
                                    $time[$key]['is_booked'] = 0;
                                }
                            }
                        }
                    }
                }
            }

            // if($end < $endTime){
            //     while (strtotime($end) <= strtotime($endTime)) {
            //         $start = $end;
            //         $endT = date('H:i:s', strtotime('+' . $interval . ' minutes', strtotime($start)));
            //         $end = date('H:i:s', strtotime('+' . $interval . ' minutes', strtotime($start)));
            //         $i++;
            //         if (strtotime($end) <= strtotime($endTime)) {
            //             if (($start > $currentDateTime && $request->date == Carbon::now('Asia/Kolkata')->toDateString())|| $request->date > Carbon::now('Asia/Kolkata')) {
            //                 $time[$i]['slot_start_time'] = $start;
            //                 $time[$i]['slot_end_time'] = $endT;
            //                 $time[$i]['is_booked'] = 0;
            //             }
            //         }
            //     }
            // }
        }

        return $time;
    }

    /**
     * Book Appointment for patient
     *
     * @return string
     */
    public function createAppointment($request)
    {
        try {

            $data = $request->all();

            $data['user_id'] = Auth::guard('api')->user()->id;
            $data['height'] = encrypt($request->height) ?? encrypt(null);
            $data['weight'] = encrypt($request->weight) ?? encrypt(null);
            $data['age'] = encrypt($request->age) ?? encrypt(null);
            $data['gender'] = $request->gender ?? encrypt(null);

            if (!empty($request->health_issue)) {
                $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, $request->other_issue, 'other')));
            } else if (!empty($request->other_issue)) {
                $data['health_issue']['other'] = encrypt(json_encode($request->other_issue));
            }

            $patientDetail = PatientDetail::updateOrCreate(['user_id' => Auth::guard('api')->user()->id], $data);

            $data['patient_id'] = Auth::guard('api')->user()->id;
            $data['security_code'] = generateSecurityCodeAppointment();

            $doctorAppointment = DoctorAppointment::whereHas('transaction', function ($q) {
                $q->where('payment_status', '!=', TransactionPaymentStatus::Reward);
            })->where('patient_id', auth()->user()->id)->where('payment_status', PaymentStatus::Incomplete);
            $doctorAppointment->delete();

            $bookedTimes = DoctorAppointment::where('appointment_date', $request->appointment_date)
                ->where(function ($query) use ($request) {
                    $query->where('doctor_id', $request->doctor_id)
                        ->orWhere('patient_id', auth()->user()->id);
                })
                ->get();

            if (!empty($bookedTimes)) {
                foreach ($bookedTimes as $booked) {
                    $a = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::HOUR);
                    $b = Period::make($request->appointment_date . " " . $request->start_time, $request->appointment_date . " " . $request->end_time, Precision::HOUR);
                    $overlap = $a->overlapsWith($b);
                    if ($overlap) {
                        $c = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::MINUTE, Boundaries::EXCLUDE_END);
                        $d = Period::make($request->appointment_date . " " . $request->start_time, $request->appointment_date . " " . $request->end_time, Precision::MINUTE, Boundaries::EXCLUDE_END);

                        $overlapped = $c->overlapsWith($d);
                        if ($overlapped) {
                            throw new Exception(config('messages.availability_overlap'), 409);
                        }
                    }
                }
            }
            $data['unique_id'] = generateUniqueAppointmentId();
            $appointment = DoctorAppointment::create($data)->refresh();

            ModelLog::create([
                'user_id' => auth()->user()->id,
                'user_type' => SenderType::User,
                'transaction_id' => null,
                'req_log' => json_encode($request->all()),
                'res_log' => json_encode($appointment),
                'log_type' => 'add-appointment',
            ]);
        } catch (\Exception $e) {
            Log::info("Error => " . $e->getMessage());
            Log::info("File => " . $e->getFile());
            Log::info("Line => " . $e->getLine());
        }

        return fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Book Appointment for patient
     *
     * @return string
     */
    public function updateAppointment($request)
    {
        $data = $request->all();

        $data['security_code'] = generateSecurityCodeAppointment();

        $doctorAppointment = DoctorAppointment::where('unique_id', $request->unique_id)->first();

        $bookedTimes = DoctorAppointment::where('appointment_date', $request->appointment_date)->where('doctor_id', $request->doctor_id)->get();
        if (!empty($bookedTimes)) {
            foreach ($bookedTimes as $booked) {
                $a = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::HOUR);
                $b = Period::make($request->appointment_date . " " . $request->start_time, $request->appointment_date . " " . $request->end_time, Precision::HOUR);
                $overlap = $a->overlapsWith($b);
                if ($overlap) {
                    $c = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::MINUTE, Boundaries::EXCLUDE_END);
                    $d = Period::make($request->appointment_date . " " . $request->start_time, $request->appointment_date . " " . $request->end_time, Precision::MINUTE, Boundaries::EXCLUDE_END);

                    $overlapped = $c->overlapsWith($d);
                    if ($overlapped) {
                        throw new Exception(config('messages.availability_overlap'), 409);
                    }
                }
            }
        }

        $appointment = DoctorAppointment::where('unique_id', $request->unique_id)->first();
        $transaction = Transaction::where('payment_id', $appointment->id)
            ->where(function ($q) {
                $q->where('payment_type', 'Consultation')
                    ->orWhere('payment_type', 1);
            })->first();

        $data['payment_status'] = PaymentStatus::completed;
        $doctorAppointment = $doctorAppointment->update($data);
        if ($transaction->payment_status == TransactionPaymentStatus::Reward) {
            $transaction->update(['payment_status' => TransactionPaymentStatus::RewardComplete]);
        }

        return fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Get Booked appoint detail
     *
     * @return string
     */
    public function getBookingSummery($request)
    {
        $appointment = DoctorAppointment::whereHas('transaction')->where('patient_id', auth()->user()->id)->latest()->first();
        return fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Get appoint list
     *
     * @return string
     */
    public function myBooking($request, $name)
    {
        $appointment = DoctorAppointment::whereHas('transaction')->where('patient_id', auth()->user()->id)->where('payment_status', PaymentStatus::completed);

        if ($name == 'upcoming') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Scheduled);
        } elseif ($name == 'ongoing') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Ongoing);
        } elseif ($name == 'completed') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Completed);
        }

        $appointment = $appointment->orderBy('created_at', 'DESC')->paginate(10);

        return fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Get doctor appoint list
     *
     * @return string
     */
    public function doctorBooking($request, $name)
    {
        $appointment = DoctorAppointment::whereHas('transaction')->where('doctor_id', auth()->user()->id)->where('payment_status', PaymentStatus::completed);

        if ($name == 'upcoming') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Scheduled);
        } elseif ($name == 'ongoing') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Ongoing);
        } elseif ($name == 'completed') {
            $appointment = $appointment->where('appointment_status', AppointmentStatus::Completed);
        }

        $appointment = $appointment->orderBy('created_at', 'DESC')->paginate(10);

        return fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * List of medicine
     *
     * @return array
     */
    public function getList($request)
    {
        $speciality = DoctorAppointment::select(['doctor_appointments.*', 'patient.first_name as patient_first_name', 'doctor.first_name as doctor_first_name'])->with('patient', 'doctor', 'transaction')
            ->join('users as patient', 'doctor_appointments.patient_id', '=', 'patient.id')
            ->join('users as doctor', 'doctor_appointments.doctor_id', '=', 'doctor.id');

        if (!empty($request->start_date)) {
            $start_date = $request->start_date;
            $end_date = $request->end_date;
            $speciality = $speciality->whereHas('transaction', function ($que) use ($start_date, $end_date) {
                $que->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$start_date, $end_date]);
            });
        }

        return DataTables::of($speciality)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->appointment_status == AppointmentStatus::Completed) {
                    $html .= '<a href="#" class="menu-link px-3 changeAppointmentStatus" data-url="' . route('admin.appointment.change-status-active') . '" id="' . $row->id . '">
                        Open
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.appointment.view', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="view_row">
                            View
                        </a>
                    </div>

                </div>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->filterColumn('transaction_id', function ($query, $keyword) {
                return $query->whereHas('transaction', function ($q) use ($keyword) {
                    $q->where('transaction_id', $keyword);;
                });
            })
            ->filterColumn('patient_name', function ($query, $keyword) {
                return $query->whereHas('patient', function ($q) use ($keyword) {
                    $q->searchByName($keyword);;
                });
            })
            ->filterColumn('doctor_name', function ($query, $keyword) {
                return $query->whereHas('doctor', function ($q) use ($keyword) {
                    $q->searchByName($keyword);;
                });
            })
            ->orderColumn('patient_name', function ($query, $order) {
                $query->orderBy('patient_first_name', $order);
            })
            ->orderColumn('doctor_name', function ($query, $order) {
                $query->orderBy('doctor_first_name', $order);
            })
            ->orderColumn('call_time', function ($query, $order) {
                $query->orderBy('start_time', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();

        return DoctorAppointment::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $medicine = DoctorAppointment::findOrFail($id);

        return $medicine;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function view($request, $id)
    {
        $appointment = DoctorAppointment::findOrFail($id);

        return $appointment;
    }

    public function support($request, $id)
    {
        $support = Support::with('user')->where('type', 'appointment')->where('ref_id', $id);

        if (!empty($request->start_date)) {
            $support = $support->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($support)
            ->addIndexColumn()
            ->make(true);
    }


    /** @param $request
     * @param $id
     * @return array
     */
    public function labview($request, $id)
    {
        $appointment = PatientLabTest::with('patientLabTestDescription')->where('appointment_id', $id)->orderBy('id', 'desc')->paginate(10);
        $appointment->appointment_id = $id;

        return $appointment;
    }
    /** @param $request
     * @param $id
     * @return array
     */
    public function prescriptionview($request, $id)
    {
        $appointment = PatientPrescription::with('patientPrescriptionMedicine')->where('appointment_id', $id)->orderBy('id', 'desc')->paginate(10);
        $appointment->appointment_id = $id;
        return $appointment;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function notesview($request, $id)
    {
        $appointment = PatientNote::where('appointment_id', $id)->orderBy('id', 'desc')->paginate(10);
        $appointment->appointment_id = $id;
        return $appointment;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $medicine = DoctorAppointment::findOrFail($id);

        $data = $request->all();
        if ($request->has('image')) {
            $data['image'] = self::imageUpload($request->image, 'molema/medicine');
            self::delete($medicine->getOriginal('image'));
        }
        return $medicine->update($data);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = DoctorAppointment::findOrFail($request->input('id'));
        $user->update(['appointment_status' => AppointmentStatus::Scheduled]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = DoctorAppointment::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = DoctorAppointment::findOrFail($request->input('id'));
        $patient->delete();
    }

    /**
     * update appointment status
     *
     * @return string
     */
    public function updateOngoing($request)
    {
        $doctorAppointment = DoctorAppointment::where('payment_status', PaymentStatus::completed)->findOrFail($request->appointment_id);
        if ($doctorAppointment->consultation_type == ConsultationType::PhysicalConsultation || $doctorAppointment->consultation_type == ConsultationType::HomeConsultation) {
            if ($doctorAppointment->security_code != $request->security_code) {
                throw new Exception(config('messages.verify_security_code_error'), 401);
            }
        }
        $doctorAppointment->update(['appointment_status' => AppointmentStatus::Ongoing]);
    }

    /**
     * update appointment status
     *
     * @return string
     */
    public function updateCompleted($request)
    {
        $doctorAppointment = DoctorAppointment::where('payment_status', PaymentStatus::completed)->findOrFail($request->appointment_id);

        $doctorAppointment->update(['appointment_status' => AppointmentStatus::Completed]);

        //Send Notification to Patient
        $sender = User::findOrFail($doctorAppointment->doctor_id);
        $receiver = User::findOrFail($doctorAppointment->patient_id);
        $extra = [
            'notification_type' => NotificationType::CloseConsultation,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Close Consultation',
            'message' => $sender->name . " has close this consultation.",
            'ref_id' => $doctorAppointment->id,
            'ref_type' => 'DoctorAppointment'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);
    }

    public function appointmentDetail($request)
    {
        $appointment = DoctorAppointment::with('patient', 'transaction')->findOrFail($request->appointment_id);
        $hashKey = substr(hash('sha256', $appointment->unique_id), 0, 16);

        ModelLog::create([
            'user_id' => auth()->user()->id,
            'user_type' => SenderType::User,
            'transaction_id' => isset($appointment->transaction->transaction_id) ? $appointment->transaction->transaction_id : "",
            'req_log' => json_encode($request->all()),
            'res_log' => json_encode($appointment),
            'log_type' => 'appointment-detail',
        ]);

        return fractal($appointment, new AppointmentDetailTransformer(['hashKey' => $hashKey]))->serializeWith(new CustomArraySerializer());
    }

    public function callLogs($request, $id)
    {
        $appointmentCall = AppointmentCall::with(['initiator', 'receiver', 'doctorAppointment'])->where('appointment_id', $id)->orderBy('created_at', 'DESC');

        if (!empty($request->start_date)) {
            $appointmentCall = $appointmentCall->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($appointmentCall)
            ->addIndexColumn()
            ->make(true);
    }

    public function callList($request)
    {
        $appointmentCall = AppointmentCall::where('appointment_id', $request->appointment_id)->orderBy('created_at', 'DESC')->get();
        return fractal($appointmentCall, new CallDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    public function todayAppointment($request)
    {

        $walletTransaction = DoctorWalletTransaction::with('transaction')
            ->whereHas('transaction', function ($query) {
                $query->where('doctor_id', auth()->user()->id);
            });

        $totalEarningWallet = clone ($walletTransaction);
        $totalEarningGet = clone ($walletTransaction);

        $totalEarning = $totalEarningWallet->where('wallet_type', 1)->sum('wallet_amount');

        $totalGet = $totalEarningGet->where('wallet_type', 0)->sum('wallet_amount');

        $wallet = $totalEarning - $totalGet;

        $appointment = DoctorAppointment::whereHas('transaction')->where('doctor_id', auth()->user()->id)
            ->where('appointment_date', date('Y-m-d'))
            ->where(function ($query) {
                $query->where('appointment_status', AppointmentStatus::Scheduled)
                    ->orWhere('appointment_status', AppointmentStatus::Ongoing);
            })
            ->orderBy('updated_at', 'DESC')->limit(10)->get();

        return ['notification_indicator' => auth()->user()->notification_indicator, 'total_earnings' => $totalEarning, 'wallet' => $wallet, 'today_appointment' => fractal($appointment, new DoctorAppointmentTransformer())->serializeWith(new CustomArraySerializer())];
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
