/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!********************************************!*\
  !*** ./resources/assets/js/appointment.js ***!
  \********************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#appointment_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[9, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#appointment_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "patient_name",
        name: "patient_name"
      }, {
        data: "unique_id",
        name: "unique_id"
      }, {
        data: "doctor_name",
        name: "doctor_name"
      }, {
        data: "appointment_date",
        name: "appointment_date"
      }, {
        data: "call_time",
        name: "call_time",
        searchable: false
      }, {
        data: "consultation_type",
        name: "consultation_type"
      }, {
        data: "appointment_status",
        name: "appointment_status"
      }, {
        data: "transaction_id",
        name: "transaction_id",
        orderable: false
      }, {
        data: "created_date",
        name: "created_date",
        searchable: false
      }, {
        data: "action",
        name: "action",
        searchable: false,
        orderable: false
      }],
      columnDefs: [{
        targets: 1,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.patient.avatar_url, "\" alt=\"").concat(row.patient.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.patient.name, "</a>\n                            <span>").concat(row.patient.username, "</span>\n                        </div>");
        }
      }, {
        targets: 3,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.patient.avatar_url, "\" alt=\"").concat(row.patient.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.doctor.name, "</a>\n                            <span>").concat(row.doctor.username, "</span>\n                        </div>");
        }
      }, {
        targets: 5,
        render: function render(data, type, row) {
          return "".concat(row.start_time, " - ").concat(row.end_time);
        }
      }, {
        targets: 6,
        render: function render(data, type, row) {
          if (row.consultation_type == 1) {
            return "<div class=\"badge badge-outline badge-primary\">Virtual Consultation</div>";
          } else if (row.consultation_type == 2) {
            return "<div class=\"badge badge-outline badge-warning\">Physical Consultation</div>";
          } else {
            return "<div class=\"badge badge-outline badge-dark\">Home Consultation</div>";
          }
        }
      }, {
        targets: 7,
        render: function render(data, type, row) {
          if (row.appointment_status == 1) {
            return "<div class=\"badge badge-light-primary\">Scheduled</div>";
          } else if (row.appointment_status == 2) {
            return "<div class=\"badge badge-light-warning\">Confirmed</div>";
          } else {
            return "<div class=\"badge badge-light-dark\">Completed</div>";
          }
        }
      }, {
        targets: 8,
        render: function render(data, type, row) {
          return "".concat(row.transaction.transaction_id);
        }
      }
      // {
      //     targets: 8,
      //     render: function (data, type, row) {
      //         return `${row.transaction.created_date}`;
      //     },
      // }
      ]
    });

    table = datatable.$;
    $('#appointment_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
    var selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    filterButton.addEventListener("click", function () {
      var filterString = "";

      // Get filter values
      selectOptions.forEach(function (item, index) {
        if (item.value && item.value !== "") {
          if (index == 0) {
            datatable.column(6).search(item.value).draw();
          }
          if (index == 1) {
            datatable.column(7).search(item.value).draw();
          }
        } else {
          if (index == 0) {
            datatable.column(6).search("").draw();
          }
          if (index == 1) {
            datatable.column(7).search("").draw();
          }
        }
      });
    });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

    // Reset datatable
    resetButton.addEventListener("click", function () {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
      selectOptions.forEach(function (select) {
        $(select).val("").trigger("change");
      });

      // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
      datatable.search("").columns().search("").draw();
    });
  };
  $('input[name="search"]').on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
    var start_date = picker.startDate.format("YYYY/MM/DD");
    var end_date = picker.endDate.format("YYYY/MM/DD");
    $("#appointment_table").data("dt_params", {
      start_date: start_date,
      end_date: end_date
    });
    datatable.draw();
  });
  $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#appointment_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });
  var initDaterangepicker = function initDaterangepicker() {
    var start = moment().startOf("year");
    var end = moment().endOf("year");
    var input = $('input[name="search"]');
    function cb(start, end) {
      input.val(start.format("YYYY/MM/DD") + " - " + end.format("YYYY/MM/DD"));
      var start_date = start.format("YYYY/MM/DD");
      var end_date = end.format("YYYY/MM/DD");
      $("#appointment_table").data("dt_params", {
        start_date: start_date,
        end_date: end_date
      });
      datatable.draw();
    }
    input.daterangepicker({
      startDate: start,
      endDate: end,
      ranges: {
        Today: [moment(), moment()],
        Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
        "Last 7 Days": [moment().subtract(6, "days"), moment()],
        "Last 30 Days": [moment().subtract(29, "days"), moment()],
        "This Month": [moment().startOf("month"), moment().endOf("month")],
        "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
        "This Year": [moment().startOf("year"), moment().endOf("year")],
        "Last Year": [moment().subtract(1, "year").startOf("year"), moment().subtract(1, "year").endOf("year")]
      }
    }, cb);
    cb(start, end);
  };

  // Public methods
  return {
    init: function init() {
      initDatatable();
      initDaterangepicker();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeAppointmentStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#appointment_table").DataTable().ajax.reload();
    }
  });
});
if ($("#export").length > 0) {
  var url = new URL($('#export').attr('href'));
  $('#consultation_type').on('change', function () {
    url.searchParams.set('consultation_type', this.value);
    $("a#export").attr("href", url);
  });
  $('#appointment_status').on('change', function () {
    url.searchParams.set('appointment_status', this.value);
    $("a#export").attr("href", url);
  });
  $('#datepick').on('change', function () {
    url.searchParams.set('datepick', this.value);
    $("a#export").attr("href", url);
  });
  $('#search').on("input", function () {
    url.searchParams.set('search', this.value);
    $("a#export").attr("href", url);
  });
}
/******/ })()
;