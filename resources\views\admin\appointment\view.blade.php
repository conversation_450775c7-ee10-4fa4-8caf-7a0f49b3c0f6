@extends('admin.layouts.app')
@section('content')	
<div id="kt_app_content" class="app-content flex-column-fluid">

	{{ Breadcrumbs::render('admin.appointment.view') }}
	<!--begin::Content container-->
	<div id="kt_app_content_container" class="app-container container-xxl">
		<!--begin::Navbar-->
		<div class="card mb-5 mb-xxl-8">
			<div class="pb-0" style="padding-left: 10px;">
				
				<!--begin::Navs-->
				<ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="view" class="nav-link text-active-primary ms-0 me-10 py-5 active" href="{{route('admin.appointment.view',$appointment->id)}}">Overview</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="notesview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.notesview',$appointment->id)}}">Notes</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="prescriptionview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.prescriptionview',$appointment->id)}}">Prescriptions</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="labview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.labview',$appointment->id)}}">Lab Tests</a>
					</li>
					<!--end::Nav item-->
				</ul>
				<!--begin::Navs-->
			</div>
		</div>
		<!--end::Navbar-->
		<!--begin::Row-->
		<div class="row g-5 g-xxl-8">
			<!--begin::Col-->
			<div class="col-xl-12">
				<div class="flex-column flex-lg-row-auto mb-10">
					<!--begin::Card-->
					<div class="card mb-5 mb-xl-8">
						<!--begin::Card body-->
						<div class="card-body">
							<!--begin::Details content-->
							<div id="kt_user_view_details" class="collapse show">
								<div class="pb-5 fs-6">
								<!--begin::Details item-->
								@if($appointment->patient)
								<div class="fw-bold mt-5">Patient Name</div>
								<div class="text-gray-600">{{$appointment->patient->name}}</div>
								@endif
								<!--begin::Details item-->
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Appointment Date</div>
								<div class="text-gray-600">{{$appointment->appointment_date}}</div>
								<!--begin::Details item-->
								<!--begin::Details item-->
								@if($appointment->doctor)
								<div class="fw-bold mt-5">Doctor Name</div>
								<div class="text-gray-600">{{$appointment->doctor->name}}</div>
								@endif
								<!--begin::Details item-->
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Start Time</div>
								<div class="text-gray-600">{{$appointment->start_time}}</div>
								<!--begin::Details item-->
								<!--begin::Details item-->
								<div class="fw-bold mt-5">End Time</div>
								<div class="text-gray-600">{{$appointment->end_time}}</div>
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Consultation Type</div>
								<div class="text-gray-600">
									@if($appointment->consultation_type == 1)
										Virtual Consultation
									@elseif($appointment->consultation_type == 2)
										Physical Consultation
									@elseif($appointment->consultation_type == 3)
										Home Consultation
									@endif
								</div>
								<!--begin::Details item-->
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Appointment Status</div>
								<div class="text-gray-600">
									@if($appointment->appointment_status == 1)
									Scheduled
									@elseif($appointment->appointment_status == 2)
									Ongoing
									@elseif($appointment->appointment_status == 3)
									Completed
									@endif
								</div>
								<!--begin::Details item-->
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Payment Status</div>
								<div class="text-gray-600">
									@if($appointment->payment_status == 0)
									Incomplete
									@elseif($appointment->payment_status == 1)
									Completed
									@endif
								</div>
								<!--begin::Details item-->
								<div class="fw-bold mt-5">Created On</div>
								<div class="text-gray-600">{{$appointment->created_date}}</div>
								<!--begin::Details item-->
								</div>
							</div>
							<!--end::Details content-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
					
				</div>
			</div>
			<!--end::Col-->
			
		</div>
		<!--end::Row-->
	</div>
	<!--end::Content container-->
</div>
<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript">
	$(document).ready(function() {
	    var current = window.location.pathname.split("/")[4];
	    if(current == "prescriptionview"){
	    	$('#prescriptionview').addClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "labview"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').addClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "view"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').addClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "notesview"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').addClass('active');
	    }
	    
	});
</script>
@endsection