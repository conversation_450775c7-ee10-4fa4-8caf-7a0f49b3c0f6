<?php

namespace App\Repositories;

use App\Enums\ApproveStatus;
use App\Enums\NotificationType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\Notification;
use App\Jobs\PrescriptionPdf;
use App\Models\DoctorAppointment;
use App\Models\Medicine;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\PatientPrescriptionRepository;
use App\Models\PatientPrescription;
use App\Models\PatientPrescriptionMedicine;
use App\Models\User;
use App\Transformers\MedicineTransformer;
use App\Transformers\PatientPrescriptionTransformer;
use App\Validators\PatientPrescriptionValidator;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

/**
 * Class PatientPrescriptionRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class PatientPrescriptionRepositoryEloquent extends BaseRepository implements PatientPrescriptionRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return PatientPrescription::class;
    }

    /**
     * View Medicine List
     *
     * @return array
     */
    public function medicineList($request){

        $medicine = Medicine::where('approve_status', ApproveStatus::Approved);

        if($request->search){
            $medicine = $medicine->where('name', 'LIKE','%'.$request->search.'%');
        }

        $medicine = $medicine->paginate(10);

        return fractal($medicine, new MedicineTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Add medicine by doctor
     *
     * @return array
     */
    public function addMedicine($request){
        $data = $request->all();
        $data['doctor_id'] = auth()->user()->id;
        $data['approve_status'] = ApproveStatus::Requested;
        return Medicine::create($data);
    }

    /**
     * Add patient prescription by doctor
     *
     * @return array
     */
    public function addPrescription($request){

        $uniqueId = generateUniquePrescriptionId();
        $patientPrescription = PatientPrescription::create(['appointment_id' => $request->prescription[0]['appointment_id'], 'unique_id' => $uniqueId]);

        $pres = [];
        foreach($request->prescription as $key=>$prescription){
            $pres[$key]["patient_prescription_id"] = $patientPrescription->id;
            $pres[$key]["medicine_id"] = $prescription['medicine_id'];
            $pres[$key]["medicine_name"] = $prescription['medicine_name'];
            $pres[$key]["medicine_type"] = $prescription['medicine_type'];
            $pres[$key]["medicine_course"] = $prescription['medicine_course'];
            $pres[$key]["medicine_duration"] = $prescription['medicine_duration'];
            $pres[$key]["medicine_quantity"] = $prescription['medicine_quantity'];
            $pres[$key]["medicine_direction"] = $prescription['medicine_direction'];
        }

        $medicine = PatientPrescriptionMedicine::insert($pres);

        $appointment = DoctorAppointment::findOrFail($request->prescription[0]['appointment_id']);

        //Download PDF file
        dispatch(new PrescriptionPdf($patientPrescription->id));

        //Send Notification to Patient
        $sender = auth()->user();
        $receiver = User::findOrFail($patientPrescription->appointment->patient_id);
        $extra = [
            'notification_type' => NotificationType::PrescriptionAdd,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'doctor_id' => $sender->id,
            'patient_id' => $receiver->id,
            'booking_id' => $appointment->unique_id,
            'title' => 'Prescription',
            'message' => $sender->name." has given you prescription.",
            'ref_id' => $appointment->id,
            'ref_type' => 'PatientPrescription'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return $medicine;
    }

    /**
     * get patient prescriptions given by doctor
     *
     * @return array
     */
    public function getPrescriptions($request){
        $prescription = PatientPrescription::select('id', 'unique_id', 'created_at')->where('appointment_id', $request->appointment_id)->orderBy('created_at', 'DESC')->get();
        return ['prescriptions' => $prescription];
    }

    /**
     * view patient selected prescription
     *
     * @return array
     */
    public function viewPrescription($request, $id){
        $patientPrescription = PatientPrescription::with('patientPrescriptionMedicine')->findOrFail($id);

        $appointmentDetail = DoctorAppointment::with('doctor', 'transaction', 'patient')->whereHas('transaction')->findOrFail($patientPrescription->appointment_id);

        return [
            'doctor_name' => $appointmentDetail->doctor->name,
            'patient_name' => $appointmentDetail->patient->name,
            'booking_id' => $appointmentDetail->unique_id,
            'prescription' => fractal($patientPrescription, new PatientPrescriptionTransformer())->serializeWith(new CustomArraySerializer())
        ];
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
