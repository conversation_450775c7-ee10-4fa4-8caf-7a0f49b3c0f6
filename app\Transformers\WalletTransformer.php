<?php

namespace App\Transformers;

use App\Models\DoctorWalletTransaction;
use App\Models\Transaction;
use League\Fractal\TransformerAbstract;

class WalletTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorWalletTransaction $walletTransaction)
    {
        return [
            "id" => $walletTransaction->id,
            "patient_id" => $walletTransaction->transaction->patient_id,
            "doctor_id" => $walletTransaction->transaction->doctor_id,
            "patient_name" => $walletTransaction->transaction->patient->name,
            "doctor_name" => $walletTransaction->transaction->doctor->name,
            "request_id" => $walletTransaction->transaction->request_id,
            "appointment_id" => $walletTransaction->appointment_id,
            "booking_id" => $walletTransaction->doctorAppointment->unique_id,
            "amount" => $walletTransaction->wallet_amount,
            "currency" => $walletTransaction->transaction->currency,
            "wallet_type" => $walletTransaction->wallet_type,
            "created_at" => $walletTransaction->created_at
        ];
    }

}
