<?php

namespace App\Transformers;

use App\Models\PatientNote;
use League\Fractal\TransformerAbstract;

class PatientNotesTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientNote $patientNote)
    {
        return [
            'appointment_id' => $patientNote->appointment_id,
            'subject' => $patientNote->subject,
            'notes' => $patientNote->notes,
        ];
    }
}
