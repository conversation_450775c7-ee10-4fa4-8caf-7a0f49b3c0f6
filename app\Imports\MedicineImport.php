<?php

namespace App\Imports;

use App\Enums\Status;
use App\Models\Medicine;
use Maatwebsite\Excel\Concerns\ToModel;

class MedicineImport implements ToModel
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new Medicine([
            'name' => $row[0],
            'status' => Status::Active
        ]);
    }
}
