{"/js/app.js": "/js/app.js", "/js/alert.js": "/js/alert.js", "/js/patient.js": "/js/patient.js", "/js/doctor.js": "/js/doctor.js", "/js/speciality.js": "/js/speciality.js", "/js/symptom.js": "/js/symptom.js", "/js/faq.js": "/js/faq.js", "/js/cms.js": "/js/cms.js", "/js/support.js": "/js/support.js", "/js/setting.js": "/js/setting.js", "/js/medicine.js": "/js/medicine.js", "/js/lab-test.js": "/js/lab-test.js", "/js/appointment.js": "/js/appointment.js", "/js/appointmentticket.js": "/js/appointmentticket.js", "/js/calllogs.js": "/js/calllogs.js", "/js/call.js": "/js/call.js", "/js/transaction.js": "/js/transaction.js", "/js/refer.js": "/js/refer.js", "/js/wallet-transaction.js": "/js/wallet-transaction.js", "/js/pharmacytransactions.js": "/js/pharmacytransactions.js", "/js/subscriptiontransactions.js": "/js/subscriptiontransactions.js", "/js/appointmenttransactions.js": "/js/appointmenttransactions.js", "/js/subscription.js": "/js/subscription.js", "/js/subscription-user.js": "/js/subscription-user.js", "/js/video.js": "/js/video.js", "/js/email-template.js": "/js/email-template.js", "/js/notification.js": "/js/notification.js", "/js/log.js": "/js/log.js", "/css/app.css": "/css/app.css"}