<?php

namespace App\Transformers;

use App\Models\CallLog;
use League\Fractal\TransformerAbstract;

class CallLogTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(CallLog $callLog)
    {
        return [
            'id' => $callLog->id,
            'session_id' => $callLog->session_id,
            'appointment_call_id' => $callLog->appointment_call_id,
            'technical_error' => $callLog->technical_error,
            'error_message' => $callLog->error_message,
            'status' => $callLog->status
        ];
    }
}
