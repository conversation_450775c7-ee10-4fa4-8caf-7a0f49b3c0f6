<?php

namespace App\Http\Requests;

use App\Http\CommonTraits\ApiValidationMessageFormat;
use App\Models\Setting;
use Illuminate\Foundation\Http\FormRequest;

class UpdateConsultationTypeRequest extends FormRequest
{
    use ApiValidationMessageFormat;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $virtualPriceCap = Setting::where('slug', 'virtual-consultation-min-price')->first();
        $physicalPriceCap = Setting::where('slug', 'physical-consultation-min-price')->first();
        $homePriceCap = Setting::where('slug', 'home-consultation-min-price')->first();
        return [
            'user_id' => auth()->guard('api')->check() ? '' : 'required',
            'virtual_consultation_price' => 'numeric|min:'.$virtualPriceCap->value,
            'physical_consultation_price' => 'numeric|min:'.$physicalPriceCap->value,
            'home_consultation_price' => 'numeric|min:'.$homePriceCap->value,
        ];
    }
}
