@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($labtest))
    {{ Breadcrumbs::render('admin.lab-test.edit') }}
    @else
    {{ Breadcrumbs::render('admin.lab-test.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($labtest))
                        {{__('Edit Lab Test Detail')}}
                        @else
                        {{__('Add Lab Test Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.lab-test.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="lab-test_details" class="collapse show">
                    @if(!empty($labtest))
                    {{ Form::model($labtest, ['route' => ['admin.lab-test.update', $labtest->id], 'method' =>
                    'post','class'=>'form','id'=>'lab-test-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$labtest->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.lab-test.store',
                    'method'=>'post','class'=>'form','id'=>'lab-test-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Test Name', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('name',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'name','placeholder'=>'Enter lab tests name']) !!}
                                        @if($errors->has('name'))
                                        <div id="name-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('name') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Test code', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('code',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'code','placeholder'=>'Enter lab tests code']) !!}
                                        @if($errors->has('code'))
                                        <div id="code-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('code') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('status', statusArray(), $labtest->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\LabTestRequest', '#lab-test-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\LabTestRequest', '#lab-test-edit-form'); !!}
@endsection
