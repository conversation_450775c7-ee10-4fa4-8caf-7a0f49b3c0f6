<?php

namespace App\Transformers;

use App\Models\Transaction;
use League\Fractal\TransformerAbstract;

class TransactionTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Transaction $transaction)
    {
        return [
            "id" => $transaction->id,
            "patient_id" => $transaction->patient_id,
            "doctor_id" => $transaction->doctor_id,
            "doctor_name" => $transaction->doctor->name,
            "request_id" => $transaction->request_id,
            "payment_id" => $transaction->payment_id,
            "booking_id" => $transaction->doctorAppointment->unique_id ?? '-',
            "order_info" => $transaction->order_info,
            "secure_hash" => $transaction->secure_hash,
            "transaction_id" => $transaction->transaction_id,
            "payment_type" => $transaction->payment_type,
            "amount" => $transaction->amount,
            "currency" => $transaction->currency,
            "payment_status" => (int)$transaction->payment_status,
            'transaction_type' => $transaction->transaction_type,
            'payment_channel' => $transaction->payment_channel,
            "created_at" => $transaction->created_at
        ];
    }
}
