@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($symptom))
    {{ Breadcrumbs::render('admin.symptom.edit') }}
    @else
    {{ Breadcrumbs::render('admin.symptom.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($symptom))
                        {{__('Edit Symptoms Detail')}}
                        @else
                        {{__('Add Symptoms Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.symptom.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="symptom_details" class="collapse show">
                    @if(!empty($symptom))
                    {{ Form::model($symptom, ['route' => ['admin.symptom.update', $symptom->id], 'method' =>
                    'post','class'=>'form','id'=>'symptom-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$symptom->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.symptom.store',
                    'method'=>'post','class'=>'form','id'=>'symptom-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Image', null, ['class' => 'col-lg-4 col-form-label fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="image-input image-input-outline" data-kt-image-input="true"
                                    style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url({{ !empty($symptom->image_url) ?$symptom->image_url: asset('assets/media/avatars/blank.png') }})"></div>
                                    <label
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        title="Change avatar">
                                        <i class="bi bi-pencil-fill fs-7"></i>
                                        {!! Form::file('image',['accept'=>'.png, .jpg, .jpeg']) !!}
                                        @if($errors->has('image'))
                                        <div id="image-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('image') }}
                                        </div>
                                        @endif
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        title="Cancel avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        title="Remove avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                </div>
                                <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Symptom Name En', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('symptoms_name_en', !empty($symptom) ? $symptom->getTranslation('symptoms_name', 'en') : null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'symptoms_name_en','placeholder'=>'Enter symptoms english name']) !!}
                                        @if($errors->has('symptoms_name_en'))
                                        <div id="symptoms_name_en-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('symptoms_name_en') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Symptom Name Es', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('symptoms_name_es',!empty($symptom) ? $symptom->getTranslation('symptoms_name', 'es') : null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'symptoms_name_es','placeholder'=>'Enter symptoms spanish name']) !!}
                                        @if($errors->has('symptoms_name_es'))
                                        <div id="symptoms_name_es-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('symptoms_name_es') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('status', statusArray(), $symptom->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('type', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('is_history', historyArray(), $symptom->is_history ??
                                        null,['id'=>'is_history',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\SymptomRequest', '#symptom-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\SymptomRequest', '#symptom-edit-form'); !!}
@endsection
