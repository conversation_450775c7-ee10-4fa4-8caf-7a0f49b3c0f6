<?php

namespace App\Models;

use App\Enums\TransactionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorSubscription extends Model
{
    use HasFactory;

    protected  $fillable = ['user_id', 'subscription_id', 'transaction_id', 'subscription_start_date', 'subscription_end_date'];

    protected $appends = [
        'created_date'
    ];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }


    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function transaction()
    {
        return $this->hasOne(Transaction::class, 'payment_id', 'id');
    }

}
