<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('subscriptions')->truncate();

        DB::table('subscriptions')->insert([
            [
                'name' => 'Free Trial',
                'slug' => 'free_trial',
                'description' => '30 days free trail.',
                'validity' => 30,
                'price' => 00,
                'status' => 1,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'name' => 'One Month Subscription',
                'slug' => 'one_month_subscription',
                'description' => 'Subscription for 30 days.',
                'validity' => 30,
                'price' => 200,
                'status' => 1,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'name' => 'Three Month Subscription',
                'slug' => 'three_month_subscription',
                'description' => 'Subscription for 90 days.',
                'validity' => 90,
                'price' => 500,
                'status' => 1,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'name' => 'Six Month Subscription',
                'slug' => 'six_month_subscription',
                'description' => 'Subscription for 180 days.',
                'validity' => 180,
                'price' => 1000,
                'status' => 1,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ], [
                'name' => 'One Year Subscription',
                'slug' => 'one_year_subscription',
                'description' => 'Subscription for 365 days.',
                'validity' => 365,
                'price' => 1900,
                'status' => 1,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            ]
        ]);
    }
}
