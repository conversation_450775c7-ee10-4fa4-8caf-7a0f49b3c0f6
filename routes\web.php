<?php

use App\Entities\Transaction;
use App\Models\Symptom;
use App\Enums\ConsultationType;
use App\Enums\TransactionPaymentStatus;
use App\Enums\TransactionType;
use App\Models\PatientPrescription;
use App\Models\PatientPrescriptionMedicine;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Route;
use GuzzleHttp\Client;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group whichq2
| contains the "web" middleware group. Now create something great!
|
*/
use App\Models\DoctorSubscription;
use App\Models\Subscription;
use App\Models\Video;
use Illuminate\Support\Facades\Session;

Route::any('jailconnect-webhook', 'Web\PaymentController@jailWebhook');

Route::get('command', function () {
    Artisan::call('config:cache');
    Artisan::call('config:clear');
    Artisan::call('cache:clear');
    Artisan::call('optimize:clear');
    echo "command run";
});

Route::get('test', function () {
    // $jsonData = '{
    //     "en": "<p style=\"font-size: 16px; color: #666;\">Hi [[USERNAME]],</p>\n        <p style=\"font-size: 16px; color: #666;\">The appointment has been successfully booked. Below are your appointment details:</p>\n        <p style=\"font-size: 16px; color: #666;\">\n            <b>Appointment ID: </b> [[APPOINTMENT_ID]] <br/> \n            <b>Doctor Name: </b> [[DOCTOR_NAME]] <br/> \n            <b>Patient Name: </b> [[PATIENT_NAME]] <br/> \n            <b>Date: </b> [[APPOINTMENT_DATE]] <br/> \n            <b>Time: </b> [[START_TIME]] - [[END_TIME]] <br/>\n            <b>Consultation Type: </b> [[CONSULTATION_TYPE]] <br/> \n            <b>Status: </b> [[STATUS]] <br/> \n        </p>\n        <p style=\"font-size: 16px; color: #666;\">Regards,</p>\n        <p style=\"font-size: 16px; color: #666;\">[[PRODUCT_NAME]] Team.</p>",
    //     "es": "<p style=\"font-size: 16px; color: #666;\">Hola [[USERNAME]],</p>\n        <p style=\"font-size: 16px; color: #666;\">La cita se ha reservado correctamente. A continuaci\\u00f3n se muestran los detalles de su cita:</p>\n        <p style=\"font-size: 16px; color: #666;\">\n            <b>ID de cita: </b> [[APPOINTMENT_ID]] <br/> \n            <b>Nombre del médico: </b> [[DOCTOR_NAME]] <br/> \n            <b>Nombre del paciente: </b> [[PATIENT_NAME]] <br/> \n            <b>Fecha: </b> [[APPOINTMENT_DATE]] <br/> \n            <b>Tiempo: </b> [[START_TIME]] - [[END_TIME]] <br/>\n            <b>Tipo de consulta: </b> [[CONSULTATION_TYPE]] <br/> \n            <b>Estado: </b> [[STATUS]] <br/> \n        </p>\n        <p style=\"font-size: 16px; color: #666;\">Saludos,</p>\n        <p style=\"font-size: 16px; color: #666;\">[[PRODUCT_NAME]] Equipo.</p>"
    //   }';
    //   dd(json_decode($jsonData));
    $templateArray = [
        'en' => '<p style="font-size: 16px; color: #666;">Hi [[USERNAME]],</p>
        <p style="font-size: 16px; color: #666;">You have new appointment for patient: [[PATIENT_NAME]]. Below are your apointment details:</p>
        <p style="font-size: 16px; color: #666;">
            <b>Appointment ID: </b> [[APPOINTMENT_ID]] <br/>
            <b>Patient Name: </b> [[PATIENT_NAME]] <br/>
            <b>Date: </b> [[APPOINTMENT_DATE]] <br/>
            <b>Time: </b> [[START_TIME]] - [[END_TIME]] <br/>
            <b>Consultation Type: </b> [[CONSULTATION_TYPE]] <br/>
            <b>Status: </b> [[STATUS]] <br/>
        </p>
        <p style="font-size: 16px; color: #666;">Regards, <br/>[[PRODUCT_NAME]] Team.</p>',
        'es' => '<p style="font-size: 16px; color: #666;">Hola [[USERNAME]],</p>
        <p style="font-size: 16px; color: #666;">Tiene una nueva cita para el paciente: [[PATIENT_NAME]]. A continuación se muestran los detalles de su cita:</p>
        <p style="font-size: 16px; color: #666;">
            <b>ID de cita: </b> [[APPOINTMENT_ID]] <br/>
            <b>Nombre del paciente: </b> [[PATIENT_NAME]] <br/>
            <b>Fecha: </b> [[APPOINTMENT_DATE]] <br/>
            <b>Tiempo: </b> [[START_TIME]] - [[END_TIME]] <br/>
            <b>Tipo de consulta: </b> [[CONSULTATION_TYPE]] <br/>
            <b>Estado: </b> [[STATUS]] <br/>
        </p>
        <p style="font-size: 16px; color: #666;">Saludos, <br/>[[PRODUCT_NAME]] Equipo.</p>'
    ];

    echo "<pre>";
    dd(json_encode($templateArray));
    exit;
});

Route::match(['post', 'get'], '/ecoBankWebhook', 'Api\EcoBankController@ecoBankWebhook')->name('ecoBankWebhook');
Route::get('/privacy-policy', 'Admin\CmsPagesController@viewPrivacyPolicy')->name('privacy-policy');
Route::get('/terms-and-conditions', 'Admin\CmsPagesController@viewTermsAndCondition')->name('terms-and-conditions');
Route::get('/about-us', 'Admin\CmsPagesController@viewAboutUs')->name('about-us');
Route::get('/delete-account-instruction', 'Admin\CmsPagesController@viewDeleteAccountInstruction')->name('delete-account-instruction');
Route::group(['middleware' => ['auth:admin', 'prevent-back-history']], function () {
    Route::get('admin/dashboard', function () {
        return view('admin.dashboard.index');
    })->middleware(['auth'])->name('dashboard');

    Route::group(['namespace' => 'Admin', 'prefix' => 'admin'], function () {

        Route::get('/my-profile', 'AdminController@show')->name('admin.my-profile');
        Route::post('/my-profile', 'AdminController@update')->name('admin.update.profile');
        Route::post('/update-password', 'AdminController@changePassword')->name('admin.update.password');
        Route::post('/check-password', 'AdminController@checkPassword')->name('admin.check.password');

        // Patient Route
        Route::group(['prefix' => 'patient'], function () {
            Route::controller('PatientController')->group(function () {
                Route::get('/', 'index')->name('admin.patient.index');
                Route::any('/get-list', 'getList')->name('admin.patient.get-list');
                Route::get('/add', 'create')->name('admin.patient.create');
                Route::post('/add', 'store')->name('admin.patient.store');
                Route::get('/{id}/edit', 'edit')->name('admin.patient.edit');
                Route::get('/{id}/view', 'view')->name('admin.patient.view');
                Route::post('/{id}/edit', 'update')->name('admin.patient.update');
                Route::get('/delete', 'destroy')->name('admin.patient.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.patient.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.patient.change-status-deactive');
                Route::get('/export', 'export')->name('admin.patient.export')->withoutMiddleware('prevent-back-history');
            });
        });

        // Doctor Route
        Route::group(['prefix' => 'doctor'], function () {
            Route::controller('DoctorController')->group(function () {
                Route::get('/', 'index')->name('admin.doctor.index');
                Route::any('/get-list', 'getList')->name('admin.doctor.get-list');
                Route::get('/add', 'create')->name('admin.doctor.create');
                Route::post('/add', 'store')->name('admin.doctor.store');
                Route::get('/{id}/edit', 'edit')->name('admin.doctor.edit');
                Route::get('/{id}/view', 'view')->name('admin.doctor.view');
                Route::post('/{id}/edit', 'update')->name('admin.doctor.update');
                Route::get('/delete', 'destroy')->name('admin.doctor.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.doctor.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.doctor.change-status-deactive');
                Route::get('/account-verify', 'accountVerify')->name('admin.doctor.account-verify');
                Route::get('/account-verify-doctor/{id}', 'accountVerifyDoctor')->name('admin.doctor.account-verify-doctor');
                Route::get('/doctor-invitation', 'doctorInvitation')->name('admin.doctor.invitation');
                Route::get('/export', 'export')->name('admin.doctor.export')->withoutMiddleware('prevent-back-history');
                Route::match(['get', 'post'], '/edit-bank-details/{id}', 'bankedit')->name('admin.bank_details.edit');
            });
        });

        // Doctor Invitaion  Route
        Route::group(['prefix' => 'doctor-invitation'], function () {
            Route::controller('DoctorInvitationController')->group(function () {
                Route::get('/', 'index')->name('admin.doctor-invitation.index');
                Route::post('/import', 'import')->name('admin.doctor-invitation.import');
                Route::any('/get-list', 'getList')->name('admin.doctor-invitation.get-list');
                Route::get('/add', 'create')->name('admin.doctor-invitation.create');
                Route::post('/add', 'store')->name('admin.doctor-invitation.store');
                Route::get('/{id}/edit', 'edit')->name('admin.doctor-invitation.edit');
                Route::post('/{id}/edit', 'update')->name('admin.doctor-invitation.update');
                Route::get('/delete', 'destroy')->name('admin.doctor-invitation.destroy');
                Route::get('/changeStatusApproved', 'changeStatusApproved')->name('admin.doctor-invitation.change-status-approve');
                Route::get('/changeStatusDeclined', 'changeStatusDeclined')->name('admin.doctor-invitation.change-status-decline');
            });
        });


        // Speciality Route
        Route::group(['prefix' => 'speciality'], function () {
            Route::controller('SpecialityController')->group(function () {
                Route::get('/', 'index')->name('admin.speciality.index');
                Route::any('/get-list', 'getList')->name('admin.speciality.get-list');
                Route::get('/add', 'create')->name('admin.speciality.create');
                Route::post('/add', 'store')->name('admin.speciality.store');
                Route::get('/{id}/edit', 'edit')->name('admin.speciality.edit');
                Route::post('/{id}/edit', 'update')->name('admin.speciality.update');
                Route::get('/delete', 'destroy')->name('admin.speciality.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.speciality.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.speciality.change-status-deactive');
            });
        });

        // Symptoms Route
        Route::group(['prefix' => 'symptom'], function () {
            Route::controller('SymptomController')->group(function () {
                Route::get('/', 'index')->name('admin.symptom.index');
                Route::any('/get-list', 'getList')->name('admin.symptom.get-list');
                Route::get('/add', 'create')->name('admin.symptom.create');
                Route::post('/add', 'store')->name('admin.symptom.store');
                Route::get('/{id}/edit', 'edit')->name('admin.symptom.edit');
                Route::post('/{id}/edit', 'update')->name('admin.symptom.update');
                Route::get('/delete', 'destroy')->name('admin.symptom.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.symptom.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.symptom.change-status-deactive');
            });
        });

        // Medicine Route
        Route::group(['prefix' => 'medicine'], function () {
            Route::controller('MedicineController')->group(function () {
                Route::get('/', 'index')->name('admin.medicine.index');
                Route::post('/import', 'import')->name('admin.medicine.import');
                Route::any('/get-list', 'getList')->name('admin.medicine.get-list');
                Route::get('/add', 'create')->name('admin.medicine.create');
                Route::post('/add', 'store')->name('admin.medicine.store');
                Route::get('/{id}/edit', 'edit')->name('admin.medicine.edit');
                Route::post('/{id}/edit', 'update')->name('admin.medicine.update');
                Route::get('/delete', 'destroy')->name('admin.medicine.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.medicine.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.medicine.change-status-deactive');
                Route::get('/export', 'export')->name('admin.medicine.export')->withoutMiddleware('prevent-back-history');
                Route::get('/change-approve-status', 'changeApproveStatus')->name('admin.medicine.change-approve-status');
            });
        });

        // lab-test Route
        Route::group(['prefix' => 'lab-test'], function () {
            Route::controller('LabTestController')->group(function () {
                Route::get('/', 'index')->name('admin.lab-test.index');
                Route::post('/import', 'import')->name('admin.lab-test.import');
                Route::any('/get-list', 'getList')->name('admin.lab-test.get-list');
                Route::get('/add', 'create')->name('admin.lab-test.create');
                Route::post('/add', 'store')->name('admin.lab-test.store');
                Route::get('/{id}/edit', 'edit')->name('admin.lab-test.edit');
                Route::post('/{id}/edit', 'update')->name('admin.lab-test.update');
                Route::get('/delete', 'destroy')->name('admin.lab-test.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.lab-test.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.lab-test.change-status-deactive');
                Route::get('/export', 'export')->name('admin.lab-test.export')->withoutMiddleware('prevent-back-history');
                Route::get('/change-approve-status', 'changeApproveStatus')->name('admin.lab-test.change-approve-status');
            });
        });

        // appointment
        Route::group(['prefix' => 'appointment'], function () {
            Route::controller('DoctorAppointmentController')->group(function () {
                Route::get('/', 'index')->name('admin.appointment.index');
                Route::any('/get-list', 'getList')->name('admin.appointment.get-list');
                Route::get('/add', 'create')->name('admin.appointment.create');
                Route::post('/add', 'store')->name('admin.appointment.store');
                Route::get('/{id}/edit', 'edit')->name('admin.appointment.edit');
                Route::post('/{id}/edit', 'update')->name('admin.appointment.update');
                Route::get('/{id}/view', 'view')->name('admin.appointment.view');
                Route::get('/{id}/supportview', 'supportview')->name('admin.appointment.supportview');
                Route::get('/{id}/calllogsview', 'calllogsview')->name('admin.appointment.calllogsview');
                Route::get('/{id}/labview', 'labview')->name('admin.appointment.labview');
                Route::get('/{id}/prescriptionview', 'prescriptionview')->name('admin.appointment.prescriptionview');
                Route::get('/{id}/notesview', 'notesview')->name('admin.appointment.notesview');
                Route::get('/delete', 'destroy')->name('admin.appointment.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.appointment.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.appointment.change-status-deactive');
                Route::get('/export', 'export')->name('admin.appointment.export')->withoutMiddleware('prevent-back-history');
            });
        });

        // call
        Route::group(['prefix' => 'call'], function () {
            Route::controller('AppointmentCallController')->group(function () {
                Route::get('/', 'index')->name('admin.call.index');
                Route::any('/get-list', 'getList')->name('admin.call.get-list');
                Route::get('/add', 'create')->name('admin.call.create');
                Route::post('/add', 'store')->name('admin.call.store');
                Route::get('/{id}/edit', 'edit')->name('admin.call.edit');
                Route::post('/{id}/edit', 'update')->name('admin.call.update');
                Route::get('/delete', 'destroy')->name('admin.call.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.call.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.call.change-status-deactive');
                Route::get('/export', 'export')->name('admin.call.export')->withoutMiddleware('prevent-back-history');
            });
        });

        //transaction
        Route::group(['prefix' => 'transactions'], function () {
            Route::controller('TransactionController')->group(function () {
                Route::get('/', 'index')->name('admin.transactions.index');
                Route::get('/count-amount', 'countAmount')->name('admin.transaction.count');
                Route::get('/appointment-transaction', 'appointmentTransaction')->name('admin.transactions.appointment-transaction-index');
                Route::get('/pharmacy-transaction', 'pharmacyTransaction')->name('admin.transactions.pharmacy-transaction-index');
                Route::get('/subscription-transaction', 'subscriptionTransaction')->name('admin.transactions.subscription-transaction-index');
                Route::get('/transaction', 'transactionIndex')->name('admin.transactions.transaction');
                Route::any('/get-list', 'getList')->name('admin.transaction.get-list');
                Route::any('/appointment-transaction-get-list', 'appointmentTransactionGetList')->name('admin.transaction.appointment-transaction-get-list');
                Route::get('/pharmacy-transaction-get-list', 'pharmacyTransactionGetList')->name('admin.transactions.pharmacy-transaction-get-list');
                Route::get('/subscription-transaction-get-list', 'subscriptionTransactionGetList')->name('admin.transactions.subscription-transaction-get-list');
                Route::get('/add', 'create')->name('admin.transaction.create');
                Route::post('/add', 'store')->name('admin.transaction.store');
                Route::get('/{id}/edit', 'edit')->name('admin.transaction.edit');
                Route::post('/{id}/edit', 'update')->name('admin.transaction.update');
                Route::get('/delete', 'destroy')->name('admin.transaction.destroy');
                Route::get('/changeStatusReward', 'changeStatusReward')->name('admin.transactions.change-status-reward');
                Route::get('/export', 'export')->name('admin.transactions.export')->withoutMiddleware('prevent-back-history');
                Route::post('/import-reward-file', 'importRewardFile')->name('admin.transaction.import-reward');
            });
        });

        //wallet-transaction
        Route::group(['prefix' => 'wallet-transactions'], function () {
            Route::controller('WalletTransactionController')->group(function () {
                Route::get('/', 'index')->name('admin.wallet-transaction.index');
                Route::get('/count-amount', 'countAmount')->name('admin.wallet-transaction.count');
                Route::any('/get-list', 'getList')->name('admin.wallet-transaction.get-list');
                Route::get('/add', 'create')->name('admin.wallet-transaction.create');
                Route::post('/add', 'store')->name('admin.wallet-transaction.store');
                Route::get('/{id}/edit', 'edit')->name('admin.wallet-transaction.edit');
                Route::post('/{id}/edit', 'update')->name('admin.wallet-transaction.update');
                Route::get('/delete', 'destroy')->name('admin.wallet-transaction.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.wallet-transaction.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.wallet-transaction.change-status-deactive');
                Route::get('/export', 'export')->name('admin.wallet-transaction.export')->withoutMiddleware('prevent-back-history');
            });
        });

        //Subscription
        Route::group(['prefix' => 'subscription'], function () {
            Route::controller('SubscriptionController')->group(function () {
                Route::get('/', 'index')->name('admin.subscription.index');
                Route::any('/get-list', 'getList')->name('admin.subscription.get-list');
                Route::get('/add', 'create')->name('admin.subscription.create');
                Route::post('/add', 'store')->name('admin.subscription.store');
                Route::get('/{id}/edit', 'edit')->name('admin.subscription.edit');
                Route::post('/{id}/edit', 'update')->name('admin.subscription.update');
                Route::get('/{id}/view', 'view')->name('admin.subscription.view');
                Route::any('/{id}/user-subscription-get-list', 'userSubscriptionGetList')->name('admin.subscription-user.get-list');
                Route::get('/delete', 'destroy')->name('admin.subscription.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.subscription.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.subscription.change-status-deactive');
            });
        });

        // faq
        Route::group(['prefix' => 'faq'], function () {
            Route::controller('FaqController')->group(function () {
                Route::get('/', 'index')->name('admin.faq.index');
                Route::any('/get-list', 'getList')->name('admin.faq.get-list');
                Route::get('/add', 'create')->name('admin.faq.create');
                Route::post('/add', 'store')->name('admin.faq.store');
                Route::get('/{id}/edit', 'edit')->name('admin.faq.edit');
                Route::post('/{id}/edit', 'update')->name('admin.faq.update');
                Route::get('/delete', 'destroy')->name('admin.faq.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.faq.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.faq.change-status-deactive');
                Route::get('/export', 'export')->name('admin.faq.export')->withoutMiddleware('prevent-back-history');
            });
        });

        //CMS Pages
        Route::group(['prefix' => 'cms'], function () {
            Route::controller('CmsPagesController')->group(function () {
                Route::get('/', 'index')->name('admin.cms.index');
                Route::get('/get-list', 'getList')->name('admin.cms.get-list');
                Route::get('/add', 'create')->name('admin.cms.create');
                Route::post('/add', 'store')->name('admin.cms.store');
                Route::get('/{id}/view', 'show')->name('admin.cms.view');
                Route::get('/{id}/edit', 'edit')->name('admin.cms.edit');
                Route::post('/{id}/edit', 'update')->name('admin.cms.update');
                Route::get('/delete', 'destroy')->name('admin.cms.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.cms.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.cms.change-status-deactive');
            });
        });

        //support module
        Route::group(['prefix' => 'support'], function () {
            Route::controller('SupportController')->group(function () {
                Route::get('/', 'index')->name('admin.support.index');
                Route::get('/get-list', 'getList')->name('admin.support.get-list');
                Route::get('/changeStatusActive/{id}', 'changeStatusActive')->name('admin.support.change-status-active');
                Route::get('/changeStatusDeactive/{id}', 'changeStatusDeactive')->name('admin.support.change-status-deactive');
                Route::get('/chat/{id}', 'chat')->name('admin.support.chat');
                Route::post('/chat/{id}/create', 'createChat')->name('admin.support.chat.create');
            });
        });

        //email template module
        Route::group(['prefix' => 'email-template'], function () {
            Route::controller('EmailTemplateController')->group(function () {
                Route::get('/', 'index')->name('admin.email-template.index');
                Route::get('/get-list', 'getList')->name('admin.email-template.get-list');
                Route::get('/changeStatusActive/{id}', 'changeStatusActive')->name('admin.email-template.change-status-active');
                Route::get('/changeStatusDeactive/{id}', 'changeStatusDeactive')->name('admin.email-template.change-status-deactive');
            });
        });

        // settings module
        Route::group(['prefix' => 'setting'], function () {
            Route::controller('SettingController')->group(function () {
                Route::get('/', 'index')->name('admin.setting.index');
                Route::any('/get-list', 'getList')->name('admin.setting.get-list');
                Route::get('/add', 'create')->name('admin.setting.create');
                Route::post('/add', 'store')->name('admin.setting.store');
                Route::get('/{id}/edit', 'edit')->name('admin.setting.edit');
                Route::post('/{id}/edit', 'update')->name('admin.setting.update');
                Route::get('/delete', 'destroy')->name('admin.setting.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.setting.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.setting.change-status-deactive');
                Route::get('/export', 'export')->name('admin.setting.export')->withoutMiddleware('prevent-back-history');
            });
        });

        //Bank Detail module
        Route::group(['prefix' => 'bank-detail'], function () {
            Route::controller('BankDetailController')->group(function () {
                Route::get('/', 'bankDetail')->name('admin.bank-detail.index');
                Route::post('/update', 'updateAdminBankDetail')->name('admin.bank-detail.update');
            });
        });

        // Video module
        Route::group(['prefix' => 'video'], function () {
            Route::controller('VideoController')->group(function () {
                Route::get('/', 'index')->name('admin.video.index');
                Route::any('/get-list', 'getList')->name('admin.video.get-list');
                Route::get('/add', 'create')->name('admin.video.create');
                Route::post('/add', 'store')->name('admin.video.store');
                Route::get('/{id}/edit', 'edit')->name('admin.video.edit');
                Route::post('/{id}/edit', 'update')->name('admin.video.update');
                Route::get('/delete', 'destroy')->name('admin.video.destroy');
                Route::get('/changeStatusActive', 'changeStatusActive')->name('admin.video.change-status-active');
                Route::get('/changeStatusDeactive', 'changeStatusDeactive')->name('admin.video.change-status-deactive');
                Route::get('/export', 'export')->name('admin.video.export')->withoutMiddleware('prevent-back-history');
            });
        });
        // Doctor Payout module
        Route::group(['prefix' => 'payout'], function () {
            Route::controller('PayoutController')->group(function () {
                Route::get('/', 'index')->name('admin.payout.index');
                Route::any('/get-list', 'getList')->name('admin.payout.get-list');
                Route::any('/release/{id}', 'releasePayment')->name('admin.payout.release');
            });
        });

        // Notification
        Route::group(['prefix' => 'notification'], function () {
            Route::controller('NotificationController')->group(function () {
                Route::get('/', 'index')->name('admin.notification.index');
                Route::any('/get-list', 'getList')->name('admin.notification.get-list');
            });
        });

        // Logs
        Route::group(['prefix' => 'log'], function () {
            Route::controller('LogController')->group(function () {
                Route::get('/', 'index')->name('admin.log.index');
                Route::any('/get-list', 'getList')->name('admin.log.get-list');
            });
        });
    });
});

Route::get('/', function () {
    $symptoms = Symptom::where('status', 1)->get();
    $video = Video::where('slug', 'website-home')->orderBy('priority', 'ASC')->get();
    return view('web.index', compact('symptoms', 'video'));
})->name('home');



Route::group(['prefix' => '{locale}', 'where' => ['locale' => '[a-zA-Z]{2}'], 'middleware' => 'web_localization'], function () {
    Route::get('/', function () {
        $symptoms = Symptom::where('status', 1)->get();
        $video = Video::where('slug', 'website-home')->orderBy('priority', 'ASC')->get();
        return view('web.index', compact('symptoms', 'video'));
    })->name('home');

    Route::group(['middleware' => 'guest:web'], function () {
        Route::group(['namespace' => 'Web'], function () {
            Route::get('login', 'Auth\AuthenticatedSessionController@create')
                ->name('web.login');

            Route::post('login', 'Auth\AuthenticatedSessionController@store');
        });
    });

    Route::group(['middleware' => ['auth:web', 'prevent-back-history']], function () {

        Route::get('web/subscription', function () {
            $subscriptions = Subscription::where('status', 1)->where('slug', '!=', 'free_trial')->get();
            $doctorSubscription = DoctorSubscription::with('doctor:id,first_name,last_name', 'subscription')->where('user_id', auth()->user()->id)->orderBy('created_at', 'DESC')->paginate(5);
            return view('web.subscription', compact('subscriptions', 'doctorSubscription'));
        })->name('web.subscription');

        Route::get('web/final-purchase/{id}', 'Web\PaymentController@purchase')->name('admin.subscription.purchase');
    });
});
Route::any('web/ecobank/fail-response', 'Web\PaymentController@ecobankFailedResponse')->name('ecobank-fail-response');
Route::any('web/ecobank/success-response', 'Web\PaymentController@ecobankSuccessResponse')->name('ecobank-success-response');

Route::post('web/ecobank/webhook', 'Web\PaymentController@ecobankWebhook')->name('ecobank-webhook');
Route::post('web/ecobank/smswebhook', 'Web\PaymentController@smswebhook')->name('smswebhook');

Route::get('web/ecobank-redirection', function () {
    echo "Completed";
    exit;
});

require __DIR__ . '/auth.php';
