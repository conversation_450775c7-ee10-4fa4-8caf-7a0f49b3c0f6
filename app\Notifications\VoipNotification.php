<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Apn\ApnChannel;
use NotificationChannels\Apn\ApnMessage;

class VoipNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $message, $extra, $sender_id, $sender_name, $uuid;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($message, $uuid, $sender_id, $sender_name, $extra)
    {
        $this->message = $message;
        $this->uuid = $uuid;
        $this->sender_id = $sender_id;
        $this->sender_name = $sender_name;
        $this->extra = $extra;
    }

    public function via($notifiable)
    {
        return [ApnChannel::class];
    }

    public function toApn($notifiable)
    {
        return ApnMessage::create()
            ->badge(1)
            ->title('molema')
            ->body($this->message)
            ->uuid($this->uuid)
            ->incomingCallerId($this->sender_id)
            ->incomingCallerName($this->sender_name)
            ->setCustom($this->extra);
    }
    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
