<?php

namespace App\Notifications;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Apn\ApnChannel;
use NotificationChannels\Apn\ApnMessage;

class VoipNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $message, $extra, $sender_id, $sender_name, $uuid;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($message, $uuid, $sender_id, $sender_name, $extra)
    {
        $this->message = $message;
        $this->uuid = $uuid;
        $this->sender_id = $sender_id;
        $this->sender_name = $sender_name;
        $this->extra = $extra;
    }

    public function via($notifiable)
    {
        return [ApnChannel::class];
    }

    public function toApn($notifiable)
    {
        Log::info("|| Creating VoIP APN message");
        Log::info("|| Notifiable VoIP Token: " . ($notifiable->voip_token ? 'Available' : 'Not Available'));
        Log::info("|| Message: " . $this->message);
        Log::info("|| UUID: " . $this->uuid);
        Log::info("|| Sender ID: " . $this->sender_id);
        Log::info("|| Sender Name: " . $this->sender_name);
        Log::info("|| Extra Data: " . json_encode($this->extra));

        try {
            $apnMessage = ApnMessage::create()
                ->badge(1)
                ->title('molema')
                ->body($this->message)
                ->uuid($this->uuid)
                ->incomingCallerId($this->sender_id)
                ->incomingCallerName($this->sender_name)
                ->setCustom($this->extra);

            Log::info("|| VoIP APN message created successfully");
            return $apnMessage;
        } catch (Exception $e) {
            Log::error("|| Failed to create VoIP APN message");
            Log::error("|| Error: " . $e->getMessage());
            Log::error("|| File: " . $e->getFile());
            Log::error("|| Line: " . $e->getLine());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error("|| VoIP Notification Job Failed");
        Log::error("|| Error: " . $exception->getMessage());
        Log::error("|| File: " . $exception->getFile());
        Log::error("|| Line: " . $exception->getLine());
        Log::error("|| Message: " . $this->message);
        Log::error("|| UUID: " . $this->uuid);
        Log::error("|| Sender ID: " . $this->sender_id);
        Log::error("|| Sender Name: " . $this->sender_name);
        Log::error("|| Extra Data: " . json_encode($this->extra));
    }
    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
