<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\SubscriptionRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    /**
     * @var SupportRepositoryEloquent
     */
    protected $subscriptionRepositoryEloquent;

    /**
     * @var ApiFormattedResponse
     */
    protected $apiResponse;

    /**
     * FaqController constructor.
     * @param SupportRepositoryEloquent $subscriptionRepositoryEloquent
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(SubscriptionRepositoryEloquent $subscriptionRepositoryEloquent, ApiFormattedResponse $apiResponse)
    {
        $this->subscriptionRepositoryEloquent = $subscriptionRepositoryEloquent;
        $this->apiResponse = $apiResponse;
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        try {
            $this->subscriptionRepositoryEloquent->store($request);
            return $this->apiResponse->respondWithMessage(trans('messages.support_created'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function viewSubscription(Request $request)
    {
        try {
            $subscription = $this->subscriptionRepositoryEloquent->viewSubscription($request);
            return $this->apiResponse->respondWithMessageAndPayload($subscription, trans('messages.subscription_view_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

}
