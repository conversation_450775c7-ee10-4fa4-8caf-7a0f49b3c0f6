<?php

namespace App\Transformers;

use App\Models\Speciality;
use League\Fractal\TransformerAbstract;

class SpecialityTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Speciality $speciality)
    {
        return [
            'id' => $speciality->id,
            'image' => $speciality->image_url,
            'specialist' => $speciality->specialist,
            'symptoms' => $speciality->symptom->symptoms_name
        ];
    }
}
