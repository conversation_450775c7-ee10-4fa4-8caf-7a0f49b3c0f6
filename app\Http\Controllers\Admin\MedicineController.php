<?php

namespace App\Http\Controllers\Admin;

use App\Exports\MedicineExport;
use App\Http\Requests\MedicineRequest;
use App\Repositories\MedicineRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class MedicineController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "medicine";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var MedicineRepositoryEloquent|string
     */
    public $medicineRepository = "";

    public function __construct(MedicineRepositoryEloquent $medicineRepository)
    {
        parent::__construct();
        $this->medicineRepository = $medicineRepository;
    }

    /**
     * Return List of Medicine
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Medicine']);
        return view('admin.medicine.index');
    }

    /**
     * Import medicines
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function import(Request $request)
    {
        try {
            $this->medicineRepository->import($request);
            $request->session()->flash('success', config("messages.file_import_success"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return medicine list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->medicineRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * create a new medicines
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Medicine']);
        return view('admin.medicine.medicine');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(MedicineRequest $request)
    {
        try {
            $medicine = $this->medicineRepository->store($request);
            $request->session()->flash('success', config("messages.medicine_created"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit medicine list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Medicine']);
        try {
            $medicine = $this->medicineRepository->edit($request, $id);
            return view('admin.medicine.medicine', compact('medicine'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * Update medicine list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(MedicineRequest $request, $id)
    {
        try {
            $medicine = $this->medicineRepository->update($request, $id);
            $request->session()->flash('success', config("messages.medicine_updated"));
            return redirect()->route('admin.medicine.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->medicineRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->medicineRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeApproveStatus(Request $request)
    {
        try {
            $this->medicineRepository->changeApproveStatus($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->medicineRepository->destroy($request);
            $request->session()->flash('success', config("messages.medicine_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.medicine.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "medicine_" . time() . ".xlsx";
        return Excel::download(new MedicineExport($request->all()), $fileName);
    }

}
