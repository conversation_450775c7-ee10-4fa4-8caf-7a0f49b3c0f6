<?php

namespace App\Repositories;

use App\Enums\ApproveStatus;
use App\Enums\NotificationType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\LabTestPdf;
use App\Jobs\Notification;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\PatientLabTestRepository;
use App\Models\PatientLabTest;
use App\Models\DoctorAppointment;
use App\Models\LabTest;
use App\Models\PatientLabTestDescription;
use App\Models\PatientLabTestReport;
use App\Models\User;
use App\Transformers\LabTestTransformer;
use App\Transformers\PatientLabTestTransformer;
use App\Validators\PatientLabTestValidator;
use Barryvdh\DomPDF\Facade\Pdf;

/**
 * Class PatientLabTestRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class PatientLabTestRepositoryEloquent extends BaseRepository implements PatientLabTestRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return PatientLabTest::class;
    }

    /**
     * Lab test listing
     *
     * @return array
     */
    public function labTestList($request)
    {
        $labtest = LabTest::query();

        if($request->search){
            $labtest = $labtest->where('name', 'LIKE','%'.$request->search.'%');
        }

        $labtest = $labtest->paginate(10);

        return fractal($labtest, new LabTestTransformer())->serializeWith(new PaginationArraySerializer());
    }

     /**
     * Add medicine by doctor
     *
     * @return array
     */
    public function addLaboratory($request){
        $data = $request->all();
        $data['doctor_id'] = auth()->user()->id;
        $data['approve_status'] = ApproveStatus::Requested;
        return LabTest::create($data);
    }

    /**
     * Add patient labtest by doctor
     *
     * @return array
     */
    public function addPatientLabTest($request)
    {

        $uniqueId = generateUniquePrescriptionId();
        $patientLabTest = PatientLabTest::create(['appointment_id' => $request->labs[0]['appointment_id'], 'unique_id' => $uniqueId]);

        $labs = [];
        foreach($request->labs as $key=>$labTest){
            $labs[$key]["patient_lab_test_id"] = $patientLabTest->id;
            $labs[$key]["lab_test_id"] = $labTest['lab_test_id'];
            $labs[$key]["lab_test_name"] = $labTest['lab_test_name'];
        }

        $labTests = PatientLabTestDescription::insert($labs);

        $appointment = DoctorAppointment::findOrFail($request->labs[0]['appointment_id']);

        //Download PDF file
        dispatch(new LabTestPdf($patientLabTest->id));

        //Send Notification to Patient
        $sender = auth()->user();
        $receiver = User::findOrFail($patientLabTest->appointment->patient_id);
        $extra = [
            'notification_type' => NotificationType::LabTestAdd,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'doctor_id' => $sender->id,
            'patient_id' => $receiver->id,
            'booking_id' => $appointment->unique_id,
            'title' => 'Lab Test',
            'message' => $sender->name." has given you lab test.",
            'ref_id' => $appointment->id,
            'ref_type' => 'PatientLabTest'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return $labTests;
    }

    /**
     * get patient labtest given by doctor
     *
     * @return array
     */
    public function getPatientLabtests($request)
    {
        $labTests = PatientLabTest::select('id', 'unique_id', 'created_at')->where('appointment_id', $request->appointment_id)->orderBy('created_at', 'DESC')->get();
        return ['labTests' => $labTests];
    }

    /**
     * view patient selected labtest
     *
     * @return array
     */
    public function viewPatientLabTest($request, $id)
    {

        $labtest = PatientLabTest::with('patientLabTestDescription')->findOrFail($id);

        $appointmentDetail = DoctorAppointment::with('doctor', 'patient', 'transaction')->whereHas('transaction')->findOrFail($labtest->first()->appointment_id);

        return [
            'doctor_name' => $appointmentDetail->doctor->name,
            'patient_name' => $appointmentDetail->patient->name,
            'booking_id' => $appointmentDetail->unique_id,
            'labtest' => fractal($labtest, new PatientLabTestTransformer())->serializeWith(new CustomArraySerializer())
        ];
    }

    /**
     * upload reports to view doctor
     *
     * @return array
     */
    public function uploadReports($request)
    {
        if ($request->has('report')) {
            $input['report'] = self::fileUpload($request->report, 'molema/user-reports');
        }
        $input['patient_id'] = auth()->user()->id;
        $input['patient_lab_test_id'] = $request->patient_lab_test_id;
        $patientLabTest = PatientLabTest::findOrFail($request->patient_lab_test_id);
        $doctorAppointment = DoctorAppointment::findOrFail($patientLabTest->appointment_id);
        $input['doctor_id'] = $doctorAppointment->doctor_id;

        $report = PatientLabTestReport::create($input);

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($doctorAppointment->patient_id);
        $extra = [
            'notification_type' => NotificationType::LabTestReportUpload,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Lab Test Report',
            'message' => $sender->name." has submitted you lab test report.",
            'ref_id' => $patientLabTest->id,
            'ref_type' => 'PatientLabTestReport'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return $report;
    }


    /**
     * upload reports to view doctor
     *
     * @return array
     */
    public function viewReports($request)
    {
       $patientReport = PatientLabTestReport::where('patient_lab_test_id', $request->patient_lab_test_id)->get();
       return $patientReport;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
