@extends('admin.includes.auth')
@section('content')
<div class="d-flex flex-column flex-root" id="kt_app_root">
    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
            <div class="d-flex flex-center flex-lg-start flex-column">
                <!--begin::Logo-->
                <a href="#" class="mb-7">
                    <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px"
                        src="{{ asset('assets/media/auth/logo.png') }}" alt="" />
                </a>
                <!--end::Logo-->
                <!--begin::Title-->
                <h2 class="text-blue fw-normal font-blue m-5">Tu salud está en tus manos</h2>
                <!--end::Title-->
            </div>
        </div>
        <div class="d-flex flex-center w-lg-50 p-10">
            <div class="card rounded-3 w-md-550px">
                <div class="card-body d-flex flex-column p-10 p-lg-20 pb-lg-10">
                    <div class="d-flex flex-center flex-column-fluid pb-15 pb-lg-20">
                        {!! Form::open(['class'=>'form w-100 admin-forgot-password-form','id'=>'admin-forgot-password-form
                        kt_password_reset_form']) !!}
                        <div class="text-center mb-10">
                            <h1 class="text-dark fw-bolder mb-3">Forgot Password ?</h1>
                            <div class="text-gray-500 fw-semibold fs-6">Enter your email to reset your password.</div>
                        </div>
                        @if($errors->has('email'))
                        <div id="email-error" class="error invalid-feedback" style="display: block;">
                            {{ $errors->first('email') }}
                        </div>
                        @endif
                        <div class="fv-row mb-8">
                            {!! Form::email('email', old('email'), $attributes =
                            ['class'=>'form-control','autocomplete'=>'off','placeholder'=>'Email']) !!}
                        </div>
                        <div class="d-flex flex-wrap justify-content-center pb-lg-0">
                            {!! Form::submit('Request',['id'=>'kt_password_reset_submit','class'=>'btn btn-blue me-4']) !!}
                            &nbsp;&nbsp;
                            <a href="{{ route('login') }}" id="kt_login_forgot_cancel" class="btn btn-light">Cancel</a>
                        </div>
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\AdminForgotPasswordRequest', '.admin-forgot-password-form'); !!}
@endsection
