stages:
  - get_env
  - deploy_prod

# Global variables to be used across stages
# Test
variables:
  BUCKET_NAME_PROD: "molema-prod"  
  AWS_DEFAULT_REGION_PROD: "af-south-1"   
  CI_COMMIT_REF_NAME: "production" 

# Stage 1: Fetch the .env file from S3
get_env:
  stage: get_env
  environment:
    name: $CI_COMMIT_REF_SLUG
  variables:
    TARGET_PATH: ".env"
  image:
    name: amazon/aws-cli:2.4.9
    entrypoint: [""]
  script:
    # Fetch .env from S3 bucket
    - echo "Fetching .env from S3 bucket..."
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID_PROD
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY_PROD
    - aws configure set region af-south-1
    - aws s3 ls s3://$BUCKET_NAME_PROD/prod-env/ --region af-south-1
    #chnage $TARGET_PATH to .env
    - aws s3 cp s3://$BUCKET_NAME_PROD/prod-env/.env $TARGET_PATH --region af-south-1
    - echo ".env file downloaded to $TARGET_PATH"
  
  artifacts:
    paths:
      - $TARGET_PATH
      #-.env
    expire_in: 1 hour
  only:
    - production

# Stage 2: Deploy the application
deploy_prod:
  stage: deploy_prod
  environment:
    name: "${CI_COMMIT_REF_NAME}"
    #url: https://molemasalud.com
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    
    - ssh -o StrictHostKeyChecking=no ubuntu@************** "./script.sh"
    - scp -o StrictHostKeyChecking=no .env ubuntu@**************:/var/www/html/cms-prod/molema_health_cms/.env

    
    #- cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
    #- ls -la .env
    #- scp -o StrictHostKeyChecking=no .env ubuntu@**************:/tmp/.env
    #- ssh -o StrictHostKeyChecking=no ubuntu@************** "mv /tmp/.env /var/www/html/cms-prod/molema_health_cms/.env && chmod 640 /var/www/html/cms-prod/molema_health_cms/.env && ./script.sh"
  only:
    - production
