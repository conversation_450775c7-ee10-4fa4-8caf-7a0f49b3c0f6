<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'sender_id',
        'receiver_id',
        'sender_type',
        'receiver_type',
        'title',
        'description',
        'ref_id',
        'ref_type',
        'notification_type',
        'read'
    ];

    protected $appends = ['created_date'];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * The doctor_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id', 'id')->withTrashed();
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id', 'id')->withTrashed();
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function doctorRating(){
        return $this->belongsTo(DoctorRating::class, 'ref_id', 'id');
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function doctorAppointment(){
        return $this->belongsTo(DoctorAppointment::class, 'ref_id', 'id');
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function support(){
        return $this->belongsTo(Support::class, 'ref_id', 'id');
    }
}
