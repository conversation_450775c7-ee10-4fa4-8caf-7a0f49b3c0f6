{"version": 3, "sources": ["node_modules/jquery-validation/dist/jquery.validate.js", "node_modules/php-date-formatter/js/php-date-formatter.js", "resources/assets/js/jsvalidation.js", "es-build/helpers.js", "resources/assets/js/timezones.js", "resources/assets/js/validations.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5nDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC7oBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC/XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChlEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9dA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jsvalidation.js", "sourcesContent": ["/*!\n * jQuery Validation Plugin v1.19.5\n *\n * https://jqueryvalidation.org/\n *\n * Copyright (c) 2022 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n */\n(function( factory ) {\n\tif ( typeof define === \"function\" && define.amd ) {\n\t\tdefine( [\"jquery\"], factory );\n\t} else if (typeof module === \"object\" && module.exports) {\n\t\tmodule.exports = factory( require( \"jquery\" ) );\n\t} else {\n\t\tfactory( jQuery );\n\t}\n}(function( $ ) {\n\n$.extend( $.fn, {\n\n\t// https://jqueryvalidation.org/validate/\n\tvalidate: function( options ) {\n\n\t\t// If nothing is selected, return nothing; can't chain anyway\n\t\tif ( !this.length ) {\n\t\t\tif ( options && options.debug && window.console ) {\n\t\t\t\tconsole.warn( \"Nothing selected, can't validate, returning nothing.\" );\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// Check if a validator for this form was already created\n\t\tvar validator = $.data( this[ 0 ], \"validator\" );\n\t\tif ( validator ) {\n\t\t\treturn validator;\n\t\t}\n\n\t\t// Add novalidate tag if HTML5.\n\t\tthis.attr( \"novalidate\", \"novalidate\" );\n\n\t\tvalidator = new $.validator( options, this[ 0 ] );\n\t\t$.data( this[ 0 ], \"validator\", validator );\n\n\t\tif ( validator.settings.onsubmit ) {\n\n\t\t\tthis.on( \"click.validate\", \":submit\", function( event ) {\n\n\t\t\t\t// Track the used submit button to properly handle scripted\n\t\t\t\t// submits later.\n\t\t\t\tvalidator.submitButton = event.currentTarget;\n\n\t\t\t\t// Allow suppressing validation by adding a cancel class to the submit button\n\t\t\t\tif ( $( this ).hasClass( \"cancel\" ) ) {\n\t\t\t\t\tvalidator.cancelSubmit = true;\n\t\t\t\t}\n\n\t\t\t\t// Allow suppressing validation by adding the html5 formnovalidate attribute to the submit button\n\t\t\t\tif ( $( this ).attr( \"formnovalidate\" ) !== undefined ) {\n\t\t\t\t\tvalidator.cancelSubmit = true;\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Validate the form on submit\n\t\t\tthis.on( \"submit.validate\", function( event ) {\n\t\t\t\tif ( validator.settings.debug ) {\n\n\t\t\t\t\t// Prevent form submit to be able to see console output\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\n\t\t\t\tfunction handle() {\n\t\t\t\t\tvar hidden, result;\n\n\t\t\t\t\t// Insert a hidden input as a replacement for the missing submit button\n\t\t\t\t\t// The hidden input is inserted in two cases:\n\t\t\t\t\t//   - A user defined a `submitHandler`\n\t\t\t\t\t//   - There was a pending request due to `remote` method and `stopRequest()`\n\t\t\t\t\t//     was called to submit the form in case it's valid\n\t\t\t\t\tif ( validator.submitButton && ( validator.settings.submitHandler || validator.formSubmitted ) ) {\n\t\t\t\t\t\thidden = $( \"<input type='hidden'/>\" )\n\t\t\t\t\t\t\t.attr( \"name\", validator.submitButton.name )\n\t\t\t\t\t\t\t.val( $( validator.submitButton ).val() )\n\t\t\t\t\t\t\t.appendTo( validator.currentForm );\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( validator.settings.submitHandler && !validator.settings.debug ) {\n\t\t\t\t\t\tresult = validator.settings.submitHandler.call( validator, validator.currentForm, event );\n\t\t\t\t\t\tif ( hidden ) {\n\n\t\t\t\t\t\t\t// And clean up afterwards; thanks to no-block-scope, hidden can be referenced\n\t\t\t\t\t\t\thidden.remove();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ( result !== undefined ) {\n\t\t\t\t\t\t\treturn result;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\t// Prevent submit for invalid forms or custom submit handlers\n\t\t\t\tif ( validator.cancelSubmit ) {\n\t\t\t\t\tvalidator.cancelSubmit = false;\n\t\t\t\t\treturn handle();\n\t\t\t\t}\n\t\t\t\tif ( validator.form() ) {\n\t\t\t\t\tif ( validator.pendingRequest ) {\n\t\t\t\t\t\tvalidator.formSubmitted = true;\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn handle();\n\t\t\t\t} else {\n\t\t\t\t\tvalidator.focusInvalid();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\treturn validator;\n\t},\n\n\t// https://jqueryvalidation.org/valid/\n\tvalid: function() {\n\t\tvar valid, validator, errorList;\n\n\t\tif ( $( this[ 0 ] ).is( \"form\" ) ) {\n\t\t\tvalid = this.validate().form();\n\t\t} else {\n\t\t\terrorList = [];\n\t\t\tvalid = true;\n\t\t\tvalidator = $( this[ 0 ].form ).validate();\n\t\t\tthis.each( function() {\n\t\t\t\tvalid = validator.element( this ) && valid;\n\t\t\t\tif ( !valid ) {\n\t\t\t\t\terrorList = errorList.concat( validator.errorList );\n\t\t\t\t}\n\t\t\t} );\n\t\t\tvalidator.errorList = errorList;\n\t\t}\n\t\treturn valid;\n\t},\n\n\t// https://jqueryvalidation.org/rules/\n\trules: function( command, argument ) {\n\t\tvar element = this[ 0 ],\n\t\t\tisContentEditable = typeof this.attr( \"contenteditable\" ) !== \"undefined\" && this.attr( \"contenteditable\" ) !== \"false\",\n\t\t\tsettings, staticRules, existingRules, data, param, filtered;\n\n\t\t// If nothing is selected, return empty object; can't chain anyway\n\t\tif ( element == null ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( !element.form && isContentEditable ) {\n\t\t\telement.form = this.closest( \"form\" )[ 0 ];\n\t\t\telement.name = this.attr( \"name\" );\n\t\t}\n\n\t\tif ( element.form == null ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( command ) {\n\t\t\tsettings = $.data( element.form, \"validator\" ).settings;\n\t\t\tstaticRules = settings.rules;\n\t\t\texistingRules = $.validator.staticRules( element );\n\t\t\tswitch ( command ) {\n\t\t\tcase \"add\":\n\t\t\t\t$.extend( existingRules, $.validator.normalizeRule( argument ) );\n\n\t\t\t\t// Remove messages from rules, but allow them to be set separately\n\t\t\t\tdelete existingRules.messages;\n\t\t\t\tstaticRules[ element.name ] = existingRules;\n\t\t\t\tif ( argument.messages ) {\n\t\t\t\t\tsettings.messages[ element.name ] = $.extend( settings.messages[ element.name ], argument.messages );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"remove\":\n\t\t\t\tif ( !argument ) {\n\t\t\t\t\tdelete staticRules[ element.name ];\n\t\t\t\t\treturn existingRules;\n\t\t\t\t}\n\t\t\t\tfiltered = {};\n\t\t\t\t$.each( argument.split( /\\s/ ), function( index, method ) {\n\t\t\t\t\tfiltered[ method ] = existingRules[ method ];\n\t\t\t\t\tdelete existingRules[ method ];\n\t\t\t\t} );\n\t\t\t\treturn filtered;\n\t\t\t}\n\t\t}\n\n\t\tdata = $.validator.normalizeRules(\n\t\t$.extend(\n\t\t\t{},\n\t\t\t$.validator.classRules( element ),\n\t\t\t$.validator.attributeRules( element ),\n\t\t\t$.validator.dataRules( element ),\n\t\t\t$.validator.staticRules( element )\n\t\t), element );\n\n\t\t// Make sure required is at front\n\t\tif ( data.required ) {\n\t\t\tparam = data.required;\n\t\t\tdelete data.required;\n\t\t\tdata = $.extend( { required: param }, data );\n\t\t}\n\n\t\t// Make sure remote is at back\n\t\tif ( data.remote ) {\n\t\t\tparam = data.remote;\n\t\t\tdelete data.remote;\n\t\t\tdata = $.extend( data, { remote: param } );\n\t\t}\n\n\t\treturn data;\n\t}\n} );\n\n// JQuery trim is deprecated, provide a trim method based on String.prototype.trim\nvar trim = function( str ) {\n\n\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/trim#Polyfill\n\treturn str.replace( /^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, \"\" );\n};\n\n// Custom selectors\n$.extend( $.expr.pseudos || $.expr[ \":\" ], {\t\t// '|| $.expr[ \":\" ]' here enables backwards compatibility to jQuery 1.7. Can be removed when dropping jQ 1.7.x support\n\n\t// https://jqueryvalidation.org/blank-selector/\n\tblank: function( a ) {\n\t\treturn !trim( \"\" + $( a ).val() );\n\t},\n\n\t// https://jqueryvalidation.org/filled-selector/\n\tfilled: function( a ) {\n\t\tvar val = $( a ).val();\n\t\treturn val !== null && !!trim( \"\" + val );\n\t},\n\n\t// https://jqueryvalidation.org/unchecked-selector/\n\tunchecked: function( a ) {\n\t\treturn !$( a ).prop( \"checked\" );\n\t}\n} );\n\n// Constructor for validator\n$.validator = function( options, form ) {\n\tthis.settings = $.extend( true, {}, $.validator.defaults, options );\n\tthis.currentForm = form;\n\tthis.init();\n};\n\n// https://jqueryvalidation.org/jQuery.validator.format/\n$.validator.format = function( source, params ) {\n\tif ( arguments.length === 1 ) {\n\t\treturn function() {\n\t\t\tvar args = $.makeArray( arguments );\n\t\t\targs.unshift( source );\n\t\t\treturn $.validator.format.apply( this, args );\n\t\t};\n\t}\n\tif ( params === undefined ) {\n\t\treturn source;\n\t}\n\tif ( arguments.length > 2 && params.constructor !== Array  ) {\n\t\tparams = $.makeArray( arguments ).slice( 1 );\n\t}\n\tif ( params.constructor !== Array ) {\n\t\tparams = [ params ];\n\t}\n\t$.each( params, function( i, n ) {\n\t\tsource = source.replace( new RegExp( \"\\\\{\" + i + \"\\\\}\", \"g\" ), function() {\n\t\t\treturn n;\n\t\t} );\n\t} );\n\treturn source;\n};\n\n$.extend( $.validator, {\n\n\tdefaults: {\n\t\tmessages: {},\n\t\tgroups: {},\n\t\trules: {},\n\t\terrorClass: \"error\",\n\t\tpendingClass: \"pending\",\n\t\tvalidClass: \"valid\",\n\t\terrorElement: \"label\",\n\t\tfocusCleanup: false,\n\t\tfocusInvalid: true,\n\t\terrorContainer: $( [] ),\n\t\terrorLabelContainer: $( [] ),\n\t\tonsubmit: true,\n\t\tignore: \":hidden\",\n\t\tignoreTitle: false,\n\t\tonfocusin: function( element ) {\n\t\t\tthis.lastActive = element;\n\n\t\t\t// Hide error label and remove error class on focus if enabled\n\t\t\tif ( this.settings.focusCleanup ) {\n\t\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, element, this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t\tthis.hideThese( this.errorsFor( element ) );\n\t\t\t}\n\t\t},\n\t\tonfocusout: function( element ) {\n\t\t\tif ( !this.checkable( element ) && ( element.name in this.submitted || !this.optional( element ) ) ) {\n\t\t\t\tthis.element( element );\n\t\t\t}\n\t\t},\n\t\tonkeyup: function( element, event ) {\n\n\t\t\t// Avoid revalidate the field when pressing one of the following keys\n\t\t\t// Shift       => 16\n\t\t\t// Ctrl        => 17\n\t\t\t// Alt         => 18\n\t\t\t// Caps lock   => 20\n\t\t\t// End         => 35\n\t\t\t// Home        => 36\n\t\t\t// Left arrow  => 37\n\t\t\t// Up arrow    => 38\n\t\t\t// Right arrow => 39\n\t\t\t// Down arrow  => 40\n\t\t\t// Insert      => 45\n\t\t\t// Num lock    => 144\n\t\t\t// AltGr key   => 225\n\t\t\tvar excludedKeys = [\n\t\t\t\t16, 17, 18, 20, 35, 36, 37,\n\t\t\t\t38, 39, 40, 45, 144, 225\n\t\t\t];\n\n\t\t\tif ( event.which === 9 && this.elementValue( element ) === \"\" || $.inArray( event.keyCode, excludedKeys ) !== -1 ) {\n\t\t\t\treturn;\n\t\t\t} else if ( element.name in this.submitted || element.name in this.invalid ) {\n\t\t\t\tthis.element( element );\n\t\t\t}\n\t\t},\n\t\tonclick: function( element ) {\n\n\t\t\t// Click on selects, radiobuttons and checkboxes\n\t\t\tif ( element.name in this.submitted ) {\n\t\t\t\tthis.element( element );\n\n\t\t\t// Or option elements, check parent select in that case\n\t\t\t} else if ( element.parentNode.name in this.submitted ) {\n\t\t\t\tthis.element( element.parentNode );\n\t\t\t}\n\t\t},\n\t\thighlight: function( element, errorClass, validClass ) {\n\t\t\tif ( element.type === \"radio\" ) {\n\t\t\t\tthis.findByName( element.name ).addClass( errorClass ).removeClass( validClass );\n\t\t\t} else {\n\t\t\t\t$( element ).addClass( errorClass ).removeClass( validClass );\n\t\t\t}\n\t\t},\n\t\tunhighlight: function( element, errorClass, validClass ) {\n\t\t\tif ( element.type === \"radio\" ) {\n\t\t\t\tthis.findByName( element.name ).removeClass( errorClass ).addClass( validClass );\n\t\t\t} else {\n\t\t\t\t$( element ).removeClass( errorClass ).addClass( validClass );\n\t\t\t}\n\t\t}\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.setDefaults/\n\tsetDefaults: function( settings ) {\n\t\t$.extend( $.validator.defaults, settings );\n\t},\n\n\tmessages: {\n\t\trequired: \"This field is required.\",\n\t\tremote: \"Please fix this field.\",\n\t\temail: \"Please enter a valid email address.\",\n\t\turl: \"Please enter a valid URL.\",\n\t\tdate: \"Please enter a valid date.\",\n\t\tdateISO: \"Please enter a valid date (ISO).\",\n\t\tnumber: \"Please enter a valid number.\",\n\t\tdigits: \"Please enter only digits.\",\n\t\tequalTo: \"Please enter the same value again.\",\n\t\tmaxlength: $.validator.format( \"Please enter no more than {0} characters.\" ),\n\t\tminlength: $.validator.format( \"Please enter at least {0} characters.\" ),\n\t\trangelength: $.validator.format( \"Please enter a value between {0} and {1} characters long.\" ),\n\t\trange: $.validator.format( \"Please enter a value between {0} and {1}.\" ),\n\t\tmax: $.validator.format( \"Please enter a value less than or equal to {0}.\" ),\n\t\tmin: $.validator.format( \"Please enter a value greater than or equal to {0}.\" ),\n\t\tstep: $.validator.format( \"Please enter a multiple of {0}.\" )\n\t},\n\n\tautoCreateRanges: false,\n\n\tprototype: {\n\n\t\tinit: function() {\n\t\t\tthis.labelContainer = $( this.settings.errorLabelContainer );\n\t\t\tthis.errorContext = this.labelContainer.length && this.labelContainer || $( this.currentForm );\n\t\t\tthis.containers = $( this.settings.errorContainer ).add( this.settings.errorLabelContainer );\n\t\t\tthis.submitted = {};\n\t\t\tthis.valueCache = {};\n\t\t\tthis.pendingRequest = 0;\n\t\t\tthis.pending = {};\n\t\t\tthis.invalid = {};\n\t\t\tthis.reset();\n\n\t\t\tvar currentForm = this.currentForm,\n\t\t\t\tgroups = ( this.groups = {} ),\n\t\t\t\trules;\n\t\t\t$.each( this.settings.groups, function( key, value ) {\n\t\t\t\tif ( typeof value === \"string\" ) {\n\t\t\t\t\tvalue = value.split( /\\s/ );\n\t\t\t\t}\n\t\t\t\t$.each( value, function( index, name ) {\n\t\t\t\t\tgroups[ name ] = key;\n\t\t\t\t} );\n\t\t\t} );\n\t\t\trules = this.settings.rules;\n\t\t\t$.each( rules, function( key, value ) {\n\t\t\t\trules[ key ] = $.validator.normalizeRule( value );\n\t\t\t} );\n\n\t\t\tfunction delegate( event ) {\n\t\t\t\tvar isContentEditable = typeof $( this ).attr( \"contenteditable\" ) !== \"undefined\" && $( this ).attr( \"contenteditable\" ) !== \"false\";\n\n\t\t\t\t// Set form expando on contenteditable\n\t\t\t\tif ( !this.form && isContentEditable ) {\n\t\t\t\t\tthis.form = $( this ).closest( \"form\" )[ 0 ];\n\t\t\t\t\tthis.name = $( this ).attr( \"name\" );\n\t\t\t\t}\n\n\t\t\t\t// Ignore the element if it belongs to another form. This will happen mainly\n\t\t\t\t// when setting the `form` attribute of an input to the id of another form.\n\t\t\t\tif ( currentForm !== this.form ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar validator = $.data( this.form, \"validator\" ),\n\t\t\t\t\teventType = \"on\" + event.type.replace( /^validate/, \"\" ),\n\t\t\t\t\tsettings = validator.settings;\n\t\t\t\tif ( settings[ eventType ] && !$( this ).is( settings.ignore ) ) {\n\t\t\t\t\tsettings[ eventType ].call( validator, this, event );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t$( this.currentForm )\n\t\t\t\t.on( \"focusin.validate focusout.validate keyup.validate\",\n\t\t\t\t\t\":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], \" +\n\t\t\t\t\t\"[type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], \" +\n\t\t\t\t\t\"[type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], \" +\n\t\t\t\t\t\"[type='radio'], [type='checkbox'], [contenteditable], [type='button']\", delegate )\n\n\t\t\t\t// Support: Chrome, oldIE\n\t\t\t\t// \"select\" is provided as event.target when clicking a option\n\t\t\t\t.on( \"click.validate\", \"select, option, [type='radio'], [type='checkbox']\", delegate );\n\n\t\t\tif ( this.settings.invalidHandler ) {\n\t\t\t\t$( this.currentForm ).on( \"invalid-form.validate\", this.settings.invalidHandler );\n\t\t\t}\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.form/\n\t\tform: function() {\n\t\t\tthis.checkForm();\n\t\t\t$.extend( this.submitted, this.errorMap );\n\t\t\tthis.invalid = $.extend( {}, this.errorMap );\n\t\t\tif ( !this.valid() ) {\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\n\t\t\t}\n\t\t\tthis.showErrors();\n\t\t\treturn this.valid();\n\t\t},\n\n\t\tcheckForm: function() {\n\t\t\tthis.prepareForm();\n\t\t\tfor ( var i = 0, elements = ( this.currentElements = this.elements() ); elements[ i ]; i++ ) {\n\t\t\t\tthis.check( elements[ i ] );\n\t\t\t}\n\t\t\treturn this.valid();\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.element/\n\t\telement: function( element ) {\n\t\t\tvar cleanElement = this.clean( element ),\n\t\t\t\tcheckElement = this.validationTargetFor( cleanElement ),\n\t\t\t\tv = this,\n\t\t\t\tresult = true,\n\t\t\t\trs, group;\n\n\t\t\tif ( checkElement === undefined ) {\n\t\t\t\tdelete this.invalid[ cleanElement.name ];\n\t\t\t} else {\n\t\t\t\tthis.prepareElement( checkElement );\n\t\t\t\tthis.currentElements = $( checkElement );\n\n\t\t\t\t// If this element is grouped, then validate all group elements already\n\t\t\t\t// containing a value\n\t\t\t\tgroup = this.groups[ checkElement.name ];\n\t\t\t\tif ( group ) {\n\t\t\t\t\t$.each( this.groups, function( name, testgroup ) {\n\t\t\t\t\t\tif ( testgroup === group && name !== checkElement.name ) {\n\t\t\t\t\t\t\tcleanElement = v.validationTargetFor( v.clean( v.findByName( name ) ) );\n\t\t\t\t\t\t\tif ( cleanElement && cleanElement.name in v.invalid ) {\n\t\t\t\t\t\t\t\tv.currentElements.push( cleanElement );\n\t\t\t\t\t\t\t\tresult = v.check( cleanElement ) && result;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\trs = this.check( checkElement ) !== false;\n\t\t\t\tresult = result && rs;\n\t\t\t\tif ( rs ) {\n\t\t\t\t\tthis.invalid[ checkElement.name ] = false;\n\t\t\t\t} else {\n\t\t\t\t\tthis.invalid[ checkElement.name ] = true;\n\t\t\t\t}\n\n\t\t\t\tif ( !this.numberOfInvalids() ) {\n\n\t\t\t\t\t// Hide error containers on last error\n\t\t\t\t\tthis.toHide = this.toHide.add( this.containers );\n\t\t\t\t}\n\t\t\t\tthis.showErrors();\n\n\t\t\t\t// Add aria-invalid status for screen readers\n\t\t\t\t$( element ).attr( \"aria-invalid\", !rs );\n\t\t\t}\n\n\t\t\treturn result;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.showErrors/\n\t\tshowErrors: function( errors ) {\n\t\t\tif ( errors ) {\n\t\t\t\tvar validator = this;\n\n\t\t\t\t// Add items to error list and map\n\t\t\t\t$.extend( this.errorMap, errors );\n\t\t\t\tthis.errorList = $.map( this.errorMap, function( message, name ) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tmessage: message,\n\t\t\t\t\t\telement: validator.findByName( name )[ 0 ]\n\t\t\t\t\t};\n\t\t\t\t} );\n\n\t\t\t\t// Remove items from success list\n\t\t\t\tthis.successList = $.grep( this.successList, function( element ) {\n\t\t\t\t\treturn !( element.name in errors );\n\t\t\t\t} );\n\t\t\t}\n\t\t\tif ( this.settings.showErrors ) {\n\t\t\t\tthis.settings.showErrors.call( this, this.errorMap, this.errorList );\n\t\t\t} else {\n\t\t\t\tthis.defaultShowErrors();\n\t\t\t}\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.resetForm/\n\t\tresetForm: function() {\n\t\t\tif ( $.fn.resetForm ) {\n\t\t\t\t$( this.currentForm ).resetForm();\n\t\t\t}\n\t\t\tthis.invalid = {};\n\t\t\tthis.submitted = {};\n\t\t\tthis.prepareForm();\n\t\t\tthis.hideErrors();\n\t\t\tvar elements = this.elements()\n\t\t\t\t.removeData( \"previousValue\" )\n\t\t\t\t.removeAttr( \"aria-invalid\" );\n\n\t\t\tthis.resetElements( elements );\n\t\t},\n\n\t\tresetElements: function( elements ) {\n\t\t\tvar i;\n\n\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\tfor ( i = 0; elements[ i ]; i++ ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ],\n\t\t\t\t\t\tthis.settings.errorClass, \"\" );\n\t\t\t\t\tthis.findByName( elements[ i ].name ).removeClass( this.settings.validClass );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\telements\n\t\t\t\t\t.removeClass( this.settings.errorClass )\n\t\t\t\t\t.removeClass( this.settings.validClass );\n\t\t\t}\n\t\t},\n\n\t\tnumberOfInvalids: function() {\n\t\t\treturn this.objectLength( this.invalid );\n\t\t},\n\n\t\tobjectLength: function( obj ) {\n\t\t\t/* jshint unused: false */\n\t\t\tvar count = 0,\n\t\t\t\ti;\n\t\t\tfor ( i in obj ) {\n\n\t\t\t\t// This check allows counting elements with empty error\n\t\t\t\t// message as invalid elements\n\t\t\t\tif ( obj[ i ] !== undefined && obj[ i ] !== null && obj[ i ] !== false ) {\n\t\t\t\t\tcount++;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn count;\n\t\t},\n\n\t\thideErrors: function() {\n\t\t\tthis.hideThese( this.toHide );\n\t\t},\n\n\t\thideThese: function( errors ) {\n\t\t\terrors.not( this.containers ).text( \"\" );\n\t\t\tthis.addWrapper( errors ).hide();\n\t\t},\n\n\t\tvalid: function() {\n\t\t\treturn this.size() === 0;\n\t\t},\n\n\t\tsize: function() {\n\t\t\treturn this.errorList.length;\n\t\t},\n\n\t\tfocusInvalid: function() {\n\t\t\tif ( this.settings.focusInvalid ) {\n\t\t\t\ttry {\n\t\t\t\t\t$( this.findLastActive() || this.errorList.length && this.errorList[ 0 ].element || [] )\n\t\t\t\t\t.filter( \":visible\" )\n\t\t\t\t\t.trigger( \"focus\" )\n\n\t\t\t\t\t// Manually trigger focusin event; without it, focusin handler isn't called, findLastActive won't have anything to find\n\t\t\t\t\t.trigger( \"focusin\" );\n\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t// Ignore IE throwing errors when focusing hidden elements\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tfindLastActive: function() {\n\t\t\tvar lastActive = this.lastActive;\n\t\t\treturn lastActive && $.grep( this.errorList, function( n ) {\n\t\t\t\treturn n.element.name === lastActive.name;\n\t\t\t} ).length === 1 && lastActive;\n\t\t},\n\n\t\telements: function() {\n\t\t\tvar validator = this,\n\t\t\t\trulesCache = {};\n\n\t\t\t// Select all valid inputs inside the form (no submit or reset buttons)\n\t\t\treturn $( this.currentForm )\n\t\t\t.find( \"input, select, textarea, [contenteditable]\" )\n\t\t\t.not( \":submit, :reset, :image, :disabled\" )\n\t\t\t.not( this.settings.ignore )\n\t\t\t.filter( function() {\n\t\t\t\tvar name = this.name || $( this ).attr( \"name\" ); // For contenteditable\n\t\t\t\tvar isContentEditable = typeof $( this ).attr( \"contenteditable\" ) !== \"undefined\" && $( this ).attr( \"contenteditable\" ) !== \"false\";\n\n\t\t\t\tif ( !name && validator.settings.debug && window.console ) {\n\t\t\t\t\tconsole.error( \"%o has no name assigned\", this );\n\t\t\t\t}\n\n\t\t\t\t// Set form expando on contenteditable\n\t\t\t\tif ( isContentEditable ) {\n\t\t\t\t\tthis.form = $( this ).closest( \"form\" )[ 0 ];\n\t\t\t\t\tthis.name = name;\n\t\t\t\t}\n\n\t\t\t\t// Ignore elements that belong to other/nested forms\n\t\t\t\tif ( this.form !== validator.currentForm ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// Select only the first element for each name, and only those with rules specified\n\t\t\t\tif ( name in rulesCache || !validator.objectLength( $( this ).rules() ) ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\trulesCache[ name ] = true;\n\t\t\t\treturn true;\n\t\t\t} );\n\t\t},\n\n\t\tclean: function( selector ) {\n\t\t\treturn $( selector )[ 0 ];\n\t\t},\n\n\t\terrors: function() {\n\t\t\tvar errorClass = this.settings.errorClass.split( \" \" ).join( \".\" );\n\t\t\treturn $( this.settings.errorElement + \".\" + errorClass, this.errorContext );\n\t\t},\n\n\t\tresetInternals: function() {\n\t\t\tthis.successList = [];\n\t\t\tthis.errorList = [];\n\t\t\tthis.errorMap = {};\n\t\t\tthis.toShow = $( [] );\n\t\t\tthis.toHide = $( [] );\n\t\t},\n\n\t\treset: function() {\n\t\t\tthis.resetInternals();\n\t\t\tthis.currentElements = $( [] );\n\t\t},\n\n\t\tprepareForm: function() {\n\t\t\tthis.reset();\n\t\t\tthis.toHide = this.errors().add( this.containers );\n\t\t},\n\n\t\tprepareElement: function( element ) {\n\t\t\tthis.reset();\n\t\t\tthis.toHide = this.errorsFor( element );\n\t\t},\n\n\t\telementValue: function( element ) {\n\t\t\tvar $element = $( element ),\n\t\t\t\ttype = element.type,\n\t\t\t\tisContentEditable = typeof $element.attr( \"contenteditable\" ) !== \"undefined\" && $element.attr( \"contenteditable\" ) !== \"false\",\n\t\t\t\tval, idx;\n\n\t\t\tif ( type === \"radio\" || type === \"checkbox\" ) {\n\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).val();\n\t\t\t} else if ( type === \"number\" && typeof element.validity !== \"undefined\" ) {\n\t\t\t\treturn element.validity.badInput ? \"NaN\" : $element.val();\n\t\t\t}\n\n\t\t\tif ( isContentEditable ) {\n\t\t\t\tval = $element.text();\n\t\t\t} else {\n\t\t\t\tval = $element.val();\n\t\t\t}\n\n\t\t\tif ( type === \"file\" ) {\n\n\t\t\t\t// Modern browser (chrome & safari)\n\t\t\t\tif ( val.substr( 0, 12 ) === \"C:\\\\fakepath\\\\\" ) {\n\t\t\t\t\treturn val.substr( 12 );\n\t\t\t\t}\n\n\t\t\t\t// Legacy browsers\n\t\t\t\t// Unix-based path\n\t\t\t\tidx = val.lastIndexOf( \"/\" );\n\t\t\t\tif ( idx >= 0 ) {\n\t\t\t\t\treturn val.substr( idx + 1 );\n\t\t\t\t}\n\n\t\t\t\t// Windows-based path\n\t\t\t\tidx = val.lastIndexOf( \"\\\\\" );\n\t\t\t\tif ( idx >= 0 ) {\n\t\t\t\t\treturn val.substr( idx + 1 );\n\t\t\t\t}\n\n\t\t\t\t// Just the file name\n\t\t\t\treturn val;\n\t\t\t}\n\n\t\t\tif ( typeof val === \"string\" ) {\n\t\t\t\treturn val.replace( /\\r/g, \"\" );\n\t\t\t}\n\t\t\treturn val;\n\t\t},\n\n\t\tcheck: function( element ) {\n\t\t\telement = this.validationTargetFor( this.clean( element ) );\n\n\t\t\tvar rules = $( element ).rules(),\n\t\t\t\trulesCount = $.map( rules, function( n, i ) {\n\t\t\t\t\treturn i;\n\t\t\t\t} ).length,\n\t\t\t\tdependencyMismatch = false,\n\t\t\t\tval = this.elementValue( element ),\n\t\t\t\tresult, method, rule, normalizer;\n\n\t\t\t// Prioritize the local normalizer defined for this element over the global one\n\t\t\t// if the former exists, otherwise user the global one in case it exists.\n\t\t\tif ( typeof rules.normalizer === \"function\" ) {\n\t\t\t\tnormalizer = rules.normalizer;\n\t\t\t} else if (\ttypeof this.settings.normalizer === \"function\" ) {\n\t\t\t\tnormalizer = this.settings.normalizer;\n\t\t\t}\n\n\t\t\t// If normalizer is defined, then call it to retreive the changed value instead\n\t\t\t// of using the real one.\n\t\t\t// Note that `this` in the normalizer is `element`.\n\t\t\tif ( normalizer ) {\n\t\t\t\tval = normalizer.call( element, val );\n\n\t\t\t\t// Delete the normalizer from rules to avoid treating it as a pre-defined method.\n\t\t\t\tdelete rules.normalizer;\n\t\t\t}\n\n\t\t\tfor ( method in rules ) {\n\t\t\t\trule = { method: method, parameters: rules[ method ] };\n\t\t\t\ttry {\n\t\t\t\t\tresult = $.validator.methods[ method ].call( this, val, element, rule.parameters );\n\n\t\t\t\t\t// If a method indicates that the field is optional and therefore valid,\n\t\t\t\t\t// don't mark it as valid when there are no other rules\n\t\t\t\t\tif ( result === \"dependency-mismatch\" && rulesCount === 1 ) {\n\t\t\t\t\t\tdependencyMismatch = true;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tdependencyMismatch = false;\n\n\t\t\t\t\tif ( result === \"pending\" ) {\n\t\t\t\t\t\tthis.toHide = this.toHide.not( this.errorsFor( element ) );\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( !result ) {\n\t\t\t\t\t\tthis.formatAndAdd( element, rule );\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t} catch ( e ) {\n\t\t\t\t\tif ( this.settings.debug && window.console ) {\n\t\t\t\t\t\tconsole.log( \"Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\", e );\n\t\t\t\t\t}\n\t\t\t\t\tif ( e instanceof TypeError ) {\n\t\t\t\t\t\te.message += \".  Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\";\n\t\t\t\t\t}\n\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( dependencyMismatch ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( this.objectLength( rules ) ) {\n\t\t\t\tthis.successList.push( element );\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\n\t\t// Return the custom message for the given element and validation method\n\t\t// specified in the element's HTML5 data attribute\n\t\t// return the generic message if present and no method specific message is present\n\t\tcustomDataMessage: function( element, method ) {\n\t\t\treturn $( element ).data( \"msg\" + method.charAt( 0 ).toUpperCase() +\n\t\t\t\tmethod.substring( 1 ).toLowerCase() ) || $( element ).data( \"msg\" );\n\t\t},\n\n\t\t// Return the custom message for the given element name and validation method\n\t\tcustomMessage: function( name, method ) {\n\t\t\tvar m = this.settings.messages[ name ];\n\t\t\treturn m && ( m.constructor === String ? m : m[ method ] );\n\t\t},\n\n\t\t// Return the first defined argument, allowing empty strings\n\t\tfindDefined: function() {\n\t\t\tfor ( var i = 0; i < arguments.length; i++ ) {\n\t\t\t\tif ( arguments[ i ] !== undefined ) {\n\t\t\t\t\treturn arguments[ i ];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn undefined;\n\t\t},\n\n\t\t// The second parameter 'rule' used to be a string, and extended to an object literal\n\t\t// of the following form:\n\t\t// rule = {\n\t\t//     method: \"method name\",\n\t\t//     parameters: \"the given method parameters\"\n\t\t// }\n\t\t//\n\t\t// The old behavior still supported, kept to maintain backward compatibility with\n\t\t// old code, and will be removed in the next major release.\n\t\tdefaultMessage: function( element, rule ) {\n\t\t\tif ( typeof rule === \"string\" ) {\n\t\t\t\trule = { method: rule };\n\t\t\t}\n\n\t\t\tvar message = this.findDefined(\n\t\t\t\t\tthis.customMessage( element.name, rule.method ),\n\t\t\t\t\tthis.customDataMessage( element, rule.method ),\n\n\t\t\t\t\t// 'title' is never undefined, so handle empty string as undefined\n\t\t\t\t\t!this.settings.ignoreTitle && element.title || undefined,\n\t\t\t\t\t$.validator.messages[ rule.method ],\n\t\t\t\t\t\"<strong>Warning: No message defined for \" + element.name + \"</strong>\"\n\t\t\t\t),\n\t\t\t\ttheregex = /\\$?\\{(\\d+)\\}/g;\n\t\t\tif ( typeof message === \"function\" ) {\n\t\t\t\tmessage = message.call( this, rule.parameters, element );\n\t\t\t} else if ( theregex.test( message ) ) {\n\t\t\t\tmessage = $.validator.format( message.replace( theregex, \"{$1}\" ), rule.parameters );\n\t\t\t}\n\n\t\t\treturn message;\n\t\t},\n\n\t\tformatAndAdd: function( element, rule ) {\n\t\t\tvar message = this.defaultMessage( element, rule );\n\n\t\t\tthis.errorList.push( {\n\t\t\t\tmessage: message,\n\t\t\t\telement: element,\n\t\t\t\tmethod: rule.method\n\t\t\t} );\n\n\t\t\tthis.errorMap[ element.name ] = message;\n\t\t\tthis.submitted[ element.name ] = message;\n\t\t},\n\n\t\taddWrapper: function( toToggle ) {\n\t\t\tif ( this.settings.wrapper ) {\n\t\t\t\ttoToggle = toToggle.add( toToggle.parent( this.settings.wrapper ) );\n\t\t\t}\n\t\t\treturn toToggle;\n\t\t},\n\n\t\tdefaultShowErrors: function() {\n\t\t\tvar i, elements, error;\n\t\t\tfor ( i = 0; this.errorList[ i ]; i++ ) {\n\t\t\t\terror = this.errorList[ i ];\n\t\t\t\tif ( this.settings.highlight ) {\n\t\t\t\t\tthis.settings.highlight.call( this, error.element, this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t\tthis.showLabel( error.element, error.message );\n\t\t\t}\n\t\t\tif ( this.errorList.length ) {\n\t\t\t\tthis.toShow = this.toShow.add( this.containers );\n\t\t\t}\n\t\t\tif ( this.settings.success ) {\n\t\t\t\tfor ( i = 0; this.successList[ i ]; i++ ) {\n\t\t\t\t\tthis.showLabel( this.successList[ i ] );\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\tfor ( i = 0, elements = this.validElements(); elements[ i ]; i++ ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ], this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.toHide = this.toHide.not( this.toShow );\n\t\t\tthis.hideErrors();\n\t\t\tthis.addWrapper( this.toShow ).show();\n\t\t},\n\n\t\tvalidElements: function() {\n\t\t\treturn this.currentElements.not( this.invalidElements() );\n\t\t},\n\n\t\tinvalidElements: function() {\n\t\t\treturn $( this.errorList ).map( function() {\n\t\t\t\treturn this.element;\n\t\t\t} );\n\t\t},\n\n\t\tshowLabel: function( element, message ) {\n\t\t\tvar place, group, errorID, v,\n\t\t\t\terror = this.errorsFor( element ),\n\t\t\t\telementID = this.idOrName( element ),\n\t\t\t\tdescribedBy = $( element ).attr( \"aria-describedby\" );\n\n\t\t\tif ( error.length ) {\n\n\t\t\t\t// Refresh error/success class\n\t\t\t\terror.removeClass( this.settings.validClass ).addClass( this.settings.errorClass );\n\n\t\t\t\t// Replace message on existing label\n\t\t\t\terror.html( message );\n\t\t\t} else {\n\n\t\t\t\t// Create error element\n\t\t\t\terror = $( \"<\" + this.settings.errorElement + \">\" )\n\t\t\t\t\t.attr( \"id\", elementID + \"-error\" )\n\t\t\t\t\t.addClass( this.settings.errorClass )\n\t\t\t\t\t.html( message || \"\" );\n\n\t\t\t\t// Maintain reference to the element to be placed into the DOM\n\t\t\t\tplace = error;\n\t\t\t\tif ( this.settings.wrapper ) {\n\n\t\t\t\t\t// Make sure the element is visible, even in IE\n\t\t\t\t\t// actually showing the wrapped element is handled elsewhere\n\t\t\t\t\tplace = error.hide().show().wrap( \"<\" + this.settings.wrapper + \"/>\" ).parent();\n\t\t\t\t}\n\t\t\t\tif ( this.labelContainer.length ) {\n\t\t\t\t\tthis.labelContainer.append( place );\n\t\t\t\t} else if ( this.settings.errorPlacement ) {\n\t\t\t\t\tthis.settings.errorPlacement.call( this, place, $( element ) );\n\t\t\t\t} else {\n\t\t\t\t\tplace.insertAfter( element );\n\t\t\t\t}\n\n\t\t\t\t// Link error back to the element\n\t\t\t\tif ( error.is( \"label\" ) ) {\n\n\t\t\t\t\t// If the error is a label, then associate using 'for'\n\t\t\t\t\terror.attr( \"for\", elementID );\n\n\t\t\t\t\t// If the element is not a child of an associated label, then it's necessary\n\t\t\t\t\t// to explicitly apply aria-describedby\n\t\t\t\t} else if ( error.parents( \"label[for='\" + this.escapeCssMeta( elementID ) + \"']\" ).length === 0 ) {\n\t\t\t\t\terrorID = error.attr( \"id\" );\n\n\t\t\t\t\t// Respect existing non-error aria-describedby\n\t\t\t\t\tif ( !describedBy ) {\n\t\t\t\t\t\tdescribedBy = errorID;\n\t\t\t\t\t} else if ( !describedBy.match( new RegExp( \"\\\\b\" + this.escapeCssMeta( errorID ) + \"\\\\b\" ) ) ) {\n\n\t\t\t\t\t\t// Add to end of list if not already present\n\t\t\t\t\t\tdescribedBy += \" \" + errorID;\n\t\t\t\t\t}\n\t\t\t\t\t$( element ).attr( \"aria-describedby\", describedBy );\n\n\t\t\t\t\t// If this element is grouped, then assign to all elements in the same group\n\t\t\t\t\tgroup = this.groups[ element.name ];\n\t\t\t\t\tif ( group ) {\n\t\t\t\t\t\tv = this;\n\t\t\t\t\t\t$.each( v.groups, function( name, testgroup ) {\n\t\t\t\t\t\t\tif ( testgroup === group ) {\n\t\t\t\t\t\t\t\t$( \"[name='\" + v.escapeCssMeta( name ) + \"']\", v.currentForm )\n\t\t\t\t\t\t\t\t\t.attr( \"aria-describedby\", error.attr( \"id\" ) );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( !message && this.settings.success ) {\n\t\t\t\terror.text( \"\" );\n\t\t\t\tif ( typeof this.settings.success === \"string\" ) {\n\t\t\t\t\terror.addClass( this.settings.success );\n\t\t\t\t} else {\n\t\t\t\t\tthis.settings.success( error, element );\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.toShow = this.toShow.add( error );\n\t\t},\n\n\t\terrorsFor: function( element ) {\n\t\t\tvar name = this.escapeCssMeta( this.idOrName( element ) ),\n\t\t\t\tdescriber = $( element ).attr( \"aria-describedby\" ),\n\t\t\t\tselector = \"label[for='\" + name + \"'], label[for='\" + name + \"'] *\";\n\n\t\t\t// 'aria-describedby' should directly reference the error element\n\t\t\tif ( describer ) {\n\t\t\t\tselector = selector + \", #\" + this.escapeCssMeta( describer )\n\t\t\t\t\t.replace( /\\s+/g, \", #\" );\n\t\t\t}\n\n\t\t\treturn this\n\t\t\t\t.errors()\n\t\t\t\t.filter( selector );\n\t\t},\n\n\t\t// See https://api.jquery.com/category/selectors/, for CSS\n\t\t// meta-characters that should be escaped in order to be used with JQuery\n\t\t// as a literal part of a name/id or any selector.\n\t\tescapeCssMeta: function( string ) {\n\t\t\tif ( string === undefined ) {\n\t\t\t\treturn \"\";\n\t\t\t}\n\n\t\t\treturn string.replace( /([\\\\!\"#$%&'()*+,./:;<=>?@\\[\\]^`{|}~])/g, \"\\\\$1\" );\n\t\t},\n\n\t\tidOrName: function( element ) {\n\t\t\treturn this.groups[ element.name ] || ( this.checkable( element ) ? element.name : element.id || element.name );\n\t\t},\n\n\t\tvalidationTargetFor: function( element ) {\n\n\t\t\t// If radio/checkbox, validate first element in group instead\n\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\telement = this.findByName( element.name );\n\t\t\t}\n\n\t\t\t// Always apply ignore filter\n\t\t\treturn $( element ).not( this.settings.ignore )[ 0 ];\n\t\t},\n\n\t\tcheckable: function( element ) {\n\t\t\treturn ( /radio|checkbox/i ).test( element.type );\n\t\t},\n\n\t\tfindByName: function( name ) {\n\t\t\treturn $( this.currentForm ).find( \"[name='\" + this.escapeCssMeta( name ) + \"']\" );\n\t\t},\n\n\t\tgetLength: function( value, element ) {\n\t\t\tswitch ( element.nodeName.toLowerCase() ) {\n\t\t\tcase \"select\":\n\t\t\t\treturn $( \"option:selected\", element ).length;\n\t\t\tcase \"input\":\n\t\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).length;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn value.length;\n\t\t},\n\n\t\tdepend: function( param, element ) {\n\t\t\treturn this.dependTypes[ typeof param ] ? this.dependTypes[ typeof param ]( param, element ) : true;\n\t\t},\n\n\t\tdependTypes: {\n\t\t\t\"boolean\": function( param ) {\n\t\t\t\treturn param;\n\t\t\t},\n\t\t\t\"string\": function( param, element ) {\n\t\t\t\treturn !!$( param, element.form ).length;\n\t\t\t},\n\t\t\t\"function\": function( param, element ) {\n\t\t\t\treturn param( element );\n\t\t\t}\n\t\t},\n\n\t\toptional: function( element ) {\n\t\t\tvar val = this.elementValue( element );\n\t\t\treturn !$.validator.methods.required.call( this, val, element ) && \"dependency-mismatch\";\n\t\t},\n\n\t\tstartRequest: function( element ) {\n\t\t\tif ( !this.pending[ element.name ] ) {\n\t\t\t\tthis.pendingRequest++;\n\t\t\t\t$( element ).addClass( this.settings.pendingClass );\n\t\t\t\tthis.pending[ element.name ] = true;\n\t\t\t}\n\t\t},\n\n\t\tstopRequest: function( element, valid ) {\n\t\t\tthis.pendingRequest--;\n\n\t\t\t// Sometimes synchronization fails, make sure pendingRequest is never < 0\n\t\t\tif ( this.pendingRequest < 0 ) {\n\t\t\t\tthis.pendingRequest = 0;\n\t\t\t}\n\t\t\tdelete this.pending[ element.name ];\n\t\t\t$( element ).removeClass( this.settings.pendingClass );\n\t\t\tif ( valid && this.pendingRequest === 0 && this.formSubmitted && this.form() && this.pendingRequest === 0 ) {\n\t\t\t\t$( this.currentForm ).trigger( \"submit\" );\n\n\t\t\t\t// Remove the hidden input that was used as a replacement for the\n\t\t\t\t// missing submit button. The hidden input is added by `handle()`\n\t\t\t\t// to ensure that the value of the used submit button is passed on\n\t\t\t\t// for scripted submits triggered by this method\n\t\t\t\tif ( this.submitButton ) {\n\t\t\t\t\t$( \"input:hidden[name='\" + this.submitButton.name + \"']\", this.currentForm ).remove();\n\t\t\t\t}\n\n\t\t\t\tthis.formSubmitted = false;\n\t\t\t} else if ( !valid && this.pendingRequest === 0 && this.formSubmitted ) {\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\n\t\t\t\tthis.formSubmitted = false;\n\t\t\t}\n\t\t},\n\n\t\tpreviousValue: function( element, method ) {\n\t\t\tmethod = typeof method === \"string\" && method || \"remote\";\n\n\t\t\treturn $.data( element, \"previousValue\" ) || $.data( element, \"previousValue\", {\n\t\t\t\told: null,\n\t\t\t\tvalid: true,\n\t\t\t\tmessage: this.defaultMessage( element, { method: method } )\n\t\t\t} );\n\t\t},\n\n\t\t// Cleans up all forms and elements, removes validator-specific events\n\t\tdestroy: function() {\n\t\t\tthis.resetForm();\n\n\t\t\t$( this.currentForm )\n\t\t\t\t.off( \".validate\" )\n\t\t\t\t.removeData( \"validator\" )\n\t\t\t\t.find( \".validate-equalTo-blur\" )\n\t\t\t\t\t.off( \".validate-equalTo\" )\n\t\t\t\t\t.removeClass( \"validate-equalTo-blur\" )\n\t\t\t\t.find( \".validate-lessThan-blur\" )\n\t\t\t\t\t.off( \".validate-lessThan\" )\n\t\t\t\t\t.removeClass( \"validate-lessThan-blur\" )\n\t\t\t\t.find( \".validate-lessThanEqual-blur\" )\n\t\t\t\t\t.off( \".validate-lessThanEqual\" )\n\t\t\t\t\t.removeClass( \"validate-lessThanEqual-blur\" )\n\t\t\t\t.find( \".validate-greaterThanEqual-blur\" )\n\t\t\t\t\t.off( \".validate-greaterThanEqual\" )\n\t\t\t\t\t.removeClass( \"validate-greaterThanEqual-blur\" )\n\t\t\t\t.find( \".validate-greaterThan-blur\" )\n\t\t\t\t\t.off( \".validate-greaterThan\" )\n\t\t\t\t\t.removeClass( \"validate-greaterThan-blur\" );\n\t\t}\n\n\t},\n\n\tclassRuleSettings: {\n\t\trequired: { required: true },\n\t\temail: { email: true },\n\t\turl: { url: true },\n\t\tdate: { date: true },\n\t\tdateISO: { dateISO: true },\n\t\tnumber: { number: true },\n\t\tdigits: { digits: true },\n\t\tcreditcard: { creditcard: true }\n\t},\n\n\taddClassRules: function( className, rules ) {\n\t\tif ( className.constructor === String ) {\n\t\t\tthis.classRuleSettings[ className ] = rules;\n\t\t} else {\n\t\t\t$.extend( this.classRuleSettings, className );\n\t\t}\n\t},\n\n\tclassRules: function( element ) {\n\t\tvar rules = {},\n\t\t\tclasses = $( element ).attr( \"class\" );\n\n\t\tif ( classes ) {\n\t\t\t$.each( classes.split( \" \" ), function() {\n\t\t\t\tif ( this in $.validator.classRuleSettings ) {\n\t\t\t\t\t$.extend( rules, $.validator.classRuleSettings[ this ] );\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\t\treturn rules;\n\t},\n\n\tnormalizeAttributeRule: function( rules, type, method, value ) {\n\n\t\t// Convert the value to a number for number inputs, and for text for backwards compability\n\t\t// allows type=\"date\" and others to be compared as strings\n\t\tif ( /min|max|step/.test( method ) && ( type === null || /number|range|text/.test( type ) ) ) {\n\t\t\tvalue = Number( value );\n\n\t\t\t// Support Opera Mini, which returns NaN for undefined minlength\n\t\t\tif ( isNaN( value ) ) {\n\t\t\t\tvalue = undefined;\n\t\t\t}\n\t\t}\n\n\t\tif ( value || value === 0 ) {\n\t\t\trules[ method ] = value;\n\t\t} else if ( type === method && type !== \"range\" ) {\n\n\t\t\t// Exception: the jquery validate 'range' method\n\t\t\t// does not test for the html5 'range' type\n\t\t\trules[ type === \"date\" ? \"dateISO\" : method ] = true;\n\t\t}\n\t},\n\n\tattributeRules: function( element ) {\n\t\tvar rules = {},\n\t\t\t$element = $( element ),\n\t\t\ttype = element.getAttribute( \"type\" ),\n\t\t\tmethod, value;\n\n\t\tfor ( method in $.validator.methods ) {\n\n\t\t\t// Support for <input required> in both html5 and older browsers\n\t\t\tif ( method === \"required\" ) {\n\t\t\t\tvalue = element.getAttribute( method );\n\n\t\t\t\t// Some browsers return an empty string for the required attribute\n\t\t\t\t// and non-HTML5 browsers might have required=\"\" markup\n\t\t\t\tif ( value === \"\" ) {\n\t\t\t\t\tvalue = true;\n\t\t\t\t}\n\n\t\t\t\t// Force non-HTML5 browsers to return bool\n\t\t\t\tvalue = !!value;\n\t\t\t} else {\n\t\t\t\tvalue = $element.attr( method );\n\t\t\t}\n\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\n\t\t}\n\n\t\t// 'maxlength' may be returned as -1, 2147483647 ( IE ) and 524288 ( safari ) for text inputs\n\t\tif ( rules.maxlength && /-1|2147483647|524288/.test( rules.maxlength ) ) {\n\t\t\tdelete rules.maxlength;\n\t\t}\n\n\t\treturn rules;\n\t},\n\n\tdataRules: function( element ) {\n\t\tvar rules = {},\n\t\t\t$element = $( element ),\n\t\t\ttype = element.getAttribute( \"type\" ),\n\t\t\tmethod, value;\n\n\t\tfor ( method in $.validator.methods ) {\n\t\t\tvalue = $element.data( \"rule\" + method.charAt( 0 ).toUpperCase() + method.substring( 1 ).toLowerCase() );\n\n\t\t\t// Cast empty attributes like `data-rule-required` to `true`\n\t\t\tif ( value === \"\" ) {\n\t\t\t\tvalue = true;\n\t\t\t}\n\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\n\t\t}\n\t\treturn rules;\n\t},\n\n\tstaticRules: function( element ) {\n\t\tvar rules = {},\n\t\t\tvalidator = $.data( element.form, \"validator\" );\n\n\t\tif ( validator.settings.rules ) {\n\t\t\trules = $.validator.normalizeRule( validator.settings.rules[ element.name ] ) || {};\n\t\t}\n\t\treturn rules;\n\t},\n\n\tnormalizeRules: function( rules, element ) {\n\n\t\t// Handle dependency check\n\t\t$.each( rules, function( prop, val ) {\n\n\t\t\t// Ignore rule when param is explicitly false, eg. required:false\n\t\t\tif ( val === false ) {\n\t\t\t\tdelete rules[ prop ];\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( val.param || val.depends ) {\n\t\t\t\tvar keepRule = true;\n\t\t\t\tswitch ( typeof val.depends ) {\n\t\t\t\tcase \"string\":\n\t\t\t\t\tkeepRule = !!$( val.depends, element.form ).length;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"function\":\n\t\t\t\t\tkeepRule = val.depends.call( element, element );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tif ( keepRule ) {\n\t\t\t\t\trules[ prop ] = val.param !== undefined ? val.param : true;\n\t\t\t\t} else {\n\t\t\t\t\t$.data( element.form, \"validator\" ).resetElements( $( element ) );\n\t\t\t\t\tdelete rules[ prop ];\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\n\t\t// Evaluate parameters\n\t\t$.each( rules, function( rule, parameter ) {\n\t\t\trules[ rule ] = typeof parameter === \"function\" && rule !== \"normalizer\" ? parameter( element ) : parameter;\n\t\t} );\n\n\t\t// Clean number parameters\n\t\t$.each( [ \"minlength\", \"maxlength\" ], function() {\n\t\t\tif ( rules[ this ] ) {\n\t\t\t\trules[ this ] = Number( rules[ this ] );\n\t\t\t}\n\t\t} );\n\t\t$.each( [ \"rangelength\", \"range\" ], function() {\n\t\t\tvar parts;\n\t\t\tif ( rules[ this ] ) {\n\t\t\t\tif ( Array.isArray( rules[ this ] ) ) {\n\t\t\t\t\trules[ this ] = [ Number( rules[ this ][ 0 ] ), Number( rules[ this ][ 1 ] ) ];\n\t\t\t\t} else if ( typeof rules[ this ] === \"string\" ) {\n\t\t\t\t\tparts = rules[ this ].replace( /[\\[\\]]/g, \"\" ).split( /[\\s,]+/ );\n\t\t\t\t\trules[ this ] = [ Number( parts[ 0 ] ), Number( parts[ 1 ] ) ];\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\n\t\tif ( $.validator.autoCreateRanges ) {\n\n\t\t\t// Auto-create ranges\n\t\t\tif ( rules.min != null && rules.max != null ) {\n\t\t\t\trules.range = [ rules.min, rules.max ];\n\t\t\t\tdelete rules.min;\n\t\t\t\tdelete rules.max;\n\t\t\t}\n\t\t\tif ( rules.minlength != null && rules.maxlength != null ) {\n\t\t\t\trules.rangelength = [ rules.minlength, rules.maxlength ];\n\t\t\t\tdelete rules.minlength;\n\t\t\t\tdelete rules.maxlength;\n\t\t\t}\n\t\t}\n\n\t\treturn rules;\n\t},\n\n\t// Converts a simple string to a {string: true} rule, e.g., \"required\" to {required:true}\n\tnormalizeRule: function( data ) {\n\t\tif ( typeof data === \"string\" ) {\n\t\t\tvar transformed = {};\n\t\t\t$.each( data.split( /\\s/ ), function() {\n\t\t\t\ttransformed[ this ] = true;\n\t\t\t} );\n\t\t\tdata = transformed;\n\t\t}\n\t\treturn data;\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.addMethod/\n\taddMethod: function( name, method, message ) {\n\t\t$.validator.methods[ name ] = method;\n\t\t$.validator.messages[ name ] = message !== undefined ? message : $.validator.messages[ name ];\n\t\tif ( method.length < 3 ) {\n\t\t\t$.validator.addClassRules( name, $.validator.normalizeRule( name ) );\n\t\t}\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.methods/\n\tmethods: {\n\n\t\t// https://jqueryvalidation.org/required-method/\n\t\trequired: function( value, element, param ) {\n\n\t\t\t// Check if dependency is met\n\t\t\tif ( !this.depend( param, element ) ) {\n\t\t\t\treturn \"dependency-mismatch\";\n\t\t\t}\n\t\t\tif ( element.nodeName.toLowerCase() === \"select\" ) {\n\n\t\t\t\t// Could be an array for select-multiple or a string, both are fine this way\n\t\t\t\tvar val = $( element ).val();\n\t\t\t\treturn val && val.length > 0;\n\t\t\t}\n\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\treturn this.getLength( value, element ) > 0;\n\t\t\t}\n\t\t\treturn value !== undefined && value !== null && value.length > 0;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/email-method/\n\t\temail: function( value, element ) {\n\n\t\t\t// From https://html.spec.whatwg.org/multipage/forms.html#valid-e-mail-address\n\t\t\t// Retrieved 2014-01-14\n\t\t\t// If you have a problem with this implementation, report a bug against the above spec\n\t\t\t// Or use custom methods to implement your own email validation\n\t\t\treturn this.optional( element ) || /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/url-method/\n\t\turl: function( value, element ) {\n\n\t\t\t// Copyright (c) 2010-2013 Diego Perini, MIT licensed\n\t\t\t// https://gist.github.com/dperini/729294\n\t\t\t// see also https://mathiasbynens.be/demo/url-regex\n\t\t\t// modified to allow protocol-relative URLs\n\t\t\treturn this.optional( element ) || /^(?:(?:(?:https?|ftp):)?\\/\\/)(?:(?:[^\\]\\[?\\/<~#`!@$^&*()+=}|:\";',>{ ]|%[0-9A-Fa-f]{2})+(?::(?:[^\\]\\[?\\/<~#`!@$^&*()+=}|:\";',>{ ]|%[0-9A-Fa-f]{2})*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z0-9\\u00a1-\\uffff][a-z0-9\\u00a1-\\uffff_-]{0,62})?[a-z0-9\\u00a1-\\uffff]\\.)+(?:[a-z\\u00a1-\\uffff]{2,}\\.?))(?::\\d{2,5})?(?:[/?#]\\S*)?$/i.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/date-method/\n\t\tdate: ( function() {\n\t\t\tvar called = false;\n\n\t\t\treturn function( value, element ) {\n\t\t\t\tif ( !called ) {\n\t\t\t\t\tcalled = true;\n\t\t\t\t\tif ( this.settings.debug && window.console ) {\n\t\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t\t\"The `date` method is deprecated and will be removed in version '2.0.0'.\\n\" +\n\t\t\t\t\t\t\t\"Please don't use it, since it relies on the Date constructor, which\\n\" +\n\t\t\t\t\t\t\t\"behaves very differently across browsers and locales. Use `dateISO`\\n\" +\n\t\t\t\t\t\t\t\"instead or one of the locale specific methods in `localizations/`\\n\" +\n\t\t\t\t\t\t\t\"and `additional-methods.js`.\"\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn this.optional( element ) || !/Invalid|NaN/.test( new Date( value ).toString() );\n\t\t\t};\n\t\t}() ),\n\n\t\t// https://jqueryvalidation.org/dateISO-method/\n\t\tdateISO: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/number-method/\n\t\tnumber: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/digits-method/\n\t\tdigits: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^\\d+$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/minlength-method/\n\t\tminlength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || length >= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/maxlength-method/\n\t\tmaxlength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || length <= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/rangelength-method/\n\t\trangelength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || ( length >= param[ 0 ] && length <= param[ 1 ] );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/min-method/\n\t\tmin: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || value >= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/max-method/\n\t\tmax: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || value <= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/range-method/\n\t\trange: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || ( value >= param[ 0 ] && value <= param[ 1 ] );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/step-method/\n\t\tstep: function( value, element, param ) {\n\t\t\tvar type = $( element ).attr( \"type\" ),\n\t\t\t\terrorMessage = \"Step attribute on input type \" + type + \" is not supported.\",\n\t\t\t\tsupportedTypes = [ \"text\", \"number\", \"range\" ],\n\t\t\t\tre = new RegExp( \"\\\\b\" + type + \"\\\\b\" ),\n\t\t\t\tnotSupported = type && !re.test( supportedTypes.join() ),\n\t\t\t\tdecimalPlaces = function( num ) {\n\t\t\t\t\tvar match = ( \"\" + num ).match( /(?:\\.(\\d+))?$/ );\n\t\t\t\t\tif ( !match ) {\n\t\t\t\t\t\treturn 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Number of digits right of decimal point.\n\t\t\t\t\treturn match[ 1 ] ? match[ 1 ].length : 0;\n\t\t\t\t},\n\t\t\t\ttoInt = function( num ) {\n\t\t\t\t\treturn Math.round( num * Math.pow( 10, decimals ) );\n\t\t\t\t},\n\t\t\t\tvalid = true,\n\t\t\t\tdecimals;\n\n\t\t\t// Works only for text, number and range input types\n\t\t\t// TODO find a way to support input types date, datetime, datetime-local, month, time and week\n\t\t\tif ( notSupported ) {\n\t\t\t\tthrow new Error( errorMessage );\n\t\t\t}\n\n\t\t\tdecimals = decimalPlaces( param );\n\n\t\t\t// Value can't have too many decimals\n\t\t\tif ( decimalPlaces( value ) > decimals || toInt( value ) % toInt( param ) !== 0 ) {\n\t\t\t\tvalid = false;\n\t\t\t}\n\n\t\t\treturn this.optional( element ) || valid;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/equalTo-method/\n\t\tequalTo: function( value, element, param ) {\n\n\t\t\t// Bind to the blur event of the target in order to revalidate whenever the target field is updated\n\t\t\tvar target = $( param );\n\t\t\tif ( this.settings.onfocusout && target.not( \".validate-equalTo-blur\" ).length ) {\n\t\t\t\ttarget.addClass( \"validate-equalTo-blur\" ).on( \"blur.validate-equalTo\", function() {\n\t\t\t\t\t$( element ).valid();\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn value === target.val();\n\t\t},\n\n\t\t// https://jqueryvalidation.org/remote-method/\n\t\tremote: function( value, element, param, method ) {\n\t\t\tif ( this.optional( element ) ) {\n\t\t\t\treturn \"dependency-mismatch\";\n\t\t\t}\n\n\t\t\tmethod = typeof method === \"string\" && method || \"remote\";\n\n\t\t\tvar previous = this.previousValue( element, method ),\n\t\t\t\tvalidator, data, optionDataString;\n\n\t\t\tif ( !this.settings.messages[ element.name ] ) {\n\t\t\t\tthis.settings.messages[ element.name ] = {};\n\t\t\t}\n\t\t\tprevious.originalMessage = previous.originalMessage || this.settings.messages[ element.name ][ method ];\n\t\t\tthis.settings.messages[ element.name ][ method ] = previous.message;\n\n\t\t\tparam = typeof param === \"string\" && { url: param } || param;\n\t\t\toptionDataString = $.param( $.extend( { data: value }, param.data ) );\n\t\t\tif ( previous.old === optionDataString ) {\n\t\t\t\treturn previous.valid;\n\t\t\t}\n\n\t\t\tprevious.old = optionDataString;\n\t\t\tvalidator = this;\n\t\t\tthis.startRequest( element );\n\t\t\tdata = {};\n\t\t\tdata[ element.name ] = value;\n\t\t\t$.ajax( $.extend( true, {\n\t\t\t\tmode: \"abort\",\n\t\t\t\tport: \"validate\" + element.name,\n\t\t\t\tdataType: \"json\",\n\t\t\t\tdata: data,\n\t\t\t\tcontext: validator.currentForm,\n\t\t\t\tsuccess: function( response ) {\n\t\t\t\t\tvar valid = response === true || response === \"true\",\n\t\t\t\t\t\terrors, message, submitted;\n\n\t\t\t\t\tvalidator.settings.messages[ element.name ][ method ] = previous.originalMessage;\n\t\t\t\t\tif ( valid ) {\n\t\t\t\t\t\tsubmitted = validator.formSubmitted;\n\t\t\t\t\t\tvalidator.resetInternals();\n\t\t\t\t\t\tvalidator.toHide = validator.errorsFor( element );\n\t\t\t\t\t\tvalidator.formSubmitted = submitted;\n\t\t\t\t\t\tvalidator.successList.push( element );\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = false;\n\t\t\t\t\t\tvalidator.showErrors();\n\t\t\t\t\t} else {\n\t\t\t\t\t\terrors = {};\n\t\t\t\t\t\tmessage = response || validator.defaultMessage( element, { method: method, parameters: value } );\n\t\t\t\t\t\terrors[ element.name ] = previous.message = message;\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = true;\n\t\t\t\t\t\tvalidator.showErrors( errors );\n\t\t\t\t\t}\n\t\t\t\t\tprevious.valid = valid;\n\t\t\t\t\tvalidator.stopRequest( element, valid );\n\t\t\t\t}\n\t\t\t}, param ) );\n\t\t\treturn \"pending\";\n\t\t}\n\t}\n\n} );\n\n// Ajax mode: abort\n// usage: $.ajax({ mode: \"abort\"[, port: \"uniqueport\"]});\n// if mode:\"abort\" is used, the previous request on that port (port can be undefined) is aborted via XMLHttpRequest.abort()\n\nvar pendingRequests = {},\n\tajax;\n\n// Use a prefilter if available (1.5+)\nif ( $.ajaxPrefilter ) {\n\t$.ajaxPrefilter( function( settings, _, xhr ) {\n\t\tvar port = settings.port;\n\t\tif ( settings.mode === \"abort\" ) {\n\t\t\tif ( pendingRequests[ port ] ) {\n\t\t\t\tpendingRequests[ port ].abort();\n\t\t\t}\n\t\t\tpendingRequests[ port ] = xhr;\n\t\t}\n\t} );\n} else {\n\n\t// Proxy ajax\n\tajax = $.ajax;\n\t$.ajax = function( settings ) {\n\t\tvar mode = ( \"mode\" in settings ? settings : $.ajaxSettings ).mode,\n\t\t\tport = ( \"port\" in settings ? settings : $.ajaxSettings ).port;\n\t\tif ( mode === \"abort\" ) {\n\t\t\tif ( pendingRequests[ port ] ) {\n\t\t\t\tpendingRequests[ port ].abort();\n\t\t\t}\n\t\t\tpendingRequests[ port ] = ajax.apply( this, arguments );\n\t\t\treturn pendingRequests[ port ];\n\t\t}\n\t\treturn ajax.apply( this, arguments );\n\t};\n}\nreturn $;\n}));", "/*!\r\n * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2020\r\n * @version 1.3.6\r\n *\r\n * Date formatter utility library that allows formatting date/time variables or Date objects using PHP DateTime format.\r\n * This library is a standalone javascript library and does not depend on other libraries or plugins like jQuery. The\r\n * library also adds support for Universal Module Definition (UMD).\r\n * \r\n * @see http://php.net/manual/en/function.date.php\r\n *\r\n * For more JQuery plugins visit http://plugins.krajee.com\r\n * For more Yii related demos visit http://demos.krajee.com\r\n */\r\n(function (root, factory) {\r\n    // noinspection JSUnresolvedVariable\r\n    if (typeof define === 'function' && define.amd) { // AMD\r\n        // noinspection JSUnresolvedFunction\r\n        define([], factory);\r\n    } else {\r\n        // noinspection JSUnresolvedVariable\r\n        if (typeof module === 'object' && module.exports) { // Node\r\n            // noinspection JSUnresolvedVariable\r\n            module.exports = factory();\r\n        } else { // Browser globals\r\n            root.DateFormatter = factory();\r\n        }\r\n    }\r\n}(typeof self !== 'undefined' ? self : this, function () {\r\n    var DateFormatter, $h;\r\n    /**\r\n     * Global helper object\r\n     */\r\n    $h = {\r\n        DAY: 1000 * 60 * 60 * 24,\r\n        HOUR: 3600,\r\n        defaults: {\r\n            dateSettings: {\r\n                days: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\r\n                daysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\r\n                months: [\r\n                    'January', 'February', 'March', 'April', 'May', 'June', 'July',\r\n                    'August', 'September', 'October', 'November', 'December'\r\n                ],\r\n                monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\r\n                meridiem: ['AM', 'PM'],\r\n                ordinal: function (number) {\r\n                    var n = number % 10, suffixes = {1: 'st', 2: 'nd', 3: 'rd'};\r\n                    return Math.floor(number % 100 / 10) === 1 || !suffixes[n] ? 'th' : suffixes[n];\r\n                }\r\n            },\r\n            separators: /[ \\-+\\/.:@]/g,\r\n            validParts: /[dDjlNSwzWFmMntLoYyaABgGhHisueTIOPZcrU]/g,\r\n            intParts: /[djwNzmnyYhHgGis]/g,\r\n            tzParts: /\\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\\d{4})?)\\b/g,\r\n            tzClip: /[^-+\\dA-Z]/g\r\n        },\r\n        getInt: function (str, radix) {\r\n            return parseInt(str, (radix ? radix : 10));\r\n        },\r\n        compare: function (str1, str2) {\r\n            return typeof (str1) === 'string' && typeof (str2) === 'string' && str1.toLowerCase() === str2.toLowerCase();\r\n        },\r\n        lpad: function (value, length, chr) {\r\n            var val = value.toString();\r\n            chr = chr || '0';\r\n            return val.length < length ? $h.lpad(chr + val, length) : val;\r\n        },\r\n        merge: function (out) {\r\n            var i, obj;\r\n            out = out || {};\r\n            for (i = 1; i < arguments.length; i++) {\r\n                obj = arguments[i];\r\n                if (!obj) {\r\n                    continue;\r\n                }\r\n                for (var key in obj) {\r\n                    if (obj.hasOwnProperty(key)) {\r\n                        if (typeof obj[key] === 'object') {\r\n                            $h.merge(out[key], obj[key]);\r\n                        } else {\r\n                            out[key] = obj[key];\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return out;\r\n        },\r\n        getIndex: function (val, arr) {\r\n            for (var i = 0; i < arr.length; i++) {\r\n                if (arr[i].toLowerCase() === val.toLowerCase()) {\r\n                    return i;\r\n                }\r\n            }\r\n            return -1;\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Date Formatter Library Constructor\r\n     * @param options\r\n     * @constructor\r\n     */\r\n    DateFormatter = function (options) {\r\n        var self = this, config = $h.merge($h.defaults, options);\r\n        self.dateSettings = config.dateSettings;\r\n        self.separators = config.separators;\r\n        self.validParts = config.validParts;\r\n        self.intParts = config.intParts;\r\n        self.tzParts = config.tzParts;\r\n        self.tzClip = config.tzClip;\r\n    };\r\n\r\n    /**\r\n     * DateFormatter Library Prototype\r\n     */\r\n    DateFormatter.prototype = {\r\n        constructor: DateFormatter,\r\n        getMonth: function (val) {\r\n            var self = this, i;\r\n            i = $h.getIndex(val, self.dateSettings.monthsShort) + 1;\r\n            if (i === 0) {\r\n                i = $h.getIndex(val, self.dateSettings.months) + 1;\r\n            }\r\n            return i;\r\n        },\r\n        parseDate: function (vDate, vFormat) {\r\n            var self = this, vFormatParts, vDateParts, i, vDateFlag = false, vTimeFlag = false, vDatePart, iDatePart,\r\n                vSettings = self.dateSettings, vMonth, vMeriIndex, vMeriOffset, len, mer,\r\n                out = {date: null, year: null, month: null, day: null, hour: 0, min: 0, sec: 0};\r\n            if (!vDate) {\r\n                return null;\r\n            }\r\n            if (vDate instanceof Date) {\r\n                return vDate;\r\n            }\r\n            if (vFormat === 'U') {\r\n                i = $h.getInt(vDate);\r\n                return i ? new Date(i * 1000) : vDate;\r\n            }\r\n            switch (typeof vDate) {\r\n                case 'number':\r\n                    return new Date(vDate);\r\n                case 'string':\r\n                    break;\r\n                default:\r\n                    return null;\r\n            }\r\n            vFormatParts = vFormat.match(self.validParts);\r\n            if (!vFormatParts || vFormatParts.length === 0) {\r\n                throw new Error('Invalid date format definition.');\r\n            }\r\n            for (i = vFormatParts.length - 1; i >= 0; i--) {\r\n                if (vFormatParts[i] === 'S') {\r\n                    vFormatParts.splice(i, 1);\r\n                }\r\n            }\r\n            vDateParts = vDate.replace(self.separators, '\\0').split('\\0');\r\n            for (i = 0; i < vDateParts.length; i++) {\r\n                vDatePart = vDateParts[i];\r\n                iDatePart = $h.getInt(vDatePart);\r\n                switch (vFormatParts[i]) {\r\n                    case 'y':\r\n                    case 'Y':\r\n                        if (iDatePart) {\r\n                            len = vDatePart.length;\r\n                            out.year = len === 2 ? $h.getInt((iDatePart < 70 ? '20' : '19') + vDatePart) : iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'm':\r\n                    case 'n':\r\n                    case 'M':\r\n                    case 'F':\r\n                        if (isNaN(iDatePart)) {\r\n                            vMonth = self.getMonth(vDatePart);\r\n                            if (vMonth > 0) {\r\n                                out.month = vMonth;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        } else {\r\n                            if (iDatePart >= 1 && iDatePart <= 12) {\r\n                                out.month = iDatePart;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'd':\r\n                    case 'j':\r\n                        if (iDatePart >= 1 && iDatePart <= 31) {\r\n                            out.day = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'g':\r\n                    case 'h':\r\n                        vMeriIndex = (vFormatParts.indexOf('a') > -1) ? vFormatParts.indexOf('a') :\r\n                            ((vFormatParts.indexOf('A') > -1) ? vFormatParts.indexOf('A') : -1);\r\n                        mer = vDateParts[vMeriIndex];\r\n                        if (vMeriIndex !== -1) {\r\n                            vMeriOffset = $h.compare(mer, vSettings.meridiem[0]) ? 0 :\r\n                                ($h.compare(mer, vSettings.meridiem[1]) ? 12 : -1);\r\n                            if (iDatePart >= 1 && iDatePart <= 12 && vMeriOffset !== -1) {\r\n                                out.hour = iDatePart % 12 === 0 ? vMeriOffset : iDatePart + vMeriOffset;\r\n                            } else {\r\n                                if (iDatePart >= 0 && iDatePart <= 23) {\r\n                                    out.hour = iDatePart;\r\n                                }\r\n                            }\r\n                        } else {\r\n                            if (iDatePart >= 0 && iDatePart <= 23) {\r\n                                out.hour = iDatePart;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 'G':\r\n                    case 'H':\r\n                        if (iDatePart >= 0 && iDatePart <= 23) {\r\n                            out.hour = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 'i':\r\n                        if (iDatePart >= 0 && iDatePart <= 59) {\r\n                            out.min = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 's':\r\n                        if (iDatePart >= 0 && iDatePart <= 59) {\r\n                            out.sec = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                }\r\n            }\r\n            if (vDateFlag === true) {\r\n                var varY = out.year || 0, varM = out.month ? out.month - 1 : 0, varD = out.day || 1;\r\n                out.date = new Date(varY, varM, varD, out.hour, out.min, out.sec, 0);\r\n            } else {\r\n                if (vTimeFlag !== true) {\r\n                    return null;\r\n                }\r\n                out.date = new Date(0, 0, 0, out.hour, out.min, out.sec, 0);\r\n            }\r\n            return out.date;\r\n        },\r\n        guessDate: function (vDateStr, vFormat) {\r\n            if (typeof vDateStr !== 'string') {\r\n                return vDateStr;\r\n            }\r\n            var self = this, vParts = vDateStr.replace(self.separators, '\\0').split('\\0'), vPattern = /^[djmn]/g, len,\r\n                vFormatParts = vFormat.match(self.validParts), vDate = new Date(), vDigit = 0, vYear, i, n, iPart, iSec;\r\n\r\n            if (!vPattern.test(vFormatParts[0])) {\r\n                return vDateStr;\r\n            }\r\n\r\n            for (i = 0; i < vParts.length; i++) {\r\n                vDigit = 2;\r\n                iPart = vParts[i];\r\n                iSec = $h.getInt(iPart.substr(0, 2));\r\n                if (isNaN(iSec)) {\r\n                    return null;\r\n                }\r\n                switch (i) {\r\n                    case 0:\r\n                        if (vFormatParts[0] === 'm' || vFormatParts[0] === 'n') {\r\n                            vDate.setMonth(iSec - 1);\r\n                        } else {\r\n                            vDate.setDate(iSec);\r\n                        }\r\n                        break;\r\n                    case 1:\r\n                        if (vFormatParts[0] === 'm' || vFormatParts[0] === 'n') {\r\n                            vDate.setDate(iSec);\r\n                        } else {\r\n                            vDate.setMonth(iSec - 1);\r\n                        }\r\n                        break;\r\n                    case 2:\r\n                        vYear = vDate.getFullYear();\r\n                        len = iPart.length;\r\n                        vDigit = len < 4 ? len : 4;\r\n                        vYear = $h.getInt(len < 4 ? vYear.toString().substr(0, 4 - len) + iPart : iPart.substr(0, 4));\r\n                        if (!vYear) {\r\n                            return null;\r\n                        }\r\n                        vDate.setFullYear(vYear);\r\n                        break;\r\n                    case 3:\r\n                        vDate.setHours(iSec);\r\n                        break;\r\n                    case 4:\r\n                        vDate.setMinutes(iSec);\r\n                        break;\r\n                    case 5:\r\n                        vDate.setSeconds(iSec);\r\n                        break;\r\n                }\r\n                n = iPart.substr(vDigit);\r\n                if (n.length > 0) {\r\n                    vParts.splice(i + 1, 0, n);\r\n                }\r\n            }\r\n            return vDate;\r\n        },\r\n        parseFormat: function (vChar, vDate) {\r\n            var self = this, vSettings = self.dateSettings, fmt, backslash = /\\\\?(.?)/gi, doFormat = function (t, s) {\r\n                return fmt[t] ? fmt[t]() : s;\r\n            };\r\n            fmt = {\r\n                /////////\r\n                // DAY //\r\n                /////////\r\n                /**\r\n                 * Day of month with leading 0: `01..31`\r\n                 * @return {string}\r\n                 */\r\n                d: function () {\r\n                    return $h.lpad(fmt.j(), 2);\r\n                },\r\n                /**\r\n                 * Shorthand day name: `Mon...Sun`\r\n                 * @return {string}\r\n                 */\r\n                D: function () {\r\n                    return vSettings.daysShort[fmt.w()];\r\n                },\r\n                /**\r\n                 * Day of month: `1..31`\r\n                 * @return {number}\r\n                 */\r\n                j: function () {\r\n                    return vDate.getDate();\r\n                },\r\n                /**\r\n                 * Full day name: `Monday...Sunday`\r\n                 * @return {string}\r\n                 */\r\n                l: function () {\r\n                    return vSettings.days[fmt.w()];\r\n                },\r\n                /**\r\n                 * ISO-8601 day of week: `1[Mon]..7[Sun]`\r\n                 * @return {number}\r\n                 */\r\n                N: function () {\r\n                    return fmt.w() || 7;\r\n                },\r\n                /**\r\n                 * Day of week: `0[Sun]..6[Sat]`\r\n                 * @return {number}\r\n                 */\r\n                w: function () {\r\n                    return vDate.getDay();\r\n                },\r\n                /**\r\n                 * Day of year: `0..365`\r\n                 * @return {number}\r\n                 */\r\n                z: function () {\r\n                    var a = new Date(fmt.Y(), fmt.n() - 1, fmt.j()), b = new Date(fmt.Y(), 0, 1);\r\n                    return Math.round((a - b) / $h.DAY);\r\n                },\r\n\r\n                //////////\r\n                // WEEK //\r\n                //////////\r\n                /**\r\n                 * ISO-8601 week number\r\n                 * @return {number}\r\n                 */\r\n                W: function () {\r\n                    var a = new Date(fmt.Y(), fmt.n() - 1, fmt.j() - fmt.N() + 3), b = new Date(a.getFullYear(), 0, 4);\r\n                    return $h.lpad(1 + Math.round((a - b) / $h.DAY / 7), 2);\r\n                },\r\n\r\n                ///////////\r\n                // MONTH //\r\n                ///////////\r\n                /**\r\n                 * Full month name: `January...December`\r\n                 * @return {string}\r\n                 */\r\n                F: function () {\r\n                    return vSettings.months[vDate.getMonth()];\r\n                },\r\n                /**\r\n                 * Month w/leading 0: `01..12`\r\n                 * @return {string}\r\n                 */\r\n                m: function () {\r\n                    return $h.lpad(fmt.n(), 2);\r\n                },\r\n                /**\r\n                 * Shorthand month name; `Jan...Dec`\r\n                 * @return {string}\r\n                 */\r\n                M: function () {\r\n                    return vSettings.monthsShort[vDate.getMonth()];\r\n                },\r\n                /**\r\n                 * Month: `1...12`\r\n                 * @return {number}\r\n                 */\r\n                n: function () {\r\n                    return vDate.getMonth() + 1;\r\n                },\r\n                /**\r\n                 * Days in month: `28...31`\r\n                 * @return {number}\r\n                 */\r\n                t: function () {\r\n                    return (new Date(fmt.Y(), fmt.n(), 0)).getDate();\r\n                },\r\n\r\n                //////////\r\n                // YEAR //\r\n                //////////\r\n                /**\r\n                 * Is leap year? `0 or 1`\r\n                 * @return {number}\r\n                 */\r\n                L: function () {\r\n                    var Y = fmt.Y();\r\n                    return (Y % 4 === 0 && Y % 100 !== 0 || Y % 400 === 0) ? 1 : 0;\r\n                },\r\n                /**\r\n                 * ISO-8601 year\r\n                 * @return {number}\r\n                 */\r\n                o: function () {\r\n                    var n = fmt.n(), W = fmt.W(), Y = fmt.Y();\r\n                    return Y + (n === 12 && W < 9 ? 1 : n === 1 && W > 9 ? -1 : 0);\r\n                },\r\n                /**\r\n                 * Full year: `e.g. 1980...2010`\r\n                 * @return {number}\r\n                 */\r\n                Y: function () {\r\n                    return vDate.getFullYear();\r\n                },\r\n                /**\r\n                 * Last two digits of year: `00...99`\r\n                 * @return {string}\r\n                 */\r\n                y: function () {\r\n                    return fmt.Y().toString().slice(-2);\r\n                },\r\n\r\n                //////////\r\n                // TIME //\r\n                //////////\r\n                /**\r\n                 * Meridian lower: `am or pm`\r\n                 * @return {string}\r\n                 */\r\n                a: function () {\r\n                    return fmt.A().toLowerCase();\r\n                },\r\n                /**\r\n                 * Meridian upper: `AM or PM`\r\n                 * @return {string}\r\n                 */\r\n                A: function () {\r\n                    var n = fmt.G() < 12 ? 0 : 1;\r\n                    return vSettings.meridiem[n];\r\n                },\r\n                /**\r\n                 * Swatch Internet time: `000..999`\r\n                 * @return {string}\r\n                 */\r\n                B: function () {\r\n                    var H = vDate.getUTCHours() * $h.HOUR, i = vDate.getUTCMinutes() * 60, s = vDate.getUTCSeconds();\r\n                    return $h.lpad(Math.floor((H + i + s + $h.HOUR) / 86.4) % 1000, 3);\r\n                },\r\n                /**\r\n                 * 12-Hours: `1..12`\r\n                 * @return {number}\r\n                 */\r\n                g: function () {\r\n                    return fmt.G() % 12 || 12;\r\n                },\r\n                /**\r\n                 * 24-Hours: `0..23`\r\n                 * @return {number}\r\n                 */\r\n                G: function () {\r\n                    return vDate.getHours();\r\n                },\r\n                /**\r\n                 * 12-Hours with leading 0: `01..12`\r\n                 * @return {string}\r\n                 */\r\n                h: function () {\r\n                    return $h.lpad(fmt.g(), 2);\r\n                },\r\n                /**\r\n                 * 24-Hours w/leading 0: `00..23`\r\n                 * @return {string}\r\n                 */\r\n                H: function () {\r\n                    return $h.lpad(fmt.G(), 2);\r\n                },\r\n                /**\r\n                 * Minutes w/leading 0: `00..59`\r\n                 * @return {string}\r\n                 */\r\n                i: function () {\r\n                    return $h.lpad(vDate.getMinutes(), 2);\r\n                },\r\n                /**\r\n                 * Seconds w/leading 0: `00..59`\r\n                 * @return {string}\r\n                 */\r\n                s: function () {\r\n                    return $h.lpad(vDate.getSeconds(), 2);\r\n                },\r\n                /**\r\n                 * Microseconds: `000000-999000`\r\n                 * @return {string}\r\n                 */\r\n                u: function () {\r\n                    return $h.lpad(vDate.getMilliseconds() * 1000, 6);\r\n                },\r\n\r\n                //////////////\r\n                // TIMEZONE //\r\n                //////////////\r\n                /**\r\n                 * Timezone identifier: `e.g. Atlantic/Azores, ...`\r\n                 * @return {string}\r\n                 */\r\n                e: function () {\r\n                    var str = /\\((.*)\\)/.exec(String(vDate))[1];\r\n                    return str || 'Coordinated Universal Time';\r\n                },\r\n                /**\r\n                 * DST observed? `0 or 1`\r\n                 * @return {number}\r\n                 */\r\n                I: function () {\r\n                    var a = new Date(fmt.Y(), 0), c = Date.UTC(fmt.Y(), 0),\r\n                        b = new Date(fmt.Y(), 6), d = Date.UTC(fmt.Y(), 6);\r\n                    return ((a - c) !== (b - d)) ? 1 : 0;\r\n                },\r\n                /**\r\n                 * Difference to GMT in hour format: `e.g. +0200`\r\n                 * @return {string}\r\n                 */\r\n                O: function () {\r\n                    var tzo = vDate.getTimezoneOffset(), a = Math.abs(tzo);\r\n                    return (tzo > 0 ? '-' : '+') + $h.lpad(Math.floor(a / 60) * 100 + a % 60, 4);\r\n                },\r\n                /**\r\n                 * Difference to GMT with colon: `e.g. +02:00`\r\n                 * @return {string}\r\n                 */\r\n                P: function () {\r\n                    var O = fmt.O();\r\n                    return (O.substr(0, 3) + ':' + O.substr(3, 2));\r\n                },\r\n                /**\r\n                 * Timezone abbreviation: `e.g. EST, MDT, ...`\r\n                 * @return {string}\r\n                 */\r\n                T: function () {\r\n                    var str = (String(vDate).match(self.tzParts) || ['']).pop().replace(self.tzClip, '');\r\n                    return str || 'UTC';\r\n                },\r\n                /**\r\n                 * Timezone offset in seconds: `-43200...50400`\r\n                 * @return {number}\r\n                 */\r\n                Z: function () {\r\n                    return -vDate.getTimezoneOffset() * 60;\r\n                },\r\n\r\n                ////////////////////\r\n                // FULL DATE TIME //\r\n                ////////////////////\r\n                /**\r\n                 * ISO-8601 date\r\n                 * @return {string}\r\n                 */\r\n                c: function () {\r\n                    return 'Y-m-d\\\\TH:i:sP'.replace(backslash, doFormat);\r\n                },\r\n                /**\r\n                 * RFC 2822 date\r\n                 * @return {string}\r\n                 */\r\n                r: function () {\r\n                    return 'D, d M Y H:i:s O'.replace(backslash, doFormat);\r\n                },\r\n                /**\r\n                 * Seconds since UNIX epoch\r\n                 * @return {number}\r\n                 */\r\n                U: function () {\r\n                    return vDate.getTime() / 1000 || 0;\r\n                }\r\n            };\r\n            return doFormat(vChar, vChar);\r\n        },\r\n        formatDate: function (vDate, vFormat) {\r\n            var self = this, i, n, len, str, vChar, vDateStr = '', BACKSLASH = '\\\\';\r\n            if (typeof vDate === 'string') {\r\n                vDate = self.parseDate(vDate, vFormat);\r\n                if (!vDate) {\r\n                    return null;\r\n                }\r\n            }\r\n            if (vDate instanceof Date) {\r\n                len = vFormat.length;\r\n                for (i = 0; i < len; i++) {\r\n                    vChar = vFormat.charAt(i);\r\n                    if (vChar === 'S' || vChar === BACKSLASH) {\r\n                        continue;\r\n                    }\r\n                    if (i > 0 && vFormat.charAt(i - 1) === BACKSLASH) {\r\n                        vDateStr += vChar;\r\n                        continue;\r\n                    }\r\n                    str = self.parseFormat(vChar, vDate);\r\n                    if (i !== (len - 1) && self.intParts.test(vChar) && vFormat.charAt(i + 1) === 'S') {\r\n                        n = $h.getInt(str) || 0;\r\n                        str += self.dateSettings.ordinal(n);\r\n                    }\r\n                    vDateStr += str;\r\n                }\r\n                return vDateStr;\r\n            }\r\n            return '';\r\n        }\r\n    };\r\n    return DateFormatter;\r\n}));", "/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\nvar laravelValidation;\nlaravelValidation = {\n\n    implicitRules: ['Required','Confirmed'],\n\n    /**\n     * Initialize laravel validations.\n     */\n    init: function () {\n\n        // jquery-validation requires the field under validation to be present. We're adding a dummy hidden\n        // field so that any errors are not visible.\n        var constructor = $.fn.validate;\n        $.fn.validate = function( options ) {\n            var name = 'proengsoft_jsvalidation'; // must match the name defined in JsValidatorFactory.newFormRequestValidator\n            var $elm = $(this).find('input[name=\"' + name + '\"]');\n            if ($elm.length === 0) {\n                $('<input>').attr({type: 'hidden', name: name}).appendTo(this);\n            }\n\n            return constructor.apply(this, [options]);\n        };\n\n        // Disable class rules and attribute rules\n        $.validator.classRuleSettings = {};\n        $.validator.attributeRules = function () {};\n\n        $.validator.dataRules = this.arrayRules;\n        $.validator.prototype.arrayRulesCache = {};\n\n        // Register validations methods\n        this.setupValidations();\n    },\n\n    arrayRules: function(element) {\n\n        var rules = {},\n            validator = $.data( element.form, \"validator\"),\n            cache = validator.arrayRulesCache;\n\n        // Is not an Array\n        if (element.name.indexOf('[') === -1) {\n            return rules;\n        }\n\n        if (! (element.name in cache)) {\n            cache[element.name] = {};\n        }\n\n        $.each(validator.settings.rules, function(name, tmpRules) {\n            if (name in cache[element.name]) {\n                rules = laravelValidation.helpers.mergeRules(rules, cache[element.name][name]);\n            } else {\n                cache[element.name][name] = {};\n\n                var nameRegExp = laravelValidation.helpers.regexFromWildcard(name);\n                if (element.name.match(nameRegExp)) {\n                    var newRules = $.validator.normalizeRule(tmpRules) || {};\n                    cache[element.name][name] = newRules;\n\n                    rules = laravelValidation.helpers.mergeRules(rules, newRules);\n                }\n            }\n        });\n\n        return rules;\n    },\n\n    setupValidations: function () {\n\n        /**\n         * Get CSRF token.\n         *\n         * @param params\n         * @returns {string}\n         */\n        var getCsrfToken = function (params) {\n            return params[0][1][1];\n        };\n\n        /**\n         * Whether to validate all attributes.\n         *\n         * @param params\n         * @returns {boolean}\n         */\n        var isValidateAll = function (params) {\n            return params[0][1][2];\n        };\n\n        /**\n         * Determine whether the rule is implicit.\n         *\n         * @param params\n         * @returns {boolean}\n         */\n        var isImplicit = function (params) {\n            var implicit = false;\n            $.each(params, function (i, parameters) {\n                implicit = implicit || parameters[3];\n            });\n\n            return implicit;\n        };\n\n        /**\n         * Get form method from a validator instance.\n         *\n         * @param validator\n         * @returns {string}\n         */\n        var formMethod = function (validator) {\n            var formMethod = $(validator.currentForm).attr('method');\n            if ($(validator.currentForm).find('input[name=\"_method\"]').length) {\n                formMethod = $(validator.currentForm).find('input[name=\"_method\"]').val();\n            }\n\n            return formMethod;\n        };\n\n        /**\n         * Get AJAX parameters for remote requests.\n         *\n         * @param validator\n         * @param element\n         * @param params\n         * @param data\n         * @returns {object}\n         */\n        var ajaxOpts = function (validator, element, params, data) {\n            return {\n                mode: 'abort',\n                port: 'validate' + element.name,\n                dataType: 'json',\n                data: data,\n                context: validator.currentForm,\n                url: $(validator.currentForm).attr('action'),\n                type: formMethod(validator),\n                beforeSend: function (xhr) {\n                    var token = getCsrfToken(params);\n                    if (formMethod(validator) !== 'get' && token) {\n                        return xhr.setRequestHeader('X-XSRF-TOKEN', token);\n                    }\n                },\n            };\n        };\n\n        /**\n         * Validate a set of local JS based rules against an element.\n         *\n         * @param validator\n         * @param values\n         * @param element\n         * @param rules\n         * @returns {boolean}\n         */\n        var validateLocalRules = function (validator, values, element, rules) {\n            var validated = true,\n                previous = validator.previousValue(element);\n\n            $.each(rules, function (i, param) {\n                var implicit = param[3] || laravelValidation.implicitRules.indexOf(param[0]) !== -1;\n                var rule = param[0];\n                var message = param[2];\n\n                if (! implicit && validator.optional(element)) {\n                    validated = \"dependency-mismatch\";\n                    return false;\n                }\n\n                if (laravelValidation.methods[rule] !== undefined) {\n                    $.each(values, function(index, value) {\n                        validated = laravelValidation.methods[rule].call(validator, value, element, param[1], function(valid) {\n                            validator.settings.messages[element.name].laravelValidationRemote = previous.originalMessage;\n                            if (valid) {\n                                var submitted = validator.formSubmitted;\n                                validator.prepareElement(element);\n                                validator.formSubmitted = submitted;\n                                validator.successList.push(element);\n                                delete validator.invalid[element.name];\n                                validator.showErrors();\n                            } else {\n                                var errors = {};\n                                errors[ element.name ]\n                                    = previous.message\n                                    = typeof message === \"function\" ? message( value ) : message;\n                                validator.invalid[element.name] = true;\n                                validator.showErrors(errors);\n                            }\n                            validator.showErrors(validator.errorMap);\n                            previous.valid = valid;\n                        });\n\n                        // Break loop.\n                        if (validated === false) {\n                            return false;\n                        }\n                    });\n                } else {\n                    validated = false;\n                }\n\n                if (validated !== true) {\n                    if (!validator.settings.messages[element.name] ) {\n                        validator.settings.messages[element.name] = {};\n                    }\n\n                    validator.settings.messages[element.name].laravelValidation= message;\n\n                    return false;\n                }\n\n            });\n\n            return validated;\n        };\n\n        /**\n         * Create JQueryValidation check to validate Laravel rules.\n         */\n\n        $.validator.addMethod(\"laravelValidation\", function (value, element, params) {\n            var rules = [],\n                arrayRules = [];\n            $.each(params, function (i, param) {\n                // put Implicit rules in front\n                var isArrayRule = param[4].indexOf('[') !== -1;\n                if (param[3] || laravelValidation.implicitRules.indexOf(param[0]) !== -1) {\n                    isArrayRule ? arrayRules.unshift(param) : rules.unshift(param);\n                } else {\n                    isArrayRule ? arrayRules.push(param) : rules.push(param);\n                }\n            });\n\n            // Validate normal rules.\n            var localRulesResult = validateLocalRules(this, [value], element, rules);\n\n            // Validate items of the array using array rules.\n            var arrayValue = ! Array.isArray(value) ? [value] : value;\n            var arrayRulesResult = validateLocalRules(this, arrayValue, element, arrayRules);\n\n            return localRulesResult && arrayRulesResult;\n        }, '');\n\n\n        /**\n         * Create JQueryValidation check to validate Remote Laravel rules.\n         */\n        $.validator.addMethod(\"laravelValidationRemote\", function (value, element, params) {\n\n            if (! isImplicit(params) && this.optional( element )) {\n                return \"dependency-mismatch\";\n            }\n\n            var previous = this.previousValue( element ),\n                validator, data;\n\n            if (! this.settings.messages[ element.name ]) {\n                this.settings.messages[ element.name ] = {};\n            }\n            previous.originalMessage = this.settings.messages[ element.name ].laravelValidationRemote;\n            this.settings.messages[ element.name ].laravelValidationRemote = previous.message;\n\n            if (laravelValidation.helpers.arrayEquals(previous.old, value) || previous.old === value) {\n                return previous.valid;\n            }\n\n            previous.old = value;\n            validator = this;\n            this.startRequest( element );\n\n            data = $(validator.currentForm).serializeArray();\n            data.push({'name': '_jsvalidation', 'value': element.name});\n            data.push({'name': '_jsvalidation_validate_all', 'value': isValidateAll(params)});\n\n            $.ajax( ajaxOpts(validator, element, params, data) )\n                .always(function( response, textStatus ) {\n                    var errors, message, submitted, valid;\n\n                    if (textStatus === 'error') {\n                        valid = false;\n                        response = laravelValidation.helpers.parseErrorResponse(response);\n                    } else if (textStatus === 'success') {\n                        valid = response === true || response === \"true\";\n                    } else {\n                        return;\n                    }\n\n                    validator.settings.messages[ element.name ].laravelValidationRemote = previous.originalMessage;\n\n                    if ( valid ) {\n                        submitted = validator.formSubmitted;\n                        validator.prepareElement( element );\n                        validator.formSubmitted = submitted;\n                        validator.successList.push( element );\n                        delete validator.invalid[ element.name ];\n                        validator.showErrors();\n                    } else {\n                        errors = {};\n                        message = response || validator.defaultMessage( element, \"remote\" );\n                        errors[ element.name ]\n                            = previous.message\n                            = typeof message === \"function\" ? message( value ) : message[0];\n                        validator.invalid[ element.name ] = true;\n                        validator.showErrors( errors );\n                    }\n                    validator.showErrors(validator.errorMap);\n                    previous.valid = valid;\n                    validator.stopRequest( element, valid );\n                }\n            );\n            return \"pending\";\n        }, '');\n\n        /**\n         * Create JQueryValidation check to form requests.\n         */\n        $.validator.addMethod(\"laravelValidationFormRequest\", function (value, element, params) {\n\n            var validator = this,\n                previous = validator.previousValue(element);\n\n            var data = $(validator.currentForm).serializeArray();\n            data.push({name: '__proengsoft_form_request', value: 1}); // must match FormRequest.JS_VALIDATION_FIELD\n\n            // Skip AJAX if the value remains the same as a prior request.\n            if (JSON.stringify(previous.old) === JSON.stringify(data)) {\n                if (! previous.valid) {\n                    validator.showErrors(previous.errors || {});\n                }\n\n                return previous.valid;\n            }\n\n            previous.old = data;\n            this.startRequest( element );\n\n            $.ajax(ajaxOpts(validator, element, params, data))\n                .always(function( response, textStatus ) {\n                    var errors = {},\n                        valid = textStatus === 'success' && (response === true || response === 'true');\n\n                    if (valid) {\n                        validator.resetInternals();\n                        validator.toHide = validator.errorsFor( element );\n                    } else {\n                        $.each( response, function( fieldName, errorMessages ) {\n                            var errorElement = laravelValidation.helpers.findByName(validator, fieldName)[0];\n                            if (errorElement) {\n                                errors[errorElement.name] = laravelValidation.helpers.encode(errorMessages[0] || '');\n                            }\n                        });\n\n                        // Failed to find the error fields so mark the form as valid otherwise user\n                        // will be left in limbo with no visible error messages.\n                        if ($.isEmptyObject(errors)) {\n                            valid = true;\n                        }\n                    }\n\n                    previous.valid = valid;\n                    previous.errors = errors;\n                    validator.showErrors(errors);\n                    validator.stopRequest(element, valid);\n                });\n\n            return 'pending';\n        }, '');\n    }\n};\n\n$(function() {\n    laravelValidation.init();\n});\n", "/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./node_modules/locutus/php/array/array_diff.js\":\n/*!******************************************************!*\\\n  !*** ./node_modules/locutus/php/array/array_diff.js ***!\n  \\******************************************************/\n/***/ (function(module) {\n\n\n\nmodule.exports = function array_diff(arr1) {\n  // eslint-disable-line camelcase\n  //  discuss at: https://locutus.io/php/array_diff/\n  // original by: <PERSON> (https://kvz.io)\n  // improved by: <PERSON><PERSON>\n  //  revised by: <PERSON> (https://brett-zamir.me)\n  //   example 1: array_diff(['<PERSON>', 'van', '<PERSON><PERSON><PERSON><PERSON><PERSON>'], ['van', '<PERSON><PERSON>neveld'])\n  //   returns 1: {0:'Kevin'}\n\n  var retArr = {};\n  var argl = arguments.length;\n  var k1 = '';\n  var i = 1;\n  var k = '';\n  var arr = {};\n\n  arr1keys: for (k1 in arr1) {\n    // eslint-disable-line no-labels\n    for (i = 1; i < argl; i++) {\n      arr = arguments[i];\n      for (k in arr) {\n        if (arr[k] === arr1[k1]) {\n          // If it reaches here, it was found in at least one array, so try next value\n          continue arr1keys; // eslint-disable-line no-labels\n        }\n      }\n      retArr[k1] = arr1[k1];\n    }\n  }\n\n  return retArr;\n};\n//# sourceMappingURL=array_diff.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/datetime/strtotime.js\":\n/*!********************************************************!*\\\n  !*** ./node_modules/locutus/php/datetime/strtotime.js ***!\n  \\********************************************************/\n/***/ (function(module) {\n\n\n\nvar reSpace = '[ \\\\t]+';\nvar reSpaceOpt = '[ \\\\t]*';\nvar reMeridian = '(?:([ap])\\\\.?m\\\\.?([\\\\t ]|$))';\nvar reHour24 = '(2[0-4]|[01]?[0-9])';\nvar reHour24lz = '([01][0-9]|2[0-4])';\nvar reHour12 = '(0?[1-9]|1[0-2])';\nvar reMinute = '([0-5]?[0-9])';\nvar reMinutelz = '([0-5][0-9])';\nvar reSecond = '(60|[0-5]?[0-9])';\nvar reSecondlz = '(60|[0-5][0-9])';\nvar reFrac = '(?:\\\\.([0-9]+))';\n\nvar reDayfull = 'sunday|monday|tuesday|wednesday|thursday|friday|saturday';\nvar reDayabbr = 'sun|mon|tue|wed|thu|fri|sat';\nvar reDaytext = reDayfull + '|' + reDayabbr + '|weekdays?';\n\nvar reReltextnumber = 'first|second|third|fourth|fifth|sixth|seventh|eighth?|ninth|tenth|eleventh|twelfth';\nvar reReltexttext = 'next|last|previous|this';\nvar reReltextunit = '(?:second|sec|minute|min|hour|day|fortnight|forthnight|month|year)s?|weeks|' + reDaytext;\n\nvar reYear = '([0-9]{1,4})';\nvar reYear2 = '([0-9]{2})';\nvar reYear4 = '([0-9]{4})';\nvar reYear4withSign = '([+-]?[0-9]{4})';\nvar reMonth = '(1[0-2]|0?[0-9])';\nvar reMonthlz = '(0[0-9]|1[0-2])';\nvar reDay = '(?:(3[01]|[0-2]?[0-9])(?:st|nd|rd|th)?)';\nvar reDaylz = '(0[0-9]|[1-2][0-9]|3[01])';\n\nvar reMonthFull = 'january|february|march|april|may|june|july|august|september|october|november|december';\nvar reMonthAbbr = 'jan|feb|mar|apr|may|jun|jul|aug|sept?|oct|nov|dec';\nvar reMonthroman = 'i[vx]|vi{0,3}|xi{0,2}|i{1,3}';\nvar reMonthText = '(' + reMonthFull + '|' + reMonthAbbr + '|' + reMonthroman + ')';\n\nvar reTzCorrection = '((?:GMT)?([+-])' + reHour24 + ':?' + reMinute + '?)';\nvar reTzAbbr = '\\\\(?([a-zA-Z]{1,6})\\\\)?';\nvar reDayOfYear = '(00[1-9]|0[1-9][0-9]|[12][0-9][0-9]|3[0-5][0-9]|36[0-6])';\nvar reWeekOfYear = '(0[1-9]|[1-4][0-9]|5[0-3])';\n\nvar reDateNoYear = reMonthText + '[ .\\\\t-]*' + reDay + '[,.stndrh\\\\t ]*';\n\nfunction processMeridian(hour, meridian) {\n  meridian = meridian && meridian.toLowerCase();\n\n  switch (meridian) {\n    case 'a':\n      hour += hour === 12 ? -12 : 0;\n      break;\n    case 'p':\n      hour += hour !== 12 ? 12 : 0;\n      break;\n  }\n\n  return hour;\n}\n\nfunction processYear(yearStr) {\n  var year = +yearStr;\n\n  if (yearStr.length < 4 && year < 100) {\n    year += year < 70 ? 2000 : 1900;\n  }\n\n  return year;\n}\n\nfunction lookupMonth(monthStr) {\n  return {\n    jan: 0,\n    january: 0,\n    i: 0,\n    feb: 1,\n    february: 1,\n    ii: 1,\n    mar: 2,\n    march: 2,\n    iii: 2,\n    apr: 3,\n    april: 3,\n    iv: 3,\n    may: 4,\n    v: 4,\n    jun: 5,\n    june: 5,\n    vi: 5,\n    jul: 6,\n    july: 6,\n    vii: 6,\n    aug: 7,\n    august: 7,\n    viii: 7,\n    sep: 8,\n    sept: 8,\n    september: 8,\n    ix: 8,\n    oct: 9,\n    october: 9,\n    x: 9,\n    nov: 10,\n    november: 10,\n    xi: 10,\n    dec: 11,\n    december: 11,\n    xii: 11\n  }[monthStr.toLowerCase()];\n}\n\nfunction lookupWeekday(dayStr) {\n  var desiredSundayNumber = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n  var dayNumbers = {\n    mon: 1,\n    monday: 1,\n    tue: 2,\n    tuesday: 2,\n    wed: 3,\n    wednesday: 3,\n    thu: 4,\n    thursday: 4,\n    fri: 5,\n    friday: 5,\n    sat: 6,\n    saturday: 6,\n    sun: 0,\n    sunday: 0\n  };\n\n  return dayNumbers[dayStr.toLowerCase()] || desiredSundayNumber;\n}\n\nfunction lookupRelative(relText) {\n  var relativeNumbers = {\n    last: -1,\n    previous: -1,\n    this: 0,\n    first: 1,\n    next: 1,\n    second: 2,\n    third: 3,\n    fourth: 4,\n    fifth: 5,\n    sixth: 6,\n    seventh: 7,\n    eight: 8,\n    eighth: 8,\n    ninth: 9,\n    tenth: 10,\n    eleventh: 11,\n    twelfth: 12\n  };\n\n  var relativeBehavior = {\n    this: 1\n  };\n\n  var relTextLower = relText.toLowerCase();\n\n  return {\n    amount: relativeNumbers[relTextLower],\n    behavior: relativeBehavior[relTextLower] || 0\n  };\n}\n\nfunction processTzCorrection(tzOffset, oldValue) {\n  var reTzCorrectionLoose = /(?:GMT)?([+-])(\\d+)(:?)(\\d{0,2})/i;\n  tzOffset = tzOffset && tzOffset.match(reTzCorrectionLoose);\n\n  if (!tzOffset) {\n    return oldValue;\n  }\n\n  var sign = tzOffset[1] === '-' ? -1 : 1;\n  var hours = +tzOffset[2];\n  var minutes = +tzOffset[4];\n\n  if (!tzOffset[4] && !tzOffset[3]) {\n    minutes = Math.floor(hours % 100);\n    hours = Math.floor(hours / 100);\n  }\n\n  // timezone offset in seconds\n  return sign * (hours * 60 + minutes) * 60;\n}\n\n// tz abbrevation : tz offset in seconds\nvar tzAbbrOffsets = {\n  acdt: 37800,\n  acst: 34200,\n  addt: -7200,\n  adt: -10800,\n  aedt: 39600,\n  aest: 36000,\n  ahdt: -32400,\n  ahst: -36000,\n  akdt: -28800,\n  akst: -32400,\n  amt: -13840,\n  apt: -10800,\n  ast: -14400,\n  awdt: 32400,\n  awst: 28800,\n  awt: -10800,\n  bdst: 7200,\n  bdt: -36000,\n  bmt: -14309,\n  bst: 3600,\n  cast: 34200,\n  cat: 7200,\n  cddt: -14400,\n  cdt: -18000,\n  cemt: 10800,\n  cest: 7200,\n  cet: 3600,\n  cmt: -15408,\n  cpt: -18000,\n  cst: -21600,\n  cwt: -18000,\n  chst: 36000,\n  dmt: -1521,\n  eat: 10800,\n  eddt: -10800,\n  edt: -14400,\n  eest: 10800,\n  eet: 7200,\n  emt: -26248,\n  ept: -14400,\n  est: -18000,\n  ewt: -14400,\n  ffmt: -14660,\n  fmt: -4056,\n  gdt: 39600,\n  gmt: 0,\n  gst: 36000,\n  hdt: -34200,\n  hkst: 32400,\n  hkt: 28800,\n  hmt: -19776,\n  hpt: -34200,\n  hst: -36000,\n  hwt: -34200,\n  iddt: 14400,\n  idt: 10800,\n  imt: 25025,\n  ist: 7200,\n  jdt: 36000,\n  jmt: 8440,\n  jst: 32400,\n  kdt: 36000,\n  kmt: 5736,\n  kst: 30600,\n  lst: 9394,\n  mddt: -18000,\n  mdst: 16279,\n  mdt: -21600,\n  mest: 7200,\n  met: 3600,\n  mmt: 9017,\n  mpt: -21600,\n  msd: 14400,\n  msk: 10800,\n  mst: -25200,\n  mwt: -21600,\n  nddt: -5400,\n  ndt: -9052,\n  npt: -9000,\n  nst: -12600,\n  nwt: -9000,\n  nzdt: 46800,\n  nzmt: 41400,\n  nzst: 43200,\n  pddt: -21600,\n  pdt: -25200,\n  pkst: 21600,\n  pkt: 18000,\n  plmt: 25590,\n  pmt: -13236,\n  ppmt: -17340,\n  ppt: -25200,\n  pst: -28800,\n  pwt: -25200,\n  qmt: -18840,\n  rmt: 5794,\n  sast: 7200,\n  sdmt: -16800,\n  sjmt: -20173,\n  smt: -13884,\n  sst: -39600,\n  tbmt: 10751,\n  tmt: 12344,\n  uct: 0,\n  utc: 0,\n  wast: 7200,\n  wat: 3600,\n  wemt: 7200,\n  west: 3600,\n  wet: 0,\n  wib: 25200,\n  wita: 28800,\n  wit: 32400,\n  wmt: 5040,\n  yddt: -25200,\n  ydt: -28800,\n  ypt: -28800,\n  yst: -32400,\n  ywt: -28800,\n  a: 3600,\n  b: 7200,\n  c: 10800,\n  d: 14400,\n  e: 18000,\n  f: 21600,\n  g: 25200,\n  h: 28800,\n  i: 32400,\n  k: 36000,\n  l: 39600,\n  m: 43200,\n  n: -3600,\n  o: -7200,\n  p: -10800,\n  q: -14400,\n  r: -18000,\n  s: -21600,\n  t: -25200,\n  u: -28800,\n  v: -32400,\n  w: -36000,\n  x: -39600,\n  y: -43200,\n  z: 0\n};\n\nvar formats = {\n  yesterday: {\n    regex: /^yesterday/i,\n    name: 'yesterday',\n    callback: function callback() {\n      this.rd -= 1;\n      return this.resetTime();\n    }\n  },\n\n  now: {\n    regex: /^now/i,\n    name: 'now'\n    // do nothing\n  },\n\n  noon: {\n    regex: /^noon/i,\n    name: 'noon',\n    callback: function callback() {\n      return this.resetTime() && this.time(12, 0, 0, 0);\n    }\n  },\n\n  midnightOrToday: {\n    regex: /^(midnight|today)/i,\n    name: 'midnight | today',\n    callback: function callback() {\n      return this.resetTime();\n    }\n  },\n\n  tomorrow: {\n    regex: /^tomorrow/i,\n    name: 'tomorrow',\n    callback: function callback() {\n      this.rd += 1;\n      return this.resetTime();\n    }\n  },\n\n  timestamp: {\n    regex: /^@(-?\\d+)/i,\n    name: 'timestamp',\n    callback: function callback(match, timestamp) {\n      this.rs += +timestamp;\n      this.y = 1970;\n      this.m = 0;\n      this.d = 1;\n      this.dates = 0;\n\n      return this.resetTime() && this.zone(0);\n    }\n  },\n\n  firstOrLastDay: {\n    regex: /^(first|last) day of/i,\n    name: 'firstdayof | lastdayof',\n    callback: function callback(match, day) {\n      if (day.toLowerCase() === 'first') {\n        this.firstOrLastDayOfMonth = 1;\n      } else {\n        this.firstOrLastDayOfMonth = -1;\n      }\n    }\n  },\n\n  backOrFrontOf: {\n    regex: RegExp('^(back|front) of ' + reHour24 + reSpaceOpt + reMeridian + '?', 'i'),\n    name: 'backof | frontof',\n    callback: function callback(match, side, hours, meridian) {\n      var back = side.toLowerCase() === 'back';\n      var hour = +hours;\n      var minute = 15;\n\n      if (!back) {\n        hour -= 1;\n        minute = 45;\n      }\n\n      hour = processMeridian(hour, meridian);\n\n      return this.resetTime() && this.time(hour, minute, 0, 0);\n    }\n  },\n\n  weekdayOf: {\n    regex: RegExp('^(' + reReltextnumber + '|' + reReltexttext + ')' + reSpace + '(' + reDayfull + '|' + reDayabbr + ')' + reSpace + 'of', 'i'),\n    name: 'weekdayof'\n    // todo\n  },\n\n  mssqltime: {\n    regex: RegExp('^' + reHour12 + ':' + reMinutelz + ':' + reSecondlz + '[:.]([0-9]+)' + reMeridian, 'i'),\n    name: 'mssqltime',\n    callback: function callback(match, hour, minute, second, frac, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, +second, +frac.substr(0, 3));\n    }\n  },\n\n  timeLong12: {\n    regex: RegExp('^' + reHour12 + '[:.]' + reMinute + '[:.]' + reSecondlz + reSpaceOpt + reMeridian, 'i'),\n    name: 'timelong12',\n    callback: function callback(match, hour, minute, second, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, +second, 0);\n    }\n  },\n\n  timeShort12: {\n    regex: RegExp('^' + reHour12 + '[:.]' + reMinutelz + reSpaceOpt + reMeridian, 'i'),\n    name: 'timeshort12',\n    callback: function callback(match, hour, minute, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, 0, 0);\n    }\n  },\n\n  timeTiny12: {\n    regex: RegExp('^' + reHour12 + reSpaceOpt + reMeridian, 'i'),\n    name: 'timetiny12',\n    callback: function callback(match, hour, meridian) {\n      return this.time(processMeridian(+hour, meridian), 0, 0, 0);\n    }\n  },\n\n  soap: {\n    regex: RegExp('^' + reYear4 + '-' + reMonthlz + '-' + reDaylz + 'T' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz + reFrac + reTzCorrection + '?', 'i'),\n    name: 'soap',\n    callback: function callback(match, year, month, day, hour, minute, second, frac, tzCorrection) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, +frac.substr(0, 3)) && this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  wddx: {\n    regex: RegExp('^' + reYear4 + '-' + reMonth + '-' + reDay + 'T' + reHour24 + ':' + reMinute + ':' + reSecond),\n    name: 'wddx',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  exif: {\n    regex: RegExp('^' + reYear4 + ':' + reMonthlz + ':' + reDaylz + ' ' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz, 'i'),\n    name: 'exif',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  xmlRpc: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz + 'T' + reHour24 + ':' + reMinutelz + ':' + reSecondlz),\n    name: 'xmlrpc',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  xmlRpcNoColon: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz + '[Tt]' + reHour24 + reMinutelz + reSecondlz),\n    name: 'xmlrpcnocolon',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  clf: {\n    regex: RegExp('^' + reDay + '/(' + reMonthAbbr + ')/' + reYear4 + ':' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz + reSpace + reTzCorrection, 'i'),\n    name: 'clf',\n    callback: function callback(match, day, month, year, hour, minute, second, tzCorrection) {\n      return this.ymd(+year, lookupMonth(month), +day) && this.time(+hour, +minute, +second, 0) && this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  iso8601long: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond + reFrac, 'i'),\n    name: 'iso8601long',\n    callback: function callback(match, hour, minute, second, frac) {\n      return this.time(+hour, +minute, +second, +frac.substr(0, 3));\n    }\n  },\n\n  dateTextual: {\n    regex: RegExp('^' + reMonthText + '[ .\\\\t-]*' + reDay + '[,.stndrh\\\\t ]+' + reYear, 'i'),\n    name: 'datetextual',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  pointedDate4: {\n    regex: RegExp('^' + reDay + '[.\\\\t-]' + reMonth + '[.-]' + reYear4),\n    name: 'pointeddate4',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  pointedDate2: {\n    regex: RegExp('^' + reDay + '[.\\\\t]' + reMonth + '\\\\.' + reYear2),\n    name: 'pointeddate2',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  timeLong24: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond),\n    name: 'timelong24',\n    callback: function callback(match, hour, minute, second) {\n      return this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  dateNoColon: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz),\n    name: 'datenocolon',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  pgydotd: {\n    regex: RegExp('^' + reYear4 + '\\\\.?' + reDayOfYear),\n    name: 'pgydotd',\n    callback: function callback(match, year, day) {\n      return this.ymd(+year, 0, +day);\n    }\n  },\n\n  timeShort24: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute, 'i'),\n    name: 'timeshort24',\n    callback: function callback(match, hour, minute) {\n      return this.time(+hour, +minute, 0, 0);\n    }\n  },\n\n  iso8601noColon: {\n    regex: RegExp('^t?' + reHour24lz + reMinutelz + reSecondlz, 'i'),\n    name: 'iso8601nocolon',\n    callback: function callback(match, hour, minute, second) {\n      return this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  iso8601dateSlash: {\n    // eventhough the trailing slash is optional in PHP\n    // here it's mandatory and inputs without the slash\n    // are handled by dateslash\n    regex: RegExp('^' + reYear4 + '/' + reMonthlz + '/' + reDaylz + '/'),\n    name: 'iso8601dateslash',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  dateSlash: {\n    regex: RegExp('^' + reYear4 + '/' + reMonth + '/' + reDay),\n    name: 'dateslash',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  american: {\n    regex: RegExp('^' + reMonth + '/' + reDay + '/' + reYear),\n    name: 'american',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  americanShort: {\n    regex: RegExp('^' + reMonth + '/' + reDay),\n    name: 'americanshort',\n    callback: function callback(match, month, day) {\n      return this.ymd(this.y, month - 1, +day);\n    }\n  },\n\n  gnuDateShortOrIso8601date2: {\n    // iso8601date2 is complete subset of gnudateshort\n    regex: RegExp('^' + reYear + '-' + reMonth + '-' + reDay),\n    name: 'gnudateshort | iso8601date2',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  iso8601date4: {\n    regex: RegExp('^' + reYear4withSign + '-' + reMonthlz + '-' + reDaylz),\n    name: 'iso8601date4',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  gnuNoColon: {\n    regex: RegExp('^t?' + reHour24lz + reMinutelz, 'i'),\n    name: 'gnunocolon',\n    callback: function callback(match, hour, minute) {\n      // this rule is a special case\n      // if time was already set once by any preceding rule, it sets the captured value as year\n      switch (this.times) {\n        case 0:\n          return this.time(+hour, +minute, 0, this.f);\n        case 1:\n          this.y = hour * 100 + +minute;\n          this.times++;\n\n          return true;\n        default:\n          return false;\n      }\n    }\n  },\n\n  gnuDateShorter: {\n    regex: RegExp('^' + reYear4 + '-' + reMonth),\n    name: 'gnudateshorter',\n    callback: function callback(match, year, month) {\n      return this.ymd(+year, month - 1, 1);\n    }\n  },\n\n  pgTextReverse: {\n    // note: allowed years are from 32-9999\n    // years below 32 should be treated as days in datefull\n    regex: RegExp('^' + '(\\\\d{3,4}|[4-9]\\\\d|3[2-9])-(' + reMonthAbbr + ')-' + reDaylz, 'i'),\n    name: 'pgtextreverse',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateFull: {\n    regex: RegExp('^' + reDay + '[ \\\\t.-]*' + reMonthText + '[ \\\\t.-]*' + reYear, 'i'),\n    name: 'datefull',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateNoDay: {\n    regex: RegExp('^' + reMonthText + '[ .\\\\t-]*' + reYear4, 'i'),\n    name: 'datenoday',\n    callback: function callback(match, month, year) {\n      return this.ymd(+year, lookupMonth(month), 1);\n    }\n  },\n\n  dateNoDayRev: {\n    regex: RegExp('^' + reYear4 + '[ .\\\\t-]*' + reMonthText, 'i'),\n    name: 'datenodayrev',\n    callback: function callback(match, year, month) {\n      return this.ymd(+year, lookupMonth(month), 1);\n    }\n  },\n\n  pgTextShort: {\n    regex: RegExp('^(' + reMonthAbbr + ')-' + reDaylz + '-' + reYear, 'i'),\n    name: 'pgtextshort',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateNoYear: {\n    regex: RegExp('^' + reDateNoYear, 'i'),\n    name: 'datenoyear',\n    callback: function callback(match, month, day) {\n      return this.ymd(this.y, lookupMonth(month), +day);\n    }\n  },\n\n  dateNoYearRev: {\n    regex: RegExp('^' + reDay + '[ .\\\\t-]*' + reMonthText, 'i'),\n    name: 'datenoyearrev',\n    callback: function callback(match, day, month) {\n      return this.ymd(this.y, lookupMonth(month), +day);\n    }\n  },\n\n  isoWeekDay: {\n    regex: RegExp('^' + reYear4 + '-?W' + reWeekOfYear + '(?:-?([0-7]))?'),\n    name: 'isoweekday | isoweek',\n    callback: function callback(match, year, week, day) {\n      day = day ? +day : 1;\n\n      if (!this.ymd(+year, 0, 1)) {\n        return false;\n      }\n\n      // get day of week for Jan 1st\n      var dayOfWeek = new Date(this.y, this.m, this.d).getDay();\n\n      // and use the day to figure out the offset for day 1 of week 1\n      dayOfWeek = 0 - (dayOfWeek > 4 ? dayOfWeek - 7 : dayOfWeek);\n\n      this.rd += dayOfWeek + (week - 1) * 7 + day;\n    }\n  },\n\n  relativeText: {\n    regex: RegExp('^(' + reReltextnumber + '|' + reReltexttext + ')' + reSpace + '(' + reReltextunit + ')', 'i'),\n    name: 'relativetext',\n    callback: function callback(match, relValue, relUnit) {\n      // todo: implement handling of 'this time-unit'\n      // eslint-disable-next-line no-unused-vars\n      var _lookupRelative = lookupRelative(relValue),\n          amount = _lookupRelative.amount,\n          behavior = _lookupRelative.behavior;\n\n      switch (relUnit.toLowerCase()) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n          this.rs += amount;\n          break;\n        case 'min':\n        case 'mins':\n        case 'minute':\n        case 'minutes':\n          this.ri += amount;\n          break;\n        case 'hour':\n        case 'hours':\n          this.rh += amount;\n          break;\n        case 'day':\n        case 'days':\n          this.rd += amount;\n          break;\n        case 'fortnight':\n        case 'fortnights':\n        case 'forthnight':\n        case 'forthnights':\n          this.rd += amount * 14;\n          break;\n        case 'week':\n        case 'weeks':\n          this.rd += amount * 7;\n          break;\n        case 'month':\n        case 'months':\n          this.rm += amount;\n          break;\n        case 'year':\n        case 'years':\n          this.ry += amount;\n          break;\n        case 'mon':case 'monday':\n        case 'tue':case 'tuesday':\n        case 'wed':case 'wednesday':\n        case 'thu':case 'thursday':\n        case 'fri':case 'friday':\n        case 'sat':case 'saturday':\n        case 'sun':case 'sunday':\n          this.resetTime();\n          this.weekday = lookupWeekday(relUnit, 7);\n          this.weekdayBehavior = 1;\n          this.rd += (amount > 0 ? amount - 1 : amount) * 7;\n          break;\n        case 'weekday':\n        case 'weekdays':\n          // todo\n          break;\n      }\n    }\n  },\n\n  relative: {\n    regex: RegExp('^([+-]*)[ \\\\t]*(\\\\d+)' + reSpaceOpt + '(' + reReltextunit + '|week)', 'i'),\n    name: 'relative',\n    callback: function callback(match, signs, relValue, relUnit) {\n      var minuses = signs.replace(/[^-]/g, '').length;\n\n      var amount = +relValue * Math.pow(-1, minuses);\n\n      switch (relUnit.toLowerCase()) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n          this.rs += amount;\n          break;\n        case 'min':\n        case 'mins':\n        case 'minute':\n        case 'minutes':\n          this.ri += amount;\n          break;\n        case 'hour':\n        case 'hours':\n          this.rh += amount;\n          break;\n        case 'day':\n        case 'days':\n          this.rd += amount;\n          break;\n        case 'fortnight':\n        case 'fortnights':\n        case 'forthnight':\n        case 'forthnights':\n          this.rd += amount * 14;\n          break;\n        case 'week':\n        case 'weeks':\n          this.rd += amount * 7;\n          break;\n        case 'month':\n        case 'months':\n          this.rm += amount;\n          break;\n        case 'year':\n        case 'years':\n          this.ry += amount;\n          break;\n        case 'mon':case 'monday':\n        case 'tue':case 'tuesday':\n        case 'wed':case 'wednesday':\n        case 'thu':case 'thursday':\n        case 'fri':case 'friday':\n        case 'sat':case 'saturday':\n        case 'sun':case 'sunday':\n          this.resetTime();\n          this.weekday = lookupWeekday(relUnit, 7);\n          this.weekdayBehavior = 1;\n          this.rd += (amount > 0 ? amount - 1 : amount) * 7;\n          break;\n        case 'weekday':\n        case 'weekdays':\n          // todo\n          break;\n      }\n    }\n  },\n\n  dayText: {\n    regex: RegExp('^(' + reDaytext + ')', 'i'),\n    name: 'daytext',\n    callback: function callback(match, dayText) {\n      this.resetTime();\n      this.weekday = lookupWeekday(dayText, 0);\n\n      if (this.weekdayBehavior !== 2) {\n        this.weekdayBehavior = 1;\n      }\n    }\n  },\n\n  relativeTextWeek: {\n    regex: RegExp('^(' + reReltexttext + ')' + reSpace + 'week', 'i'),\n    name: 'relativetextweek',\n    callback: function callback(match, relText) {\n      this.weekdayBehavior = 2;\n\n      switch (relText.toLowerCase()) {\n        case 'this':\n          this.rd += 0;\n          break;\n        case 'next':\n          this.rd += 7;\n          break;\n        case 'last':\n        case 'previous':\n          this.rd -= 7;\n          break;\n      }\n\n      if (isNaN(this.weekday)) {\n        this.weekday = 1;\n      }\n    }\n  },\n\n  monthFullOrMonthAbbr: {\n    regex: RegExp('^(' + reMonthFull + '|' + reMonthAbbr + ')', 'i'),\n    name: 'monthfull | monthabbr',\n    callback: function callback(match, month) {\n      return this.ymd(this.y, lookupMonth(month), this.d);\n    }\n  },\n\n  tzCorrection: {\n    regex: RegExp('^' + reTzCorrection, 'i'),\n    name: 'tzcorrection',\n    callback: function callback(tzCorrection) {\n      return this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  tzAbbr: {\n    regex: RegExp('^' + reTzAbbr),\n    name: 'tzabbr',\n    callback: function callback(match, abbr) {\n      var offset = tzAbbrOffsets[abbr.toLowerCase()];\n\n      if (isNaN(offset)) {\n        return false;\n      }\n\n      return this.zone(offset);\n    }\n  },\n\n  ago: {\n    regex: /^ago/i,\n    name: 'ago',\n    callback: function callback() {\n      this.ry = -this.ry;\n      this.rm = -this.rm;\n      this.rd = -this.rd;\n      this.rh = -this.rh;\n      this.ri = -this.ri;\n      this.rs = -this.rs;\n      this.rf = -this.rf;\n    }\n  },\n\n  year4: {\n    regex: RegExp('^' + reYear4),\n    name: 'year4',\n    callback: function callback(match, year) {\n      this.y = +year;\n      return true;\n    }\n  },\n\n  whitespace: {\n    regex: /^[ .,\\t]+/,\n    name: 'whitespace'\n    // do nothing\n  },\n\n  dateShortWithTimeLong: {\n    regex: RegExp('^' + reDateNoYear + 't?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond, 'i'),\n    name: 'dateshortwithtimelong',\n    callback: function callback(match, month, day, hour, minute, second) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  dateShortWithTimeLong12: {\n    regex: RegExp('^' + reDateNoYear + reHour12 + '[:.]' + reMinute + '[:.]' + reSecondlz + reSpaceOpt + reMeridian, 'i'),\n    name: 'dateshortwithtimelong12',\n    callback: function callback(match, month, day, hour, minute, second, meridian) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(processMeridian(+hour, meridian), +minute, +second, 0);\n    }\n  },\n\n  dateShortWithTimeShort: {\n    regex: RegExp('^' + reDateNoYear + 't?' + reHour24 + '[:.]' + reMinute, 'i'),\n    name: 'dateshortwithtimeshort',\n    callback: function callback(match, month, day, hour, minute) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(+hour, +minute, 0, 0);\n    }\n  },\n\n  dateShortWithTimeShort12: {\n    regex: RegExp('^' + reDateNoYear + reHour12 + '[:.]' + reMinutelz + reSpaceOpt + reMeridian, 'i'),\n    name: 'dateshortwithtimeshort12',\n    callback: function callback(match, month, day, hour, minute, meridian) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(processMeridian(+hour, meridian), +minute, 0, 0);\n    }\n  }\n};\n\nvar resultProto = {\n  // date\n  y: NaN,\n  m: NaN,\n  d: NaN,\n  // time\n  h: NaN,\n  i: NaN,\n  s: NaN,\n  f: NaN,\n\n  // relative shifts\n  ry: 0,\n  rm: 0,\n  rd: 0,\n  rh: 0,\n  ri: 0,\n  rs: 0,\n  rf: 0,\n\n  // weekday related shifts\n  weekday: NaN,\n  weekdayBehavior: 0,\n\n  // first or last day of month\n  // 0 none, 1 first, -1 last\n  firstOrLastDayOfMonth: 0,\n\n  // timezone correction in minutes\n  z: NaN,\n\n  // counters\n  dates: 0,\n  times: 0,\n  zones: 0,\n\n  // helper functions\n  ymd: function ymd(y, m, d) {\n    if (this.dates > 0) {\n      return false;\n    }\n\n    this.dates++;\n    this.y = y;\n    this.m = m;\n    this.d = d;\n    return true;\n  },\n  time: function time(h, i, s, f) {\n    if (this.times > 0) {\n      return false;\n    }\n\n    this.times++;\n    this.h = h;\n    this.i = i;\n    this.s = s;\n    this.f = f;\n\n    return true;\n  },\n  resetTime: function resetTime() {\n    this.h = 0;\n    this.i = 0;\n    this.s = 0;\n    this.f = 0;\n    this.times = 0;\n\n    return true;\n  },\n  zone: function zone(minutes) {\n    if (this.zones <= 1) {\n      this.zones++;\n      this.z = minutes;\n      return true;\n    }\n\n    return false;\n  },\n  toDate: function toDate(relativeTo) {\n    if (this.dates && !this.times) {\n      this.h = this.i = this.s = this.f = 0;\n    }\n\n    // fill holes\n    if (isNaN(this.y)) {\n      this.y = relativeTo.getFullYear();\n    }\n\n    if (isNaN(this.m)) {\n      this.m = relativeTo.getMonth();\n    }\n\n    if (isNaN(this.d)) {\n      this.d = relativeTo.getDate();\n    }\n\n    if (isNaN(this.h)) {\n      this.h = relativeTo.getHours();\n    }\n\n    if (isNaN(this.i)) {\n      this.i = relativeTo.getMinutes();\n    }\n\n    if (isNaN(this.s)) {\n      this.s = relativeTo.getSeconds();\n    }\n\n    if (isNaN(this.f)) {\n      this.f = relativeTo.getMilliseconds();\n    }\n\n    // adjust special early\n    switch (this.firstOrLastDayOfMonth) {\n      case 1:\n        this.d = 1;\n        break;\n      case -1:\n        this.d = 0;\n        this.m += 1;\n        break;\n    }\n\n    if (!isNaN(this.weekday)) {\n      var date = new Date(relativeTo.getTime());\n      date.setFullYear(this.y, this.m, this.d);\n      date.setHours(this.h, this.i, this.s, this.f);\n\n      var dow = date.getDay();\n\n      if (this.weekdayBehavior === 2) {\n        // To make \"this week\" work, where the current day of week is a \"sunday\"\n        if (dow === 0 && this.weekday !== 0) {\n          this.weekday = -6;\n        }\n\n        // To make \"sunday this week\" work, where the current day of week is not a \"sunday\"\n        if (this.weekday === 0 && dow !== 0) {\n          this.weekday = 7;\n        }\n\n        this.d -= dow;\n        this.d += this.weekday;\n      } else {\n        var diff = this.weekday - dow;\n\n        // some PHP magic\n        if (this.rd < 0 && diff < 0 || this.rd >= 0 && diff <= -this.weekdayBehavior) {\n          diff += 7;\n        }\n\n        if (this.weekday >= 0) {\n          this.d += diff;\n        } else {\n          this.d -= 7 - (Math.abs(this.weekday) - dow);\n        }\n\n        this.weekday = NaN;\n      }\n    }\n\n    // adjust relative\n    this.y += this.ry;\n    this.m += this.rm;\n    this.d += this.rd;\n\n    this.h += this.rh;\n    this.i += this.ri;\n    this.s += this.rs;\n    this.f += this.rf;\n\n    this.ry = this.rm = this.rd = 0;\n    this.rh = this.ri = this.rs = this.rf = 0;\n\n    var result = new Date(relativeTo.getTime());\n    // since Date constructor treats years <= 99 as 1900+\n    // it can't be used, thus this weird way\n    result.setFullYear(this.y, this.m, this.d);\n    result.setHours(this.h, this.i, this.s, this.f);\n\n    // note: this is done twice in PHP\n    // early when processing special relatives\n    // and late\n    // todo: check if the logic can be reduced\n    // to just one time action\n    switch (this.firstOrLastDayOfMonth) {\n      case 1:\n        result.setDate(1);\n        break;\n      case -1:\n        result.setMonth(result.getMonth() + 1, 0);\n        break;\n    }\n\n    // adjust timezone\n    if (!isNaN(this.z) && result.getTimezoneOffset() !== this.z) {\n      result.setUTCFullYear(result.getFullYear(), result.getMonth(), result.getDate());\n\n      result.setUTCHours(result.getHours(), result.getMinutes(), result.getSeconds() - this.z, result.getMilliseconds());\n    }\n\n    return result;\n  }\n};\n\nmodule.exports = function strtotime(str, now) {\n  //       discuss at: https://locutus.io/php/strtotime/\n  //      original by: Caio Ariede (https://caioariede.com)\n  //      improved by: Kevin van Zonneveld (https://kvz.io)\n  //      improved by: Caio Ariede (https://caioariede.com)\n  //      improved by: A. Matías Quezada (https://amatiasq.com)\n  //      improved by: preuter\n  //      improved by: Brett Zamir (https://brett-zamir.me)\n  //      improved by: Mirko Faber\n  //         input by: David\n  //      bugfixed by: Wagner B. Soares\n  //      bugfixed by: Artur Tchernychev\n  //      bugfixed by: Stephan Bösch-Plepelits (https://github.com/plepe)\n  // reimplemented by: Rafał Kukawski\n  //           note 1: Examples all have a fixed timestamp to prevent\n  //           note 1: tests to fail because of variable time(zones)\n  //        example 1: strtotime('+1 day', 1129633200)\n  //        returns 1: 1129719600\n  //        example 2: strtotime('+1 week 2 days 4 hours 2 seconds', 1129633200)\n  //        returns 2: 1130425202\n  //        example 3: strtotime('last month', 1129633200)\n  //        returns 3: 1127041200\n  //        example 4: strtotime('2009-05-04 08:30:00+00')\n  //        returns 4: 1241425800\n  //        example 5: strtotime('2009-05-04 08:30:00+02:00')\n  //        returns 5: 1241418600\n  //        example 6: strtotime('2009-05-04 08:30:00 YWT')\n  //        returns 6: 1241454600\n\n  if (now == null) {\n    now = Math.floor(Date.now() / 1000);\n  }\n\n  // the rule order is important\n  // if multiple rules match, the longest match wins\n  // if multiple rules match the same string, the first match wins\n  var rules = [formats.yesterday, formats.now, formats.noon, formats.midnightOrToday, formats.tomorrow, formats.timestamp, formats.firstOrLastDay, formats.backOrFrontOf,\n  // formats.weekdayOf, // not yet implemented\n  formats.timeTiny12, formats.timeShort12, formats.timeLong12, formats.mssqltime, formats.timeShort24, formats.timeLong24, formats.iso8601long, formats.gnuNoColon, formats.iso8601noColon, formats.americanShort, formats.american, formats.iso8601date4, formats.iso8601dateSlash, formats.dateSlash, formats.gnuDateShortOrIso8601date2, formats.gnuDateShorter, formats.dateFull, formats.pointedDate4, formats.pointedDate2, formats.dateNoDay, formats.dateNoDayRev, formats.dateTextual, formats.dateNoYear, formats.dateNoYearRev, formats.dateNoColon, formats.xmlRpc, formats.xmlRpcNoColon, formats.soap, formats.wddx, formats.exif, formats.pgydotd, formats.isoWeekDay, formats.pgTextShort, formats.pgTextReverse, formats.clf, formats.year4, formats.ago, formats.dayText, formats.relativeTextWeek, formats.relativeText, formats.monthFullOrMonthAbbr, formats.tzCorrection, formats.tzAbbr, formats.dateShortWithTimeShort12, formats.dateShortWithTimeLong12, formats.dateShortWithTimeShort, formats.dateShortWithTimeLong, formats.relative, formats.whitespace];\n\n  var result = Object.create(resultProto);\n\n  while (str.length) {\n    var longestMatch = null;\n    var finalRule = null;\n\n    for (var i = 0, l = rules.length; i < l; i++) {\n      var format = rules[i];\n\n      var match = str.match(format.regex);\n\n      if (match) {\n        if (!longestMatch || match[0].length > longestMatch[0].length) {\n          longestMatch = match;\n          finalRule = format;\n        }\n      }\n    }\n\n    if (!finalRule || finalRule.callback && finalRule.callback.apply(result, longestMatch) === false) {\n      return false;\n    }\n\n    str = str.substr(longestMatch[0].length);\n    finalRule = null;\n    longestMatch = null;\n  }\n\n  return Math.floor(result.toDate(new Date(now * 1000)) / 1000);\n};\n//# sourceMappingURL=strtotime.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/info/ini_get.js\":\n/*!**************************************************!*\\\n  !*** ./node_modules/locutus/php/info/ini_get.js ***!\n  \\**************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n\n\nmodule.exports = function ini_get(varname) {\n  // eslint-disable-line camelcase\n  //  discuss at: https://locutus.io/php/ini_get/\n  // original by: Brett Zamir (https://brett-zamir.me)\n  //      note 1: The ini values must be set by ini_set or manually within an ini file\n  //   example 1: ini_set('date.timezone', 'Asia/Hong_Kong')\n  //   example 1: ini_get('date.timezone')\n  //   returns 1: 'Asia/Hong_Kong'\n\n  var $global = typeof window !== 'undefined' ? window : __webpack_require__.g;\n  $global.$locutus = $global.$locutus || {};\n  var $locutus = $global.$locutus;\n  $locutus.php = $locutus.php || {};\n  $locutus.php.ini = $locutus.php.ini || {};\n\n  if ($locutus.php.ini[varname] && $locutus.php.ini[varname].local_value !== undefined) {\n    if ($locutus.php.ini[varname].local_value === null) {\n      return '';\n    }\n    return $locutus.php.ini[varname].local_value;\n  }\n\n  return '';\n};\n//# sourceMappingURL=ini_get.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/strings/strlen.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/locutus/php/strings/strlen.js ***!\n  \\****************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n\n\nmodule.exports = function strlen(string) {\n  //  discuss at: https://locutus.io/php/strlen/\n  // original by: Kevin van Zonneveld (https://kvz.io)\n  // improved by: Sakimori\n  // improved by: Kevin van Zonneveld (https://kvz.io)\n  //    input by: Kirk Strobeck\n  // bugfixed by: Onno Marsman (https://twitter.com/onnomarsman)\n  //  revised by: Brett Zamir (https://brett-zamir.me)\n  //      note 1: May look like overkill, but in order to be truly faithful to handling all Unicode\n  //      note 1: characters and to this function in PHP which does not count the number of bytes\n  //      note 1: but counts the number of characters, something like this is really necessary.\n  //   example 1: strlen('Kevin van Zonneveld')\n  //   returns 1: 19\n  //   example 2: ini_set('unicode.semantics', 'on')\n  //   example 2: strlen('A\\ud87e\\udc04Z')\n  //   returns 2: 3\n\n  var str = string + '';\n\n  var iniVal = ( true ? __webpack_require__(/*! ../info/ini_get */ \"./node_modules/locutus/php/info/ini_get.js\")('unicode.semantics') : 0) || 'off';\n  if (iniVal === 'off') {\n    return str.length;\n  }\n\n  var i = 0;\n  var lgth = 0;\n\n  var getWholeChar = function getWholeChar(str, i) {\n    var code = str.charCodeAt(i);\n    var next = '';\n    var prev = '';\n    if (code >= 0xD800 && code <= 0xDBFF) {\n      // High surrogate (could change last hex to 0xDB7F to\n      // treat high private surrogates as single characters)\n      if (str.length <= i + 1) {\n        throw new Error('High surrogate without following low surrogate');\n      }\n      next = str.charCodeAt(i + 1);\n      if (next < 0xDC00 || next > 0xDFFF) {\n        throw new Error('High surrogate without following low surrogate');\n      }\n      return str.charAt(i) + str.charAt(i + 1);\n    } else if (code >= 0xDC00 && code <= 0xDFFF) {\n      // Low surrogate\n      if (i === 0) {\n        throw new Error('Low surrogate without preceding high surrogate');\n      }\n      prev = str.charCodeAt(i - 1);\n      if (prev < 0xD800 || prev > 0xDBFF) {\n        // (could change last hex to 0xDB7F to treat high private surrogates\n        // as single characters)\n        throw new Error('Low surrogate without preceding high surrogate');\n      }\n      // We can pass over low surrogates now as the second\n      // component in a pair which we have already processed\n      return false;\n    }\n    return str.charAt(i);\n  };\n\n  for (i = 0, lgth = 0; i < str.length; i++) {\n    if (getWholeChar(str, i) === false) {\n      continue;\n    }\n    // Adapt this line at the top of any loop, passing in the whole string and\n    // the current iteration and returning a variable to represent the individual character;\n    // purpose is to treat the first part of a surrogate pair as the whole character and then\n    // ignore the second part\n    lgth++;\n  }\n\n  return lgth;\n};\n//# sourceMappingURL=strlen.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/var/is_numeric.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/locutus/php/var/is_numeric.js ***!\n  \\****************************************************/\n/***/ (function(module) {\n\n\n\nmodule.exports = function is_numeric(mixedVar) {\n  // eslint-disable-line camelcase\n  //  discuss at: https://locutus.io/php/is_numeric/\n  // original by: Kevin van Zonneveld (https://kvz.io)\n  // improved by: David\n  // improved by: taith\n  // bugfixed by: Tim de Koning\n  // bugfixed by: WebDevHobo (https://webdevhobo.blogspot.com/)\n  // bugfixed by: Brett Zamir (https://brett-zamir.me)\n  // bugfixed by: Denis Chenu (https://shnoulle.net)\n  //   example 1: is_numeric(186.31)\n  //   returns 1: true\n  //   example 2: is_numeric('Kevin van Zonneveld')\n  //   returns 2: false\n  //   example 3: is_numeric(' +186.31e2')\n  //   returns 3: true\n  //   example 4: is_numeric('')\n  //   returns 4: false\n  //   example 5: is_numeric([])\n  //   returns 5: false\n  //   example 6: is_numeric('1 ')\n  //   returns 6: false\n\n  var whitespace = [' ', '\\n', '\\r', '\\t', '\\f', '\\x0b', '\\xa0', '\\u2000', '\\u2001', '\\u2002', '\\u2003', '\\u2004', '\\u2005', '\\u2006', '\\u2007', '\\u2008', '\\u2009', '\\u200A', '\\u200B', '\\u2028', '\\u2029', '\\u3000'].join('');\n\n  // @todo: Break this up using many single conditions with early returns\n  return (typeof mixedVar === 'number' || typeof mixedVar === 'string' && whitespace.indexOf(mixedVar.slice(-1)) === -1) && mixedVar !== '' && !isNaN(mixedVar);\n};\n//# sourceMappingURL=is_numeric.js.map\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/global */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.g = (function() {\n/******/ \t\t\tif (typeof globalThis === 'object') return globalThis;\n/******/ \t\t\ttry {\n/******/ \t\t\t\treturn this || new Function('return this')();\n/******/ \t\t\t} catch (e) {\n/******/ \t\t\t\tif (typeof window === 'object') return window;\n/******/ \t\t\t}\n/******/ \t\t})();\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\n/*!****************************************!*\\\n  !*** ./resources/assets/js/helpers.js ***!\n  \\****************************************/\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! locutus/php/strings/strlen */ \"./node_modules/locutus/php/strings/strlen.js\");\n/* harmony import */ var locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! locutus/php/array/array_diff */ \"./node_modules/locutus/php/array/array_diff.js\");\n/* harmony import */ var locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! locutus/php/datetime/strtotime */ \"./node_modules/locutus/php/datetime/strtotime.js\");\n/* harmony import */ var locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! locutus/php/var/is_numeric */ \"./node_modules/locutus/php/var/is_numeric.js\");\n/* harmony import */ var locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3__);\n/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Helper functions used by validators\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n\n\n\n$.extend(true, laravelValidation, {\n  helpers: {\n    /**\n     * Numeric rules\n     */\n    numericRules: ['Integer', 'Numeric'],\n\n    /**\n     * Gets the file information from file input.\n     *\n     * @param fieldObj\n     * @param index\n     * @returns {{file: *, extension: string, size: number}}\n     */\n    fileinfo: function (fieldObj, index) {\n      var FileName = fieldObj.value;\n      index = typeof index !== 'undefined' ? index : 0;\n\n      if (fieldObj.files !== null) {\n        if (typeof fieldObj.files[index] !== 'undefined') {\n          return {\n            file: FileName,\n            extension: FileName.substr(FileName.lastIndexOf('.') + 1),\n            size: fieldObj.files[index].size / 1024,\n            type: fieldObj.files[index].type\n          };\n        }\n      }\n\n      return false;\n    },\n\n    /**\n     * Gets the selectors for th specified field names.\n     *\n     * @param names\n     * @returns {string}\n     */\n    selector: function (names) {\n      var selector = [];\n\n      if (!this.isArray(names)) {\n        names = [names];\n      }\n\n      for (var i = 0; i < names.length; i++) {\n        selector.push(\"[name='\" + names[i] + \"']\");\n      }\n\n      return selector.join();\n    },\n\n    /**\n     * Check if element has numeric rules.\n     *\n     * @param element\n     * @returns {boolean}\n     */\n    hasNumericRules: function (element) {\n      return this.hasRules(element, this.numericRules);\n    },\n\n    /**\n     * Check if element has passed rules.\n     *\n     * @param element\n     * @param rules\n     * @returns {boolean}\n     */\n    hasRules: function (element, rules) {\n      var found = false;\n\n      if (typeof rules === 'string') {\n        rules = [rules];\n      }\n\n      var validator = $.data(element.form, \"validator\");\n      var listRules = [];\n      var cache = validator.arrayRulesCache;\n\n      if (element.name in cache) {\n        $.each(cache[element.name], function (index, arrayRule) {\n          listRules.push(arrayRule);\n        });\n      }\n\n      if (element.name in validator.settings.rules) {\n        listRules.push(validator.settings.rules[element.name]);\n      }\n\n      $.each(listRules, function (index, objRules) {\n        if ('laravelValidation' in objRules) {\n          var _rules = objRules.laravelValidation;\n\n          for (var i = 0; i < _rules.length; i++) {\n            if ($.inArray(_rules[i][0], rules) !== -1) {\n              found = true;\n              return false;\n            }\n          }\n        }\n      });\n      return found;\n    },\n\n    /**\n     * Return the string length using PHP function.\n     * http://php.net/manual/en/function.strlen.php\n     * http://phpjs.org/functions/strlen/\n     *\n     * @param string\n     */\n    strlen: function (string) {\n      return locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0___default()(string);\n    },\n\n    /**\n     * Get the size of the object depending of his type.\n     *\n     * @param obj\n     * @param element\n     * @param value\n     * @returns int\n     */\n    getSize: function getSize(obj, element, value) {\n      if (this.hasNumericRules(element) && this.is_numeric(value)) {\n        return parseFloat(value);\n      } else if (this.isArray(value)) {\n        return parseFloat(value.length);\n      } else if (element.type === 'file') {\n        return parseFloat(Math.floor(this.fileinfo(element).size));\n      }\n\n      return parseFloat(this.strlen(value));\n    },\n\n    /**\n     * Return specified rule from element.\n     *\n     * @param rule\n     * @param element\n     * @returns object\n     */\n    getLaravelValidation: function (rule, element) {\n      var found = undefined;\n      $.each($.validator.staticRules(element), function (key, rules) {\n        if (key === \"laravelValidation\") {\n          $.each(rules, function (i, value) {\n            if (value[0] === rule) {\n              found = value;\n            }\n          });\n        }\n      });\n      return found;\n    },\n\n    /**\n     * Return he timestamp of value passed using format or default format in element.\n     *\n     * @param value\n     * @param format\n     * @returns {boolean|int}\n     */\n    parseTime: function (value, format) {\n      var timeValue = false;\n      var fmt = new DateFormatter();\n\n      if (typeof value === 'number' && typeof format === 'undefined') {\n        return value;\n      }\n\n      if (typeof format === 'object') {\n        var dateRule = this.getLaravelValidation('DateFormat', format);\n\n        if (dateRule !== undefined) {\n          format = dateRule[1][0];\n        } else {\n          format = null;\n        }\n      }\n\n      if (format == null) {\n        timeValue = this.strtotime(value);\n      } else {\n        timeValue = fmt.parseDate(value, format);\n\n        if (timeValue instanceof Date && fmt.formatDate(timeValue, format) === value) {\n          timeValue = Math.round(timeValue.getTime() / 1000);\n        } else {\n          timeValue = false;\n        }\n      }\n\n      return timeValue;\n    },\n\n    /**\n     * Compare a given date against another using an operator.\n     *\n     * @param validator\n     * @param value\n     * @param element\n     * @param params\n     * @param operator\n     * @return {boolean}\n     */\n    compareDates: function (validator, value, element, params, operator) {\n      var timeCompare = this.parseTime(params);\n\n      if (!timeCompare) {\n        var target = this.dependentElement(validator, element, params);\n\n        if (target === undefined) {\n          return false;\n        }\n\n        timeCompare = this.parseTime(validator.elementValue(target), target);\n      }\n\n      var timeValue = this.parseTime(value, element);\n\n      if (timeValue === false) {\n        return false;\n      }\n\n      switch (operator) {\n        case '<':\n          return timeValue < timeCompare;\n\n        case '<=':\n          return timeValue <= timeCompare;\n\n        case '==':\n        case '===':\n          return timeValue === timeCompare;\n\n        case '>':\n          return timeValue > timeCompare;\n\n        case '>=':\n          return timeValue >= timeCompare;\n\n        default:\n          throw new Error('Unsupported operator.');\n      }\n    },\n\n    /**\n     * This method allows you to intelligently guess the date by closely matching the specific format.\n     *\n     * @param value\n     * @param format\n     * @returns {Date}\n     */\n    guessDate: function (value, format) {\n      var fmt = new DateFormatter();\n      return fmt.guessDate(value, format);\n    },\n\n    /**\n     * Returns Unix timestamp based on PHP function strototime.\n     * http://php.net/manual/es/function.strtotime.php\n     * http://phpjs.org/functions/strtotime/\n     *\n     * @param text\n     * @param now\n     * @returns {*}\n     */\n    strtotime: function (text, now) {\n      return locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2___default()(text, now);\n    },\n\n    /**\n     * Returns if value is numeric.\n     * http://php.net/manual/es/var.is_numeric.php\n     * http://phpjs.org/functions/is_numeric/\n     *\n     * @param mixed_var\n     * @returns {*}\n     */\n    is_numeric: function (mixed_var) {\n      return locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3___default()(mixed_var);\n    },\n\n    /**\n     * Check whether the argument is of type Array.\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray#Polyfill\n     *\n     * @param arg\n     * @returns {boolean}\n     */\n    isArray: function (arg) {\n      return Object.prototype.toString.call(arg) === '[object Array]';\n    },\n\n    /**\n     * Returns Array diff based on PHP function array_diff.\n     * http://php.net/manual/es/function.array_diff.php\n     * http://phpjs.org/functions/array_diff/\n     *\n     * @param arr1\n     * @param arr2\n     * @returns {*}\n     */\n    arrayDiff: function (arr1, arr2) {\n      return locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1___default()(arr1, arr2);\n    },\n\n    /**\n     * Check whether two arrays are equal to one another.\n     *\n     * @param arr1\n     * @param arr2\n     * @returns {*}\n     */\n    arrayEquals: function (arr1, arr2) {\n      if (!this.isArray(arr1) || !this.isArray(arr2)) {\n        return false;\n      }\n\n      if (arr1.length !== arr2.length) {\n        return false;\n      }\n\n      return $.isEmptyObject(this.arrayDiff(arr1, arr2));\n    },\n\n    /**\n     * Makes element dependant from other.\n     *\n     * @param validator\n     * @param element\n     * @param name\n     * @returns {*}\n     */\n    dependentElement: function (validator, element, name) {\n      var el = validator.findByName(name);\n\n      if (el[0] !== undefined && validator.settings.onfocusout) {\n        var event = 'blur';\n\n        if (el[0].tagName === 'SELECT' || el[0].tagName === 'OPTION' || el[0].type === 'checkbox' || el[0].type === 'radio') {\n          event = 'click';\n        }\n\n        var ruleName = '.validate-laravelValidation';\n        el.off(ruleName).off(event + ruleName + '-' + element.name).on(event + ruleName + '-' + element.name, function () {\n          $(element).valid();\n        });\n      }\n\n      return el[0];\n    },\n\n    /**\n     * Parses error Ajax response and gets the message.\n     *\n     * @param response\n     * @returns {string[]}\n     */\n    parseErrorResponse: function (response) {\n      var newResponse = ['Whoops, looks like something went wrong.'];\n\n      if ('responseText' in response) {\n        var errorMsg = response.responseText.match(/<h1\\s*>(.*)<\\/h1\\s*>/i);\n\n        if (this.isArray(errorMsg)) {\n          newResponse = [errorMsg[1]];\n        }\n      }\n\n      return newResponse;\n    },\n\n    /**\n     * Escape string to use as Regular Expression.\n     *\n     * @param str\n     * @returns string\n     */\n    escapeRegExp: function (str) {\n      return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\");\n    },\n\n    /**\n     * Generate RegExp from wildcard attributes.\n     *\n     * @param name\n     * @returns {RegExp}\n     */\n    regexFromWildcard: function (name) {\n      var nameParts = name.split('[*]');\n      if (nameParts.length === 1) nameParts.push('');\n      return new RegExp('^' + nameParts.map(function (x) {\n        return laravelValidation.helpers.escapeRegExp(x);\n      }).join('\\\\[[^\\\\]]*\\\\]') + '$');\n    },\n\n    /**\n     * Merge additional laravel validation rules into the current rule set.\n     *\n     * @param {object} rules\n     * @param {object} newRules\n     * @returns {object}\n     */\n    mergeRules: function (rules, newRules) {\n      var rulesList = {\n        'laravelValidation': newRules.laravelValidation || [],\n        'laravelValidationRemote': newRules.laravelValidationRemote || []\n      };\n\n      for (var key in rulesList) {\n        if (rulesList[key].length === 0) {\n          continue;\n        }\n\n        if (typeof rules[key] === \"undefined\") {\n          rules[key] = [];\n        }\n\n        rules[key] = rules[key].concat(rulesList[key]);\n      }\n\n      return rules;\n    },\n\n    /**\n     * HTML entity encode a string.\n     *\n     * @param string\n     * @returns {string}\n     */\n    encode: function (string) {\n      return $('<div/>').text(string).html();\n    },\n\n    /**\n     * Lookup name in an array.\n     *\n     * @param validator\n     * @param {string} name Name in dot notation format.\n     * @returns {*}\n     */\n    findByArrayName: function (validator, name) {\n      var sqName = name.replace(/\\.([^\\.]+)/g, '[$1]'),\n          lookups = [// Convert dot to square brackets. e.g. foo.bar.0 becomes foo[bar][0]\n      sqName, // Append [] to the name e.g. foo becomes foo[] or foo.bar.0 becomes foo[bar][0][]\n      sqName + '[]', // Remove key from last array e.g. foo[bar][0] becomes foo[bar][]\n      sqName.replace(/(.*)\\[(.*)\\]$/g, '$1[]')];\n\n      for (var i = 0; i < lookups.length; i++) {\n        var elem = validator.findByName(lookups[i]);\n\n        if (elem.length > 0) {\n          return elem;\n        }\n      }\n\n      return $(null);\n    },\n\n    /**\n     * Attempt to find an element in the DOM matching the given name.\n     * Example names include:\n     *    - domain.0 which matches domain[]\n     *    - customfield.3 which matches customfield[3]\n     *\n     * @param validator\n     * @param {string} name\n     * @returns {*}\n     */\n    findByName: function (validator, name) {\n      // Exact match.\n      var elem = validator.findByName(name);\n\n      if (elem.length > 0) {\n        return elem;\n      } // Find name in data, using dot notation.\n\n\n      var delim = '.',\n          parts = name.split(delim);\n\n      for (var i = parts.length; i > 0; i--) {\n        var reconstructed = [];\n\n        for (var c = 0; c < i; c++) {\n          reconstructed.push(parts[c]);\n        }\n\n        elem = this.findByArrayName(validator, reconstructed.join(delim));\n\n        if (elem.length > 0) {\n          return elem;\n        }\n      }\n\n      return $(null);\n    },\n\n    /**\n     * If it's an array element, get all values.\n     *\n     * @param validator\n     * @param element\n     * @returns {*|string}\n     */\n    allElementValues: function (validator, element) {\n      if (element.name.indexOf('[]') !== -1) {\n        return validator.findByName(element.name).map(function (i, e) {\n          return validator.elementValue(e);\n        }).get();\n      }\n\n      return validator.elementValue(element);\n    }\n  }\n});\n}();\n/******/ })()\n;\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaGVscGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7O0FBRW5CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZ0JBQWdCLFVBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7Ozs7Ozs7Ozs7QUNsQ2E7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLHFCQUFxQixJQUFJO0FBQ3pCLHNCQUFzQixFQUFFO0FBQ3hCLHNCQUFzQixFQUFFO0FBQ3hCLG1DQUFtQyxFQUFFO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSw2QkFBNkIsSUFBSSxJQUFJLElBQUksR0FBRyxJQUFJO0FBQ2hEOztBQUVBO0FBQ0EsOEJBQThCLElBQUk7QUFDbEM7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx3REFBd0QsSUFBSTtBQUM1RDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLElBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUTtBQUNSOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQ0FBc0MsT0FBTztBQUM3Qzs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOzs7Ozs7Ozs7O0FDOXZDYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlEQUF5RCxxQkFBTTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOzs7Ozs7Ozs7O0FDMUJhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGdCQUFnQixLQUE4QixHQUFHLG1CQUFPLENBQUMsbUVBQWlCLHlCQUF5QixDQUFTO0FBQzVHO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0IsZ0JBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7Ozs7Ozs7Ozs7QUMzRWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7Ozs7OztVQzlCQTtVQUNBOztVQUVBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBOztVQUVBO1VBQ0E7O1VBRUE7VUFDQTtVQUNBOzs7OztXQ3RCQTtXQUNBO1dBQ0E7V0FDQSxlQUFlLDRCQUE0QjtXQUMzQyxlQUFlO1dBQ2YsaUNBQWlDLFdBQVc7V0FDNUM7V0FDQTs7Ozs7V0NQQTtXQUNBO1dBQ0E7V0FDQTtXQUNBLHlDQUF5Qyx3Q0FBd0M7V0FDakY7V0FDQTtXQUNBOzs7OztXQ1BBO1dBQ0E7V0FDQTtXQUNBO1dBQ0EsR0FBRztXQUNIO1dBQ0E7V0FDQSxDQUFDOzs7OztXQ1BELDhDQUE4Qzs7Ozs7V0NBOUM7V0FDQTtXQUNBO1dBQ0EsdURBQXVELGlCQUFpQjtXQUN4RTtXQUNBLGdEQUFnRCxhQUFhO1dBQzdEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FDTkE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUVBSSxDQUFDLENBQUNDLE1BQUYsQ0FBUyxJQUFULEVBQWVDLGlCQUFmLEVBQWtDO0VBRTlCQyxPQUFPLEVBQUU7SUFFTDtBQUNSO0FBQ0E7SUFDUUMsWUFBWSxFQUFFLENBQUMsU0FBRCxFQUFZLFNBQVosQ0FMVDs7SUFPTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRQyxRQUFRLEVBQUUsVUFBVUMsUUFBVixFQUFvQkMsS0FBcEIsRUFBMkI7TUFDakMsSUFBSUMsUUFBUSxHQUFHRixRQUFRLENBQUNHLEtBQXhCO01BQ0FGLEtBQUssR0FBRyxPQUFPQSxLQUFQLEtBQWlCLFdBQWpCLEdBQStCQSxLQUEvQixHQUF1QyxDQUEvQzs7TUFDQSxJQUFLRCxRQUFRLENBQUNJLEtBQVQsS0FBbUIsSUFBeEIsRUFBK0I7UUFDM0IsSUFBSSxPQUFPSixRQUFRLENBQUNJLEtBQVQsQ0FBZUgsS0FBZixDQUFQLEtBQWlDLFdBQXJDLEVBQWtEO1VBQzlDLE9BQU87WUFDSEksSUFBSSxFQUFFSCxRQURIO1lBRUhJLFNBQVMsRUFBRUosUUFBUSxDQUFDSyxNQUFULENBQWdCTCxRQUFRLENBQUNNLFdBQVQsQ0FBcUIsR0FBckIsSUFBNEIsQ0FBNUMsQ0FGUjtZQUdIQyxJQUFJLEVBQUVULFFBQVEsQ0FBQ0ksS0FBVCxDQUFlSCxLQUFmLEVBQXNCUSxJQUF0QixHQUE2QixJQUhoQztZQUlIQyxJQUFJLEVBQUVWLFFBQVEsQ0FBQ0ksS0FBVCxDQUFlSCxLQUFmLEVBQXNCUztVQUp6QixDQUFQO1FBTUg7TUFDSjs7TUFDRCxPQUFPLEtBQVA7SUFDSCxDQTVCSTs7SUErQkw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1FDLFFBQVEsRUFBRSxVQUFVQyxLQUFWLEVBQWlCO01BQ3ZCLElBQUlELFFBQVEsR0FBRyxFQUFmOztNQUNBLElBQUksQ0FBRSxLQUFLRSxPQUFMLENBQWFELEtBQWIsQ0FBTixFQUE0QjtRQUN4QkEsS0FBSyxHQUFHLENBQUNBLEtBQUQsQ0FBUjtNQUNIOztNQUNELEtBQUssSUFBSUUsQ0FBQyxHQUFHLENBQWIsRUFBZ0JBLENBQUMsR0FBR0YsS0FBSyxDQUFDRyxNQUExQixFQUFrQ0QsQ0FBQyxFQUFuQyxFQUF1QztRQUNuQ0gsUUFBUSxDQUFDSyxJQUFULENBQWMsWUFBWUosS0FBSyxDQUFDRSxDQUFELENBQWpCLEdBQXVCLElBQXJDO01BQ0g7O01BQ0QsT0FBT0gsUUFBUSxDQUFDTSxJQUFULEVBQVA7SUFDSCxDQTlDSTs7SUFpREw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1FDLGVBQWUsRUFBRSxVQUFVQyxPQUFWLEVBQW1CO01BQ2hDLE9BQU8sS0FBS0MsUUFBTCxDQUFjRCxPQUFkLEVBQXVCLEtBQUtyQixZQUE1QixDQUFQO0lBQ0gsQ0F6REk7O0lBMkRMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1FzQixRQUFRLEVBQUUsVUFBVUQsT0FBVixFQUFtQkUsS0FBbkIsRUFBMEI7TUFFaEMsSUFBSUMsS0FBSyxHQUFHLEtBQVo7O01BQ0EsSUFBSSxPQUFPRCxLQUFQLEtBQWlCLFFBQXJCLEVBQStCO1FBQzNCQSxLQUFLLEdBQUcsQ0FBQ0EsS0FBRCxDQUFSO01BQ0g7O01BRUQsSUFBSUUsU0FBUyxHQUFHN0IsQ0FBQyxDQUFDOEIsSUFBRixDQUFPTCxPQUFPLENBQUNNLElBQWYsRUFBcUIsV0FBckIsQ0FBaEI7TUFDQSxJQUFJQyxTQUFTLEdBQUcsRUFBaEI7TUFDQSxJQUFJQyxLQUFLLEdBQUdKLFNBQVMsQ0FBQ0ssZUFBdEI7O01BQ0EsSUFBSVQsT0FBTyxDQUFDVSxJQUFSLElBQWdCRixLQUFwQixFQUEyQjtRQUN2QmpDLENBQUMsQ0FBQ29DLElBQUYsQ0FBT0gsS0FBSyxDQUFDUixPQUFPLENBQUNVLElBQVQsQ0FBWixFQUE0QixVQUFVNUIsS0FBVixFQUFpQjhCLFNBQWpCLEVBQTRCO1VBQ3BETCxTQUFTLENBQUNWLElBQVYsQ0FBZWUsU0FBZjtRQUNILENBRkQ7TUFHSDs7TUFDRCxJQUFJWixPQUFPLENBQUNVLElBQVIsSUFBZ0JOLFNBQVMsQ0FBQ1MsUUFBVixDQUFtQlgsS0FBdkMsRUFBOEM7UUFDMUNLLFNBQVMsQ0FBQ1YsSUFBVixDQUFlTyxTQUFTLENBQUNTLFFBQVYsQ0FBbUJYLEtBQW5CLENBQXlCRixPQUFPLENBQUNVLElBQWpDLENBQWY7TUFDSDs7TUFDRG5DLENBQUMsQ0FBQ29DLElBQUYsQ0FBT0osU0FBUCxFQUFrQixVQUFTekIsS0FBVCxFQUFlZ0MsUUFBZixFQUF3QjtRQUN0QyxJQUFJLHVCQUF1QkEsUUFBM0IsRUFBcUM7VUFDakMsSUFBSUMsTUFBTSxHQUFDRCxRQUFRLENBQUNyQyxpQkFBcEI7O1VBQ0EsS0FBSyxJQUFJa0IsQ0FBQyxHQUFHLENBQWIsRUFBZ0JBLENBQUMsR0FBR29CLE1BQU0sQ0FBQ25CLE1BQTNCLEVBQW1DRCxDQUFDLEVBQXBDLEVBQXdDO1lBQ3BDLElBQUlwQixDQUFDLENBQUN5QyxPQUFGLENBQVVELE1BQU0sQ0FBQ3BCLENBQUQsQ0FBTixDQUFVLENBQVYsQ0FBVixFQUF1Qk8sS0FBdkIsTUFBa0MsQ0FBQyxDQUF2QyxFQUEwQztjQUN0Q0MsS0FBSyxHQUFHLElBQVI7Y0FDQSxPQUFPLEtBQVA7WUFDSDtVQUNKO1FBQ0o7TUFDSixDQVZEO01BWUEsT0FBT0EsS0FBUDtJQUNILENBakdJOztJQW1HTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRaEMsTUFBTSxFQUFFLFVBQVU4QyxNQUFWLEVBQWtCO01BQ3RCLE9BQU85QyxpRUFBTSxDQUFDOEMsTUFBRCxDQUFiO0lBQ0gsQ0E1R0k7O0lBOEdMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUUMsT0FBTyxFQUFFLFNBQVNBLE9BQVQsQ0FBaUJDLEdBQWpCLEVBQXNCbkIsT0FBdEIsRUFBK0JoQixLQUEvQixFQUFzQztNQUUzQyxJQUFJLEtBQUtlLGVBQUwsQ0FBcUJDLE9BQXJCLEtBQWlDLEtBQUsxQixVQUFMLENBQWdCVSxLQUFoQixDQUFyQyxFQUE2RDtRQUN6RCxPQUFPb0MsVUFBVSxDQUFDcEMsS0FBRCxDQUFqQjtNQUNILENBRkQsTUFFTyxJQUFJLEtBQUtVLE9BQUwsQ0FBYVYsS0FBYixDQUFKLEVBQXlCO1FBQzVCLE9BQU9vQyxVQUFVLENBQUNwQyxLQUFLLENBQUNZLE1BQVAsQ0FBakI7TUFDSCxDQUZNLE1BRUEsSUFBSUksT0FBTyxDQUFDVCxJQUFSLEtBQWlCLE1BQXJCLEVBQTZCO1FBQ2hDLE9BQU82QixVQUFVLENBQUNDLElBQUksQ0FBQ0MsS0FBTCxDQUFXLEtBQUsxQyxRQUFMLENBQWNvQixPQUFkLEVBQXVCVixJQUFsQyxDQUFELENBQWpCO01BQ0g7O01BRUQsT0FBTzhCLFVBQVUsQ0FBQyxLQUFLakQsTUFBTCxDQUFZYSxLQUFaLENBQUQsQ0FBakI7SUFDSCxDQWpJSTs7SUFvSUw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUXVDLG9CQUFvQixFQUFFLFVBQVNDLElBQVQsRUFBZXhCLE9BQWYsRUFBd0I7TUFFMUMsSUFBSUcsS0FBSyxHQUFHc0IsU0FBWjtNQUNBbEQsQ0FBQyxDQUFDb0MsSUFBRixDQUFPcEMsQ0FBQyxDQUFDNkIsU0FBRixDQUFZc0IsV0FBWixDQUF3QjFCLE9BQXhCLENBQVAsRUFBeUMsVUFBUzJCLEdBQVQsRUFBY3pCLEtBQWQsRUFBcUI7UUFDMUQsSUFBSXlCLEdBQUcsS0FBRyxtQkFBVixFQUErQjtVQUMzQnBELENBQUMsQ0FBQ29DLElBQUYsQ0FBT1QsS0FBUCxFQUFjLFVBQVVQLENBQVYsRUFBYVgsS0FBYixFQUFvQjtZQUM5QixJQUFJQSxLQUFLLENBQUMsQ0FBRCxDQUFMLEtBQVd3QyxJQUFmLEVBQXFCO2NBQ2pCckIsS0FBSyxHQUFDbkIsS0FBTjtZQUNIO1VBQ0osQ0FKRDtRQUtIO01BQ0osQ0FSRDtNQVVBLE9BQU9tQixLQUFQO0lBQ0gsQ0F6Skk7O0lBMkpMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1F5QixTQUFTLEVBQUUsVUFBVTVDLEtBQVYsRUFBaUI2QyxNQUFqQixFQUF5QjtNQUVoQyxJQUFJQyxTQUFTLEdBQUcsS0FBaEI7TUFDQSxJQUFJQyxHQUFHLEdBQUcsSUFBSUMsYUFBSixFQUFWOztNQUVBLElBQUksT0FBT2hELEtBQVAsS0FBaUIsUUFBakIsSUFBNkIsT0FBTzZDLE1BQVAsS0FBa0IsV0FBbkQsRUFBZ0U7UUFDNUQsT0FBTzdDLEtBQVA7TUFDSDs7TUFFRCxJQUFJLE9BQU82QyxNQUFQLEtBQWtCLFFBQXRCLEVBQWdDO1FBQzVCLElBQUlJLFFBQVEsR0FBRyxLQUFLVixvQkFBTCxDQUEwQixZQUExQixFQUF3Q00sTUFBeEMsQ0FBZjs7UUFDQSxJQUFJSSxRQUFRLEtBQUtSLFNBQWpCLEVBQTRCO1VBQ3hCSSxNQUFNLEdBQUdJLFFBQVEsQ0FBQyxDQUFELENBQVIsQ0FBWSxDQUFaLENBQVQ7UUFDSCxDQUZELE1BRU87VUFDSEosTUFBTSxHQUFHLElBQVQ7UUFDSDtNQUNKOztNQUVELElBQUlBLE1BQU0sSUFBSSxJQUFkLEVBQW9CO1FBQ2hCQyxTQUFTLEdBQUcsS0FBS3pELFNBQUwsQ0FBZVcsS0FBZixDQUFaO01BQ0gsQ0FGRCxNQUVPO1FBQ0g4QyxTQUFTLEdBQUdDLEdBQUcsQ0FBQ0csU0FBSixDQUFjbEQsS0FBZCxFQUFxQjZDLE1BQXJCLENBQVo7O1FBQ0EsSUFBSUMsU0FBUyxZQUFZSyxJQUFyQixJQUE2QkosR0FBRyxDQUFDSyxVQUFKLENBQWVOLFNBQWYsRUFBMEJELE1BQTFCLE1BQXNDN0MsS0FBdkUsRUFBOEU7VUFDMUU4QyxTQUFTLEdBQUdULElBQUksQ0FBQ2dCLEtBQUwsQ0FBWVAsU0FBUyxDQUFDUSxPQUFWLEtBQXNCLElBQWxDLENBQVo7UUFDSCxDQUZELE1BRU87VUFDSFIsU0FBUyxHQUFHLEtBQVo7UUFDSDtNQUNKOztNQUVELE9BQU9BLFNBQVA7SUFDSCxDQWhNSTs7SUFrTUw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUVMsWUFBWSxFQUFFLFVBQVVuQyxTQUFWLEVBQXFCcEIsS0FBckIsRUFBNEJnQixPQUE1QixFQUFxQ3dDLE1BQXJDLEVBQTZDQyxRQUE3QyxFQUF1RDtNQUVqRSxJQUFJQyxXQUFXLEdBQUcsS0FBS2QsU0FBTCxDQUFlWSxNQUFmLENBQWxCOztNQUVBLElBQUksQ0FBQ0UsV0FBTCxFQUFrQjtRQUNkLElBQUlDLE1BQU0sR0FBRyxLQUFLQyxnQkFBTCxDQUFzQnhDLFNBQXRCLEVBQWlDSixPQUFqQyxFQUEwQ3dDLE1BQTFDLENBQWI7O1FBQ0EsSUFBSUcsTUFBTSxLQUFLbEIsU0FBZixFQUEwQjtVQUN0QixPQUFPLEtBQVA7UUFDSDs7UUFDRGlCLFdBQVcsR0FBRyxLQUFLZCxTQUFMLENBQWV4QixTQUFTLENBQUN5QyxZQUFWLENBQXVCRixNQUF2QixDQUFmLEVBQStDQSxNQUEvQyxDQUFkO01BQ0g7O01BRUQsSUFBSWIsU0FBUyxHQUFHLEtBQUtGLFNBQUwsQ0FBZTVDLEtBQWYsRUFBc0JnQixPQUF0QixDQUFoQjs7TUFDQSxJQUFJOEIsU0FBUyxLQUFLLEtBQWxCLEVBQXlCO1FBQ3JCLE9BQU8sS0FBUDtNQUNIOztNQUVELFFBQVFXLFFBQVI7UUFDSSxLQUFLLEdBQUw7VUFDSSxPQUFPWCxTQUFTLEdBQUdZLFdBQW5COztRQUVKLEtBQUssSUFBTDtVQUNJLE9BQU9aLFNBQVMsSUFBSVksV0FBcEI7O1FBRUosS0FBSyxJQUFMO1FBQ0EsS0FBSyxLQUFMO1VBQ0ksT0FBT1osU0FBUyxLQUFLWSxXQUFyQjs7UUFFSixLQUFLLEdBQUw7VUFDSSxPQUFPWixTQUFTLEdBQUdZLFdBQW5COztRQUVKLEtBQUssSUFBTDtVQUNJLE9BQU9aLFNBQVMsSUFBSVksV0FBcEI7O1FBRUo7VUFDSSxNQUFNLElBQUlJLEtBQUosQ0FBVSx1QkFBVixDQUFOO01BbEJSO0lBb0JILENBalBJOztJQW1QTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRQyxTQUFTLEVBQUUsVUFBVS9ELEtBQVYsRUFBaUI2QyxNQUFqQixFQUF5QjtNQUNoQyxJQUFJRSxHQUFHLEdBQUcsSUFBSUMsYUFBSixFQUFWO01BQ0EsT0FBT0QsR0FBRyxDQUFDZ0IsU0FBSixDQUFjL0QsS0FBZCxFQUFxQjZDLE1BQXJCLENBQVA7SUFDSCxDQTdQSTs7SUErUEw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1F4RCxTQUFTLEVBQUUsVUFBVTJFLElBQVYsRUFBZ0JDLEdBQWhCLEVBQXFCO01BQzVCLE9BQU81RSxxRUFBUyxDQUFDMkUsSUFBRCxFQUFPQyxHQUFQLENBQWhCO0lBQ0gsQ0ExUUk7O0lBNFFMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUTNFLFVBQVUsRUFBRSxVQUFVNEUsU0FBVixFQUFxQjtNQUM3QixPQUFPNUUsaUVBQVUsQ0FBQzRFLFNBQUQsQ0FBakI7SUFDSCxDQXRSSTs7SUF3Ukw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUXhELE9BQU8sRUFBRSxVQUFTeUQsR0FBVCxFQUFjO01BQ25CLE9BQU9DLE1BQU0sQ0FBQ0MsU0FBUCxDQUFpQkMsUUFBakIsQ0FBMEJDLElBQTFCLENBQStCSixHQUEvQixNQUF3QyxnQkFBL0M7SUFDSCxDQWpTSTs7SUFtU0w7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1FLLFNBQVMsRUFBRSxVQUFVQyxJQUFWLEVBQWdCQyxJQUFoQixFQUFzQjtNQUM3QixPQUFPdEYsbUVBQVUsQ0FBQ3FGLElBQUQsRUFBT0MsSUFBUCxDQUFqQjtJQUNILENBOVNJOztJQWdUTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRQyxXQUFXLEVBQUUsVUFBVUYsSUFBVixFQUFnQkMsSUFBaEIsRUFBc0I7TUFDL0IsSUFBSSxDQUFFLEtBQUtoRSxPQUFMLENBQWErRCxJQUFiLENBQUYsSUFBd0IsQ0FBRSxLQUFLL0QsT0FBTCxDQUFhZ0UsSUFBYixDQUE5QixFQUFrRDtRQUM5QyxPQUFPLEtBQVA7TUFDSDs7TUFFRCxJQUFJRCxJQUFJLENBQUM3RCxNQUFMLEtBQWdCOEQsSUFBSSxDQUFDOUQsTUFBekIsRUFBaUM7UUFDN0IsT0FBTyxLQUFQO01BQ0g7O01BRUQsT0FBT3JCLENBQUMsQ0FBQ3FGLGFBQUYsQ0FBZ0IsS0FBS0osU0FBTCxDQUFlQyxJQUFmLEVBQXFCQyxJQUFyQixDQUFoQixDQUFQO0lBQ0gsQ0FqVUk7O0lBbVVMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUWQsZ0JBQWdCLEVBQUUsVUFBU3hDLFNBQVQsRUFBb0JKLE9BQXBCLEVBQTZCVSxJQUE3QixFQUFtQztNQUVqRCxJQUFJbUQsRUFBRSxHQUFDekQsU0FBUyxDQUFDMEQsVUFBVixDQUFxQnBELElBQXJCLENBQVA7O01BRUEsSUFBS21ELEVBQUUsQ0FBQyxDQUFELENBQUYsS0FBUXBDLFNBQVIsSUFBc0JyQixTQUFTLENBQUNTLFFBQVYsQ0FBbUJrRCxVQUE5QyxFQUEyRDtRQUN2RCxJQUFJQyxLQUFLLEdBQUcsTUFBWjs7UUFDQSxJQUFJSCxFQUFFLENBQUMsQ0FBRCxDQUFGLENBQU1JLE9BQU4sS0FBa0IsUUFBbEIsSUFDQUosRUFBRSxDQUFDLENBQUQsQ0FBRixDQUFNSSxPQUFOLEtBQWtCLFFBRGxCLElBRUFKLEVBQUUsQ0FBQyxDQUFELENBQUYsQ0FBTXRFLElBQU4sS0FBZSxVQUZmLElBR0FzRSxFQUFFLENBQUMsQ0FBRCxDQUFGLENBQU10RSxJQUFOLEtBQWUsT0FIbkIsRUFJRTtVQUNFeUUsS0FBSyxHQUFHLE9BQVI7UUFDSDs7UUFFRCxJQUFJRSxRQUFRLEdBQUcsNkJBQWY7UUFDQUwsRUFBRSxDQUFDTSxHQUFILENBQVFELFFBQVIsRUFDS0MsR0FETCxDQUNTSCxLQUFLLEdBQUdFLFFBQVIsR0FBbUIsR0FBbkIsR0FBeUJsRSxPQUFPLENBQUNVLElBRDFDLEVBRUswRCxFQUZMLENBRVNKLEtBQUssR0FBR0UsUUFBUixHQUFtQixHQUFuQixHQUF5QmxFLE9BQU8sQ0FBQ1UsSUFGMUMsRUFFZ0QsWUFBVztVQUNuRG5DLENBQUMsQ0FBRXlCLE9BQUYsQ0FBRCxDQUFhcUUsS0FBYjtRQUNILENBSkw7TUFLSDs7TUFFRCxPQUFPUixFQUFFLENBQUMsQ0FBRCxDQUFUO0lBQ0gsQ0FsV0k7O0lBb1dMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRUyxrQkFBa0IsRUFBRSxVQUFVQyxRQUFWLEVBQW9CO01BQ3BDLElBQUlDLFdBQVcsR0FBRyxDQUFDLDBDQUFELENBQWxCOztNQUNBLElBQUksa0JBQWtCRCxRQUF0QixFQUFnQztRQUM1QixJQUFJRSxRQUFRLEdBQUdGLFFBQVEsQ0FBQ0csWUFBVCxDQUFzQkMsS0FBdEIsQ0FBNEIsdUJBQTVCLENBQWY7O1FBQ0EsSUFBSSxLQUFLakYsT0FBTCxDQUFhK0UsUUFBYixDQUFKLEVBQTRCO1VBQ3hCRCxXQUFXLEdBQUcsQ0FBQ0MsUUFBUSxDQUFDLENBQUQsQ0FBVCxDQUFkO1FBQ0g7TUFDSjs7TUFDRCxPQUFPRCxXQUFQO0lBQ0gsQ0FuWEk7O0lBcVhMO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRSSxZQUFZLEVBQUUsVUFBVUMsR0FBVixFQUFlO01BQ3pCLE9BQU9BLEdBQUcsQ0FBQ0MsT0FBSixDQUFZLHFDQUFaLEVBQW1ELE1BQW5ELENBQVA7SUFDSCxDQTdYSTs7SUErWEw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0lBQ1FDLGlCQUFpQixFQUFFLFVBQVVyRSxJQUFWLEVBQWdCO01BQy9CLElBQUlzRSxTQUFTLEdBQUd0RSxJQUFJLENBQUN1RSxLQUFMLENBQVcsS0FBWCxDQUFoQjtNQUNBLElBQUlELFNBQVMsQ0FBQ3BGLE1BQVYsS0FBcUIsQ0FBekIsRUFBNEJvRixTQUFTLENBQUNuRixJQUFWLENBQWUsRUFBZjtNQUU1QixPQUFPLElBQUlxRixNQUFKLENBQVcsTUFBTUYsU0FBUyxDQUFDRyxHQUFWLENBQWMsVUFBU0MsQ0FBVCxFQUFZO1FBQzlDLE9BQU8zRyxpQkFBaUIsQ0FBQ0MsT0FBbEIsQ0FBMEJrRyxZQUExQixDQUF1Q1EsQ0FBdkMsQ0FBUDtNQUNILENBRnVCLEVBRXJCdEYsSUFGcUIsQ0FFaEIsZUFGZ0IsQ0FBTixHQUVTLEdBRnBCLENBQVA7SUFHSCxDQTVZSTs7SUE4WUw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUXVGLFVBQVUsRUFBRSxVQUFVbkYsS0FBVixFQUFpQm9GLFFBQWpCLEVBQTJCO01BQ25DLElBQUlDLFNBQVMsR0FBRztRQUNaLHFCQUFxQkQsUUFBUSxDQUFDN0csaUJBQVQsSUFBOEIsRUFEdkM7UUFFWiwyQkFBMkI2RyxRQUFRLENBQUNFLHVCQUFULElBQW9DO01BRm5ELENBQWhCOztNQUtBLEtBQUssSUFBSTdELEdBQVQsSUFBZ0I0RCxTQUFoQixFQUEyQjtRQUN2QixJQUFJQSxTQUFTLENBQUM1RCxHQUFELENBQVQsQ0FBZS9CLE1BQWYsS0FBMEIsQ0FBOUIsRUFBaUM7VUFDN0I7UUFDSDs7UUFFRCxJQUFJLE9BQU9NLEtBQUssQ0FBQ3lCLEdBQUQsQ0FBWixLQUFzQixXQUExQixFQUF1QztVQUNuQ3pCLEtBQUssQ0FBQ3lCLEdBQUQsQ0FBTCxHQUFhLEVBQWI7UUFDSDs7UUFFRHpCLEtBQUssQ0FBQ3lCLEdBQUQsQ0FBTCxHQUFhekIsS0FBSyxDQUFDeUIsR0FBRCxDQUFMLENBQVc4RCxNQUFYLENBQWtCRixTQUFTLENBQUM1RCxHQUFELENBQTNCLENBQWI7TUFDSDs7TUFFRCxPQUFPekIsS0FBUDtJQUNILENBeGFJOztJQTBhTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUXdGLE1BQU0sRUFBRSxVQUFVekUsTUFBVixFQUFrQjtNQUN0QixPQUFPMUMsQ0FBQyxDQUFDLFFBQUQsQ0FBRCxDQUFZeUUsSUFBWixDQUFpQi9CLE1BQWpCLEVBQXlCMEUsSUFBekIsRUFBUDtJQUNILENBbGJJOztJQW9iTDtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNRQyxlQUFlLEVBQUUsVUFBVXhGLFNBQVYsRUFBcUJNLElBQXJCLEVBQTJCO01BQ3hDLElBQUltRixNQUFNLEdBQUduRixJQUFJLENBQUNvRSxPQUFMLENBQWEsYUFBYixFQUE0QixNQUE1QixDQUFiO01BQUEsSUFDSWdCLE9BQU8sR0FBRyxDQUNOO01BQ0FELE1BRk0sRUFHTjtNQUNBQSxNQUFNLEdBQUcsSUFKSCxFQUtOO01BQ0FBLE1BQU0sQ0FBQ2YsT0FBUCxDQUFlLGdCQUFmLEVBQWlDLE1BQWpDLENBTk0sQ0FEZDs7TUFVQSxLQUFLLElBQUluRixDQUFDLEdBQUcsQ0FBYixFQUFnQkEsQ0FBQyxHQUFHbUcsT0FBTyxDQUFDbEcsTUFBNUIsRUFBb0NELENBQUMsRUFBckMsRUFBeUM7UUFDckMsSUFBSW9HLElBQUksR0FBRzNGLFNBQVMsQ0FBQzBELFVBQVYsQ0FBcUJnQyxPQUFPLENBQUNuRyxDQUFELENBQTVCLENBQVg7O1FBQ0EsSUFBSW9HLElBQUksQ0FBQ25HLE1BQUwsR0FBYyxDQUFsQixFQUFxQjtVQUNqQixPQUFPbUcsSUFBUDtRQUNIO01BQ0o7O01BRUQsT0FBT3hILENBQUMsQ0FBQyxJQUFELENBQVI7SUFDSCxDQTljSTs7SUFnZEw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUXVGLFVBQVUsRUFBRSxVQUFVMUQsU0FBVixFQUFxQk0sSUFBckIsRUFBMkI7TUFDbkM7TUFDQSxJQUFJcUYsSUFBSSxHQUFHM0YsU0FBUyxDQUFDMEQsVUFBVixDQUFxQnBELElBQXJCLENBQVg7O01BQ0EsSUFBSXFGLElBQUksQ0FBQ25HLE1BQUwsR0FBYyxDQUFsQixFQUFxQjtRQUNqQixPQUFPbUcsSUFBUDtNQUNILENBTGtDLENBT25DOzs7TUFDQSxJQUFJQyxLQUFLLEdBQUcsR0FBWjtNQUFBLElBQ0lDLEtBQUssR0FBSXZGLElBQUksQ0FBQ3VFLEtBQUwsQ0FBV2UsS0FBWCxDQURiOztNQUVBLEtBQUssSUFBSXJHLENBQUMsR0FBR3NHLEtBQUssQ0FBQ3JHLE1BQW5CLEVBQTJCRCxDQUFDLEdBQUcsQ0FBL0IsRUFBa0NBLENBQUMsRUFBbkMsRUFBdUM7UUFDbkMsSUFBSXVHLGFBQWEsR0FBRyxFQUFwQjs7UUFDQSxLQUFLLElBQUlDLENBQUMsR0FBRyxDQUFiLEVBQWdCQSxDQUFDLEdBQUd4RyxDQUFwQixFQUF1QndHLENBQUMsRUFBeEIsRUFBNEI7VUFDeEJELGFBQWEsQ0FBQ3JHLElBQWQsQ0FBbUJvRyxLQUFLLENBQUNFLENBQUQsQ0FBeEI7UUFDSDs7UUFFREosSUFBSSxHQUFHLEtBQUtILGVBQUwsQ0FBcUJ4RixTQUFyQixFQUFnQzhGLGFBQWEsQ0FBQ3BHLElBQWQsQ0FBbUJrRyxLQUFuQixDQUFoQyxDQUFQOztRQUNBLElBQUlELElBQUksQ0FBQ25HLE1BQUwsR0FBYyxDQUFsQixFQUFxQjtVQUNqQixPQUFPbUcsSUFBUDtRQUNIO01BQ0o7O01BRUQsT0FBT3hILENBQUMsQ0FBQyxJQUFELENBQVI7SUFDSCxDQWpmSTs7SUFtZkw7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7SUFDUTZILGdCQUFnQixFQUFFLFVBQVVoRyxTQUFWLEVBQXFCSixPQUFyQixFQUE4QjtNQUM1QyxJQUFJQSxPQUFPLENBQUNVLElBQVIsQ0FBYTJGLE9BQWIsQ0FBcUIsSUFBckIsTUFBK0IsQ0FBQyxDQUFwQyxFQUF1QztRQUNuQyxPQUFPakcsU0FBUyxDQUFDMEQsVUFBVixDQUFxQjlELE9BQU8sQ0FBQ1UsSUFBN0IsRUFBbUN5RSxHQUFuQyxDQUF1QyxVQUFVeEYsQ0FBVixFQUFhMkcsQ0FBYixFQUFnQjtVQUMxRCxPQUFPbEcsU0FBUyxDQUFDeUMsWUFBVixDQUF1QnlELENBQXZCLENBQVA7UUFDSCxDQUZNLEVBRUpDLEdBRkksRUFBUDtNQUdIOztNQUVELE9BQU9uRyxTQUFTLENBQUN5QyxZQUFWLENBQXVCN0MsT0FBdkIsQ0FBUDtJQUNIO0VBbGdCSTtBQUZxQixDQUFsQyxFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2xvY3V0dXMvcGhwL2FycmF5L2FycmF5X2RpZmYuanMiLCJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL2xvY3V0dXMvcGhwL2RhdGV0aW1lL3N0cnRvdGltZS5qcyIsIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvbG9jdXR1cy9waHAvaW5mby9pbmlfZ2V0LmpzIiwid2VicGFjazovLy8uL25vZGVfbW9kdWxlcy9sb2N1dHVzL3BocC9zdHJpbmdzL3N0cmxlbi5qcyIsIndlYnBhY2s6Ly8vLi9ub2RlX21vZHVsZXMvbG9jdXR1cy9waHAvdmFyL2lzX251bWVyaWMuanMiLCJ3ZWJwYWNrOi8vL3dlYnBhY2svYm9vdHN0cmFwIiwid2VicGFjazovLy93ZWJwYWNrL3J1bnRpbWUvY29tcGF0IGdldCBkZWZhdWx0IGV4cG9ydCIsIndlYnBhY2s6Ly8vd2VicGFjay9ydW50aW1lL2RlZmluZSBwcm9wZXJ0eSBnZXR0ZXJzIiwid2VicGFjazovLy93ZWJwYWNrL3J1bnRpbWUvZ2xvYmFsIiwid2VicGFjazovLy93ZWJwYWNrL3J1bnRpbWUvaGFzT3duUHJvcGVydHkgc2hvcnRoYW5kIiwid2VicGFjazovLy93ZWJwYWNrL3J1bnRpbWUvbWFrZSBuYW1lc3BhY2Ugb2JqZWN0Iiwid2VicGFjazovLy8uL3Jlc291cmNlcy9hc3NldHMvanMvaGVscGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gYXJyYXlfZGlmZihhcnIxKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG4gIC8vICBkaXNjdXNzIGF0OiBodHRwczovL2xvY3V0dXMuaW8vcGhwL2FycmF5X2RpZmYvXG4gIC8vIG9yaWdpbmFsIGJ5OiBLZXZpbiB2YW4gWm9ubmV2ZWxkIChodHRwczovL2t2ei5pbylcbiAgLy8gaW1wcm92ZWQgYnk6IFNhbmpveSBSb3lcbiAgLy8gIHJldmlzZWQgYnk6IEJyZXR0IFphbWlyIChodHRwczovL2JyZXR0LXphbWlyLm1lKVxuICAvLyAgIGV4YW1wbGUgMTogYXJyYXlfZGlmZihbJ0tldmluJywgJ3ZhbicsICdab25uZXZlbGQnXSwgWyd2YW4nLCAnWm9ubmV2ZWxkJ10pXG4gIC8vICAgcmV0dXJucyAxOiB7MDonS2V2aW4nfVxuXG4gIHZhciByZXRBcnIgPSB7fTtcbiAgdmFyIGFyZ2wgPSBhcmd1bWVudHMubGVuZ3RoO1xuICB2YXIgazEgPSAnJztcbiAgdmFyIGkgPSAxO1xuICB2YXIgayA9ICcnO1xuICB2YXIgYXJyID0ge307XG5cbiAgYXJyMWtleXM6IGZvciAoazEgaW4gYXJyMSkge1xuICAgIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tbGFiZWxzXG4gICAgZm9yIChpID0gMTsgaSA8IGFyZ2w7IGkrKykge1xuICAgICAgYXJyID0gYXJndW1lbnRzW2ldO1xuICAgICAgZm9yIChrIGluIGFycikge1xuICAgICAgICBpZiAoYXJyW2tdID09PSBhcnIxW2sxXSkge1xuICAgICAgICAgIC8vIElmIGl0IHJlYWNoZXMgaGVyZSwgaXQgd2FzIGZvdW5kIGluIGF0IGxlYXN0IG9uZSBhcnJheSwgc28gdHJ5IG5leHQgdmFsdWVcbiAgICAgICAgICBjb250aW51ZSBhcnIxa2V5czsgLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1sYWJlbHNcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0QXJyW2sxXSA9IGFycjFbazFdO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXRBcnI7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXJyYXlfZGlmZi5qcy5tYXAiLCIndXNlIHN0cmljdCc7XG5cbnZhciByZVNwYWNlID0gJ1sgXFxcXHRdKyc7XG52YXIgcmVTcGFjZU9wdCA9ICdbIFxcXFx0XSonO1xudmFyIHJlTWVyaWRpYW4gPSAnKD86KFthcF0pXFxcXC4/bVxcXFwuPyhbXFxcXHQgXXwkKSknO1xudmFyIHJlSG91cjI0ID0gJygyWzAtNF18WzAxXT9bMC05XSknO1xudmFyIHJlSG91cjI0bHogPSAnKFswMV1bMC05XXwyWzAtNF0pJztcbnZhciByZUhvdXIxMiA9ICcoMD9bMS05XXwxWzAtMl0pJztcbnZhciByZU1pbnV0ZSA9ICcoWzAtNV0/WzAtOV0pJztcbnZhciByZU1pbnV0ZWx6ID0gJyhbMC01XVswLTldKSc7XG52YXIgcmVTZWNvbmQgPSAnKDYwfFswLTVdP1swLTldKSc7XG52YXIgcmVTZWNvbmRseiA9ICcoNjB8WzAtNV1bMC05XSknO1xudmFyIHJlRnJhYyA9ICcoPzpcXFxcLihbMC05XSspKSc7XG5cbnZhciByZURheWZ1bGwgPSAnc3VuZGF5fG1vbmRheXx0dWVzZGF5fHdlZG5lc2RheXx0aHVyc2RheXxmcmlkYXl8c2F0dXJkYXknO1xudmFyIHJlRGF5YWJiciA9ICdzdW58bW9ufHR1ZXx3ZWR8dGh1fGZyaXxzYXQnO1xudmFyIHJlRGF5dGV4dCA9IHJlRGF5ZnVsbCArICd8JyArIHJlRGF5YWJiciArICd8d2Vla2RheXM/JztcblxudmFyIHJlUmVsdGV4dG51bWJlciA9ICdmaXJzdHxzZWNvbmR8dGhpcmR8Zm91cnRofGZpZnRofHNpeHRofHNldmVudGh8ZWlnaHRoP3xuaW50aHx0ZW50aHxlbGV2ZW50aHx0d2VsZnRoJztcbnZhciByZVJlbHRleHR0ZXh0ID0gJ25leHR8bGFzdHxwcmV2aW91c3x0aGlzJztcbnZhciByZVJlbHRleHR1bml0ID0gJyg/OnNlY29uZHxzZWN8bWludXRlfG1pbnxob3VyfGRheXxmb3J0bmlnaHR8Zm9ydGhuaWdodHxtb250aHx5ZWFyKXM/fHdlZWtzfCcgKyByZURheXRleHQ7XG5cbnZhciByZVllYXIgPSAnKFswLTldezEsNH0pJztcbnZhciByZVllYXIyID0gJyhbMC05XXsyfSknO1xudmFyIHJlWWVhcjQgPSAnKFswLTldezR9KSc7XG52YXIgcmVZZWFyNHdpdGhTaWduID0gJyhbKy1dP1swLTldezR9KSc7XG52YXIgcmVNb250aCA9ICcoMVswLTJdfDA/WzAtOV0pJztcbnZhciByZU1vbnRobHogPSAnKDBbMC05XXwxWzAtMl0pJztcbnZhciByZURheSA9ICcoPzooM1swMV18WzAtMl0/WzAtOV0pKD86c3R8bmR8cmR8dGgpPyknO1xudmFyIHJlRGF5bHogPSAnKDBbMC05XXxbMS0yXVswLTldfDNbMDFdKSc7XG5cbnZhciByZU1vbnRoRnVsbCA9ICdqYW51YXJ5fGZlYnJ1YXJ5fG1hcmNofGFwcmlsfG1heXxqdW5lfGp1bHl8YXVndXN0fHNlcHRlbWJlcnxvY3RvYmVyfG5vdmVtYmVyfGRlY2VtYmVyJztcbnZhciByZU1vbnRoQWJiciA9ICdqYW58ZmVifG1hcnxhcHJ8bWF5fGp1bnxqdWx8YXVnfHNlcHQ/fG9jdHxub3Z8ZGVjJztcbnZhciByZU1vbnRocm9tYW4gPSAnaVt2eF18dml7MCwzfXx4aXswLDJ9fGl7MSwzfSc7XG52YXIgcmVNb250aFRleHQgPSAnKCcgKyByZU1vbnRoRnVsbCArICd8JyArIHJlTW9udGhBYmJyICsgJ3wnICsgcmVNb250aHJvbWFuICsgJyknO1xuXG52YXIgcmVUekNvcnJlY3Rpb24gPSAnKCg/OkdNVCk/KFsrLV0pJyArIHJlSG91cjI0ICsgJzo/JyArIHJlTWludXRlICsgJz8pJztcbnZhciByZVR6QWJiciA9ICdcXFxcKD8oW2EtekEtWl17MSw2fSlcXFxcKT8nO1xudmFyIHJlRGF5T2ZZZWFyID0gJygwMFsxLTldfDBbMS05XVswLTldfFsxMl1bMC05XVswLTldfDNbMC01XVswLTldfDM2WzAtNl0pJztcbnZhciByZVdlZWtPZlllYXIgPSAnKDBbMS05XXxbMS00XVswLTldfDVbMC0zXSknO1xuXG52YXIgcmVEYXRlTm9ZZWFyID0gcmVNb250aFRleHQgKyAnWyAuXFxcXHQtXSonICsgcmVEYXkgKyAnWywuc3RuZHJoXFxcXHQgXSonO1xuXG5mdW5jdGlvbiBwcm9jZXNzTWVyaWRpYW4oaG91ciwgbWVyaWRpYW4pIHtcbiAgbWVyaWRpYW4gPSBtZXJpZGlhbiAmJiBtZXJpZGlhbi50b0xvd2VyQ2FzZSgpO1xuXG4gIHN3aXRjaCAobWVyaWRpYW4pIHtcbiAgICBjYXNlICdhJzpcbiAgICAgIGhvdXIgKz0gaG91ciA9PT0gMTIgPyAtMTIgOiAwO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAncCc6XG4gICAgICBob3VyICs9IGhvdXIgIT09IDEyID8gMTIgOiAwO1xuICAgICAgYnJlYWs7XG4gIH1cblxuICByZXR1cm4gaG91cjtcbn1cblxuZnVuY3Rpb24gcHJvY2Vzc1llYXIoeWVhclN0cikge1xuICB2YXIgeWVhciA9ICt5ZWFyU3RyO1xuXG4gIGlmICh5ZWFyU3RyLmxlbmd0aCA8IDQgJiYgeWVhciA8IDEwMCkge1xuICAgIHllYXIgKz0geWVhciA8IDcwID8gMjAwMCA6IDE5MDA7XG4gIH1cblxuICByZXR1cm4geWVhcjtcbn1cblxuZnVuY3Rpb24gbG9va3VwTW9udGgobW9udGhTdHIpIHtcbiAgcmV0dXJuIHtcbiAgICBqYW46IDAsXG4gICAgamFudWFyeTogMCxcbiAgICBpOiAwLFxuICAgIGZlYjogMSxcbiAgICBmZWJydWFyeTogMSxcbiAgICBpaTogMSxcbiAgICBtYXI6IDIsXG4gICAgbWFyY2g6IDIsXG4gICAgaWlpOiAyLFxuICAgIGFwcjogMyxcbiAgICBhcHJpbDogMyxcbiAgICBpdjogMyxcbiAgICBtYXk6IDQsXG4gICAgdjogNCxcbiAgICBqdW46IDUsXG4gICAganVuZTogNSxcbiAgICB2aTogNSxcbiAgICBqdWw6IDYsXG4gICAganVseTogNixcbiAgICB2aWk6IDYsXG4gICAgYXVnOiA3LFxuICAgIGF1Z3VzdDogNyxcbiAgICB2aWlpOiA3LFxuICAgIHNlcDogOCxcbiAgICBzZXB0OiA4LFxuICAgIHNlcHRlbWJlcjogOCxcbiAgICBpeDogOCxcbiAgICBvY3Q6IDksXG4gICAgb2N0b2JlcjogOSxcbiAgICB4OiA5LFxuICAgIG5vdjogMTAsXG4gICAgbm92ZW1iZXI6IDEwLFxuICAgIHhpOiAxMCxcbiAgICBkZWM6IDExLFxuICAgIGRlY2VtYmVyOiAxMSxcbiAgICB4aWk6IDExXG4gIH1bbW9udGhTdHIudG9Mb3dlckNhc2UoKV07XG59XG5cbmZ1bmN0aW9uIGxvb2t1cFdlZWtkYXkoZGF5U3RyKSB7XG4gIHZhciBkZXNpcmVkU3VuZGF5TnVtYmVyID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiAwO1xuXG4gIHZhciBkYXlOdW1iZXJzID0ge1xuICAgIG1vbjogMSxcbiAgICBtb25kYXk6IDEsXG4gICAgdHVlOiAyLFxuICAgIHR1ZXNkYXk6IDIsXG4gICAgd2VkOiAzLFxuICAgIHdlZG5lc2RheTogMyxcbiAgICB0aHU6IDQsXG4gICAgdGh1cnNkYXk6IDQsXG4gICAgZnJpOiA1LFxuICAgIGZyaWRheTogNSxcbiAgICBzYXQ6IDYsXG4gICAgc2F0dXJkYXk6IDYsXG4gICAgc3VuOiAwLFxuICAgIHN1bmRheTogMFxuICB9O1xuXG4gIHJldHVybiBkYXlOdW1iZXJzW2RheVN0ci50b0xvd2VyQ2FzZSgpXSB8fCBkZXNpcmVkU3VuZGF5TnVtYmVyO1xufVxuXG5mdW5jdGlvbiBsb29rdXBSZWxhdGl2ZShyZWxUZXh0KSB7XG4gIHZhciByZWxhdGl2ZU51bWJlcnMgPSB7XG4gICAgbGFzdDogLTEsXG4gICAgcHJldmlvdXM6IC0xLFxuICAgIHRoaXM6IDAsXG4gICAgZmlyc3Q6IDEsXG4gICAgbmV4dDogMSxcbiAgICBzZWNvbmQ6IDIsXG4gICAgdGhpcmQ6IDMsXG4gICAgZm91cnRoOiA0LFxuICAgIGZpZnRoOiA1LFxuICAgIHNpeHRoOiA2LFxuICAgIHNldmVudGg6IDcsXG4gICAgZWlnaHQ6IDgsXG4gICAgZWlnaHRoOiA4LFxuICAgIG5pbnRoOiA5LFxuICAgIHRlbnRoOiAxMCxcbiAgICBlbGV2ZW50aDogMTEsXG4gICAgdHdlbGZ0aDogMTJcbiAgfTtcblxuICB2YXIgcmVsYXRpdmVCZWhhdmlvciA9IHtcbiAgICB0aGlzOiAxXG4gIH07XG5cbiAgdmFyIHJlbFRleHRMb3dlciA9IHJlbFRleHQudG9Mb3dlckNhc2UoKTtcblxuICByZXR1cm4ge1xuICAgIGFtb3VudDogcmVsYXRpdmVOdW1iZXJzW3JlbFRleHRMb3dlcl0sXG4gICAgYmVoYXZpb3I6IHJlbGF0aXZlQmVoYXZpb3JbcmVsVGV4dExvd2VyXSB8fCAwXG4gIH07XG59XG5cbmZ1bmN0aW9uIHByb2Nlc3NUekNvcnJlY3Rpb24odHpPZmZzZXQsIG9sZFZhbHVlKSB7XG4gIHZhciByZVR6Q29ycmVjdGlvbkxvb3NlID0gLyg/OkdNVCk/KFsrLV0pKFxcZCspKDo/KShcXGR7MCwyfSkvaTtcbiAgdHpPZmZzZXQgPSB0ek9mZnNldCAmJiB0ek9mZnNldC5tYXRjaChyZVR6Q29ycmVjdGlvbkxvb3NlKTtcblxuICBpZiAoIXR6T2Zmc2V0KSB7XG4gICAgcmV0dXJuIG9sZFZhbHVlO1xuICB9XG5cbiAgdmFyIHNpZ24gPSB0ek9mZnNldFsxXSA9PT0gJy0nID8gLTEgOiAxO1xuICB2YXIgaG91cnMgPSArdHpPZmZzZXRbMl07XG4gIHZhciBtaW51dGVzID0gK3R6T2Zmc2V0WzRdO1xuXG4gIGlmICghdHpPZmZzZXRbNF0gJiYgIXR6T2Zmc2V0WzNdKSB7XG4gICAgbWludXRlcyA9IE1hdGguZmxvb3IoaG91cnMgJSAxMDApO1xuICAgIGhvdXJzID0gTWF0aC5mbG9vcihob3VycyAvIDEwMCk7XG4gIH1cblxuICAvLyB0aW1lem9uZSBvZmZzZXQgaW4gc2Vjb25kc1xuICByZXR1cm4gc2lnbiAqIChob3VycyAqIDYwICsgbWludXRlcykgKiA2MDtcbn1cblxuLy8gdHogYWJicmV2YXRpb24gOiB0eiBvZmZzZXQgaW4gc2Vjb25kc1xudmFyIHR6QWJick9mZnNldHMgPSB7XG4gIGFjZHQ6IDM3ODAwLFxuICBhY3N0OiAzNDIwMCxcbiAgYWRkdDogLTcyMDAsXG4gIGFkdDogLTEwODAwLFxuICBhZWR0OiAzOTYwMCxcbiAgYWVzdDogMzYwMDAsXG4gIGFoZHQ6IC0zMjQwMCxcbiAgYWhzdDogLTM2MDAwLFxuICBha2R0OiAtMjg4MDAsXG4gIGFrc3Q6IC0zMjQwMCxcbiAgYW10OiAtMTM4NDAsXG4gIGFwdDogLTEwODAwLFxuICBhc3Q6IC0xNDQwMCxcbiAgYXdkdDogMzI0MDAsXG4gIGF3c3Q6IDI4ODAwLFxuICBhd3Q6IC0xMDgwMCxcbiAgYmRzdDogNzIwMCxcbiAgYmR0OiAtMzYwMDAsXG4gIGJtdDogLTE0MzA5LFxuICBic3Q6IDM2MDAsXG4gIGNhc3Q6IDM0MjAwLFxuICBjYXQ6IDcyMDAsXG4gIGNkZHQ6IC0xNDQwMCxcbiAgY2R0OiAtMTgwMDAsXG4gIGNlbXQ6IDEwODAwLFxuICBjZXN0OiA3MjAwLFxuICBjZXQ6IDM2MDAsXG4gIGNtdDogLTE1NDA4LFxuICBjcHQ6IC0xODAwMCxcbiAgY3N0OiAtMjE2MDAsXG4gIGN3dDogLTE4MDAwLFxuICBjaHN0OiAzNjAwMCxcbiAgZG10OiAtMTUyMSxcbiAgZWF0OiAxMDgwMCxcbiAgZWRkdDogLTEwODAwLFxuICBlZHQ6IC0xNDQwMCxcbiAgZWVzdDogMTA4MDAsXG4gIGVldDogNzIwMCxcbiAgZW10OiAtMjYyNDgsXG4gIGVwdDogLTE0NDAwLFxuICBlc3Q6IC0xODAwMCxcbiAgZXd0OiAtMTQ0MDAsXG4gIGZmbXQ6IC0xNDY2MCxcbiAgZm10OiAtNDA1NixcbiAgZ2R0OiAzOTYwMCxcbiAgZ210OiAwLFxuICBnc3Q6IDM2MDAwLFxuICBoZHQ6IC0zNDIwMCxcbiAgaGtzdDogMzI0MDAsXG4gIGhrdDogMjg4MDAsXG4gIGhtdDogLTE5Nzc2LFxuICBocHQ6IC0zNDIwMCxcbiAgaHN0OiAtMzYwMDAsXG4gIGh3dDogLTM0MjAwLFxuICBpZGR0OiAxNDQwMCxcbiAgaWR0OiAxMDgwMCxcbiAgaW10OiAyNTAyNSxcbiAgaXN0OiA3MjAwLFxuICBqZHQ6IDM2MDAwLFxuICBqbXQ6IDg0NDAsXG4gIGpzdDogMzI0MDAsXG4gIGtkdDogMzYwMDAsXG4gIGttdDogNTczNixcbiAga3N0OiAzMDYwMCxcbiAgbHN0OiA5Mzk0LFxuICBtZGR0OiAtMTgwMDAsXG4gIG1kc3Q6IDE2Mjc5LFxuICBtZHQ6IC0yMTYwMCxcbiAgbWVzdDogNzIwMCxcbiAgbWV0OiAzNjAwLFxuICBtbXQ6IDkwMTcsXG4gIG1wdDogLTIxNjAwLFxuICBtc2Q6IDE0NDAwLFxuICBtc2s6IDEwODAwLFxuICBtc3Q6IC0yNTIwMCxcbiAgbXd0OiAtMjE2MDAsXG4gIG5kZHQ6IC01NDAwLFxuICBuZHQ6IC05MDUyLFxuICBucHQ6IC05MDAwLFxuICBuc3Q6IC0xMjYwMCxcbiAgbnd0OiAtOTAwMCxcbiAgbnpkdDogNDY4MDAsXG4gIG56bXQ6IDQxNDAwLFxuICBuenN0OiA0MzIwMCxcbiAgcGRkdDogLTIxNjAwLFxuICBwZHQ6IC0yNTIwMCxcbiAgcGtzdDogMjE2MDAsXG4gIHBrdDogMTgwMDAsXG4gIHBsbXQ6IDI1NTkwLFxuICBwbXQ6IC0xMzIzNixcbiAgcHBtdDogLTE3MzQwLFxuICBwcHQ6IC0yNTIwMCxcbiAgcHN0OiAtMjg4MDAsXG4gIHB3dDogLTI1MjAwLFxuICBxbXQ6IC0xODg0MCxcbiAgcm10OiA1Nzk0LFxuICBzYXN0OiA3MjAwLFxuICBzZG10OiAtMTY4MDAsXG4gIHNqbXQ6IC0yMDE3MyxcbiAgc210OiAtMTM4ODQsXG4gIHNzdDogLTM5NjAwLFxuICB0Ym10OiAxMDc1MSxcbiAgdG10OiAxMjM0NCxcbiAgdWN0OiAwLFxuICB1dGM6IDAsXG4gIHdhc3Q6IDcyMDAsXG4gIHdhdDogMzYwMCxcbiAgd2VtdDogNzIwMCxcbiAgd2VzdDogMzYwMCxcbiAgd2V0OiAwLFxuICB3aWI6IDI1MjAwLFxuICB3aXRhOiAyODgwMCxcbiAgd2l0OiAzMjQwMCxcbiAgd210OiA1MDQwLFxuICB5ZGR0OiAtMjUyMDAsXG4gIHlkdDogLTI4ODAwLFxuICB5cHQ6IC0yODgwMCxcbiAgeXN0OiAtMzI0MDAsXG4gIHl3dDogLTI4ODAwLFxuICBhOiAzNjAwLFxuICBiOiA3MjAwLFxuICBjOiAxMDgwMCxcbiAgZDogMTQ0MDAsXG4gIGU6IDE4MDAwLFxuICBmOiAyMTYwMCxcbiAgZzogMjUyMDAsXG4gIGg6IDI4ODAwLFxuICBpOiAzMjQwMCxcbiAgazogMzYwMDAsXG4gIGw6IDM5NjAwLFxuICBtOiA0MzIwMCxcbiAgbjogLTM2MDAsXG4gIG86IC03MjAwLFxuICBwOiAtMTA4MDAsXG4gIHE6IC0xNDQwMCxcbiAgcjogLTE4MDAwLFxuICBzOiAtMjE2MDAsXG4gIHQ6IC0yNTIwMCxcbiAgdTogLTI4ODAwLFxuICB2OiAtMzI0MDAsXG4gIHc6IC0zNjAwMCxcbiAgeDogLTM5NjAwLFxuICB5OiAtNDMyMDAsXG4gIHo6IDBcbn07XG5cbnZhciBmb3JtYXRzID0ge1xuICB5ZXN0ZXJkYXk6IHtcbiAgICByZWdleDogL155ZXN0ZXJkYXkvaSxcbiAgICBuYW1lOiAneWVzdGVyZGF5JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2soKSB7XG4gICAgICB0aGlzLnJkIC09IDE7XG4gICAgICByZXR1cm4gdGhpcy5yZXNldFRpbWUoKTtcbiAgICB9XG4gIH0sXG5cbiAgbm93OiB7XG4gICAgcmVnZXg6IC9ebm93L2ksXG4gICAgbmFtZTogJ25vdydcbiAgICAvLyBkbyBub3RoaW5nXG4gIH0sXG5cbiAgbm9vbjoge1xuICAgIHJlZ2V4OiAvXm5vb24vaSxcbiAgICBuYW1lOiAnbm9vbicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCkge1xuICAgICAgcmV0dXJuIHRoaXMucmVzZXRUaW1lKCkgJiYgdGhpcy50aW1lKDEyLCAwLCAwLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgbWlkbmlnaHRPclRvZGF5OiB7XG4gICAgcmVnZXg6IC9eKG1pZG5pZ2h0fHRvZGF5KS9pLFxuICAgIG5hbWU6ICdtaWRuaWdodCB8IHRvZGF5JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2soKSB7XG4gICAgICByZXR1cm4gdGhpcy5yZXNldFRpbWUoKTtcbiAgICB9XG4gIH0sXG5cbiAgdG9tb3Jyb3c6IHtcbiAgICByZWdleDogL150b21vcnJvdy9pLFxuICAgIG5hbWU6ICd0b21vcnJvdycsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCkge1xuICAgICAgdGhpcy5yZCArPSAxO1xuICAgICAgcmV0dXJuIHRoaXMucmVzZXRUaW1lKCk7XG4gICAgfVxuICB9LFxuXG4gIHRpbWVzdGFtcDoge1xuICAgIHJlZ2V4OiAvXkAoLT9cXGQrKS9pLFxuICAgIG5hbWU6ICd0aW1lc3RhbXAnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgdGltZXN0YW1wKSB7XG4gICAgICB0aGlzLnJzICs9ICt0aW1lc3RhbXA7XG4gICAgICB0aGlzLnkgPSAxOTcwO1xuICAgICAgdGhpcy5tID0gMDtcbiAgICAgIHRoaXMuZCA9IDE7XG4gICAgICB0aGlzLmRhdGVzID0gMDtcblxuICAgICAgcmV0dXJuIHRoaXMucmVzZXRUaW1lKCkgJiYgdGhpcy56b25lKDApO1xuICAgIH1cbiAgfSxcblxuICBmaXJzdE9yTGFzdERheToge1xuICAgIHJlZ2V4OiAvXihmaXJzdHxsYXN0KSBkYXkgb2YvaSxcbiAgICBuYW1lOiAnZmlyc3RkYXlvZiB8IGxhc3RkYXlvZicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBkYXkpIHtcbiAgICAgIGlmIChkYXkudG9Mb3dlckNhc2UoKSA9PT0gJ2ZpcnN0Jykge1xuICAgICAgICB0aGlzLmZpcnN0T3JMYXN0RGF5T2ZNb250aCA9IDE7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLmZpcnN0T3JMYXN0RGF5T2ZNb250aCA9IC0xO1xuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICBiYWNrT3JGcm9udE9mOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXihiYWNrfGZyb250KSBvZiAnICsgcmVIb3VyMjQgKyByZVNwYWNlT3B0ICsgcmVNZXJpZGlhbiArICc/JywgJ2knKSxcbiAgICBuYW1lOiAnYmFja29mIHwgZnJvbnRvZicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBzaWRlLCBob3VycywgbWVyaWRpYW4pIHtcbiAgICAgIHZhciBiYWNrID0gc2lkZS50b0xvd2VyQ2FzZSgpID09PSAnYmFjayc7XG4gICAgICB2YXIgaG91ciA9ICtob3VycztcbiAgICAgIHZhciBtaW51dGUgPSAxNTtcblxuICAgICAgaWYgKCFiYWNrKSB7XG4gICAgICAgIGhvdXIgLT0gMTtcbiAgICAgICAgbWludXRlID0gNDU7XG4gICAgICB9XG5cbiAgICAgIGhvdXIgPSBwcm9jZXNzTWVyaWRpYW4oaG91ciwgbWVyaWRpYW4pO1xuXG4gICAgICByZXR1cm4gdGhpcy5yZXNldFRpbWUoKSAmJiB0aGlzLnRpbWUoaG91ciwgbWludXRlLCAwLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgd2Vla2RheU9mOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXignICsgcmVSZWx0ZXh0bnVtYmVyICsgJ3wnICsgcmVSZWx0ZXh0dGV4dCArICcpJyArIHJlU3BhY2UgKyAnKCcgKyByZURheWZ1bGwgKyAnfCcgKyByZURheWFiYnIgKyAnKScgKyByZVNwYWNlICsgJ29mJywgJ2knKSxcbiAgICBuYW1lOiAnd2Vla2RheW9mJ1xuICAgIC8vIHRvZG9cbiAgfSxcblxuICBtc3NxbHRpbWU6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlSG91cjEyICsgJzonICsgcmVNaW51dGVseiArICc6JyArIHJlU2Vjb25kbHogKyAnWzouXShbMC05XSspJyArIHJlTWVyaWRpYW4sICdpJyksXG4gICAgbmFtZTogJ21zc3FsdGltZScsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBob3VyLCBtaW51dGUsIHNlY29uZCwgZnJhYywgbWVyaWRpYW4pIHtcbiAgICAgIHJldHVybiB0aGlzLnRpbWUocHJvY2Vzc01lcmlkaWFuKCtob3VyLCBtZXJpZGlhbiksICttaW51dGUsICtzZWNvbmQsICtmcmFjLnN1YnN0cigwLCAzKSk7XG4gICAgfVxuICB9LFxuXG4gIHRpbWVMb25nMTI6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlSG91cjEyICsgJ1s6Ll0nICsgcmVNaW51dGUgKyAnWzouXScgKyByZVNlY29uZGx6ICsgcmVTcGFjZU9wdCArIHJlTWVyaWRpYW4sICdpJyksXG4gICAgbmFtZTogJ3RpbWVsb25nMTInLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgaG91ciwgbWludXRlLCBzZWNvbmQsIG1lcmlkaWFuKSB7XG4gICAgICByZXR1cm4gdGhpcy50aW1lKHByb2Nlc3NNZXJpZGlhbigraG91ciwgbWVyaWRpYW4pLCArbWludXRlLCArc2Vjb25kLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgdGltZVNob3J0MTI6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlSG91cjEyICsgJ1s6Ll0nICsgcmVNaW51dGVseiArIHJlU3BhY2VPcHQgKyByZU1lcmlkaWFuLCAnaScpLFxuICAgIG5hbWU6ICd0aW1lc2hvcnQxMicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBob3VyLCBtaW51dGUsIG1lcmlkaWFuKSB7XG4gICAgICByZXR1cm4gdGhpcy50aW1lKHByb2Nlc3NNZXJpZGlhbigraG91ciwgbWVyaWRpYW4pLCArbWludXRlLCAwLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgdGltZVRpbnkxMjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVIb3VyMTIgKyByZVNwYWNlT3B0ICsgcmVNZXJpZGlhbiwgJ2knKSxcbiAgICBuYW1lOiAndGltZXRpbnkxMicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBob3VyLCBtZXJpZGlhbikge1xuICAgICAgcmV0dXJuIHRoaXMudGltZShwcm9jZXNzTWVyaWRpYW4oK2hvdXIsIG1lcmlkaWFuKSwgMCwgMCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIHNvYXA6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlWWVhcjQgKyAnLScgKyByZU1vbnRobHogKyAnLScgKyByZURheWx6ICsgJ1QnICsgcmVIb3VyMjRseiArICc6JyArIHJlTWludXRlbHogKyAnOicgKyByZVNlY29uZGx6ICsgcmVGcmFjICsgcmVUekNvcnJlY3Rpb24gKyAnPycsICdpJyksXG4gICAgbmFtZTogJ3NvYXAnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgbW9udGgsIGRheSwgaG91ciwgbWludXRlLCBzZWNvbmQsIGZyYWMsIHR6Q29ycmVjdGlvbikge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKCt5ZWFyLCBtb250aCAtIDEsICtkYXkpICYmIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgK3NlY29uZCwgK2ZyYWMuc3Vic3RyKDAsIDMpKSAmJiB0aGlzLnpvbmUocHJvY2Vzc1R6Q29ycmVjdGlvbih0ekNvcnJlY3Rpb24pKTtcbiAgICB9XG4gIH0sXG5cbiAgd2RkeDoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVZZWFyNCArICctJyArIHJlTW9udGggKyAnLScgKyByZURheSArICdUJyArIHJlSG91cjI0ICsgJzonICsgcmVNaW51dGUgKyAnOicgKyByZVNlY29uZCksXG4gICAgbmFtZTogJ3dkZHgnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgbW9udGgsIGRheSwgaG91ciwgbWludXRlLCBzZWNvbmQpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgbW9udGggLSAxLCArZGF5KSAmJiB0aGlzLnRpbWUoK2hvdXIsICttaW51dGUsICtzZWNvbmQsIDApO1xuICAgIH1cbiAgfSxcblxuICBleGlmOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0ICsgJzonICsgcmVNb250aGx6ICsgJzonICsgcmVEYXlseiArICcgJyArIHJlSG91cjI0bHogKyAnOicgKyByZU1pbnV0ZWx6ICsgJzonICsgcmVTZWNvbmRseiwgJ2knKSxcbiAgICBuYW1lOiAnZXhpZicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5LCBob3VyLCBtaW51dGUsIHNlY29uZCkge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKCt5ZWFyLCBtb250aCAtIDEsICtkYXkpICYmIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgK3NlY29uZCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIHhtbFJwYzoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVZZWFyNCArIHJlTW9udGhseiArIHJlRGF5bHogKyAnVCcgKyByZUhvdXIyNCArICc6JyArIHJlTWludXRlbHogKyAnOicgKyByZVNlY29uZGx6KSxcbiAgICBuYW1lOiAneG1scnBjJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIHllYXIsIG1vbnRoLCBkYXksIGhvdXIsIG1pbnV0ZSwgc2Vjb25kKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQoK3llYXIsIG1vbnRoIC0gMSwgK2RheSkgJiYgdGhpcy50aW1lKCtob3VyLCArbWludXRlLCArc2Vjb25kLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgeG1sUnBjTm9Db2xvbjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVZZWFyNCArIHJlTW9udGhseiArIHJlRGF5bHogKyAnW1R0XScgKyByZUhvdXIyNCArIHJlTWludXRlbHogKyByZVNlY29uZGx6KSxcbiAgICBuYW1lOiAneG1scnBjbm9jb2xvbicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5LCBob3VyLCBtaW51dGUsIHNlY29uZCkge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKCt5ZWFyLCBtb250aCAtIDEsICtkYXkpICYmIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgK3NlY29uZCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIGNsZjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVEYXkgKyAnLygnICsgcmVNb250aEFiYnIgKyAnKS8nICsgcmVZZWFyNCArICc6JyArIHJlSG91cjI0bHogKyAnOicgKyByZU1pbnV0ZWx6ICsgJzonICsgcmVTZWNvbmRseiArIHJlU3BhY2UgKyByZVR6Q29ycmVjdGlvbiwgJ2knKSxcbiAgICBuYW1lOiAnY2xmJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGRheSwgbW9udGgsIHllYXIsIGhvdXIsIG1pbnV0ZSwgc2Vjb25kLCB0ekNvcnJlY3Rpb24pIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgbG9va3VwTW9udGgobW9udGgpLCArZGF5KSAmJiB0aGlzLnRpbWUoK2hvdXIsICttaW51dGUsICtzZWNvbmQsIDApICYmIHRoaXMuem9uZShwcm9jZXNzVHpDb3JyZWN0aW9uKHR6Q29ycmVjdGlvbikpO1xuICAgIH1cbiAgfSxcblxuICBpc284NjAxbG9uZzoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ150PycgKyByZUhvdXIyNCArICdbOi5dJyArIHJlTWludXRlICsgJ1s6Ll0nICsgcmVTZWNvbmQgKyByZUZyYWMsICdpJyksXG4gICAgbmFtZTogJ2lzbzg2MDFsb25nJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGhvdXIsIG1pbnV0ZSwgc2Vjb25kLCBmcmFjKSB7XG4gICAgICByZXR1cm4gdGhpcy50aW1lKCtob3VyLCArbWludXRlLCArc2Vjb25kLCArZnJhYy5zdWJzdHIoMCwgMykpO1xuICAgIH1cbiAgfSxcblxuICBkYXRlVGV4dHVhbDoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVNb250aFRleHQgKyAnWyAuXFxcXHQtXSonICsgcmVEYXkgKyAnWywuc3RuZHJoXFxcXHQgXSsnICsgcmVZZWFyLCAnaScpLFxuICAgIG5hbWU6ICdkYXRldGV4dHVhbCcsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBtb250aCwgZGF5LCB5ZWFyKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQocHJvY2Vzc1llYXIoeWVhciksIGxvb2t1cE1vbnRoKG1vbnRoKSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIHBvaW50ZWREYXRlNDoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVEYXkgKyAnWy5cXFxcdC1dJyArIHJlTW9udGggKyAnWy4tXScgKyByZVllYXI0KSxcbiAgICBuYW1lOiAncG9pbnRlZGRhdGU0JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGRheSwgbW9udGgsIHllYXIpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgbW9udGggLSAxLCArZGF5KTtcbiAgICB9XG4gIH0sXG5cbiAgcG9pbnRlZERhdGUyOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZURheSArICdbLlxcXFx0XScgKyByZU1vbnRoICsgJ1xcXFwuJyArIHJlWWVhcjIpLFxuICAgIG5hbWU6ICdwb2ludGVkZGF0ZTInLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgZGF5LCBtb250aCwgeWVhcikge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKHByb2Nlc3NZZWFyKHllYXIpLCBtb250aCAtIDEsICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICB0aW1lTG9uZzI0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXnQ/JyArIHJlSG91cjI0ICsgJ1s6Ll0nICsgcmVNaW51dGUgKyAnWzouXScgKyByZVNlY29uZCksXG4gICAgbmFtZTogJ3RpbWVsb25nMjQnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgaG91ciwgbWludXRlLCBzZWNvbmQpIHtcbiAgICAgIHJldHVybiB0aGlzLnRpbWUoK2hvdXIsICttaW51dGUsICtzZWNvbmQsIDApO1xuICAgIH1cbiAgfSxcblxuICBkYXRlTm9Db2xvbjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVZZWFyNCArIHJlTW9udGhseiArIHJlRGF5bHopLFxuICAgIG5hbWU6ICdkYXRlbm9jb2xvbicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5KSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQoK3llYXIsIG1vbnRoIC0gMSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIHBneWRvdGQ6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlWWVhcjQgKyAnXFxcXC4/JyArIHJlRGF5T2ZZZWFyKSxcbiAgICBuYW1lOiAncGd5ZG90ZCcsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBkYXkpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgMCwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIHRpbWVTaG9ydDI0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXnQ/JyArIHJlSG91cjI0ICsgJ1s6Ll0nICsgcmVNaW51dGUsICdpJyksXG4gICAgbmFtZTogJ3RpbWVzaG9ydDI0JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGhvdXIsIG1pbnV0ZSkge1xuICAgICAgcmV0dXJuIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgMCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIGlzbzg2MDFub0NvbG9uOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXnQ/JyArIHJlSG91cjI0bHogKyByZU1pbnV0ZWx6ICsgcmVTZWNvbmRseiwgJ2knKSxcbiAgICBuYW1lOiAnaXNvODYwMW5vY29sb24nLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgaG91ciwgbWludXRlLCBzZWNvbmQpIHtcbiAgICAgIHJldHVybiB0aGlzLnRpbWUoK2hvdXIsICttaW51dGUsICtzZWNvbmQsIDApO1xuICAgIH1cbiAgfSxcblxuICBpc284NjAxZGF0ZVNsYXNoOiB7XG4gICAgLy8gZXZlbnRob3VnaCB0aGUgdHJhaWxpbmcgc2xhc2ggaXMgb3B0aW9uYWwgaW4gUEhQXG4gICAgLy8gaGVyZSBpdCdzIG1hbmRhdG9yeSBhbmQgaW5wdXRzIHdpdGhvdXQgdGhlIHNsYXNoXG4gICAgLy8gYXJlIGhhbmRsZWQgYnkgZGF0ZXNsYXNoXG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0ICsgJy8nICsgcmVNb250aGx6ICsgJy8nICsgcmVEYXlseiArICcvJyksXG4gICAgbmFtZTogJ2lzbzg2MDFkYXRlc2xhc2gnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgbW9udGgsIGRheSkge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKCt5ZWFyLCBtb250aCAtIDEsICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICBkYXRlU2xhc2g6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlWWVhcjQgKyAnLycgKyByZU1vbnRoICsgJy8nICsgcmVEYXkpLFxuICAgIG5hbWU6ICdkYXRlc2xhc2gnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgbW9udGgsIGRheSkge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKCt5ZWFyLCBtb250aCAtIDEsICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICBhbWVyaWNhbjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVNb250aCArICcvJyArIHJlRGF5ICsgJy8nICsgcmVZZWFyKSxcbiAgICBuYW1lOiAnYW1lcmljYW4nLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgbW9udGgsIGRheSwgeWVhcikge1xuICAgICAgcmV0dXJuIHRoaXMueW1kKHByb2Nlc3NZZWFyKHllYXIpLCBtb250aCAtIDEsICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICBhbWVyaWNhblNob3J0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZU1vbnRoICsgJy8nICsgcmVEYXkpLFxuICAgIG5hbWU6ICdhbWVyaWNhbnNob3J0JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIG1vbnRoLCBkYXkpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCh0aGlzLnksIG1vbnRoIC0gMSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIGdudURhdGVTaG9ydE9ySXNvODYwMWRhdGUyOiB7XG4gICAgLy8gaXNvODYwMWRhdGUyIGlzIGNvbXBsZXRlIHN1YnNldCBvZiBnbnVkYXRlc2hvcnRcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlWWVhciArICctJyArIHJlTW9udGggKyAnLScgKyByZURheSksXG4gICAgbmFtZTogJ2dudWRhdGVzaG9ydCB8IGlzbzg2MDFkYXRlMicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5KSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQocHJvY2Vzc1llYXIoeWVhciksIG1vbnRoIC0gMSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIGlzbzg2MDFkYXRlNDoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVZZWFyNHdpdGhTaWduICsgJy0nICsgcmVNb250aGx6ICsgJy0nICsgcmVEYXlseiksXG4gICAgbmFtZTogJ2lzbzg2MDFkYXRlNCcsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5KSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQoK3llYXIsIG1vbnRoIC0gMSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIGdudU5vQ29sb246IHtcbiAgICByZWdleDogUmVnRXhwKCdedD8nICsgcmVIb3VyMjRseiArIHJlTWludXRlbHosICdpJyksXG4gICAgbmFtZTogJ2dudW5vY29sb24nLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgaG91ciwgbWludXRlKSB7XG4gICAgICAvLyB0aGlzIHJ1bGUgaXMgYSBzcGVjaWFsIGNhc2VcbiAgICAgIC8vIGlmIHRpbWUgd2FzIGFscmVhZHkgc2V0IG9uY2UgYnkgYW55IHByZWNlZGluZyBydWxlLCBpdCBzZXRzIHRoZSBjYXB0dXJlZCB2YWx1ZSBhcyB5ZWFyXG4gICAgICBzd2l0Y2ggKHRoaXMudGltZXMpIHtcbiAgICAgICAgY2FzZSAwOlxuICAgICAgICAgIHJldHVybiB0aGlzLnRpbWUoK2hvdXIsICttaW51dGUsIDAsIHRoaXMuZik7XG4gICAgICAgIGNhc2UgMTpcbiAgICAgICAgICB0aGlzLnkgPSBob3VyICogMTAwICsgK21pbnV0ZTtcbiAgICAgICAgICB0aGlzLnRpbWVzKys7XG5cbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICB9LFxuXG4gIGdudURhdGVTaG9ydGVyOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0ICsgJy0nICsgcmVNb250aCksXG4gICAgbmFtZTogJ2dudWRhdGVzaG9ydGVyJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIHllYXIsIG1vbnRoKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQoK3llYXIsIG1vbnRoIC0gMSwgMSk7XG4gICAgfVxuICB9LFxuXG4gIHBnVGV4dFJldmVyc2U6IHtcbiAgICAvLyBub3RlOiBhbGxvd2VkIHllYXJzIGFyZSBmcm9tIDMyLTk5OTlcbiAgICAvLyB5ZWFycyBiZWxvdyAzMiBzaG91bGQgYmUgdHJlYXRlZCBhcyBkYXlzIGluIGRhdGVmdWxsXG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyAnKFxcXFxkezMsNH18WzQtOV1cXFxcZHwzWzItOV0pLSgnICsgcmVNb250aEFiYnIgKyAnKS0nICsgcmVEYXlseiwgJ2knKSxcbiAgICBuYW1lOiAncGd0ZXh0cmV2ZXJzZScsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCB5ZWFyLCBtb250aCwgZGF5KSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQocHJvY2Vzc1llYXIoeWVhciksIGxvb2t1cE1vbnRoKG1vbnRoKSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIGRhdGVGdWxsOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZURheSArICdbIFxcXFx0Li1dKicgKyByZU1vbnRoVGV4dCArICdbIFxcXFx0Li1dKicgKyByZVllYXIsICdpJyksXG4gICAgbmFtZTogJ2RhdGVmdWxsJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGRheSwgbW9udGgsIHllYXIpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZChwcm9jZXNzWWVhcih5ZWFyKSwgbG9va3VwTW9udGgobW9udGgpLCArZGF5KTtcbiAgICB9XG4gIH0sXG5cbiAgZGF0ZU5vRGF5OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZU1vbnRoVGV4dCArICdbIC5cXFxcdC1dKicgKyByZVllYXI0LCAnaScpLFxuICAgIG5hbWU6ICdkYXRlbm9kYXknLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgbW9udGgsIHllYXIpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgbG9va3VwTW9udGgobW9udGgpLCAxKTtcbiAgICB9XG4gIH0sXG5cbiAgZGF0ZU5vRGF5UmV2OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0ICsgJ1sgLlxcXFx0LV0qJyArIHJlTW9udGhUZXh0LCAnaScpLFxuICAgIG5hbWU6ICdkYXRlbm9kYXlyZXYnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgbW9udGgpIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCgreWVhciwgbG9va3VwTW9udGgobW9udGgpLCAxKTtcbiAgICB9XG4gIH0sXG5cbiAgcGdUZXh0U2hvcnQ6IHtcbiAgICByZWdleDogUmVnRXhwKCdeKCcgKyByZU1vbnRoQWJiciArICcpLScgKyByZURheWx6ICsgJy0nICsgcmVZZWFyLCAnaScpLFxuICAgIG5hbWU6ICdwZ3RleHRzaG9ydCcsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBtb250aCwgZGF5LCB5ZWFyKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQocHJvY2Vzc1llYXIoeWVhciksIGxvb2t1cE1vbnRoKG1vbnRoKSwgK2RheSk7XG4gICAgfVxuICB9LFxuXG4gIGRhdGVOb1llYXI6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlRGF0ZU5vWWVhciwgJ2knKSxcbiAgICBuYW1lOiAnZGF0ZW5veWVhcicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBtb250aCwgZGF5KSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICBkYXRlTm9ZZWFyUmV2OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZURheSArICdbIC5cXFxcdC1dKicgKyByZU1vbnRoVGV4dCwgJ2knKSxcbiAgICBuYW1lOiAnZGF0ZW5veWVhcnJldicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBkYXksIG1vbnRoKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksICtkYXkpO1xuICAgIH1cbiAgfSxcblxuICBpc29XZWVrRGF5OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0ICsgJy0/VycgKyByZVdlZWtPZlllYXIgKyAnKD86LT8oWzAtN10pKT8nKSxcbiAgICBuYW1lOiAnaXNvd2Vla2RheSB8IGlzb3dlZWsnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhciwgd2VlaywgZGF5KSB7XG4gICAgICBkYXkgPSBkYXkgPyArZGF5IDogMTtcblxuICAgICAgaWYgKCF0aGlzLnltZCgreWVhciwgMCwgMSkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBnZXQgZGF5IG9mIHdlZWsgZm9yIEphbiAxc3RcbiAgICAgIHZhciBkYXlPZldlZWsgPSBuZXcgRGF0ZSh0aGlzLnksIHRoaXMubSwgdGhpcy5kKS5nZXREYXkoKTtcblxuICAgICAgLy8gYW5kIHVzZSB0aGUgZGF5IHRvIGZpZ3VyZSBvdXQgdGhlIG9mZnNldCBmb3IgZGF5IDEgb2Ygd2VlayAxXG4gICAgICBkYXlPZldlZWsgPSAwIC0gKGRheU9mV2VlayA+IDQgPyBkYXlPZldlZWsgLSA3IDogZGF5T2ZXZWVrKTtcblxuICAgICAgdGhpcy5yZCArPSBkYXlPZldlZWsgKyAod2VlayAtIDEpICogNyArIGRheTtcbiAgICB9XG4gIH0sXG5cbiAgcmVsYXRpdmVUZXh0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXignICsgcmVSZWx0ZXh0bnVtYmVyICsgJ3wnICsgcmVSZWx0ZXh0dGV4dCArICcpJyArIHJlU3BhY2UgKyAnKCcgKyByZVJlbHRleHR1bml0ICsgJyknLCAnaScpLFxuICAgIG5hbWU6ICdyZWxhdGl2ZXRleHQnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgcmVsVmFsdWUsIHJlbFVuaXQpIHtcbiAgICAgIC8vIHRvZG86IGltcGxlbWVudCBoYW5kbGluZyBvZiAndGhpcyB0aW1lLXVuaXQnXG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcbiAgICAgIHZhciBfbG9va3VwUmVsYXRpdmUgPSBsb29rdXBSZWxhdGl2ZShyZWxWYWx1ZSksXG4gICAgICAgICAgYW1vdW50ID0gX2xvb2t1cFJlbGF0aXZlLmFtb3VudCxcbiAgICAgICAgICBiZWhhdmlvciA9IF9sb29rdXBSZWxhdGl2ZS5iZWhhdmlvcjtcblxuICAgICAgc3dpdGNoIChyZWxVbml0LnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgY2FzZSAnc2VjJzpcbiAgICAgICAgY2FzZSAnc2Vjcyc6XG4gICAgICAgIGNhc2UgJ3NlY29uZCc6XG4gICAgICAgIGNhc2UgJ3NlY29uZHMnOlxuICAgICAgICAgIHRoaXMucnMgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdtaW4nOlxuICAgICAgICBjYXNlICdtaW5zJzpcbiAgICAgICAgY2FzZSAnbWludXRlJzpcbiAgICAgICAgY2FzZSAnbWludXRlcyc6XG4gICAgICAgICAgdGhpcy5yaSArPSBhbW91bnQ7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2hvdXInOlxuICAgICAgICBjYXNlICdob3Vycyc6XG4gICAgICAgICAgdGhpcy5yaCArPSBhbW91bnQ7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2RheSc6XG4gICAgICAgIGNhc2UgJ2RheXMnOlxuICAgICAgICAgIHRoaXMucmQgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdmb3J0bmlnaHQnOlxuICAgICAgICBjYXNlICdmb3J0bmlnaHRzJzpcbiAgICAgICAgY2FzZSAnZm9ydGhuaWdodCc6XG4gICAgICAgIGNhc2UgJ2ZvcnRobmlnaHRzJzpcbiAgICAgICAgICB0aGlzLnJkICs9IGFtb3VudCAqIDE0O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICd3ZWVrJzpcbiAgICAgICAgY2FzZSAnd2Vla3MnOlxuICAgICAgICAgIHRoaXMucmQgKz0gYW1vdW50ICogNztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbW9udGgnOlxuICAgICAgICBjYXNlICdtb250aHMnOlxuICAgICAgICAgIHRoaXMucm0gKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICd5ZWFyJzpcbiAgICAgICAgY2FzZSAneWVhcnMnOlxuICAgICAgICAgIHRoaXMucnkgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdtb24nOmNhc2UgJ21vbmRheSc6XG4gICAgICAgIGNhc2UgJ3R1ZSc6Y2FzZSAndHVlc2RheSc6XG4gICAgICAgIGNhc2UgJ3dlZCc6Y2FzZSAnd2VkbmVzZGF5JzpcbiAgICAgICAgY2FzZSAndGh1JzpjYXNlICd0aHVyc2RheSc6XG4gICAgICAgIGNhc2UgJ2ZyaSc6Y2FzZSAnZnJpZGF5JzpcbiAgICAgICAgY2FzZSAnc2F0JzpjYXNlICdzYXR1cmRheSc6XG4gICAgICAgIGNhc2UgJ3N1bic6Y2FzZSAnc3VuZGF5JzpcbiAgICAgICAgICB0aGlzLnJlc2V0VGltZSgpO1xuICAgICAgICAgIHRoaXMud2Vla2RheSA9IGxvb2t1cFdlZWtkYXkocmVsVW5pdCwgNyk7XG4gICAgICAgICAgdGhpcy53ZWVrZGF5QmVoYXZpb3IgPSAxO1xuICAgICAgICAgIHRoaXMucmQgKz0gKGFtb3VudCA+IDAgPyBhbW91bnQgLSAxIDogYW1vdW50KSAqIDc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3dlZWtkYXknOlxuICAgICAgICBjYXNlICd3ZWVrZGF5cyc6XG4gICAgICAgICAgLy8gdG9kb1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICByZWxhdGl2ZToge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14oWystXSopWyBcXFxcdF0qKFxcXFxkKyknICsgcmVTcGFjZU9wdCArICcoJyArIHJlUmVsdGV4dHVuaXQgKyAnfHdlZWspJywgJ2knKSxcbiAgICBuYW1lOiAncmVsYXRpdmUnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgc2lnbnMsIHJlbFZhbHVlLCByZWxVbml0KSB7XG4gICAgICB2YXIgbWludXNlcyA9IHNpZ25zLnJlcGxhY2UoL1teLV0vZywgJycpLmxlbmd0aDtcblxuICAgICAgdmFyIGFtb3VudCA9ICtyZWxWYWx1ZSAqIE1hdGgucG93KC0xLCBtaW51c2VzKTtcblxuICAgICAgc3dpdGNoIChyZWxVbml0LnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgY2FzZSAnc2VjJzpcbiAgICAgICAgY2FzZSAnc2Vjcyc6XG4gICAgICAgIGNhc2UgJ3NlY29uZCc6XG4gICAgICAgIGNhc2UgJ3NlY29uZHMnOlxuICAgICAgICAgIHRoaXMucnMgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdtaW4nOlxuICAgICAgICBjYXNlICdtaW5zJzpcbiAgICAgICAgY2FzZSAnbWludXRlJzpcbiAgICAgICAgY2FzZSAnbWludXRlcyc6XG4gICAgICAgICAgdGhpcy5yaSArPSBhbW91bnQ7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2hvdXInOlxuICAgICAgICBjYXNlICdob3Vycyc6XG4gICAgICAgICAgdGhpcy5yaCArPSBhbW91bnQ7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2RheSc6XG4gICAgICAgIGNhc2UgJ2RheXMnOlxuICAgICAgICAgIHRoaXMucmQgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdmb3J0bmlnaHQnOlxuICAgICAgICBjYXNlICdmb3J0bmlnaHRzJzpcbiAgICAgICAgY2FzZSAnZm9ydGhuaWdodCc6XG4gICAgICAgIGNhc2UgJ2ZvcnRobmlnaHRzJzpcbiAgICAgICAgICB0aGlzLnJkICs9IGFtb3VudCAqIDE0O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICd3ZWVrJzpcbiAgICAgICAgY2FzZSAnd2Vla3MnOlxuICAgICAgICAgIHRoaXMucmQgKz0gYW1vdW50ICogNztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbW9udGgnOlxuICAgICAgICBjYXNlICdtb250aHMnOlxuICAgICAgICAgIHRoaXMucm0gKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICd5ZWFyJzpcbiAgICAgICAgY2FzZSAneWVhcnMnOlxuICAgICAgICAgIHRoaXMucnkgKz0gYW1vdW50O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdtb24nOmNhc2UgJ21vbmRheSc6XG4gICAgICAgIGNhc2UgJ3R1ZSc6Y2FzZSAndHVlc2RheSc6XG4gICAgICAgIGNhc2UgJ3dlZCc6Y2FzZSAnd2VkbmVzZGF5JzpcbiAgICAgICAgY2FzZSAndGh1JzpjYXNlICd0aHVyc2RheSc6XG4gICAgICAgIGNhc2UgJ2ZyaSc6Y2FzZSAnZnJpZGF5JzpcbiAgICAgICAgY2FzZSAnc2F0JzpjYXNlICdzYXR1cmRheSc6XG4gICAgICAgIGNhc2UgJ3N1bic6Y2FzZSAnc3VuZGF5JzpcbiAgICAgICAgICB0aGlzLnJlc2V0VGltZSgpO1xuICAgICAgICAgIHRoaXMud2Vla2RheSA9IGxvb2t1cFdlZWtkYXkocmVsVW5pdCwgNyk7XG4gICAgICAgICAgdGhpcy53ZWVrZGF5QmVoYXZpb3IgPSAxO1xuICAgICAgICAgIHRoaXMucmQgKz0gKGFtb3VudCA+IDAgPyBhbW91bnQgLSAxIDogYW1vdW50KSAqIDc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3dlZWtkYXknOlxuICAgICAgICBjYXNlICd3ZWVrZGF5cyc6XG4gICAgICAgICAgLy8gdG9kb1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICBkYXlUZXh0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXignICsgcmVEYXl0ZXh0ICsgJyknLCAnaScpLFxuICAgIG5hbWU6ICdkYXl0ZXh0JyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGRheVRleHQpIHtcbiAgICAgIHRoaXMucmVzZXRUaW1lKCk7XG4gICAgICB0aGlzLndlZWtkYXkgPSBsb29rdXBXZWVrZGF5KGRheVRleHQsIDApO1xuXG4gICAgICBpZiAodGhpcy53ZWVrZGF5QmVoYXZpb3IgIT09IDIpIHtcbiAgICAgICAgdGhpcy53ZWVrZGF5QmVoYXZpb3IgPSAxO1xuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICByZWxhdGl2ZVRleHRXZWVrOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXignICsgcmVSZWx0ZXh0dGV4dCArICcpJyArIHJlU3BhY2UgKyAnd2VlaycsICdpJyksXG4gICAgbmFtZTogJ3JlbGF0aXZldGV4dHdlZWsnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgcmVsVGV4dCkge1xuICAgICAgdGhpcy53ZWVrZGF5QmVoYXZpb3IgPSAyO1xuXG4gICAgICBzd2l0Y2ggKHJlbFRleHQudG9Mb3dlckNhc2UoKSkge1xuICAgICAgICBjYXNlICd0aGlzJzpcbiAgICAgICAgICB0aGlzLnJkICs9IDA7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ25leHQnOlxuICAgICAgICAgIHRoaXMucmQgKz0gNztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbGFzdCc6XG4gICAgICAgIGNhc2UgJ3ByZXZpb3VzJzpcbiAgICAgICAgICB0aGlzLnJkIC09IDc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG5cbiAgICAgIGlmIChpc05hTih0aGlzLndlZWtkYXkpKSB7XG4gICAgICAgIHRoaXMud2Vla2RheSA9IDE7XG4gICAgICB9XG4gICAgfVxuICB9LFxuXG4gIG1vbnRoRnVsbE9yTW9udGhBYmJyOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXignICsgcmVNb250aEZ1bGwgKyAnfCcgKyByZU1vbnRoQWJiciArICcpJywgJ2knKSxcbiAgICBuYW1lOiAnbW9udGhmdWxsIHwgbW9udGhhYmJyJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIG1vbnRoKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksIHRoaXMuZCk7XG4gICAgfVxuICB9LFxuXG4gIHR6Q29ycmVjdGlvbjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVUekNvcnJlY3Rpb24sICdpJyksXG4gICAgbmFtZTogJ3R6Y29ycmVjdGlvbicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKHR6Q29ycmVjdGlvbikge1xuICAgICAgcmV0dXJuIHRoaXMuem9uZShwcm9jZXNzVHpDb3JyZWN0aW9uKHR6Q29ycmVjdGlvbikpO1xuICAgIH1cbiAgfSxcblxuICB0ekFiYnI6IHtcbiAgICByZWdleDogUmVnRXhwKCdeJyArIHJlVHpBYmJyKSxcbiAgICBuYW1lOiAndHphYmJyJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIGFiYnIpIHtcbiAgICAgIHZhciBvZmZzZXQgPSB0ekFiYnJPZmZzZXRzW2FiYnIudG9Mb3dlckNhc2UoKV07XG5cbiAgICAgIGlmIChpc05hTihvZmZzZXQpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRoaXMuem9uZShvZmZzZXQpO1xuICAgIH1cbiAgfSxcblxuICBhZ286IHtcbiAgICByZWdleDogL15hZ28vaSxcbiAgICBuYW1lOiAnYWdvJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2soKSB7XG4gICAgICB0aGlzLnJ5ID0gLXRoaXMucnk7XG4gICAgICB0aGlzLnJtID0gLXRoaXMucm07XG4gICAgICB0aGlzLnJkID0gLXRoaXMucmQ7XG4gICAgICB0aGlzLnJoID0gLXRoaXMucmg7XG4gICAgICB0aGlzLnJpID0gLXRoaXMucmk7XG4gICAgICB0aGlzLnJzID0gLXRoaXMucnM7XG4gICAgICB0aGlzLnJmID0gLXRoaXMucmY7XG4gICAgfVxuICB9LFxuXG4gIHllYXI0OiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZVllYXI0KSxcbiAgICBuYW1lOiAneWVhcjQnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgeWVhcikge1xuICAgICAgdGhpcy55ID0gK3llYXI7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH0sXG5cbiAgd2hpdGVzcGFjZToge1xuICAgIHJlZ2V4OiAvXlsgLixcXHRdKy8sXG4gICAgbmFtZTogJ3doaXRlc3BhY2UnXG4gICAgLy8gZG8gbm90aGluZ1xuICB9LFxuXG4gIGRhdGVTaG9ydFdpdGhUaW1lTG9uZzoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVEYXRlTm9ZZWFyICsgJ3Q/JyArIHJlSG91cjI0ICsgJ1s6Ll0nICsgcmVNaW51dGUgKyAnWzouXScgKyByZVNlY29uZCwgJ2knKSxcbiAgICBuYW1lOiAnZGF0ZXNob3J0d2l0aHRpbWVsb25nJyxcbiAgICBjYWxsYmFjazogZnVuY3Rpb24gY2FsbGJhY2sobWF0Y2gsIG1vbnRoLCBkYXksIGhvdXIsIG1pbnV0ZSwgc2Vjb25kKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksICtkYXkpICYmIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgK3NlY29uZCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIGRhdGVTaG9ydFdpdGhUaW1lTG9uZzEyOiB7XG4gICAgcmVnZXg6IFJlZ0V4cCgnXicgKyByZURhdGVOb1llYXIgKyByZUhvdXIxMiArICdbOi5dJyArIHJlTWludXRlICsgJ1s6Ll0nICsgcmVTZWNvbmRseiArIHJlU3BhY2VPcHQgKyByZU1lcmlkaWFuLCAnaScpLFxuICAgIG5hbWU6ICdkYXRlc2hvcnR3aXRodGltZWxvbmcxMicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBtb250aCwgZGF5LCBob3VyLCBtaW51dGUsIHNlY29uZCwgbWVyaWRpYW4pIHtcbiAgICAgIHJldHVybiB0aGlzLnltZCh0aGlzLnksIGxvb2t1cE1vbnRoKG1vbnRoKSwgK2RheSkgJiYgdGhpcy50aW1lKHByb2Nlc3NNZXJpZGlhbigraG91ciwgbWVyaWRpYW4pLCArbWludXRlLCArc2Vjb25kLCAwKTtcbiAgICB9XG4gIH0sXG5cbiAgZGF0ZVNob3J0V2l0aFRpbWVTaG9ydDoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVEYXRlTm9ZZWFyICsgJ3Q/JyArIHJlSG91cjI0ICsgJ1s6Ll0nICsgcmVNaW51dGUsICdpJyksXG4gICAgbmFtZTogJ2RhdGVzaG9ydHdpdGh0aW1lc2hvcnQnLFxuICAgIGNhbGxiYWNrOiBmdW5jdGlvbiBjYWxsYmFjayhtYXRjaCwgbW9udGgsIGRheSwgaG91ciwgbWludXRlKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksICtkYXkpICYmIHRoaXMudGltZSgraG91ciwgK21pbnV0ZSwgMCwgMCk7XG4gICAgfVxuICB9LFxuXG4gIGRhdGVTaG9ydFdpdGhUaW1lU2hvcnQxMjoge1xuICAgIHJlZ2V4OiBSZWdFeHAoJ14nICsgcmVEYXRlTm9ZZWFyICsgcmVIb3VyMTIgKyAnWzouXScgKyByZU1pbnV0ZWx6ICsgcmVTcGFjZU9wdCArIHJlTWVyaWRpYW4sICdpJyksXG4gICAgbmFtZTogJ2RhdGVzaG9ydHdpdGh0aW1lc2hvcnQxMicsXG4gICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKG1hdGNoLCBtb250aCwgZGF5LCBob3VyLCBtaW51dGUsIG1lcmlkaWFuKSB7XG4gICAgICByZXR1cm4gdGhpcy55bWQodGhpcy55LCBsb29rdXBNb250aChtb250aCksICtkYXkpICYmIHRoaXMudGltZShwcm9jZXNzTWVyaWRpYW4oK2hvdXIsIG1lcmlkaWFuKSwgK21pbnV0ZSwgMCwgMCk7XG4gICAgfVxuICB9XG59O1xuXG52YXIgcmVzdWx0UHJvdG8gPSB7XG4gIC8vIGRhdGVcbiAgeTogTmFOLFxuICBtOiBOYU4sXG4gIGQ6IE5hTixcbiAgLy8gdGltZVxuICBoOiBOYU4sXG4gIGk6IE5hTixcbiAgczogTmFOLFxuICBmOiBOYU4sXG5cbiAgLy8gcmVsYXRpdmUgc2hpZnRzXG4gIHJ5OiAwLFxuICBybTogMCxcbiAgcmQ6IDAsXG4gIHJoOiAwLFxuICByaTogMCxcbiAgcnM6IDAsXG4gIHJmOiAwLFxuXG4gIC8vIHdlZWtkYXkgcmVsYXRlZCBzaGlmdHNcbiAgd2Vla2RheTogTmFOLFxuICB3ZWVrZGF5QmVoYXZpb3I6IDAsXG5cbiAgLy8gZmlyc3Qgb3IgbGFzdCBkYXkgb2YgbW9udGhcbiAgLy8gMCBub25lLCAxIGZpcnN0LCAtMSBsYXN0XG4gIGZpcnN0T3JMYXN0RGF5T2ZNb250aDogMCxcblxuICAvLyB0aW1lem9uZSBjb3JyZWN0aW9uIGluIG1pbnV0ZXNcbiAgejogTmFOLFxuXG4gIC8vIGNvdW50ZXJzXG4gIGRhdGVzOiAwLFxuICB0aW1lczogMCxcbiAgem9uZXM6IDAsXG5cbiAgLy8gaGVscGVyIGZ1bmN0aW9uc1xuICB5bWQ6IGZ1bmN0aW9uIHltZCh5LCBtLCBkKSB7XG4gICAgaWYgKHRoaXMuZGF0ZXMgPiAwKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgdGhpcy5kYXRlcysrO1xuICAgIHRoaXMueSA9IHk7XG4gICAgdGhpcy5tID0gbTtcbiAgICB0aGlzLmQgPSBkO1xuICAgIHJldHVybiB0cnVlO1xuICB9LFxuICB0aW1lOiBmdW5jdGlvbiB0aW1lKGgsIGksIHMsIGYpIHtcbiAgICBpZiAodGhpcy50aW1lcyA+IDApIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICB0aGlzLnRpbWVzKys7XG4gICAgdGhpcy5oID0gaDtcbiAgICB0aGlzLmkgPSBpO1xuICAgIHRoaXMucyA9IHM7XG4gICAgdGhpcy5mID0gZjtcblxuICAgIHJldHVybiB0cnVlO1xuICB9LFxuICByZXNldFRpbWU6IGZ1bmN0aW9uIHJlc2V0VGltZSgpIHtcbiAgICB0aGlzLmggPSAwO1xuICAgIHRoaXMuaSA9IDA7XG4gICAgdGhpcy5zID0gMDtcbiAgICB0aGlzLmYgPSAwO1xuICAgIHRoaXMudGltZXMgPSAwO1xuXG4gICAgcmV0dXJuIHRydWU7XG4gIH0sXG4gIHpvbmU6IGZ1bmN0aW9uIHpvbmUobWludXRlcykge1xuICAgIGlmICh0aGlzLnpvbmVzIDw9IDEpIHtcbiAgICAgIHRoaXMuem9uZXMrKztcbiAgICAgIHRoaXMueiA9IG1pbnV0ZXM7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICByZXR1cm4gZmFsc2U7XG4gIH0sXG4gIHRvRGF0ZTogZnVuY3Rpb24gdG9EYXRlKHJlbGF0aXZlVG8pIHtcbiAgICBpZiAodGhpcy5kYXRlcyAmJiAhdGhpcy50aW1lcykge1xuICAgICAgdGhpcy5oID0gdGhpcy5pID0gdGhpcy5zID0gdGhpcy5mID0gMDtcbiAgICB9XG5cbiAgICAvLyBmaWxsIGhvbGVzXG4gICAgaWYgKGlzTmFOKHRoaXMueSkpIHtcbiAgICAgIHRoaXMueSA9IHJlbGF0aXZlVG8uZ2V0RnVsbFllYXIoKTtcbiAgICB9XG5cbiAgICBpZiAoaXNOYU4odGhpcy5tKSkge1xuICAgICAgdGhpcy5tID0gcmVsYXRpdmVUby5nZXRNb250aCgpO1xuICAgIH1cblxuICAgIGlmIChpc05hTih0aGlzLmQpKSB7XG4gICAgICB0aGlzLmQgPSByZWxhdGl2ZVRvLmdldERhdGUoKTtcbiAgICB9XG5cbiAgICBpZiAoaXNOYU4odGhpcy5oKSkge1xuICAgICAgdGhpcy5oID0gcmVsYXRpdmVUby5nZXRIb3VycygpO1xuICAgIH1cblxuICAgIGlmIChpc05hTih0aGlzLmkpKSB7XG4gICAgICB0aGlzLmkgPSByZWxhdGl2ZVRvLmdldE1pbnV0ZXMoKTtcbiAgICB9XG5cbiAgICBpZiAoaXNOYU4odGhpcy5zKSkge1xuICAgICAgdGhpcy5zID0gcmVsYXRpdmVUby5nZXRTZWNvbmRzKCk7XG4gICAgfVxuXG4gICAgaWYgKGlzTmFOKHRoaXMuZikpIHtcbiAgICAgIHRoaXMuZiA9IHJlbGF0aXZlVG8uZ2V0TWlsbGlzZWNvbmRzKCk7XG4gICAgfVxuXG4gICAgLy8gYWRqdXN0IHNwZWNpYWwgZWFybHlcbiAgICBzd2l0Y2ggKHRoaXMuZmlyc3RPckxhc3REYXlPZk1vbnRoKSB7XG4gICAgICBjYXNlIDE6XG4gICAgICAgIHRoaXMuZCA9IDE7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAtMTpcbiAgICAgICAgdGhpcy5kID0gMDtcbiAgICAgICAgdGhpcy5tICs9IDE7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGlmICghaXNOYU4odGhpcy53ZWVrZGF5KSkge1xuICAgICAgdmFyIGRhdGUgPSBuZXcgRGF0ZShyZWxhdGl2ZVRvLmdldFRpbWUoKSk7XG4gICAgICBkYXRlLnNldEZ1bGxZZWFyKHRoaXMueSwgdGhpcy5tLCB0aGlzLmQpO1xuICAgICAgZGF0ZS5zZXRIb3Vycyh0aGlzLmgsIHRoaXMuaSwgdGhpcy5zLCB0aGlzLmYpO1xuXG4gICAgICB2YXIgZG93ID0gZGF0ZS5nZXREYXkoKTtcblxuICAgICAgaWYgKHRoaXMud2Vla2RheUJlaGF2aW9yID09PSAyKSB7XG4gICAgICAgIC8vIFRvIG1ha2UgXCJ0aGlzIHdlZWtcIiB3b3JrLCB3aGVyZSB0aGUgY3VycmVudCBkYXkgb2Ygd2VlayBpcyBhIFwic3VuZGF5XCJcbiAgICAgICAgaWYgKGRvdyA9PT0gMCAmJiB0aGlzLndlZWtkYXkgIT09IDApIHtcbiAgICAgICAgICB0aGlzLndlZWtkYXkgPSAtNjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFRvIG1ha2UgXCJzdW5kYXkgdGhpcyB3ZWVrXCIgd29yaywgd2hlcmUgdGhlIGN1cnJlbnQgZGF5IG9mIHdlZWsgaXMgbm90IGEgXCJzdW5kYXlcIlxuICAgICAgICBpZiAodGhpcy53ZWVrZGF5ID09PSAwICYmIGRvdyAhPT0gMCkge1xuICAgICAgICAgIHRoaXMud2Vla2RheSA9IDc7XG4gICAgICAgIH1cblxuICAgICAgICB0aGlzLmQgLT0gZG93O1xuICAgICAgICB0aGlzLmQgKz0gdGhpcy53ZWVrZGF5O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmFyIGRpZmYgPSB0aGlzLndlZWtkYXkgLSBkb3c7XG5cbiAgICAgICAgLy8gc29tZSBQSFAgbWFnaWNcbiAgICAgICAgaWYgKHRoaXMucmQgPCAwICYmIGRpZmYgPCAwIHx8IHRoaXMucmQgPj0gMCAmJiBkaWZmIDw9IC10aGlzLndlZWtkYXlCZWhhdmlvcikge1xuICAgICAgICAgIGRpZmYgKz0gNztcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh0aGlzLndlZWtkYXkgPj0gMCkge1xuICAgICAgICAgIHRoaXMuZCArPSBkaWZmO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRoaXMuZCAtPSA3IC0gKE1hdGguYWJzKHRoaXMud2Vla2RheSkgLSBkb3cpO1xuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy53ZWVrZGF5ID0gTmFOO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIGFkanVzdCByZWxhdGl2ZVxuICAgIHRoaXMueSArPSB0aGlzLnJ5O1xuICAgIHRoaXMubSArPSB0aGlzLnJtO1xuICAgIHRoaXMuZCArPSB0aGlzLnJkO1xuXG4gICAgdGhpcy5oICs9IHRoaXMucmg7XG4gICAgdGhpcy5pICs9IHRoaXMucmk7XG4gICAgdGhpcy5zICs9IHRoaXMucnM7XG4gICAgdGhpcy5mICs9IHRoaXMucmY7XG5cbiAgICB0aGlzLnJ5ID0gdGhpcy5ybSA9IHRoaXMucmQgPSAwO1xuICAgIHRoaXMucmggPSB0aGlzLnJpID0gdGhpcy5ycyA9IHRoaXMucmYgPSAwO1xuXG4gICAgdmFyIHJlc3VsdCA9IG5ldyBEYXRlKHJlbGF0aXZlVG8uZ2V0VGltZSgpKTtcbiAgICAvLyBzaW5jZSBEYXRlIGNvbnN0cnVjdG9yIHRyZWF0cyB5ZWFycyA8PSA5OSBhcyAxOTAwK1xuICAgIC8vIGl0IGNhbid0IGJlIHVzZWQsIHRodXMgdGhpcyB3ZWlyZCB3YXlcbiAgICByZXN1bHQuc2V0RnVsbFllYXIodGhpcy55LCB0aGlzLm0sIHRoaXMuZCk7XG4gICAgcmVzdWx0LnNldEhvdXJzKHRoaXMuaCwgdGhpcy5pLCB0aGlzLnMsIHRoaXMuZik7XG5cbiAgICAvLyBub3RlOiB0aGlzIGlzIGRvbmUgdHdpY2UgaW4gUEhQXG4gICAgLy8gZWFybHkgd2hlbiBwcm9jZXNzaW5nIHNwZWNpYWwgcmVsYXRpdmVzXG4gICAgLy8gYW5kIGxhdGVcbiAgICAvLyB0b2RvOiBjaGVjayBpZiB0aGUgbG9naWMgY2FuIGJlIHJlZHVjZWRcbiAgICAvLyB0byBqdXN0IG9uZSB0aW1lIGFjdGlvblxuICAgIHN3aXRjaCAodGhpcy5maXJzdE9yTGFzdERheU9mTW9udGgpIHtcbiAgICAgIGNhc2UgMTpcbiAgICAgICAgcmVzdWx0LnNldERhdGUoMSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAtMTpcbiAgICAgICAgcmVzdWx0LnNldE1vbnRoKHJlc3VsdC5nZXRNb250aCgpICsgMSwgMCk7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIC8vIGFkanVzdCB0aW1lem9uZVxuICAgIGlmICghaXNOYU4odGhpcy56KSAmJiByZXN1bHQuZ2V0VGltZXpvbmVPZmZzZXQoKSAhPT0gdGhpcy56KSB7XG4gICAgICByZXN1bHQuc2V0VVRDRnVsbFllYXIocmVzdWx0LmdldEZ1bGxZZWFyKCksIHJlc3VsdC5nZXRNb250aCgpLCByZXN1bHQuZ2V0RGF0ZSgpKTtcblxuICAgICAgcmVzdWx0LnNldFVUQ0hvdXJzKHJlc3VsdC5nZXRIb3VycygpLCByZXN1bHQuZ2V0TWludXRlcygpLCByZXN1bHQuZ2V0U2Vjb25kcygpIC0gdGhpcy56LCByZXN1bHQuZ2V0TWlsbGlzZWNvbmRzKCkpO1xuICAgIH1cblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gc3RydG90aW1lKHN0ciwgbm93KSB7XG4gIC8vICAgICAgIGRpc2N1c3MgYXQ6IGh0dHBzOi8vbG9jdXR1cy5pby9waHAvc3RydG90aW1lL1xuICAvLyAgICAgIG9yaWdpbmFsIGJ5OiBDYWlvIEFyaWVkZSAoaHR0cHM6Ly9jYWlvYXJpZWRlLmNvbSlcbiAgLy8gICAgICBpbXByb3ZlZCBieTogS2V2aW4gdmFuIFpvbm5ldmVsZCAoaHR0cHM6Ly9rdnouaW8pXG4gIC8vICAgICAgaW1wcm92ZWQgYnk6IENhaW8gQXJpZWRlIChodHRwczovL2NhaW9hcmllZGUuY29tKVxuICAvLyAgICAgIGltcHJvdmVkIGJ5OiBBLiBNYXTDrWFzIFF1ZXphZGEgKGh0dHBzOi8vYW1hdGlhc3EuY29tKVxuICAvLyAgICAgIGltcHJvdmVkIGJ5OiBwcmV1dGVyXG4gIC8vICAgICAgaW1wcm92ZWQgYnk6IEJyZXR0IFphbWlyIChodHRwczovL2JyZXR0LXphbWlyLm1lKVxuICAvLyAgICAgIGltcHJvdmVkIGJ5OiBNaXJrbyBGYWJlclxuICAvLyAgICAgICAgIGlucHV0IGJ5OiBEYXZpZFxuICAvLyAgICAgIGJ1Z2ZpeGVkIGJ5OiBXYWduZXIgQi4gU29hcmVzXG4gIC8vICAgICAgYnVnZml4ZWQgYnk6IEFydHVyIFRjaGVybnljaGV2XG4gIC8vICAgICAgYnVnZml4ZWQgYnk6IFN0ZXBoYW4gQsO2c2NoLVBsZXBlbGl0cyAoaHR0cHM6Ly9naXRodWIuY29tL3BsZXBlKVxuICAvLyByZWltcGxlbWVudGVkIGJ5OiBSYWZhxYIgS3VrYXdza2lcbiAgLy8gICAgICAgICAgIG5vdGUgMTogRXhhbXBsZXMgYWxsIGhhdmUgYSBmaXhlZCB0aW1lc3RhbXAgdG8gcHJldmVudFxuICAvLyAgICAgICAgICAgbm90ZSAxOiB0ZXN0cyB0byBmYWlsIGJlY2F1c2Ugb2YgdmFyaWFibGUgdGltZSh6b25lcylcbiAgLy8gICAgICAgIGV4YW1wbGUgMTogc3RydG90aW1lKCcrMSBkYXknLCAxMTI5NjMzMjAwKVxuICAvLyAgICAgICAgcmV0dXJucyAxOiAxMTI5NzE5NjAwXG4gIC8vICAgICAgICBleGFtcGxlIDI6IHN0cnRvdGltZSgnKzEgd2VlayAyIGRheXMgNCBob3VycyAyIHNlY29uZHMnLCAxMTI5NjMzMjAwKVxuICAvLyAgICAgICAgcmV0dXJucyAyOiAxMTMwNDI1MjAyXG4gIC8vICAgICAgICBleGFtcGxlIDM6IHN0cnRvdGltZSgnbGFzdCBtb250aCcsIDExMjk2MzMyMDApXG4gIC8vICAgICAgICByZXR1cm5zIDM6IDExMjcwNDEyMDBcbiAgLy8gICAgICAgIGV4YW1wbGUgNDogc3RydG90aW1lKCcyMDA5LTA1LTA0IDA4OjMwOjAwKzAwJylcbiAgLy8gICAgICAgIHJldHVybnMgNDogMTI0MTQyNTgwMFxuICAvLyAgICAgICAgZXhhbXBsZSA1OiBzdHJ0b3RpbWUoJzIwMDktMDUtMDQgMDg6MzA6MDArMDI6MDAnKVxuICAvLyAgICAgICAgcmV0dXJucyA1OiAxMjQxNDE4NjAwXG4gIC8vICAgICAgICBleGFtcGxlIDY6IHN0cnRvdGltZSgnMjAwOS0wNS0wNCAwODozMDowMCBZV1QnKVxuICAvLyAgICAgICAgcmV0dXJucyA2OiAxMjQxNDU0NjAwXG5cbiAgaWYgKG5vdyA9PSBudWxsKSB7XG4gICAgbm93ID0gTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG4gIH1cblxuICAvLyB0aGUgcnVsZSBvcmRlciBpcyBpbXBvcnRhbnRcbiAgLy8gaWYgbXVsdGlwbGUgcnVsZXMgbWF0Y2gsIHRoZSBsb25nZXN0IG1hdGNoIHdpbnNcbiAgLy8gaWYgbXVsdGlwbGUgcnVsZXMgbWF0Y2ggdGhlIHNhbWUgc3RyaW5nLCB0aGUgZmlyc3QgbWF0Y2ggd2luc1xuICB2YXIgcnVsZXMgPSBbZm9ybWF0cy55ZXN0ZXJkYXksIGZvcm1hdHMubm93LCBmb3JtYXRzLm5vb24sIGZvcm1hdHMubWlkbmlnaHRPclRvZGF5LCBmb3JtYXRzLnRvbW9ycm93LCBmb3JtYXRzLnRpbWVzdGFtcCwgZm9ybWF0cy5maXJzdE9yTGFzdERheSwgZm9ybWF0cy5iYWNrT3JGcm9udE9mLFxuICAvLyBmb3JtYXRzLndlZWtkYXlPZiwgLy8gbm90IHlldCBpbXBsZW1lbnRlZFxuICBmb3JtYXRzLnRpbWVUaW55MTIsIGZvcm1hdHMudGltZVNob3J0MTIsIGZvcm1hdHMudGltZUxvbmcxMiwgZm9ybWF0cy5tc3NxbHRpbWUsIGZvcm1hdHMudGltZVNob3J0MjQsIGZvcm1hdHMudGltZUxvbmcyNCwgZm9ybWF0cy5pc284NjAxbG9uZywgZm9ybWF0cy5nbnVOb0NvbG9uLCBmb3JtYXRzLmlzbzg2MDFub0NvbG9uLCBmb3JtYXRzLmFtZXJpY2FuU2hvcnQsIGZvcm1hdHMuYW1lcmljYW4sIGZvcm1hdHMuaXNvODYwMWRhdGU0LCBmb3JtYXRzLmlzbzg2MDFkYXRlU2xhc2gsIGZvcm1hdHMuZGF0ZVNsYXNoLCBmb3JtYXRzLmdudURhdGVTaG9ydE9ySXNvODYwMWRhdGUyLCBmb3JtYXRzLmdudURhdGVTaG9ydGVyLCBmb3JtYXRzLmRhdGVGdWxsLCBmb3JtYXRzLnBvaW50ZWREYXRlNCwgZm9ybWF0cy5wb2ludGVkRGF0ZTIsIGZvcm1hdHMuZGF0ZU5vRGF5LCBmb3JtYXRzLmRhdGVOb0RheVJldiwgZm9ybWF0cy5kYXRlVGV4dHVhbCwgZm9ybWF0cy5kYXRlTm9ZZWFyLCBmb3JtYXRzLmRhdGVOb1llYXJSZXYsIGZvcm1hdHMuZGF0ZU5vQ29sb24sIGZvcm1hdHMueG1sUnBjLCBmb3JtYXRzLnhtbFJwY05vQ29sb24sIGZvcm1hdHMuc29hcCwgZm9ybWF0cy53ZGR4LCBmb3JtYXRzLmV4aWYsIGZvcm1hdHMucGd5ZG90ZCwgZm9ybWF0cy5pc29XZWVrRGF5LCBmb3JtYXRzLnBnVGV4dFNob3J0LCBmb3JtYXRzLnBnVGV4dFJldmVyc2UsIGZvcm1hdHMuY2xmLCBmb3JtYXRzLnllYXI0LCBmb3JtYXRzLmFnbywgZm9ybWF0cy5kYXlUZXh0LCBmb3JtYXRzLnJlbGF0aXZlVGV4dFdlZWssIGZvcm1hdHMucmVsYXRpdmVUZXh0LCBmb3JtYXRzLm1vbnRoRnVsbE9yTW9udGhBYmJyLCBmb3JtYXRzLnR6Q29ycmVjdGlvbiwgZm9ybWF0cy50ekFiYnIsIGZvcm1hdHMuZGF0ZVNob3J0V2l0aFRpbWVTaG9ydDEyLCBmb3JtYXRzLmRhdGVTaG9ydFdpdGhUaW1lTG9uZzEyLCBmb3JtYXRzLmRhdGVTaG9ydFdpdGhUaW1lU2hvcnQsIGZvcm1hdHMuZGF0ZVNob3J0V2l0aFRpbWVMb25nLCBmb3JtYXRzLnJlbGF0aXZlLCBmb3JtYXRzLndoaXRlc3BhY2VdO1xuXG4gIHZhciByZXN1bHQgPSBPYmplY3QuY3JlYXRlKHJlc3VsdFByb3RvKTtcblxuICB3aGlsZSAoc3RyLmxlbmd0aCkge1xuICAgIHZhciBsb25nZXN0TWF0Y2ggPSBudWxsO1xuICAgIHZhciBmaW5hbFJ1bGUgPSBudWxsO1xuXG4gICAgZm9yICh2YXIgaSA9IDAsIGwgPSBydWxlcy5sZW5ndGg7IGkgPCBsOyBpKyspIHtcbiAgICAgIHZhciBmb3JtYXQgPSBydWxlc1tpXTtcblxuICAgICAgdmFyIG1hdGNoID0gc3RyLm1hdGNoKGZvcm1hdC5yZWdleCk7XG5cbiAgICAgIGlmIChtYXRjaCkge1xuICAgICAgICBpZiAoIWxvbmdlc3RNYXRjaCB8fCBtYXRjaFswXS5sZW5ndGggPiBsb25nZXN0TWF0Y2hbMF0ubGVuZ3RoKSB7XG4gICAgICAgICAgbG9uZ2VzdE1hdGNoID0gbWF0Y2g7XG4gICAgICAgICAgZmluYWxSdWxlID0gZm9ybWF0O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCFmaW5hbFJ1bGUgfHwgZmluYWxSdWxlLmNhbGxiYWNrICYmIGZpbmFsUnVsZS5jYWxsYmFjay5hcHBseShyZXN1bHQsIGxvbmdlc3RNYXRjaCkgPT09IGZhbHNlKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgc3RyID0gc3RyLnN1YnN0cihsb25nZXN0TWF0Y2hbMF0ubGVuZ3RoKTtcbiAgICBmaW5hbFJ1bGUgPSBudWxsO1xuICAgIGxvbmdlc3RNYXRjaCA9IG51bGw7XG4gIH1cblxuICByZXR1cm4gTWF0aC5mbG9vcihyZXN1bHQudG9EYXRlKG5ldyBEYXRlKG5vdyAqIDEwMDApKSAvIDEwMDApO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0cnRvdGltZS5qcy5tYXAiLCIndXNlIHN0cmljdCc7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gaW5pX2dldCh2YXJuYW1lKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG4gIC8vICBkaXNjdXNzIGF0OiBodHRwczovL2xvY3V0dXMuaW8vcGhwL2luaV9nZXQvXG4gIC8vIG9yaWdpbmFsIGJ5OiBCcmV0dCBaYW1pciAoaHR0cHM6Ly9icmV0dC16YW1pci5tZSlcbiAgLy8gICAgICBub3RlIDE6IFRoZSBpbmkgdmFsdWVzIG11c3QgYmUgc2V0IGJ5IGluaV9zZXQgb3IgbWFudWFsbHkgd2l0aGluIGFuIGluaSBmaWxlXG4gIC8vICAgZXhhbXBsZSAxOiBpbmlfc2V0KCdkYXRlLnRpbWV6b25lJywgJ0FzaWEvSG9uZ19Lb25nJylcbiAgLy8gICBleGFtcGxlIDE6IGluaV9nZXQoJ2RhdGUudGltZXpvbmUnKVxuICAvLyAgIHJldHVybnMgMTogJ0FzaWEvSG9uZ19Lb25nJ1xuXG4gIHZhciAkZ2xvYmFsID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cgOiBnbG9iYWw7XG4gICRnbG9iYWwuJGxvY3V0dXMgPSAkZ2xvYmFsLiRsb2N1dHVzIHx8IHt9O1xuICB2YXIgJGxvY3V0dXMgPSAkZ2xvYmFsLiRsb2N1dHVzO1xuICAkbG9jdXR1cy5waHAgPSAkbG9jdXR1cy5waHAgfHwge307XG4gICRsb2N1dHVzLnBocC5pbmkgPSAkbG9jdXR1cy5waHAuaW5pIHx8IHt9O1xuXG4gIGlmICgkbG9jdXR1cy5waHAuaW5pW3Zhcm5hbWVdICYmICRsb2N1dHVzLnBocC5pbmlbdmFybmFtZV0ubG9jYWxfdmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgIGlmICgkbG9jdXR1cy5waHAuaW5pW3Zhcm5hbWVdLmxvY2FsX3ZhbHVlID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gJyc7XG4gICAgfVxuICAgIHJldHVybiAkbG9jdXR1cy5waHAuaW5pW3Zhcm5hbWVdLmxvY2FsX3ZhbHVlO1xuICB9XG5cbiAgcmV0dXJuICcnO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluaV9nZXQuanMubWFwIiwiJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHN0cmxlbihzdHJpbmcpIHtcbiAgLy8gIGRpc2N1c3MgYXQ6IGh0dHBzOi8vbG9jdXR1cy5pby9waHAvc3RybGVuL1xuICAvLyBvcmlnaW5hbCBieTogS2V2aW4gdmFuIFpvbm5ldmVsZCAoaHR0cHM6Ly9rdnouaW8pXG4gIC8vIGltcHJvdmVkIGJ5OiBTYWtpbW9yaVxuICAvLyBpbXByb3ZlZCBieTogS2V2aW4gdmFuIFpvbm5ldmVsZCAoaHR0cHM6Ly9rdnouaW8pXG4gIC8vICAgIGlucHV0IGJ5OiBLaXJrIFN0cm9iZWNrXG4gIC8vIGJ1Z2ZpeGVkIGJ5OiBPbm5vIE1hcnNtYW4gKGh0dHBzOi8vdHdpdHRlci5jb20vb25ub21hcnNtYW4pXG4gIC8vICByZXZpc2VkIGJ5OiBCcmV0dCBaYW1pciAoaHR0cHM6Ly9icmV0dC16YW1pci5tZSlcbiAgLy8gICAgICBub3RlIDE6IE1heSBsb29rIGxpa2Ugb3ZlcmtpbGwsIGJ1dCBpbiBvcmRlciB0byBiZSB0cnVseSBmYWl0aGZ1bCB0byBoYW5kbGluZyBhbGwgVW5pY29kZVxuICAvLyAgICAgIG5vdGUgMTogY2hhcmFjdGVycyBhbmQgdG8gdGhpcyBmdW5jdGlvbiBpbiBQSFAgd2hpY2ggZG9lcyBub3QgY291bnQgdGhlIG51bWJlciBvZiBieXRlc1xuICAvLyAgICAgIG5vdGUgMTogYnV0IGNvdW50cyB0aGUgbnVtYmVyIG9mIGNoYXJhY3RlcnMsIHNvbWV0aGluZyBsaWtlIHRoaXMgaXMgcmVhbGx5IG5lY2Vzc2FyeS5cbiAgLy8gICBleGFtcGxlIDE6IHN0cmxlbignS2V2aW4gdmFuIFpvbm5ldmVsZCcpXG4gIC8vICAgcmV0dXJucyAxOiAxOVxuICAvLyAgIGV4YW1wbGUgMjogaW5pX3NldCgndW5pY29kZS5zZW1hbnRpY3MnLCAnb24nKVxuICAvLyAgIGV4YW1wbGUgMjogc3RybGVuKCdBXFx1ZDg3ZVxcdWRjMDRaJylcbiAgLy8gICByZXR1cm5zIDI6IDNcblxuICB2YXIgc3RyID0gc3RyaW5nICsgJyc7XG5cbiAgdmFyIGluaVZhbCA9ICh0eXBlb2YgcmVxdWlyZSAhPT0gJ3VuZGVmaW5lZCcgPyByZXF1aXJlKCcuLi9pbmZvL2luaV9nZXQnKSgndW5pY29kZS5zZW1hbnRpY3MnKSA6IHVuZGVmaW5lZCkgfHwgJ29mZic7XG4gIGlmIChpbmlWYWwgPT09ICdvZmYnKSB7XG4gICAgcmV0dXJuIHN0ci5sZW5ndGg7XG4gIH1cblxuICB2YXIgaSA9IDA7XG4gIHZhciBsZ3RoID0gMDtcblxuICB2YXIgZ2V0V2hvbGVDaGFyID0gZnVuY3Rpb24gZ2V0V2hvbGVDaGFyKHN0ciwgaSkge1xuICAgIHZhciBjb2RlID0gc3RyLmNoYXJDb2RlQXQoaSk7XG4gICAgdmFyIG5leHQgPSAnJztcbiAgICB2YXIgcHJldiA9ICcnO1xuICAgIGlmIChjb2RlID49IDB4RDgwMCAmJiBjb2RlIDw9IDB4REJGRikge1xuICAgICAgLy8gSGlnaCBzdXJyb2dhdGUgKGNvdWxkIGNoYW5nZSBsYXN0IGhleCB0byAweERCN0YgdG9cbiAgICAgIC8vIHRyZWF0IGhpZ2ggcHJpdmF0ZSBzdXJyb2dhdGVzIGFzIHNpbmdsZSBjaGFyYWN0ZXJzKVxuICAgICAgaWYgKHN0ci5sZW5ndGggPD0gaSArIDEpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdIaWdoIHN1cnJvZ2F0ZSB3aXRob3V0IGZvbGxvd2luZyBsb3cgc3Vycm9nYXRlJyk7XG4gICAgICB9XG4gICAgICBuZXh0ID0gc3RyLmNoYXJDb2RlQXQoaSArIDEpO1xuICAgICAgaWYgKG5leHQgPCAweERDMDAgfHwgbmV4dCA+IDB4REZGRikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0hpZ2ggc3Vycm9nYXRlIHdpdGhvdXQgZm9sbG93aW5nIGxvdyBzdXJyb2dhdGUnKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzdHIuY2hhckF0KGkpICsgc3RyLmNoYXJBdChpICsgMSk7XG4gICAgfSBlbHNlIGlmIChjb2RlID49IDB4REMwMCAmJiBjb2RlIDw9IDB4REZGRikge1xuICAgICAgLy8gTG93IHN1cnJvZ2F0ZVxuICAgICAgaWYgKGkgPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMb3cgc3Vycm9nYXRlIHdpdGhvdXQgcHJlY2VkaW5nIGhpZ2ggc3Vycm9nYXRlJyk7XG4gICAgICB9XG4gICAgICBwcmV2ID0gc3RyLmNoYXJDb2RlQXQoaSAtIDEpO1xuICAgICAgaWYgKHByZXYgPCAweEQ4MDAgfHwgcHJldiA+IDB4REJGRikge1xuICAgICAgICAvLyAoY291bGQgY2hhbmdlIGxhc3QgaGV4IHRvIDB4REI3RiB0byB0cmVhdCBoaWdoIHByaXZhdGUgc3Vycm9nYXRlc1xuICAgICAgICAvLyBhcyBzaW5nbGUgY2hhcmFjdGVycylcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMb3cgc3Vycm9nYXRlIHdpdGhvdXQgcHJlY2VkaW5nIGhpZ2ggc3Vycm9nYXRlJyk7XG4gICAgICB9XG4gICAgICAvLyBXZSBjYW4gcGFzcyBvdmVyIGxvdyBzdXJyb2dhdGVzIG5vdyBhcyB0aGUgc2Vjb25kXG4gICAgICAvLyBjb21wb25lbnQgaW4gYSBwYWlyIHdoaWNoIHdlIGhhdmUgYWxyZWFkeSBwcm9jZXNzZWRcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHN0ci5jaGFyQXQoaSk7XG4gIH07XG5cbiAgZm9yIChpID0gMCwgbGd0aCA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoZ2V0V2hvbGVDaGFyKHN0ciwgaSkgPT09IGZhbHNlKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgLy8gQWRhcHQgdGhpcyBsaW5lIGF0IHRoZSB0b3Agb2YgYW55IGxvb3AsIHBhc3NpbmcgaW4gdGhlIHdob2xlIHN0cmluZyBhbmRcbiAgICAvLyB0aGUgY3VycmVudCBpdGVyYXRpb24gYW5kIHJldHVybmluZyBhIHZhcmlhYmxlIHRvIHJlcHJlc2VudCB0aGUgaW5kaXZpZHVhbCBjaGFyYWN0ZXI7XG4gICAgLy8gcHVycG9zZSBpcyB0byB0cmVhdCB0aGUgZmlyc3QgcGFydCBvZiBhIHN1cnJvZ2F0ZSBwYWlyIGFzIHRoZSB3aG9sZSBjaGFyYWN0ZXIgYW5kIHRoZW5cbiAgICAvLyBpZ25vcmUgdGhlIHNlY29uZCBwYXJ0XG4gICAgbGd0aCsrO1xuICB9XG5cbiAgcmV0dXJuIGxndGg7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RybGVuLmpzLm1hcCIsIid1c2Ugc3RyaWN0JztcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBpc19udW1lcmljKG1peGVkVmFyKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG4gIC8vICBkaXNjdXNzIGF0OiBodHRwczovL2xvY3V0dXMuaW8vcGhwL2lzX251bWVyaWMvXG4gIC8vIG9yaWdpbmFsIGJ5OiBLZXZpbiB2YW4gWm9ubmV2ZWxkIChodHRwczovL2t2ei5pbylcbiAgLy8gaW1wcm92ZWQgYnk6IERhdmlkXG4gIC8vIGltcHJvdmVkIGJ5OiB0YWl0aFxuICAvLyBidWdmaXhlZCBieTogVGltIGRlIEtvbmluZ1xuICAvLyBidWdmaXhlZCBieTogV2ViRGV2SG9ibyAoaHR0cHM6Ly93ZWJkZXZob2JvLmJsb2dzcG90LmNvbS8pXG4gIC8vIGJ1Z2ZpeGVkIGJ5OiBCcmV0dCBaYW1pciAoaHR0cHM6Ly9icmV0dC16YW1pci5tZSlcbiAgLy8gYnVnZml4ZWQgYnk6IERlbmlzIENoZW51IChodHRwczovL3Nobm91bGxlLm5ldClcbiAgLy8gICBleGFtcGxlIDE6IGlzX251bWVyaWMoMTg2LjMxKVxuICAvLyAgIHJldHVybnMgMTogdHJ1ZVxuICAvLyAgIGV4YW1wbGUgMjogaXNfbnVtZXJpYygnS2V2aW4gdmFuIFpvbm5ldmVsZCcpXG4gIC8vICAgcmV0dXJucyAyOiBmYWxzZVxuICAvLyAgIGV4YW1wbGUgMzogaXNfbnVtZXJpYygnICsxODYuMzFlMicpXG4gIC8vICAgcmV0dXJucyAzOiB0cnVlXG4gIC8vICAgZXhhbXBsZSA0OiBpc19udW1lcmljKCcnKVxuICAvLyAgIHJldHVybnMgNDogZmFsc2VcbiAgLy8gICBleGFtcGxlIDU6IGlzX251bWVyaWMoW10pXG4gIC8vICAgcmV0dXJucyA1OiBmYWxzZVxuICAvLyAgIGV4YW1wbGUgNjogaXNfbnVtZXJpYygnMSAnKVxuICAvLyAgIHJldHVybnMgNjogZmFsc2VcblxuICB2YXIgd2hpdGVzcGFjZSA9IFsnICcsICdcXG4nLCAnXFxyJywgJ1xcdCcsICdcXGYnLCAnXFx4MGInLCAnXFx4YTAnLCAnXFx1MjAwMCcsICdcXHUyMDAxJywgJ1xcdTIwMDInLCAnXFx1MjAwMycsICdcXHUyMDA0JywgJ1xcdTIwMDUnLCAnXFx1MjAwNicsICdcXHUyMDA3JywgJ1xcdTIwMDgnLCAnXFx1MjAwOScsICdcXHUyMDBBJywgJ1xcdTIwMEInLCAnXFx1MjAyOCcsICdcXHUyMDI5JywgJ1xcdTMwMDAnXS5qb2luKCcnKTtcblxuICAvLyBAdG9kbzogQnJlYWsgdGhpcyB1cCB1c2luZyBtYW55IHNpbmdsZSBjb25kaXRpb25zIHdpdGggZWFybHkgcmV0dXJuc1xuICByZXR1cm4gKHR5cGVvZiBtaXhlZFZhciA9PT0gJ251bWJlcicgfHwgdHlwZW9mIG1peGVkVmFyID09PSAnc3RyaW5nJyAmJiB3aGl0ZXNwYWNlLmluZGV4T2YobWl4ZWRWYXIuc2xpY2UoLTEpKSA9PT0gLTEpICYmIG1peGVkVmFyICE9PSAnJyAmJiAhaXNOYU4obWl4ZWRWYXIpO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzX251bWVyaWMuanMubWFwIiwiLy8gVGhlIG1vZHVsZSBjYWNoZVxudmFyIF9fd2VicGFja19tb2R1bGVfY2FjaGVfXyA9IHt9O1xuXG4vLyBUaGUgcmVxdWlyZSBmdW5jdGlvblxuZnVuY3Rpb24gX193ZWJwYWNrX3JlcXVpcmVfXyhtb2R1bGVJZCkge1xuXHQvLyBDaGVjayBpZiBtb2R1bGUgaXMgaW4gY2FjaGVcblx0dmFyIGNhY2hlZE1vZHVsZSA9IF9fd2VicGFja19tb2R1bGVfY2FjaGVfX1ttb2R1bGVJZF07XG5cdGlmIChjYWNoZWRNb2R1bGUgIT09IHVuZGVmaW5lZCkge1xuXHRcdHJldHVybiBjYWNoZWRNb2R1bGUuZXhwb3J0cztcblx0fVxuXHQvLyBDcmVhdGUgYSBuZXcgbW9kdWxlIChhbmQgcHV0IGl0IGludG8gdGhlIGNhY2hlKVxuXHR2YXIgbW9kdWxlID0gX193ZWJwYWNrX21vZHVsZV9jYWNoZV9fW21vZHVsZUlkXSA9IHtcblx0XHQvLyBubyBtb2R1bGUuaWQgbmVlZGVkXG5cdFx0Ly8gbm8gbW9kdWxlLmxvYWRlZCBuZWVkZWRcblx0XHRleHBvcnRzOiB7fVxuXHR9O1xuXG5cdC8vIEV4ZWN1dGUgdGhlIG1vZHVsZSBmdW5jdGlvblxuXHRfX3dlYnBhY2tfbW9kdWxlc19fW21vZHVsZUlkXShtb2R1bGUsIG1vZHVsZS5leHBvcnRzLCBfX3dlYnBhY2tfcmVxdWlyZV9fKTtcblxuXHQvLyBSZXR1cm4gdGhlIGV4cG9ydHMgb2YgdGhlIG1vZHVsZVxuXHRyZXR1cm4gbW9kdWxlLmV4cG9ydHM7XG59XG5cbiIsIi8vIGdldERlZmF1bHRFeHBvcnQgZnVuY3Rpb24gZm9yIGNvbXBhdGliaWxpdHkgd2l0aCBub24taGFybW9ueSBtb2R1bGVzXG5fX3dlYnBhY2tfcmVxdWlyZV9fLm4gPSBmdW5jdGlvbihtb2R1bGUpIHtcblx0dmFyIGdldHRlciA9IG1vZHVsZSAmJiBtb2R1bGUuX19lc01vZHVsZSA/XG5cdFx0ZnVuY3Rpb24oKSB7IHJldHVybiBtb2R1bGVbJ2RlZmF1bHQnXTsgfSA6XG5cdFx0ZnVuY3Rpb24oKSB7IHJldHVybiBtb2R1bGU7IH07XG5cdF9fd2VicGFja19yZXF1aXJlX18uZChnZXR0ZXIsIHsgYTogZ2V0dGVyIH0pO1xuXHRyZXR1cm4gZ2V0dGVyO1xufTsiLCIvLyBkZWZpbmUgZ2V0dGVyIGZ1bmN0aW9ucyBmb3IgaGFybW9ueSBleHBvcnRzXG5fX3dlYnBhY2tfcmVxdWlyZV9fLmQgPSBmdW5jdGlvbihleHBvcnRzLCBkZWZpbml0aW9uKSB7XG5cdGZvcih2YXIga2V5IGluIGRlZmluaXRpb24pIHtcblx0XHRpZihfX3dlYnBhY2tfcmVxdWlyZV9fLm8oZGVmaW5pdGlvbiwga2V5KSAmJiAhX193ZWJwYWNrX3JlcXVpcmVfXy5vKGV4cG9ydHMsIGtleSkpIHtcblx0XHRcdE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBrZXksIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBkZWZpbml0aW9uW2tleV0gfSk7XG5cdFx0fVxuXHR9XG59OyIsIl9fd2VicGFja19yZXF1aXJlX18uZyA9IChmdW5jdGlvbigpIHtcblx0aWYgKHR5cGVvZiBnbG9iYWxUaGlzID09PSAnb2JqZWN0JykgcmV0dXJuIGdsb2JhbFRoaXM7XG5cdHRyeSB7XG5cdFx0cmV0dXJuIHRoaXMgfHwgbmV3IEZ1bmN0aW9uKCdyZXR1cm4gdGhpcycpKCk7XG5cdH0gY2F0Y2ggKGUpIHtcblx0XHRpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ29iamVjdCcpIHJldHVybiB3aW5kb3c7XG5cdH1cbn0pKCk7IiwiX193ZWJwYWNrX3JlcXVpcmVfXy5vID0gZnVuY3Rpb24ob2JqLCBwcm9wKSB7IHJldHVybiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBwcm9wKTsgfSIsIi8vIGRlZmluZSBfX2VzTW9kdWxlIG9uIGV4cG9ydHNcbl9fd2VicGFja19yZXF1aXJlX18uciA9IGZ1bmN0aW9uKGV4cG9ydHMpIHtcblx0aWYodHlwZW9mIFN5bWJvbCAhPT0gJ3VuZGVmaW5lZCcgJiYgU3ltYm9sLnRvU3RyaW5nVGFnKSB7XG5cdFx0T2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFN5bWJvbC50b1N0cmluZ1RhZywgeyB2YWx1ZTogJ01vZHVsZScgfSk7XG5cdH1cblx0T2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcbn07IiwiLyohXG4gKiBMYXJhdmVsIEphdmFzY3JpcHQgVmFsaWRhdGlvblxuICpcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9wcm9lbmdzb2Z0L2xhcmF2ZWwtanN2YWxpZGF0aW9uXG4gKlxuICogSGVscGVyIGZ1bmN0aW9ucyB1c2VkIGJ5IHZhbGlkYXRvcnNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIDIwMTcgUHJvZW5nc29mdFxuICogUmVsZWFzZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlXG4gKi9cblxuaW1wb3J0IHN0cmxlbiBmcm9tICdsb2N1dHVzL3BocC9zdHJpbmdzL3N0cmxlbic7XG5pbXBvcnQgYXJyYXlfZGlmZiBmcm9tICdsb2N1dHVzL3BocC9hcnJheS9hcnJheV9kaWZmJztcbmltcG9ydCBzdHJ0b3RpbWUgZnJvbSAnbG9jdXR1cy9waHAvZGF0ZXRpbWUvc3RydG90aW1lJztcbmltcG9ydCBpc19udW1lcmljIGZyb20gJ2xvY3V0dXMvcGhwL3Zhci9pc19udW1lcmljJztcblxuJC5leHRlbmQodHJ1ZSwgbGFyYXZlbFZhbGlkYXRpb24sIHtcblxuICAgIGhlbHBlcnM6IHtcblxuICAgICAgICAvKipcbiAgICAgICAgICogTnVtZXJpYyBydWxlc1xuICAgICAgICAgKi9cbiAgICAgICAgbnVtZXJpY1J1bGVzOiBbJ0ludGVnZXInLCAnTnVtZXJpYyddLFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBHZXRzIHRoZSBmaWxlIGluZm9ybWF0aW9uIGZyb20gZmlsZSBpbnB1dC5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIGZpZWxkT2JqXG4gICAgICAgICAqIEBwYXJhbSBpbmRleFxuICAgICAgICAgKiBAcmV0dXJucyB7e2ZpbGU6ICosIGV4dGVuc2lvbjogc3RyaW5nLCBzaXplOiBudW1iZXJ9fVxuICAgICAgICAgKi9cbiAgICAgICAgZmlsZWluZm86IGZ1bmN0aW9uIChmaWVsZE9iaiwgaW5kZXgpIHtcbiAgICAgICAgICAgIHZhciBGaWxlTmFtZSA9IGZpZWxkT2JqLnZhbHVlO1xuICAgICAgICAgICAgaW5kZXggPSB0eXBlb2YgaW5kZXggIT09ICd1bmRlZmluZWQnID8gaW5kZXggOiAwO1xuICAgICAgICAgICAgaWYgKCBmaWVsZE9iai5maWxlcyAhPT0gbnVsbCApIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGZpZWxkT2JqLmZpbGVzW2luZGV4XSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGU6IEZpbGVOYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXh0ZW5zaW9uOiBGaWxlTmFtZS5zdWJzdHIoRmlsZU5hbWUubGFzdEluZGV4T2YoJy4nKSArIDEpLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZTogZmllbGRPYmouZmlsZXNbaW5kZXhdLnNpemUgLyAxMDI0LFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogZmllbGRPYmouZmlsZXNbaW5kZXhdLnR5cGVcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0sXG5cblxuICAgICAgICAvKipcbiAgICAgICAgICogR2V0cyB0aGUgc2VsZWN0b3JzIGZvciB0aCBzcGVjaWZpZWQgZmllbGQgbmFtZXMuXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSBuYW1lc1xuICAgICAgICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgICAgICAgKi9cbiAgICAgICAgc2VsZWN0b3I6IGZ1bmN0aW9uIChuYW1lcykge1xuICAgICAgICAgICAgdmFyIHNlbGVjdG9yID0gW107XG4gICAgICAgICAgICBpZiAoISB0aGlzLmlzQXJyYXkobmFtZXMpKSAge1xuICAgICAgICAgICAgICAgIG5hbWVzID0gW25hbWVzXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbmFtZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgICAgICBzZWxlY3Rvci5wdXNoKFwiW25hbWU9J1wiICsgbmFtZXNbaV0gKyBcIiddXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHNlbGVjdG9yLmpvaW4oKTtcbiAgICAgICAgfSxcblxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBDaGVjayBpZiBlbGVtZW50IGhhcyBudW1lcmljIHJ1bGVzLlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gZWxlbWVudFxuICAgICAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICAgICAgICovXG4gICAgICAgIGhhc051bWVyaWNSdWxlczogZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmhhc1J1bGVzKGVsZW1lbnQsIHRoaXMubnVtZXJpY1J1bGVzKTtcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogQ2hlY2sgaWYgZWxlbWVudCBoYXMgcGFzc2VkIHJ1bGVzLlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gZWxlbWVudFxuICAgICAgICAgKiBAcGFyYW0gcnVsZXNcbiAgICAgICAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAgICAgICAqL1xuICAgICAgICBoYXNSdWxlczogZnVuY3Rpb24gKGVsZW1lbnQsIHJ1bGVzKSB7XG5cbiAgICAgICAgICAgIHZhciBmb3VuZCA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBydWxlcyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICBydWxlcyA9IFtydWxlc107XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHZhciB2YWxpZGF0b3IgPSAkLmRhdGEoZWxlbWVudC5mb3JtLCBcInZhbGlkYXRvclwiKTtcbiAgICAgICAgICAgIHZhciBsaXN0UnVsZXMgPSBbXTtcbiAgICAgICAgICAgIHZhciBjYWNoZSA9IHZhbGlkYXRvci5hcnJheVJ1bGVzQ2FjaGU7XG4gICAgICAgICAgICBpZiAoZWxlbWVudC5uYW1lIGluIGNhY2hlKSB7XG4gICAgICAgICAgICAgICAgJC5lYWNoKGNhY2hlW2VsZW1lbnQubmFtZV0sIGZ1bmN0aW9uIChpbmRleCwgYXJyYXlSdWxlKSB7XG4gICAgICAgICAgICAgICAgICAgIGxpc3RSdWxlcy5wdXNoKGFycmF5UnVsZSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZWxlbWVudC5uYW1lIGluIHZhbGlkYXRvci5zZXR0aW5ncy5ydWxlcykge1xuICAgICAgICAgICAgICAgIGxpc3RSdWxlcy5wdXNoKHZhbGlkYXRvci5zZXR0aW5ncy5ydWxlc1tlbGVtZW50Lm5hbWVdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgICQuZWFjaChsaXN0UnVsZXMsIGZ1bmN0aW9uKGluZGV4LG9ialJ1bGVzKXtcbiAgICAgICAgICAgICAgICBpZiAoJ2xhcmF2ZWxWYWxpZGF0aW9uJyBpbiBvYmpSdWxlcykge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX3J1bGVzPW9ialJ1bGVzLmxhcmF2ZWxWYWxpZGF0aW9uO1xuICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IF9ydWxlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCQuaW5BcnJheShfcnVsZXNbaV1bMF0scnVsZXMpICE9PSAtMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvdW5kID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgcmV0dXJuIGZvdW5kO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBSZXR1cm4gdGhlIHN0cmluZyBsZW5ndGggdXNpbmcgUEhQIGZ1bmN0aW9uLlxuICAgICAgICAgKiBodHRwOi8vcGhwLm5ldC9tYW51YWwvZW4vZnVuY3Rpb24uc3RybGVuLnBocFxuICAgICAgICAgKiBodHRwOi8vcGhwanMub3JnL2Z1bmN0aW9ucy9zdHJsZW4vXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSBzdHJpbmdcbiAgICAgICAgICovXG4gICAgICAgIHN0cmxlbjogZnVuY3Rpb24gKHN0cmluZykge1xuICAgICAgICAgICAgcmV0dXJuIHN0cmxlbihzdHJpbmcpO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBHZXQgdGhlIHNpemUgb2YgdGhlIG9iamVjdCBkZXBlbmRpbmcgb2YgaGlzIHR5cGUuXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSBvYmpcbiAgICAgICAgICogQHBhcmFtIGVsZW1lbnRcbiAgICAgICAgICogQHBhcmFtIHZhbHVlXG4gICAgICAgICAqIEByZXR1cm5zIGludFxuICAgICAgICAgKi9cbiAgICAgICAgZ2V0U2l6ZTogZnVuY3Rpb24gZ2V0U2l6ZShvYmosIGVsZW1lbnQsIHZhbHVlKSB7XG5cbiAgICAgICAgICAgIGlmICh0aGlzLmhhc051bWVyaWNSdWxlcyhlbGVtZW50KSAmJiB0aGlzLmlzX251bWVyaWModmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHBhcnNlRmxvYXQodmFsdWUpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHBhcnNlRmxvYXQodmFsdWUubGVuZ3RoKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZWxlbWVudC50eXBlID09PSAnZmlsZScpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcGFyc2VGbG9hdChNYXRoLmZsb29yKHRoaXMuZmlsZWluZm8oZWxlbWVudCkuc2l6ZSkpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gcGFyc2VGbG9hdCh0aGlzLnN0cmxlbih2YWx1ZSkpO1xuICAgICAgICB9LFxuXG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIFJldHVybiBzcGVjaWZpZWQgcnVsZSBmcm9tIGVsZW1lbnQuXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSBydWxlXG4gICAgICAgICAqIEBwYXJhbSBlbGVtZW50XG4gICAgICAgICAqIEByZXR1cm5zIG9iamVjdFxuICAgICAgICAgKi9cbiAgICAgICAgZ2V0TGFyYXZlbFZhbGlkYXRpb246IGZ1bmN0aW9uKHJ1bGUsIGVsZW1lbnQpIHtcblxuICAgICAgICAgICAgdmFyIGZvdW5kID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgJC5lYWNoKCQudmFsaWRhdG9yLnN0YXRpY1J1bGVzKGVsZW1lbnQpLCBmdW5jdGlvbihrZXksIHJ1bGVzKSB7XG4gICAgICAgICAgICAgICAgaWYgKGtleT09PVwibGFyYXZlbFZhbGlkYXRpb25cIikge1xuICAgICAgICAgICAgICAgICAgICAkLmVhY2gocnVsZXMsIGZ1bmN0aW9uIChpLCB2YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHZhbHVlWzBdPT09cnVsZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvdW5kPXZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgcmV0dXJuIGZvdW5kO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBSZXR1cm4gaGUgdGltZXN0YW1wIG9mIHZhbHVlIHBhc3NlZCB1c2luZyBmb3JtYXQgb3IgZGVmYXVsdCBmb3JtYXQgaW4gZWxlbWVudC5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIHZhbHVlXG4gICAgICAgICAqIEBwYXJhbSBmb3JtYXRcbiAgICAgICAgICogQHJldHVybnMge2Jvb2xlYW58aW50fVxuICAgICAgICAgKi9cbiAgICAgICAgcGFyc2VUaW1lOiBmdW5jdGlvbiAodmFsdWUsIGZvcm1hdCkge1xuXG4gICAgICAgICAgICB2YXIgdGltZVZhbHVlID0gZmFsc2U7XG4gICAgICAgICAgICB2YXIgZm10ID0gbmV3IERhdGVGb3JtYXR0ZXIoKTtcblxuICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicgJiYgdHlwZW9mIGZvcm1hdCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmICh0eXBlb2YgZm9ybWF0ID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgICAgIHZhciBkYXRlUnVsZSA9IHRoaXMuZ2V0TGFyYXZlbFZhbGlkYXRpb24oJ0RhdGVGb3JtYXQnLCBmb3JtYXQpO1xuICAgICAgICAgICAgICAgIGlmIChkYXRlUnVsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdCA9IGRhdGVSdWxlWzFdWzBdO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdCA9IG51bGw7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoZm9ybWF0ID09IG51bGwpIHtcbiAgICAgICAgICAgICAgICB0aW1lVmFsdWUgPSB0aGlzLnN0cnRvdGltZSh2YWx1ZSk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRpbWVWYWx1ZSA9IGZtdC5wYXJzZURhdGUodmFsdWUsIGZvcm1hdCk7XG4gICAgICAgICAgICAgICAgaWYgKHRpbWVWYWx1ZSBpbnN0YW5jZW9mIERhdGUgJiYgZm10LmZvcm1hdERhdGUodGltZVZhbHVlLCBmb3JtYXQpID09PSB2YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aW1lVmFsdWUgPSBNYXRoLnJvdW5kKCh0aW1lVmFsdWUuZ2V0VGltZSgpIC8gMTAwMCkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRpbWVWYWx1ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIHRpbWVWYWx1ZTtcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogQ29tcGFyZSBhIGdpdmVuIGRhdGUgYWdhaW5zdCBhbm90aGVyIHVzaW5nIGFuIG9wZXJhdG9yLlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gdmFsaWRhdG9yXG4gICAgICAgICAqIEBwYXJhbSB2YWx1ZVxuICAgICAgICAgKiBAcGFyYW0gZWxlbWVudFxuICAgICAgICAgKiBAcGFyYW0gcGFyYW1zXG4gICAgICAgICAqIEBwYXJhbSBvcGVyYXRvclxuICAgICAgICAgKiBAcmV0dXJuIHtib29sZWFufVxuICAgICAgICAgKi9cbiAgICAgICAgY29tcGFyZURhdGVzOiBmdW5jdGlvbiAodmFsaWRhdG9yLCB2YWx1ZSwgZWxlbWVudCwgcGFyYW1zLCBvcGVyYXRvcikge1xuXG4gICAgICAgICAgICB2YXIgdGltZUNvbXBhcmUgPSB0aGlzLnBhcnNlVGltZShwYXJhbXMpO1xuXG4gICAgICAgICAgICBpZiAoIXRpbWVDb21wYXJlKSB7XG4gICAgICAgICAgICAgICAgdmFyIHRhcmdldCA9IHRoaXMuZGVwZW5kZW50RWxlbWVudCh2YWxpZGF0b3IsIGVsZW1lbnQsIHBhcmFtcyk7XG4gICAgICAgICAgICAgICAgaWYgKHRhcmdldCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGltZUNvbXBhcmUgPSB0aGlzLnBhcnNlVGltZSh2YWxpZGF0b3IuZWxlbWVudFZhbHVlKHRhcmdldCksIHRhcmdldCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHZhciB0aW1lVmFsdWUgPSB0aGlzLnBhcnNlVGltZSh2YWx1ZSwgZWxlbWVudCk7XG4gICAgICAgICAgICBpZiAodGltZVZhbHVlID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgc3dpdGNoIChvcGVyYXRvcikge1xuICAgICAgICAgICAgICAgIGNhc2UgJzwnOlxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGltZVZhbHVlIDwgdGltZUNvbXBhcmU7XG5cbiAgICAgICAgICAgICAgICBjYXNlICc8PSc6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0aW1lVmFsdWUgPD0gdGltZUNvbXBhcmU7XG5cbiAgICAgICAgICAgICAgICBjYXNlICc9PSc6XG4gICAgICAgICAgICAgICAgY2FzZSAnPT09JzpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRpbWVWYWx1ZSA9PT0gdGltZUNvbXBhcmU7XG5cbiAgICAgICAgICAgICAgICBjYXNlICc+JzpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRpbWVWYWx1ZSA+IHRpbWVDb21wYXJlO1xuXG4gICAgICAgICAgICAgICAgY2FzZSAnPj0nOlxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGltZVZhbHVlID49IHRpbWVDb21wYXJlO1xuXG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVbnN1cHBvcnRlZCBvcGVyYXRvci4nKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogVGhpcyBtZXRob2QgYWxsb3dzIHlvdSB0byBpbnRlbGxpZ2VudGx5IGd1ZXNzIHRoZSBkYXRlIGJ5IGNsb3NlbHkgbWF0Y2hpbmcgdGhlIHNwZWNpZmljIGZvcm1hdC5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIHZhbHVlXG4gICAgICAgICAqIEBwYXJhbSBmb3JtYXRcbiAgICAgICAgICogQHJldHVybnMge0RhdGV9XG4gICAgICAgICAqL1xuICAgICAgICBndWVzc0RhdGU6IGZ1bmN0aW9uICh2YWx1ZSwgZm9ybWF0KSB7XG4gICAgICAgICAgICB2YXIgZm10ID0gbmV3IERhdGVGb3JtYXR0ZXIoKTtcbiAgICAgICAgICAgIHJldHVybiBmbXQuZ3Vlc3NEYXRlKHZhbHVlLCBmb3JtYXQpXG4gICAgICAgIH0sXG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIFJldHVybnMgVW5peCB0aW1lc3RhbXAgYmFzZWQgb24gUEhQIGZ1bmN0aW9uIHN0cm90b3RpbWUuXG4gICAgICAgICAqIGh0dHA6Ly9waHAubmV0L21hbnVhbC9lcy9mdW5jdGlvbi5zdHJ0b3RpbWUucGhwXG4gICAgICAgICAqIGh0dHA6Ly9waHBqcy5vcmcvZnVuY3Rpb25zL3N0cnRvdGltZS9cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIHRleHRcbiAgICAgICAgICogQHBhcmFtIG5vd1xuICAgICAgICAgKiBAcmV0dXJucyB7Kn1cbiAgICAgICAgICovXG4gICAgICAgIHN0cnRvdGltZTogZnVuY3Rpb24gKHRleHQsIG5vdykge1xuICAgICAgICAgICAgcmV0dXJuIHN0cnRvdGltZSh0ZXh0LCBub3cpXG4gICAgICAgIH0sXG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIFJldHVybnMgaWYgdmFsdWUgaXMgbnVtZXJpYy5cbiAgICAgICAgICogaHR0cDovL3BocC5uZXQvbWFudWFsL2VzL3Zhci5pc19udW1lcmljLnBocFxuICAgICAgICAgKiBodHRwOi8vcGhwanMub3JnL2Z1bmN0aW9ucy9pc19udW1lcmljL1xuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gbWl4ZWRfdmFyXG4gICAgICAgICAqIEByZXR1cm5zIHsqfVxuICAgICAgICAgKi9cbiAgICAgICAgaXNfbnVtZXJpYzogZnVuY3Rpb24gKG1peGVkX3Zhcikge1xuICAgICAgICAgICAgcmV0dXJuIGlzX251bWVyaWMobWl4ZWRfdmFyKVxuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBDaGVjayB3aGV0aGVyIHRoZSBhcmd1bWVudCBpcyBvZiB0eXBlIEFycmF5LlxuICAgICAgICAgKiBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9KYXZhU2NyaXB0L1JlZmVyZW5jZS9HbG9iYWxfT2JqZWN0cy9BcnJheS9pc0FycmF5I1BvbHlmaWxsXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSBhcmdcbiAgICAgICAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAgICAgICAqL1xuICAgICAgICBpc0FycmF5OiBmdW5jdGlvbihhcmcpIHtcbiAgICAgICAgICAgIHJldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoYXJnKSA9PT0gJ1tvYmplY3QgQXJyYXldJztcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogUmV0dXJucyBBcnJheSBkaWZmIGJhc2VkIG9uIFBIUCBmdW5jdGlvbiBhcnJheV9kaWZmLlxuICAgICAgICAgKiBodHRwOi8vcGhwLm5ldC9tYW51YWwvZXMvZnVuY3Rpb24uYXJyYXlfZGlmZi5waHBcbiAgICAgICAgICogaHR0cDovL3BocGpzLm9yZy9mdW5jdGlvbnMvYXJyYXlfZGlmZi9cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIGFycjFcbiAgICAgICAgICogQHBhcmFtIGFycjJcbiAgICAgICAgICogQHJldHVybnMgeyp9XG4gICAgICAgICAqL1xuICAgICAgICBhcnJheURpZmY6IGZ1bmN0aW9uIChhcnIxLCBhcnIyKSB7XG4gICAgICAgICAgICByZXR1cm4gYXJyYXlfZGlmZihhcnIxLCBhcnIyKTtcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogQ2hlY2sgd2hldGhlciB0d28gYXJyYXlzIGFyZSBlcXVhbCB0byBvbmUgYW5vdGhlci5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIGFycjFcbiAgICAgICAgICogQHBhcmFtIGFycjJcbiAgICAgICAgICogQHJldHVybnMgeyp9XG4gICAgICAgICAqL1xuICAgICAgICBhcnJheUVxdWFsczogZnVuY3Rpb24gKGFycjEsIGFycjIpIHtcbiAgICAgICAgICAgIGlmICghIHRoaXMuaXNBcnJheShhcnIxKSB8fCAhIHRoaXMuaXNBcnJheShhcnIyKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKGFycjEubGVuZ3RoICE9PSBhcnIyLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgcmV0dXJuICQuaXNFbXB0eU9iamVjdCh0aGlzLmFycmF5RGlmZihhcnIxLCBhcnIyKSk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIE1ha2VzIGVsZW1lbnQgZGVwZW5kYW50IGZyb20gb3RoZXIuXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSB2YWxpZGF0b3JcbiAgICAgICAgICogQHBhcmFtIGVsZW1lbnRcbiAgICAgICAgICogQHBhcmFtIG5hbWVcbiAgICAgICAgICogQHJldHVybnMgeyp9XG4gICAgICAgICAqL1xuICAgICAgICBkZXBlbmRlbnRFbGVtZW50OiBmdW5jdGlvbih2YWxpZGF0b3IsIGVsZW1lbnQsIG5hbWUpIHtcblxuICAgICAgICAgICAgdmFyIGVsPXZhbGlkYXRvci5maW5kQnlOYW1lKG5hbWUpO1xuXG4gICAgICAgICAgICBpZiAoIGVsWzBdIT09dW5kZWZpbmVkICAmJiB2YWxpZGF0b3Iuc2V0dGluZ3Mub25mb2N1c291dCApIHtcbiAgICAgICAgICAgICAgICB2YXIgZXZlbnQgPSAnYmx1cic7XG4gICAgICAgICAgICAgICAgaWYgKGVsWzBdLnRhZ05hbWUgPT09ICdTRUxFQ1QnIHx8XG4gICAgICAgICAgICAgICAgICAgIGVsWzBdLnRhZ05hbWUgPT09ICdPUFRJT04nIHx8XG4gICAgICAgICAgICAgICAgICAgIGVsWzBdLnR5cGUgPT09ICdjaGVja2JveCcgfHxcbiAgICAgICAgICAgICAgICAgICAgZWxbMF0udHlwZSA9PT0gJ3JhZGlvJ1xuICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgICBldmVudCA9ICdjbGljayc7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgdmFyIHJ1bGVOYW1lID0gJy52YWxpZGF0ZS1sYXJhdmVsVmFsaWRhdGlvbic7XG4gICAgICAgICAgICAgICAgZWwub2ZmKCBydWxlTmFtZSApXG4gICAgICAgICAgICAgICAgICAgIC5vZmYoZXZlbnQgKyBydWxlTmFtZSArICctJyArIGVsZW1lbnQubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgLm9uKCBldmVudCArIHJ1bGVOYW1lICsgJy0nICsgZWxlbWVudC5uYW1lLCBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICQoIGVsZW1lbnQgKS52YWxpZCgpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIGVsWzBdO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBQYXJzZXMgZXJyb3IgQWpheCByZXNwb25zZSBhbmQgZ2V0cyB0aGUgbWVzc2FnZS5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIHJlc3BvbnNlXG4gICAgICAgICAqIEByZXR1cm5zIHtzdHJpbmdbXX1cbiAgICAgICAgICovXG4gICAgICAgIHBhcnNlRXJyb3JSZXNwb25zZTogZnVuY3Rpb24gKHJlc3BvbnNlKSB7XG4gICAgICAgICAgICB2YXIgbmV3UmVzcG9uc2UgPSBbJ1dob29wcywgbG9va3MgbGlrZSBzb21ldGhpbmcgd2VudCB3cm9uZy4nXTtcbiAgICAgICAgICAgIGlmICgncmVzcG9uc2VUZXh0JyBpbiByZXNwb25zZSkge1xuICAgICAgICAgICAgICAgIHZhciBlcnJvck1zZyA9IHJlc3BvbnNlLnJlc3BvbnNlVGV4dC5tYXRjaCgvPGgxXFxzKj4oLiopPFxcL2gxXFxzKj4vaSk7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNBcnJheShlcnJvck1zZykpIHtcbiAgICAgICAgICAgICAgICAgICAgbmV3UmVzcG9uc2UgPSBbZXJyb3JNc2dbMV1dO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBuZXdSZXNwb25zZTtcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogRXNjYXBlIHN0cmluZyB0byB1c2UgYXMgUmVndWxhciBFeHByZXNzaW9uLlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gc3RyXG4gICAgICAgICAqIEByZXR1cm5zIHN0cmluZ1xuICAgICAgICAgKi9cbiAgICAgICAgZXNjYXBlUmVnRXhwOiBmdW5jdGlvbiAoc3RyKSB7XG4gICAgICAgICAgICByZXR1cm4gc3RyLnJlcGxhY2UoL1tcXC1cXFtcXF1cXC9cXHtcXH1cXChcXClcXCpcXCtcXD9cXC5cXFxcXFxeXFwkXFx8XS9nLCBcIlxcXFwkJlwiKTtcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogR2VuZXJhdGUgUmVnRXhwIGZyb20gd2lsZGNhcmQgYXR0cmlidXRlcy5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIG5hbWVcbiAgICAgICAgICogQHJldHVybnMge1JlZ0V4cH1cbiAgICAgICAgICovXG4gICAgICAgIHJlZ2V4RnJvbVdpbGRjYXJkOiBmdW5jdGlvbiAobmFtZSkge1xuICAgICAgICAgICAgdmFyIG5hbWVQYXJ0cyA9IG5hbWUuc3BsaXQoJ1sqXScpO1xuICAgICAgICAgICAgaWYgKG5hbWVQYXJ0cy5sZW5ndGggPT09IDEpIG5hbWVQYXJ0cy5wdXNoKCcnKTtcblxuICAgICAgICAgICAgcmV0dXJuIG5ldyBSZWdFeHAoJ14nICsgbmFtZVBhcnRzLm1hcChmdW5jdGlvbih4KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGxhcmF2ZWxWYWxpZGF0aW9uLmhlbHBlcnMuZXNjYXBlUmVnRXhwKHgpXG4gICAgICAgICAgICB9KS5qb2luKCdcXFxcW1teXFxcXF1dKlxcXFxdJykgKyAnJCcpO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBNZXJnZSBhZGRpdGlvbmFsIGxhcmF2ZWwgdmFsaWRhdGlvbiBydWxlcyBpbnRvIHRoZSBjdXJyZW50IHJ1bGUgc2V0LlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0ge29iamVjdH0gcnVsZXNcbiAgICAgICAgICogQHBhcmFtIHtvYmplY3R9IG5ld1J1bGVzXG4gICAgICAgICAqIEByZXR1cm5zIHtvYmplY3R9XG4gICAgICAgICAqL1xuICAgICAgICBtZXJnZVJ1bGVzOiBmdW5jdGlvbiAocnVsZXMsIG5ld1J1bGVzKSB7XG4gICAgICAgICAgICB2YXIgcnVsZXNMaXN0ID0ge1xuICAgICAgICAgICAgICAgICdsYXJhdmVsVmFsaWRhdGlvbic6IG5ld1J1bGVzLmxhcmF2ZWxWYWxpZGF0aW9uIHx8IFtdLFxuICAgICAgICAgICAgICAgICdsYXJhdmVsVmFsaWRhdGlvblJlbW90ZSc6IG5ld1J1bGVzLmxhcmF2ZWxWYWxpZGF0aW9uUmVtb3RlIHx8IFtdXG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICBmb3IgKHZhciBrZXkgaW4gcnVsZXNMaXN0KSB7XG4gICAgICAgICAgICAgICAgaWYgKHJ1bGVzTGlzdFtrZXldLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHJ1bGVzW2tleV0gPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgcnVsZXNba2V5XSA9IFtdO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIHJ1bGVzW2tleV0gPSBydWxlc1trZXldLmNvbmNhdChydWxlc0xpc3Rba2V5XSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiBydWxlcztcbiAgICAgICAgfSxcblxuICAgICAgICAvKipcbiAgICAgICAgICogSFRNTCBlbnRpdHkgZW5jb2RlIGEgc3RyaW5nLlxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gc3RyaW5nXG4gICAgICAgICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAgICAgICAqL1xuICAgICAgICBlbmNvZGU6IGZ1bmN0aW9uIChzdHJpbmcpIHtcbiAgICAgICAgICAgIHJldHVybiAkKCc8ZGl2Lz4nKS50ZXh0KHN0cmluZykuaHRtbCgpO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBMb29rdXAgbmFtZSBpbiBhbiBhcnJheS5cbiAgICAgICAgICpcbiAgICAgICAgICogQHBhcmFtIHZhbGlkYXRvclxuICAgICAgICAgKiBAcGFyYW0ge3N0cmluZ30gbmFtZSBOYW1lIGluIGRvdCBub3RhdGlvbiBmb3JtYXQuXG4gICAgICAgICAqIEByZXR1cm5zIHsqfVxuICAgICAgICAgKi9cbiAgICAgICAgZmluZEJ5QXJyYXlOYW1lOiBmdW5jdGlvbiAodmFsaWRhdG9yLCBuYW1lKSB7XG4gICAgICAgICAgICB2YXIgc3FOYW1lID0gbmFtZS5yZXBsYWNlKC9cXC4oW15cXC5dKykvZywgJ1skMV0nKSxcbiAgICAgICAgICAgICAgICBsb29rdXBzID0gW1xuICAgICAgICAgICAgICAgICAgICAvLyBDb252ZXJ0IGRvdCB0byBzcXVhcmUgYnJhY2tldHMuIGUuZy4gZm9vLmJhci4wIGJlY29tZXMgZm9vW2Jhcl1bMF1cbiAgICAgICAgICAgICAgICAgICAgc3FOYW1lLFxuICAgICAgICAgICAgICAgICAgICAvLyBBcHBlbmQgW10gdG8gdGhlIG5hbWUgZS5nLiBmb28gYmVjb21lcyBmb29bXSBvciBmb28uYmFyLjAgYmVjb21lcyBmb29bYmFyXVswXVtdXG4gICAgICAgICAgICAgICAgICAgIHNxTmFtZSArICdbXScsXG4gICAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSBrZXkgZnJvbSBsYXN0IGFycmF5IGUuZy4gZm9vW2Jhcl1bMF0gYmVjb21lcyBmb29bYmFyXVtdXG4gICAgICAgICAgICAgICAgICAgIHNxTmFtZS5yZXBsYWNlKC8oLiopXFxbKC4qKVxcXSQvZywgJyQxW10nKVxuICAgICAgICAgICAgICAgIF07XG5cbiAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbG9va3Vwcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIHZhciBlbGVtID0gdmFsaWRhdG9yLmZpbmRCeU5hbWUobG9va3Vwc1tpXSk7XG4gICAgICAgICAgICAgICAgaWYgKGVsZW0ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZWxlbTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiAkKG51bGwpO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBBdHRlbXB0IHRvIGZpbmQgYW4gZWxlbWVudCBpbiB0aGUgRE9NIG1hdGNoaW5nIHRoZSBnaXZlbiBuYW1lLlxuICAgICAgICAgKiBFeGFtcGxlIG5hbWVzIGluY2x1ZGU6XG4gICAgICAgICAqICAgIC0gZG9tYWluLjAgd2hpY2ggbWF0Y2hlcyBkb21haW5bXVxuICAgICAgICAgKiAgICAtIGN1c3RvbWZpZWxkLjMgd2hpY2ggbWF0Y2hlcyBjdXN0b21maWVsZFszXVxuICAgICAgICAgKlxuICAgICAgICAgKiBAcGFyYW0gdmFsaWRhdG9yXG4gICAgICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICAgICAqIEByZXR1cm5zIHsqfVxuICAgICAgICAgKi9cbiAgICAgICAgZmluZEJ5TmFtZTogZnVuY3Rpb24gKHZhbGlkYXRvciwgbmFtZSkge1xuICAgICAgICAgICAgLy8gRXhhY3QgbWF0Y2guXG4gICAgICAgICAgICB2YXIgZWxlbSA9IHZhbGlkYXRvci5maW5kQnlOYW1lKG5hbWUpO1xuICAgICAgICAgICAgaWYgKGVsZW0ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBlbGVtO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBGaW5kIG5hbWUgaW4gZGF0YSwgdXNpbmcgZG90IG5vdGF0aW9uLlxuICAgICAgICAgICAgdmFyIGRlbGltID0gJy4nLFxuICAgICAgICAgICAgICAgIHBhcnRzICA9IG5hbWUuc3BsaXQoZGVsaW0pO1xuICAgICAgICAgICAgZm9yICh2YXIgaSA9IHBhcnRzLmxlbmd0aDsgaSA+IDA7IGktLSkge1xuICAgICAgICAgICAgICAgIHZhciByZWNvbnN0cnVjdGVkID0gW107XG4gICAgICAgICAgICAgICAgZm9yICh2YXIgYyA9IDA7IGMgPCBpOyBjKyspIHtcbiAgICAgICAgICAgICAgICAgICAgcmVjb25zdHJ1Y3RlZC5wdXNoKHBhcnRzW2NdKTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBlbGVtID0gdGhpcy5maW5kQnlBcnJheU5hbWUodmFsaWRhdG9yLCByZWNvbnN0cnVjdGVkLmpvaW4oZGVsaW0pKTtcbiAgICAgICAgICAgICAgICBpZiAoZWxlbS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlbGVtO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuICQobnVsbCk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLyoqXG4gICAgICAgICAqIElmIGl0J3MgYW4gYXJyYXkgZWxlbWVudCwgZ2V0IGFsbCB2YWx1ZXMuXG4gICAgICAgICAqXG4gICAgICAgICAqIEBwYXJhbSB2YWxpZGF0b3JcbiAgICAgICAgICogQHBhcmFtIGVsZW1lbnRcbiAgICAgICAgICogQHJldHVybnMgeyp8c3RyaW5nfVxuICAgICAgICAgKi9cbiAgICAgICAgYWxsRWxlbWVudFZhbHVlczogZnVuY3Rpb24gKHZhbGlkYXRvciwgZWxlbWVudCkge1xuICAgICAgICAgICAgaWYgKGVsZW1lbnQubmFtZS5pbmRleE9mKCdbXScpICE9PSAtMSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB2YWxpZGF0b3IuZmluZEJ5TmFtZShlbGVtZW50Lm5hbWUpLm1hcChmdW5jdGlvbiAoaSwgZSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsaWRhdG9yLmVsZW1lbnRWYWx1ZShlKTtcbiAgICAgICAgICAgICAgICB9KS5nZXQoKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIHZhbGlkYXRvci5lbGVtZW50VmFsdWUoZWxlbWVudCk7XG4gICAgICAgIH1cbiAgICB9XG59KTtcbiJdLCJuYW1lcyI6WyJzdHJsZW4iLCJhcnJheV9kaWZmIiwic3RydG90aW1lIiwiaXNfbnVtZXJpYyIsIiQiLCJleHRlbmQiLCJsYXJhdmVsVmFsaWRhdGlvbiIsImhlbHBlcnMiLCJudW1lcmljUnVsZXMiLCJmaWxlaW5mbyIsImZpZWxkT2JqIiwiaW5kZXgiLCJGaWxlTmFtZSIsInZhbHVlIiwiZmlsZXMiLCJmaWxlIiwiZXh0ZW5zaW9uIiwic3Vic3RyIiwibGFzdEluZGV4T2YiLCJzaXplIiwidHlwZSIsInNlbGVjdG9yIiwibmFtZXMiLCJpc0FycmF5IiwiaSIsImxlbmd0aCIsInB1c2giLCJqb2luIiwiaGFzTnVtZXJpY1J1bGVzIiwiZWxlbWVudCIsImhhc1J1bGVzIiwicnVsZXMiLCJmb3VuZCIsInZhbGlkYXRvciIsImRhdGEiLCJmb3JtIiwibGlzdFJ1bGVzIiwiY2FjaGUiLCJhcnJheVJ1bGVzQ2FjaGUiLCJuYW1lIiwiZWFjaCIsImFycmF5UnVsZSIsInNldHRpbmdzIiwib2JqUnVsZXMiLCJfcnVsZXMiLCJpbkFycmF5Iiwic3RyaW5nIiwiZ2V0U2l6ZSIsIm9iaiIsInBhcnNlRmxvYXQiLCJNYXRoIiwiZmxvb3IiLCJnZXRMYXJhdmVsVmFsaWRhdGlvbiIsInJ1bGUiLCJ1bmRlZmluZWQiLCJzdGF0aWNSdWxlcyIsImtleSIsInBhcnNlVGltZSIsImZvcm1hdCIsInRpbWVWYWx1ZSIsImZtdCIsIkRhdGVGb3JtYXR0ZXIiLCJkYXRlUnVsZSIsInBhcnNlRGF0ZSIsIkRhdGUiLCJmb3JtYXREYXRlIiwicm91bmQiLCJnZXRUaW1lIiwiY29tcGFyZURhdGVzIiwicGFyYW1zIiwib3BlcmF0b3IiLCJ0aW1lQ29tcGFyZSIsInRhcmdldCIsImRlcGVuZGVudEVsZW1lbnQiLCJlbGVtZW50VmFsdWUiLCJFcnJvciIsImd1ZXNzRGF0ZSIsInRleHQiLCJub3ciLCJtaXhlZF92YXIiLCJhcmciLCJPYmplY3QiLCJwcm90b3R5cGUiLCJ0b1N0cmluZyIsImNhbGwiLCJhcnJheURpZmYiLCJhcnIxIiwiYXJyMiIsImFycmF5RXF1YWxzIiwiaXNFbXB0eU9iamVjdCIsImVsIiwiZmluZEJ5TmFtZSIsIm9uZm9jdXNvdXQiLCJldmVudCIsInRhZ05hbWUiLCJydWxlTmFtZSIsIm9mZiIsIm9uIiwidmFsaWQiLCJwYXJzZUVycm9yUmVzcG9uc2UiLCJyZXNwb25zZSIsIm5ld1Jlc3BvbnNlIiwiZXJyb3JNc2ciLCJyZXNwb25zZVRleHQiLCJtYXRjaCIsImVzY2FwZVJlZ0V4cCIsInN0ciIsInJlcGxhY2UiLCJyZWdleEZyb21XaWxkY2FyZCIsIm5hbWVQYXJ0cyIsInNwbGl0IiwiUmVnRXhwIiwibWFwIiwieCIsIm1lcmdlUnVsZXMiLCJuZXdSdWxlcyIsInJ1bGVzTGlzdCIsImxhcmF2ZWxWYWxpZGF0aW9uUmVtb3RlIiwiY29uY2F0IiwiZW5jb2RlIiwiaHRtbCIsImZpbmRCeUFycmF5TmFtZSIsInNxTmFtZSIsImxvb2t1cHMiLCJlbGVtIiwiZGVsaW0iLCJwYXJ0cyIsInJlY29uc3RydWN0ZWQiLCJjIiwiYWxsRWxlbWVudFZhbHVlcyIsImluZGV4T2YiLCJlIiwiZ2V0Il0sInNvdXJjZVJvb3QiOiIifQ==", "/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Timezone Helper functions used by validators\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n$.extend(true, laravelValidation, {\n\n    helpers: {\n\n        /**\n         * Check if the specified timezone is valid.\n         *\n         * @param value\n         * @returns {boolean}\n         */\n        isTimezone: function (value) {\n\n            var timezones={\n                \"africa\": [\n                    \"abidjan\",\n                    \"accra\",\n                    \"addis_ababa\",\n                    \"algiers\",\n                    \"asmara\",\n                    \"bamako\",\n                    \"bangui\",\n                    \"banjul\",\n                    \"bissau\",\n                    \"blantyre\",\n                    \"brazzaville\",\n                    \"bujumbura\",\n                    \"cairo\",\n                    \"casablanca\",\n                    \"ceuta\",\n                    \"conakry\",\n                    \"dakar\",\n                    \"dar_es_salaam\",\n                    \"djibouti\",\n                    \"douala\",\n                    \"el_aaiun\",\n                    \"freetown\",\n                    \"gaborone\",\n                    \"harare\",\n                    \"johannesburg\",\n                    \"juba\",\n                    \"kampala\",\n                    \"khartoum\",\n                    \"kigali\",\n                    \"kinshasa\",\n                    \"lagos\",\n                    \"libreville\",\n                    \"lome\",\n                    \"luanda\",\n                    \"lubumbashi\",\n                    \"lusaka\",\n                    \"malabo\",\n                    \"maputo\",\n                    \"maseru\",\n                    \"mbabane\",\n                    \"mogadishu\",\n                    \"monrovia\",\n                    \"nairobi\",\n                    \"ndjamena\",\n                    \"niamey\",\n                    \"nouakchott\",\n                    \"ouagadougou\",\n                    \"porto-novo\",\n                    \"sao_tome\",\n                    \"tripoli\",\n                    \"tunis\",\n                    \"windhoek\"\n                ],\n                \"america\": [\n                    \"adak\",\n                    \"anchorage\",\n                    \"anguilla\",\n                    \"antigua\",\n                    \"araguaina\",\n                    \"argentina\\/buenos_aires\",\n                    \"argentina\\/catamarca\",\n                    \"argentina\\/cordoba\",\n                    \"argentina\\/jujuy\",\n                    \"argentina\\/la_rioja\",\n                    \"argentina\\/mendoza\",\n                    \"argentina\\/rio_gallegos\",\n                    \"argentina\\/salta\",\n                    \"argentina\\/san_juan\",\n                    \"argentina\\/san_luis\",\n                    \"argentina\\/tucuman\",\n                    \"argentina\\/ushuaia\",\n                    \"aruba\",\n                    \"asuncion\",\n                    \"atikokan\",\n                    \"bahia\",\n                    \"bahia_banderas\",\n                    \"barbados\",\n                    \"belem\",\n                    \"belize\",\n                    \"blanc-sablon\",\n                    \"boa_vista\",\n                    \"bogota\",\n                    \"boise\",\n                    \"cambridge_bay\",\n                    \"campo_grande\",\n                    \"cancun\",\n                    \"caracas\",\n                    \"cayenne\",\n                    \"cayman\",\n                    \"chicago\",\n                    \"chihuahua\",\n                    \"costa_rica\",\n                    \"creston\",\n                    \"cuiaba\",\n                    \"curacao\",\n                    \"danmarkshavn\",\n                    \"dawson\",\n                    \"dawson_creek\",\n                    \"denver\",\n                    \"detroit\",\n                    \"dominica\",\n                    \"edmonton\",\n                    \"eirunepe\",\n                    \"el_salvador\",\n                    \"fortaleza\",\n                    \"glace_bay\",\n                    \"godthab\",\n                    \"goose_bay\",\n                    \"grand_turk\",\n                    \"grenada\",\n                    \"guadeloupe\",\n                    \"guatemala\",\n                    \"guayaquil\",\n                    \"guyana\",\n                    \"halifax\",\n                    \"havana\",\n                    \"hermosillo\",\n                    \"indiana\\/indianapolis\",\n                    \"indiana\\/knox\",\n                    \"indiana\\/marengo\",\n                    \"indiana\\/petersburg\",\n                    \"indiana\\/tell_city\",\n                    \"indiana\\/vevay\",\n                    \"indiana\\/vincennes\",\n                    \"indiana\\/winamac\",\n                    \"inuvik\",\n                    \"iqaluit\",\n                    \"jamaica\",\n                    \"juneau\",\n                    \"kentucky\\/louisville\",\n                    \"kentucky\\/monticello\",\n                    \"kralendijk\",\n                    \"la_paz\",\n                    \"lima\",\n                    \"los_angeles\",\n                    \"lower_princes\",\n                    \"maceio\",\n                    \"managua\",\n                    \"manaus\",\n                    \"marigot\",\n                    \"martinique\",\n                    \"matamoros\",\n                    \"mazatlan\",\n                    \"menominee\",\n                    \"merida\",\n                    \"metlakatla\",\n                    \"mexico_city\",\n                    \"miquelon\",\n                    \"moncton\",\n                    \"monterrey\",\n                    \"montevideo\",\n                    \"montreal\",\n                    \"montserrat\",\n                    \"nassau\",\n                    \"new_york\",\n                    \"nipigon\",\n                    \"nome\",\n                    \"noronha\",\n                    \"north_dakota\\/beulah\",\n                    \"north_dakota\\/center\",\n                    \"north_dakota\\/new_salem\",\n                    \"ojinaga\",\n                    \"panama\",\n                    \"pangnirtung\",\n                    \"paramaribo\",\n                    \"phoenix\",\n                    \"port-au-prince\",\n                    \"port_of_spain\",\n                    \"porto_velho\",\n                    \"puerto_rico\",\n                    \"rainy_river\",\n                    \"rankin_inlet\",\n                    \"recife\",\n                    \"regina\",\n                    \"resolute\",\n                    \"rio_branco\",\n                    \"santa_isabel\",\n                    \"santarem\",\n                    \"santiago\",\n                    \"santo_domingo\",\n                    \"sao_paulo\",\n                    \"scoresbysund\",\n                    \"shiprock\",\n                    \"sitka\",\n                    \"st_barthelemy\",\n                    \"st_johns\",\n                    \"st_kitts\",\n                    \"st_lucia\",\n                    \"st_thomas\",\n                    \"st_vincent\",\n                    \"swift_current\",\n                    \"tegucigalpa\",\n                    \"thule\",\n                    \"thunder_bay\",\n                    \"tijuana\",\n                    \"toronto\",\n                    \"tortola\",\n                    \"vancouver\",\n                    \"whitehorse\",\n                    \"winnipeg\",\n                    \"yakutat\",\n                    \"yellowknife\"\n                ],\n                \"antarctica\": [\n                    \"casey\",\n                    \"davis\",\n                    \"dumontdurville\",\n                    \"macquarie\",\n                    \"mawson\",\n                    \"mcmurdo\",\n                    \"palmer\",\n                    \"rothera\",\n                    \"south_pole\",\n                    \"syowa\",\n                    \"vostok\"\n                ],\n                \"arctic\": [\n                    \"longyearbyen\"\n                ],\n                \"asia\": [\n                    \"aden\",\n                    \"almaty\",\n                    \"amman\",\n                    \"anadyr\",\n                    \"aqtau\",\n                    \"aqtobe\",\n                    \"ashgabat\",\n                    \"baghdad\",\n                    \"bahrain\",\n                    \"baku\",\n                    \"bangkok\",\n                    \"beirut\",\n                    \"bishkek\",\n                    \"brunei\",\n                    \"choibalsan\",\n                    \"chongqing\",\n                    \"colombo\",\n                    \"damascus\",\n                    \"dhaka\",\n                    \"dili\",\n                    \"dubai\",\n                    \"dushanbe\",\n                    \"gaza\",\n                    \"harbin\",\n                    \"hebron\",\n                    \"ho_chi_minh\",\n                    \"hong_kong\",\n                    \"hovd\",\n                    \"irkutsk\",\n                    \"jakarta\",\n                    \"jayapura\",\n                    \"jerusalem\",\n                    \"kabul\",\n                    \"kamchatka\",\n                    \"karachi\",\n                    \"kashgar\",\n                    \"kathmandu\",\n                    \"khandyga\",\n                    \"kolkata\",\n                    \"krasnoyarsk\",\n                    \"kuala_lumpur\",\n                    \"kuching\",\n                    \"kuwait\",\n                    \"macau\",\n                    \"magadan\",\n                    \"makassar\",\n                    \"manila\",\n                    \"muscat\",\n                    \"nicosia\",\n                    \"novokuznetsk\",\n                    \"novosibirsk\",\n                    \"omsk\",\n                    \"oral\",\n                    \"phnom_penh\",\n                    \"pontianak\",\n                    \"pyongyang\",\n                    \"qatar\",\n                    \"qyzylorda\",\n                    \"rangoon\",\n                    \"riyadh\",\n                    \"sakhalin\",\n                    \"samarkand\",\n                    \"seoul\",\n                    \"shanghai\",\n                    \"singapore\",\n                    \"taipei\",\n                    \"tashkent\",\n                    \"tbilisi\",\n                    \"tehran\",\n                    \"thimphu\",\n                    \"tokyo\",\n                    \"ulaanbaatar\",\n                    \"urumqi\",\n                    \"ust-nera\",\n                    \"vientiane\",\n                    \"vladivostok\",\n                    \"yakutsk\",\n                    \"yekaterinburg\",\n                    \"yerevan\"\n                ],\n                \"atlantic\": [\n                    \"azores\",\n                    \"bermuda\",\n                    \"canary\",\n                    \"cape_verde\",\n                    \"faroe\",\n                    \"madeira\",\n                    \"reykjavik\",\n                    \"south_georgia\",\n                    \"st_helena\",\n                    \"stanley\"\n                ],\n                \"australia\": [\n                    \"adelaide\",\n                    \"brisbane\",\n                    \"broken_hill\",\n                    \"currie\",\n                    \"darwin\",\n                    \"eucla\",\n                    \"hobart\",\n                    \"lindeman\",\n                    \"lord_howe\",\n                    \"melbourne\",\n                    \"perth\",\n                    \"sydney\"\n                ],\n                \"europe\": [\n                    \"amsterdam\",\n                    \"andorra\",\n                    \"athens\",\n                    \"belgrade\",\n                    \"berlin\",\n                    \"bratislava\",\n                    \"brussels\",\n                    \"bucharest\",\n                    \"budapest\",\n                    \"busingen\",\n                    \"chisinau\",\n                    \"copenhagen\",\n                    \"dublin\",\n                    \"gibraltar\",\n                    \"guernsey\",\n                    \"helsinki\",\n                    \"isle_of_man\",\n                    \"istanbul\",\n                    \"jersey\",\n                    \"kaliningrad\",\n                    \"kiev\",\n                    \"lisbon\",\n                    \"ljubljana\",\n                    \"london\",\n                    \"luxembourg\",\n                    \"madrid\",\n                    \"malta\",\n                    \"mariehamn\",\n                    \"minsk\",\n                    \"monaco\",\n                    \"moscow\",\n                    \"oslo\",\n                    \"paris\",\n                    \"podgorica\",\n                    \"prague\",\n                    \"riga\",\n                    \"rome\",\n                    \"samara\",\n                    \"san_marino\",\n                    \"sarajevo\",\n                    \"simferopol\",\n                    \"skopje\",\n                    \"sofia\",\n                    \"stockholm\",\n                    \"tallinn\",\n                    \"tirane\",\n                    \"uzhgorod\",\n                    \"vaduz\",\n                    \"vatican\",\n                    \"vienna\",\n                    \"vilnius\",\n                    \"volgograd\",\n                    \"warsaw\",\n                    \"zagreb\",\n                    \"zaporozhye\",\n                    \"zurich\"\n                ],\n                \"indian\": [\n                    \"antananarivo\",\n                    \"chagos\",\n                    \"christmas\",\n                    \"cocos\",\n                    \"comoro\",\n                    \"kerguelen\",\n                    \"mahe\",\n                    \"maldives\",\n                    \"mauritius\",\n                    \"mayotte\",\n                    \"reunion\"\n                ],\n                \"pacific\": [\n                    \"apia\",\n                    \"auckland\",\n                    \"chatham\",\n                    \"chuuk\",\n                    \"easter\",\n                    \"efate\",\n                    \"enderbury\",\n                    \"fakaofo\",\n                    \"fiji\",\n                    \"funafuti\",\n                    \"galapagos\",\n                    \"gambier\",\n                    \"guadalcanal\",\n                    \"guam\",\n                    \"honolulu\",\n                    \"johnston\",\n                    \"kiritimati\",\n                    \"kosrae\",\n                    \"kwajalein\",\n                    \"majuro\",\n                    \"marquesas\",\n                    \"midway\",\n                    \"nauru\",\n                    \"niue\",\n                    \"norfolk\",\n                    \"noumea\",\n                    \"pago_pago\",\n                    \"palau\",\n                    \"pitcairn\",\n                    \"pohnpei\",\n                    \"port_moresby\",\n                    \"rarotonga\",\n                    \"saipan\",\n                    \"tahiti\",\n                    \"tarawa\",\n                    \"tongatapu\",\n                    \"wake\",\n                    \"wallis\"\n                ],\n                \"utc\": [\n                    \"\"\n                ]\n            };\n\n            var tzparts= value.split('/',2);\n            var continent=tzparts[0].toLowerCase();\n            var city='';\n            if (tzparts[1]) {\n                city=tzparts[1].toLowerCase();\n            }\n\n            return (continent in timezones && ( timezones[continent].length===0 || timezones[continent].indexOf(city)!==-1))\n        }\n    }\n});\n", "/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Methods that implement Laravel Validations\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n$.extend(true, laravelValidation, {\n\n    methods:{\n\n        helpers: laravelValidation.helpers,\n\n        jsRemoteTimer:0,\n\n        /**\n         * \"Validate\" optional attributes.\n         * Always returns true, just lets us put sometimes in rules.\n         *\n         * @return {boolean}\n         */\n        Sometimes: function() {\n            return true;\n        },\n\n        /**\n         * Bail This is the default behaivour os JSValidation.\n         * Always returns true, just lets us put sometimes in rules.\n         *\n         * @return {boolean}\n         */\n        Bail: function() {\n            return true;\n        },\n\n        /**\n         * \"Indicate\" validation should pass if value is null.\n         * Always returns true, just lets us put \"nullable\" in rules.\n         *\n         * @return {boolean}\n         */\n        Nullable: function() {\n            return true;\n        },\n\n        /**\n         * Validate the given attribute is filled if it is present.\n         */\n        Filled: function(value, element) {\n            return $.validator.methods.required.call(this, value, element, true);\n        },\n\n\n        /**\n         * Validate that a required attribute exists.\n         */\n        Required: function(value, element) {\n            return  $.validator.methods.required.call(this, value, element);\n        },\n\n        /**\n         * Validate that an attribute exists when any other attribute exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWith: function(value, element, params) {\n            var validator=this,\n                required=false;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required=required || (\n                    target!==undefined &&\n                    $.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when all other attribute exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithAll: function(value, element, params) {\n            var validator=this,\n                required=true;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required && (\n                      target!==undefined &&\n                      $.validator.methods.required.call(\n                          validator,\n                          currentObject.elementValue(target),\n                          target,true\n                      ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when any other attribute does not exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithout: function(value, element, params) {\n            var validator=this,\n                required=false;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required ||\n                    target===undefined||\n                    !$.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    );\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when all other attribute does not exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithoutAll: function(value, element, params) {\n            var validator=this,\n                required=true,\n                currentObject=this;\n\n            $.each(params,function(i, param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required && (\n                    target===undefined ||\n                    !$.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when another attribute has a given value.\n         *\n         * @return {boolean}\n         */\n        RequiredIf: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                var val=String(this.elementValue(target));\n                if (typeof val !== 'undefined') {\n                    var data = params.slice(1);\n                    if ($.inArray(val, data) !== -1) {\n                        return $.validator.methods.required.call(\n                            this, value, element, true\n                        );\n                    }\n                }\n            }\n\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when another\n         * attribute does not have a given value.\n         *\n         * @return {boolean}\n         */\n        RequiredUnless: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                var val=String(this.elementValue(target));\n                if (typeof val !== 'undefined') {\n                    var data = params.slice(1);\n                    if ($.inArray(val, data) !== -1) {\n                        return true;\n                    }\n                }\n            }\n\n            return $.validator.methods.required.call(\n                this, value, element, true\n            );\n\n        },\n\n        /**\n         * Validate that an attribute has a matching confirmation.\n         *\n         * @return {boolean}\n         */\n        Confirmed: function(value, element, params) {\n            return laravelValidation.methods.Same.call(this,value, element, params);\n        },\n\n        /**\n         * Validate that two attributes match.\n         *\n         * @return {boolean}\n         */\n        Same: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                return String(value) === String(this.elementValue(target));\n            }\n            return false;\n        },\n\n        /**\n         * Validate that the values of an attribute is in another attribute.\n         *\n         * @param value\n         * @param element\n         * @param params\n         * @returns {boolean}\n         * @constructor\n         */\n        InArray: function (value, element, params) {\n            if (typeof params[0] === 'undefined') {\n                return false;\n            }\n            var elements = this.elements();\n            var found = false;\n            var nameRegExp = laravelValidation.helpers.regexFromWildcard(params[0]);\n\n            for ( var i = 0; i < elements.length ; i++ ) {\n                var targetName = elements[i].name;\n                if (targetName.match(nameRegExp)) {\n                    var equals = laravelValidation.methods.Same.call(this,value, element, [targetName]);\n                    found = found || equals;\n                }\n            }\n\n            return found;\n        },\n\n        /**\n         * Validate an attribute is unique among other values.\n         *\n         * @param value\n         * @param element\n         * @param params\n         * @returns {boolean}\n         */\n        Distinct: function (value, element, params) {\n            if (typeof params[0] === 'undefined') {\n                return false;\n            }\n\n            var elements = this.elements();\n            var found = false;\n            var nameRegExp = laravelValidation.helpers.regexFromWildcard(params[0]);\n\n            for ( var i = 0; i < elements.length ; i++ ) {\n                var targetName = elements[i].name;\n                if (targetName !== element.name && targetName.match(nameRegExp)) {\n                    var equals = laravelValidation.methods.Same.call(this,value, element, [targetName]);\n                    found = found || equals;\n                }\n            }\n\n            return !found;\n        },\n\n\n        /**\n         * Validate that an attribute is different from another attribute.\n         *\n         * @return {boolean}\n         */\n        Different: function(value, element, params) {\n            return ! laravelValidation.methods.Same.call(this,value, element, params);\n        },\n\n        /**\n         * Validate that an attribute was \"accepted\".\n         * This validation rule implies the attribute is \"required\".\n         *\n         * @return {boolean}\n         */\n        Accepted: function(value) {\n            var regex = new RegExp(\"^(?:(yes|on|1|true))$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is an array.\n         *\n         * @param value\n         * @param element\n         */\n        Array: function(value, element) {\n            if (element.name.indexOf('[') !== -1 && element.name.indexOf(']') !== -1) {\n                return true;\n            }\n\n            return laravelValidation.helpers.isArray(value);\n        },\n\n        /**\n         * Validate that an attribute is a boolean.\n         *\n         * @return {boolean}\n         */\n        Boolean: function(value) {\n            var regex= new RegExp(\"^(?:(true|false|1|0))$\",'i');\n            return  regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is an integer.\n         *\n         * @return {boolean}\n         */\n        Integer: function(value) {\n            var regex= new RegExp(\"^(?:-?\\\\d+)$\",'i');\n            return  regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is numeric.\n         */\n        Numeric: function(value, element) {\n            return $.validator.methods.number.call(this, value, element, true);\n        },\n\n        /**\n         * Validate that an attribute is a string.\n         *\n         * @return {boolean}\n         */\n        String: function(value) {\n            return typeof value === 'string';\n        },\n\n        /**\n         * The field under validation must be numeric and must have an exact length of value.\n         */\n        Digits: function(value, element, params) {\n            return (\n                $.validator.methods.number.call(this, value, element, true) &&\n                value.length === parseInt(params, 10)\n            );\n        },\n\n        /**\n         * The field under validation must have a length between the given min and max.\n         */\n        DigitsBetween: function(value, element, params) {\n            return ($.validator.methods.number.call(this, value, element, true)\n                && value.length>=parseFloat(params[0]) && value.length<=parseFloat(params[1]));\n        },\n\n        /**\n         * Validate the size of an attribute.\n         *\n         * @return {boolean}\n         */\n        Size: function(value, element, params) {\n            return laravelValidation.helpers.getSize(this, element,value) === parseFloat(params[0]);\n        },\n\n        /**\n         * Validate the size of an attribute is between a set of values.\n         *\n         * @return {boolean}\n         */\n        Between: function(value, element, params) {\n            return ( laravelValidation.helpers.getSize(this, element,value) >= parseFloat(params[0]) &&\n                laravelValidation.helpers.getSize(this,element,value) <= parseFloat(params[1]));\n        },\n\n        /**\n         * Validate the size of an attribute is greater than a minimum value.\n         *\n         * @return {boolean}\n         */\n        Min: function(value, element, params) {\n            value = laravelValidation.helpers.allElementValues(this, element);\n\n            return laravelValidation.helpers.getSize(this, element, value) >= parseFloat(params[0]);\n        },\n\n        /**\n         * Validate the size of an attribute is less than a maximum value.\n         *\n         * @return {boolean}\n         */\n        Max: function(value, element, params) {\n            value = laravelValidation.helpers.allElementValues(this, element);\n\n            return laravelValidation.helpers.getSize(this, element, value) <= parseFloat(params[0]);\n        },\n\n        /**\n         * Validate an attribute is contained within a list of values.\n         *\n         * @return {boolean}\n         */\n        In: function(value, element, params) {\n            if (laravelValidation.helpers.isArray(value)\n                && laravelValidation.helpers.hasRules(element, \"Array\")\n            ) {\n                var diff = laravelValidation.helpers.arrayDiff(value, params);\n\n                return Object.keys(diff).length === 0;\n            }\n\n            return params.indexOf(value.toString()) !== -1;\n        },\n\n        /**\n         * Validate an attribute is not contained within a list of values.\n         *\n         * @return {boolean}\n         */\n        NotIn: function(value, element, params) {\n            return params.indexOf(value.toString()) === -1;\n        },\n\n        /**\n         * Validate that an attribute is a valid IP.\n         *\n         * @return {boolean}\n         */\n        Ip: function(value) {\n            return /^(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/i.test(value) ||\n                /^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(value);\n        },\n\n        /**\n         * Validate that an attribute is a valid e-mail address.\n         */\n        Email: function(value, element) {\n            return $.validator.methods.email.call(this, value, element, true);\n        },\n\n        /**\n         * Validate that an attribute is a valid URL.\n         */\n        Url: function(value, element) {\n            return $.validator.methods.url.call(this, value, element, true);\n        },\n\n        /**\n         * The field under validation must be a successfully uploaded file.\n         *\n         * @return {boolean}\n         */\n        File: function(value, element) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            if ('files' in element ) {\n                return (element.files.length > 0);\n            }\n            return false;\n        },\n\n        /**\n         * Validate the MIME type of a file upload attribute is in a set of MIME types.\n         *\n         * @return {boolean}\n         */\n        Mimes: function(value, element, params) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            var lowerParams = $.map(params, function(item) {\n                return item.toLowerCase();\n            });\n\n            var fileinfo = laravelValidation.helpers.fileinfo(element);\n            return (fileinfo !== false && lowerParams.indexOf(fileinfo.extension.toLowerCase())!==-1);\n        },\n\n        /**\n         * The file under validation must match one of the given MIME types.\n         *\n         * @return {boolean}\n         */\n        Mimetypes: function(value, element, params) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            var lowerParams = $.map(params, function(item) {\n                return item.toLowerCase();\n            });\n\n            var fileinfo = laravelValidation.helpers.fileinfo(element);\n\n            if (fileinfo === false) {\n                return false;\n            }\n            return (lowerParams.indexOf(fileinfo.type.toLowerCase())!==-1);\n        },\n\n        /**\n         * Validate the MIME type of a file upload attribute is in a set of MIME types.\n         */\n        Image: function(value, element) {\n            return laravelValidation.methods.Mimes.call(this, value, element, [\n                'jpg', 'png', 'gif', 'bmp', 'svg', 'jpeg'\n            ]);\n        },\n\n        /**\n         * Validate dimensions of Image.\n         *\n         * @return {boolean|string}\n         */\n        Dimensions: function(value, element, params, callback) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            if (element.files === null || typeof element.files[0] === 'undefined') {\n                return false;\n            }\n\n            var fr = new FileReader;\n            fr.onload = function () {\n                var img = new Image();\n                img.onload = function () {\n                    var height = parseFloat(img.naturalHeight);\n                    var width = parseFloat(img.naturalWidth);\n                    var ratio = width / height;\n                    var notValid = ((params['width']) && parseFloat(params['width'] !== width)) ||\n                        ((params['min_width']) && parseFloat(params['min_width']) > width) ||\n                        ((params['max_width']) && parseFloat(params['max_width']) < width) ||\n                        ((params['height']) && parseFloat(params['height']) !== height) ||\n                        ((params['min_height']) && parseFloat(params['min_height']) > height) ||\n                        ((params['max_height']) && parseFloat(params['max_height']) < height) ||\n                        ((params['ratio']) && ratio !== parseFloat(eval(params['ratio']))\n                        );\n                    callback(! notValid);\n                };\n                img.onerror = function() {\n                    callback(false);\n                };\n                img.src = fr.result;\n            };\n            fr.readAsDataURL(element.files[0]);\n\n            return 'pending';\n        },\n\n        /**\n         * Validate that an attribute contains only alphabetic characters.\n         *\n         * @return {boolean}\n         */\n        Alpha: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n\n            var regex = new RegExp(\"^(?:^[a-z\\u00E0-\\u00FC]+$)$\",'i');\n            return  regex.test(value);\n\n        },\n\n        /**\n         * Validate that an attribute contains only alpha-numeric characters.\n         *\n         * @return {boolean}\n         */\n        AlphaNum: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n            var regex = new RegExp(\"^(?:^[a-z0-9\\u00E0-\\u00FC]+$)$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute contains only alphabetic characters.\n         *\n         * @return {boolean}\n         */\n        AlphaDash: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n            var regex = new RegExp(\"^(?:^[a-z0-9\\u00E0-\\u00FC_-]+$)$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute passes a regular expression check.\n         *\n         * @return {boolean}\n         */\n        Regex: function(value, element, params) {\n            var invalidModifiers=['x','s','u','X','U','A'];\n            // Converting php regular expression\n            var phpReg= new RegExp('^(?:\\/)(.*\\\\\\/?[^\\/]*|[^\\/]*)(?:\\/)([gmixXsuUAJ]*)?$');\n            var matches=params[0].match(phpReg);\n            if (matches === null) {\n                return false;\n            }\n            // checking modifiers\n            var php_modifiers=[];\n            if (matches[2]!==undefined) {\n                php_modifiers=matches[2].split('');\n                for (var i=0; i<php_modifiers.length<i ;i++) {\n                    if (invalidModifiers.indexOf(php_modifiers[i])!==-1) {\n                        return true;\n                    }\n                }\n            }\n            var regex = new RegExp(\"^(?:\"+matches[1]+\")$\",php_modifiers.join());\n            return   regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is a valid date.\n         *\n         * @return {boolean}\n         */\n        Date: function(value) {\n            return (laravelValidation.helpers.strtotime(value)!==false);\n        },\n\n        /**\n         * Validate that an attribute matches a date format.\n         *\n         * @return {boolean}\n         */\n        DateFormat: function(value, element, params) {\n            return laravelValidation.helpers.parseTime(value,params[0])!==false;\n        },\n\n        /**\n         * Validate the date is before a given date.\n         *\n         * @return {boolean}\n         */\n        Before: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '<');\n        },\n\n        /**\n         * Validate the date is equal or before a given date.\n         *\n         * @return {boolean}\n         */\n        BeforeOrEqual: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '<=');\n        },\n\n        /**\n         * Validate the date is after a given date.\n         *\n         * @return {boolean}\n         */\n        After: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '>');\n        },\n\n        /**\n         * Validate the date is equal or after a given date.\n         *\n         * @return {boolean}\n         */\n        AfterOrEqual: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '>=');\n        },\n\n\n        /**\n         * Validate that an attribute is a valid date.\n         */\n        Timezone: function(value) {\n            return  laravelValidation.helpers.isTimezone(value);\n        },\n\n\n        /**\n         * Validate the attribute is a valid JSON string.\n         *\n         * @param  value\n         * @return bool\n         */\n        Json: function(value) {\n            var result = true;\n            try {\n                JSON.parse(value);\n            } catch (e) {\n                result = false;\n            }\n            return result;\n        },\n\n        /**\n         * Noop (always returns true).\n         *\n         * @param value\n         * @returns {boolean}\n         */\n        ProengsoftNoop: function (value) {\n            return true;\n        },\n    }\n});\n"]}