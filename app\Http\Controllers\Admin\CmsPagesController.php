<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\CmsRequest;
use App\Repositories\CmsPageRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class CmsPagesController extends Controller
{
    /**
     * @var CmsPageRepository
     */
    protected $cmsRepository;

    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "cms";

    /**
     * CmsPagesController constructor.
     *
     * @param CmsPageRepository $cmsRepository
     */
    public function __construct(CmsPageRepositoryEloquent $cmsRepository)
    {
        $this->cmsRepository = $cmsRepository;
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.settings.cmspages.index');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function getList(Request $request)
    {
        try {
            return $this->cmsRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create()
    {
        return view('admin.settings.cmspages.cmspage');
    }

    /**
     * @param CmsPageRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CmsRequest $request){
        try {
            $this->cmsRepository->store($request);
            $request->session()->flash('success', config("messages.cmspage_created"));
            return redirect()->route('admin.cms.index');
        } catch (\Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit CMS Page']);
        try {
            $cms = $this->cmsRepository->edit($request, $id);
            return view('admin.settings.cmspages.cmspage', compact('cms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function viewPrivacyPolicy(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Privacy Policy']);
        try {
            $cms = $this->cmsRepository->viewPrivacyPolicy($request);
            return view('admin.settings.cmspages.view', compact('cms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function viewDeleteAccountInstruction(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Delete Account Instruction']);
        try {
            $cms = $this->cmsRepository->viewDeleteAccountInstruction($request);
            return view('admin.settings.cmspages.view', compact('cms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function viewTermsAndCondition(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Terms and Condition']);
        try {
            $cms = $this->cmsRepository->viewTermsAndCondition($request);
            return view('admin.settings.cmspages.view', compact('cms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function viewAboutUs(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'About Us']);
        try {
            $cms = $this->cmsRepository->viewAboutUs($request);
            return view('admin.settings.cmspages.view', compact('cms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CmsRequest $request, $id)
    {
        try {
             $this->cmsRepository->update($request, $id);
            $request->session()->flash('success', config("messages.cmspage_updated"));
            return redirect()->route('admin.cms.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->cmsRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->cmsRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->cmsRepository->destroy($request);
            $request->session()->flash('success', config("messages.cmspage_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.cms.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.cms.index');
        }
    }

}
