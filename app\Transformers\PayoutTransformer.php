<?php

namespace App\Transformers;

use League\Fractal\TransformerAbstract;
use App\Models\DoctorDetail;

class PayoutTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorDetail $doctor)
    {
        return [
            "id" => $doctor->id,
            'wallet_balance' => $doctor->wallet_balance,
            "created_at" => $doctor->created_at,
            'user' => $doctor->user
        ];
    }
}
