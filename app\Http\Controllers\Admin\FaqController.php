<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\FaqRequest;
use App\Repositories\FaqRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "faq";

    /**
     * @var FaqRepositoryEloquent|string
     */
    public $faqRepository = "";

    public function __construct(FaqRepositoryEloquent $faqRepository)
    {
        parent::__construct();
        $this->faqRepository = $faqRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Faq']);
        return view('admin.settings.faqs.index');
    }

    /**
     * Return faq list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->faqRepository->getList($request);
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Speciality']);
        return view('admin.settings.faqs.faq');
    }

    /**
     * @param FaqRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(FaqRequest $request)
    {
        try {
            $faq = $this->faqRepository->store($request);
            $request->session()->flash('success', config("messages.faq_created"));
            return redirect()->route('admin.faq.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit faq list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Patient']);
        try {
            $faq = $this->faqRepository->edit($request, $id);
            return view('admin.settings.faqs.faq', compact('faq'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.faq.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * Update faq list.
     *
     * @param FaqRequest $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(FaqRequest $request, $id)
    {
        try {
            $faq = $this->faqRepository->update($request, $id);
            $request->session()->flash('success', config("messages.faq_updated"));
            return redirect()->route('admin.faq.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.faq.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->faqRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->faqRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->faqRepository->destroy($request);
            $request->session()->flash('success', config("messages.faq_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.faq.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * @return Response
     */
    public static function export()
    {
        return Excel::download(new PatientExport, 'faq.xlsx');
    }
}
