/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!************************************!*\
  !*** ./resources/assets/js/log.js ***!
  \************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#log_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[5, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#log_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "user_id",
        name: "user_id"
      }, {
        data: "transaction_id",
        name: "transaction_id"
      }, {
        data: "req_log",
        name: "req_log"
      }, {
        data: "res_log",
        name: "res_log"
      }, {
        data: "log_type",
        name: "log_type"
      }, {
        data: "created_date",
        name: "created_date",
        searchable: false
      }],
      columnDefs: [{
        targets: 1,
        render: function render(data, type, row) {
          return "\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">".concat(row.user_id, "</a>\n                        </div>");
        }
      }]
    });
    table = datatable.$;
    $('#log_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
    var selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    filterButton.addEventListener("click", function () {
      var filterString = "";

      // Get filter values
      selectOptions.forEach(function (item, index) {
        if (item.value && item.value !== "") {
          if (index == 0) {
            datatable.column(4).search(item.value).draw();
          }
          if (index == 1) {
            datatable.column(3).search(item.value).draw();
          }
        } else {
          if (index == 0) {
            datatable.column(4).search("").draw();
          }
          if (index == 1) {
            datatable.column(3).search("").draw();
          }
        }
      });
    });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

    // Reset datatable
    resetButton.addEventListener("click", function () {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
      selectOptions.forEach(function (select) {
        $(select).val("").trigger("change");
      });

      // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
      datatable.search("").columns().search("").draw();
    });
  };

  // Public methods
  return {
    init: function init() {
      initDatatable();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
/******/ })()
;