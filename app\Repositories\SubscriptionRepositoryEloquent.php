<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\SubscriptionRepository;
use App\Entities\Subscription;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Models\DoctorSubscription;
use App\Models\Subscription as ModelsSubscription;
use App\Transformers\UserSubscriptionTransformer;
use App\Validators\SubscriptionValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Jobs\SendEmails​;
use App\Models\EmailTemplate;

/**
 * Class SubscriptionRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class SubscriptionRepositoryEloquent extends BaseRepository implements SubscriptionRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Subscription::class;
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $subscription = ModelsSubscription::query();

        if (!empty($request->start_date)) {
            $subscription = $subscription->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($subscription)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.subscription.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.subscription.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.subscription.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.subscription.view', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            View
                        </a>
                    </div>

                </div>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function userSubscriptionGetList($request, $id)
    {
        $subscription = DoctorSubscription::with('doctor', 'subscription', 'transaction')->whereHas('transaction')->where('subscription_id', $id);

        if (!empty($request->start_date)) {
            $subscription = $subscription->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($subscription)
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();
        $data['slug'] = str_replace(' ', '_', strtolower($request->name));

        return ModelsSubscription::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $subscription = ModelsSubscription::findOrFail($id);

        return $subscription;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $subscription = ModelsSubscription::findOrFail($id);

        $data = $request->all();
        return $subscription->update($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function view($request, $id)
    {
        $subscription = ModelsSubscription::with('subscriptionDoctor')->findOrFail($id);

        return $subscription;
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = ModelsSubscription::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = ModelsSubscription::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = ModelsSubscription::findOrFail($request->input('id'));
        $patient->delete();
    }

    /**
     * @param $request
     */
    public function viewSubscription($request)
    {
        $subscription = DoctorSubscription::with('subscription')->where('user_id', auth()->user()->id)->orderBy('created_at', 'DESC')->first();
        return fractal($subscription, new UserSubscriptionTransformer())->serializeWith(new CustomArraySerializer());
    }

    public function  subscriptionComplete($request){
        $subscription = DoctorSubscription::with('subscription')->where('subscription_id', $request->subscription_id)->orderBy('created_at', 'DESC')->first();
        $subscription->update([
            'transaction_id' => $request->transaction_id
        ]);

        //Send Subscription enrolled email notification to Patient
        $emailTemplate = EmailTemplate::where('slug', 'subscription_enrolled_doctor')->first();
        $emailBody = $emailTemplate->getTranslation('description', $subscription->doctor->language);
        $email_content = str_replace("[[USERNAME]]", ucwords($subscription->doctor->name), $emailBody);
        $email_content = str_replace("[[SUBSCRIPTION_ID]]", $subscription->transaction->transaction_id, $email_content);
        $email_content = str_replace("[[PLAN_NAME]]", $subscription->subscription->name, $email_content);
        $email_content = str_replace("[[PLAN_PRICE]]", $subscription->subscription->price, $email_content);
        $email_content = str_replace("[[START_DATE]]", $subscription->subscription_start_date, $email_content);
        $email_content = str_replace("[[END_DATE]]", $subscription->subscription_end_date, $email_content);
        $email_content = str_replace("[[PLAN_DURATION]]", $subscription->subscription->validity.' Days', $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);


        $doctor = [
            'email' => $subscription->doctor->email,
            'email_body' => $email_content,
            'subject' => $emailTemplate->getTranslation('title', $subscription->doctor->language)
        ];
        dispatch((new SendEmails​($doctor)));
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
