<?php

namespace App\Transformers;

use App\Models\Symptom;
use League\Fractal\TransformerAbstract;

class SymptomTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Symptom $symptom)
    {
        return [
            'id' => $symptom->id,
            'image' => $symptom->image_url ?? '',
            'symptoms_name' => $symptom->symptoms_name
        ];
    }
}
