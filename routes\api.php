<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('decrypt', function (Request $request) {
    dd(encrypt($request->data));
});


Route::group(['namespace' => 'Api', 'prefix' => 'v1', 'middleware' => ['localization']], function () {

    //User Login and Registration API
    Route::controller('UserController')->group(function () {
        Route::post('check-user', 'checkUser')->name('check-user');
        Route::post('social_auth', 'socialAuth')->name('social-auth');
        Route::post('register', 'register')->middleware('throttle:30,1')->name('register');
        Route::post('login', 'login')->middleware('throttle:30,1')->name('login');
        Route::post('verify/email_otp_code', 'verifyOtpCode');
        Route::post('verify/mobile_otp_code', 'verifyTwilioOtp');
        Route::post('send/email_otp_code', 'sendOtpVerificationCode');
        Route::post('send/mobile_otp_code', 'sendTwilioMobileOtp');
        Route::post('forgot-passwords', 'forgotPassword');
    });

    //Doctor Registration more details
    Route::controller('DoctorDetailController')->prefix('doctor')->group(function () {
        Route::post('view-speciality', 'viewDoctorSpeciality');
        Route::post('update-speciality', 'updateDoctorSpeciality');
        Route::post('update-education-experience', 'updateEducationWithExperience');
        Route::post('update-consultation-type', 'updateConsultationType');
        Route::post('update-clinic-detail', 'updateClinicDetail');
        Route::post('delete-education-document', 'deleteEducationDocument');
    });

    //forgot password api
    Route::controller('ForgotPasswordController')->group(function () {
        Route::post('forgot-password', 'sendResetLinkEmail');
        Route::post('reset-password', 'resetPassword')->name('password.reset');
    });

    //view medical history api
    Route::get('view-medical-history', 'PatientDetailController@viewMedicalHistory');

    //chat upload
    Route::post('upload-media', 'UserController@uploadMedia');

    //invite-validate
    Route::controller('DoctorInvitationController')->group(function () {
        Route::post('doctor/wish-list', 'wishList');
        Route::post('doctor/invite-validate', 'inviteValidate');
    });

    //CMS Page (terms & condition, privacy policy, about us, FAQ)
    Route::controller('CmsPagesController')->group(function () {
        Route::get('cms/{slug}', 'getPage');
    });

    //After Auth Api
    Route::group(['middleware' => ['forceJson', 'auth:api', 'check.status']], function () {

        //basic update profile and logout apis
        Route::controller('UserController')->group(function () {
            Route::post('logout', 'logout');
            Route::post('update-profile', 'updateProfile');
            Route::post('change-password', 'changePassword');
            Route::post('delete-account', 'deleteAccount');

            //chat notification
            Route::post('chat-notifications', 'chatNotification');
        });

        //CMS Page (terms & condition, privacy policy, about us, FAQ)
        Route::controller('CmsPagesController')->group(function () {
            Route::get('faq', 'getFaq');
        });

        //Agora Token generation
        Route::post('agora-token', 'AppointmentCallController@callNotification');
        Route::post('update-call-status', 'AppointmentCallController@updateStatus');

        // Patient module API
        Route::group(['prefix' => 'patient'], function () {

            //Update and view patient api
            Route::controller('PatientDetailController')->group(function () {
                Route::post('update-profile', 'updatePatientProfile');
                Route::post('view-profile', 'viewPatientProfile');
                Route::post('home-screen', 'homeScreen');
                Route::post('home-search', 'homeSearch');
            });

            Route::controller('SupportController')->group(function () {
                Route::post('support', 'store');
                Route::get('patient-support-list', 'patientSupportList');
                Route::post('mark-resolve/{id}', 'markResolve');

                Route::post('support-chat', 'createSupportChat');
                Route::get('support-chat-list/{id}', 'supportChatList');
            });

            //search doctor with speciality and symptoms
            Route::post('view-speciality', 'SpecialityController@viewSpeciality');
            Route::post('view-symptom', 'SymptomController@viewSymptom');

            Route::controller('DoctorDetailController')->group(function () {
                Route::post('view-specialist-doctor', 'viewSpecialistDoctor');
                Route::post('doctor-about/{id}', 'doctorAbout');
            });

            Route::controller('DoctorAppointmentController')->group(function () {
                //book appointment
                Route::post('time-slot', 'getTimeSlot');
                Route::post('add-appointment', 'createAppointment');
                Route::get('booking-summery', 'getBookingSummery');
                Route::post('silent-notification', 'silentNotification');

                Route::post('update-appointment', 'updateAppointment');

                //My booking api
                Route::get('my-booking/{name}', 'myBooking')->where('name', 'upcoming|ongoing|completed');

                Route::post('appointment-detail', 'appointmentDetail');

                //call-list
                Route::post('call-list', 'callList');
            });

            //Eco bank payment
            Route::post('card-payment', 'EcoBankController@cardPayment');

            Route::controller('TransactionController')->group(function () {
                Route::post('transaction-history', 'transactionHistory');
                Route::post('check-payment-status', 'checkPaymentStatus');
                Route::post('update-payment-status', 'updatePaymentStatus');
            });

            Route::controller('DoctorRatingController')->group(function () {
                // Doctor Rating
                Route::post('add-rating', 'createRating');
                Route::post('edit-rating/{id}', 'editRating');
                Route::post('delete-rating/{id}', 'deleteRating');
                Route::post('view-rating/{id}', 'viewRating');
                Route::post('my-rating', 'myRating');
                Route::post('detail-rating', 'detailRating');
            });

            // Doctor Prescriptions
            Route::controller('PrescriptionController')->group(function () {
                Route::post('prescriptions', 'getPrescriptions');
                Route::get('view-prescription/{id}', 'viewPrescription');
            });

            // Patient Notes
            Route::controller('PatientNoteController')->group(function () {
                Route::post('view-notes', 'viewNotes');
            });

            // Patient LabTest
            Route::controller('PatientLabTestController')->group(function () {
                Route::post('labtests', 'getPatientLabtests');
                Route::get('view-labtest/{id}', 'viewPatientLabTest');
                Route::post('upload-reports', 'uploadReports');
                Route::post('view-reports', 'viewReports');
            });

            //Notifications
            Route::get('view-notifications', 'NotificationController@viewNotifications');
        });


        //Doctor Module API
        Route::group(['prefix' => 'doctor'], function () {

            //update and view doctor api
            Route::controller('DoctorDetailController')->group(function () {
                Route::post('update-specialities', 'updateDoctorSpeciality');
                Route::post('update-education-experiences', 'updateEducationWithExperience');
                Route::post('update-consultation-types', 'updateConsultationType');
                Route::post('update-clinic-details', 'updateClinicDetail');
                Route::post('update-signature', 'updateSignature');
                Route::post('view-profile', 'viewDoctorProfile');
                Route::post('update-doctor-type', 'updateDoctorType');
                Route::get('check-clinic-updates', 'checkClinicUpdates');
                Route::post('update-status', 'updateStatus');
            });

            //Doctor Availability/Unavailability api
            Route::controller('DoctorAvailabilityController')->group(function () {
                Route::post('doctor-availability', 'create');
                Route::get('view-doctor-availability', 'view');
                Route::post('delete-doctor-availability', 'delete');
            });

            Route::controller('DoctorUnavailabilityController')->group(function () {
                Route::post('doctor-unavailability', 'create');
                Route::get('view-doctor-unavailability', 'view');
                Route::post('delete-unavailability', 'delete');
            });

            // Doctor Prescriptions
            Route::controller('PrescriptionController')->group(function () {
                Route::post('medicine-list', 'medicineList');
                Route::post('add-medicine', 'addMedicine');
                Route::post('add-prescription', 'addPrescription');
                Route::post('prescriptions', 'getPrescriptions');
                Route::get('view-prescription/{id}', 'viewPrescription');
            });

            // Doctor LabTest
            Route::controller('PatientLabTestController')->group(function () {
                Route::post('lab-test-list', 'labTestList');
                Route::post('add-laboratory', 'addLaboratory');
                Route::post('add-labtest', 'addPatientLabTest');
                Route::post('labtests', 'getPatientLabtests');
                Route::get('view-labtest/{id}', 'viewPatientLabTest');
                Route::post('view-reports', 'viewReports');
            });

            // Doctor Notes
            Route::controller('PatientNoteController')->group(function () {
                Route::post('add-notes', 'addNotes');
                Route::post('view-notes', 'viewNotes');
            });

            // Doctor Rating
            Route::controller('DoctorRatingController')->group(function () {
                Route::post('doctor-view-rating', 'doctorViewRating');
            });

            //get booking detail api and update appointment call status api
            Route::controller('DoctorAppointmentController')->group(function () {
                Route::get('doctor-booking/{name}', 'doctorBooking')->where('name', 'upcoming|ongoing|completed');
                Route::post('update-ongoing', 'updateOngoing');
                Route::post('update-completed', 'updateCompleted');

                Route::post('appointment-detail', 'appointmentDetail');

                Route::post('update-appointment', 'updateAppointment');

                //doctor home screen
                Route::post('home-screen', 'todayAppointment');

                //call-list
                Route::post('call-list', 'callList');
            });

            Route::controller('SupportController')->group(function () {
                Route::post('support', 'store');
                Route::get('patient-doctor-support-list', 'patientDoctorSupportList');
                Route::get('doctor-support-list', 'doctorSupportList');
                Route::post('mark-resolve/{id}', 'markResolve');

                Route::post('support-chat', 'createSupportChat');
                Route::get('support-chat-list/{id}', 'supportChatList');
            });

            //Doctor Transaction module
            Route::controller('TransactionController')->group(function () {
                Route::post('wallet-history', 'walletHistory');
            });

            //Doctor Invitation
            Route::controller('DoctorInvitationController')->group(function () {
                Route::post('refer', 'refer');
            });

            //Doctor bank
            Route::controller('BankDetailController')->group(function () {
                Route::post('bank-detail', 'addBankDetail');
                Route::get('view-bank-detail', 'viewBankDetail');
            });

            //Notifications
            Route::get('view-notifications', 'NotificationController@viewNotifications');

            //Subscription
            Route::get('view-subscriptions', 'SubscriptionController@viewSubscription');
        });
    });
});
