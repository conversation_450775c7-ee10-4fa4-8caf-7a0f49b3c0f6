<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDoctorClinicsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('doctor_clinics', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->string('clinic_name');
            $table->string('apartment_no',200)->nullable();
            $table->string('clinic_address')->nullable();
            $table->string('clinic_landmark')->nullable();
            $table->string('clinic_city')->nullable();
            $table->string('clinic_state')->nullable();
            $table->bigInteger('country')->nullable();
            $table->bigInteger('clinic_dial_code')->nullable();
            $table->bigInteger('clinic_mobile')->nullable();
            $table->string('clinic_open_time')->nullable();
            $table->string('clinic_close_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('doctor_clinics');
    }
}
