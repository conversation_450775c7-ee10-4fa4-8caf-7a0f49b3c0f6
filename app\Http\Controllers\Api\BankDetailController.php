<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\BankDetailRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\EcoBankRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class BankDetailController extends Controller
{
    /**
     * @var EcoBankRepositoryEloquent|string
     */
    public $ecoBankRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param EcoBankRepositoryEloquent $ecoBankRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(EcoBankRepositoryEloquent $ecoBankRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->ecoBankRepositoryEloquent = $ecoBankRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addBankDetail(BankDetailRequest $request)
    {
        try {
            $bankDetail = $this->ecoBankRepositoryEloquent->addBankDetail($request);
            return $this->apiResponse->respondWithMessageAndPayload($bankDetail, trans('messages.bank_detail_add_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewBankDetail(Request $request)
    {
        try {
            $bankDetail = $this->ecoBankRepositoryEloquent->viewBankDetail($request);
            return $this->apiResponse->respondWithMessageAndPayload($bankDetail, trans('messages.bank_detail_fetch_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
