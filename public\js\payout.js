/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!********************************************!*\
  !*** ./resources/assets/js/payout.js ***!
  \********************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#payout_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[2, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#payout_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "doctor_name",
        name: "doctor_name",
        orderable: false
      }, {
        data: "wallet_balance",
        name: "wallet_balance",
        orderable: false
      }, {
        data: "action",
        name: "action",
        searchable: false,
        orderable: false
      }],
      columnDefs: [{
        targets: 0,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
            return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.avatar_url, "\" alt=\"").concat(row.name, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.name, "</a>\n                            <span>").concat(row.username, "</span>\n                        </div>");
        }
      }, {
        targets: 1,
        render: function render(data, type, row) {
          return "".concat(row.doctor_detail.wallet_balance).concat(" $");
        }
      }]
    });
    table = datatable.$;
    $('#payout_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };


  $('input[name="search"]').on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
    var start_date = picker.startDate.format("YYYY/MM/DD");
    var end_date = picker.endDate.format("YYYY/MM/DD");
    $("#payout_table").data("dt_params", {
      start_date: start_date,
      end_date: end_date
    });
    datatable.draw();
  });
  $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#payout_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });

  // Public methods
  return {
    init: function init() {
      initDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#payout_table").DataTable().ajax.reload();
    }
  });
});
/******/ })()
;