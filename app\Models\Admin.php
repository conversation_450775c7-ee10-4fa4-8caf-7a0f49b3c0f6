<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Admin extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $guard = 'admin';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'country_code',
        'dial_code',
        'mobile',
        'avatar',
        'status',
    ];

    protected $appends = [
        'avatar_url', 'name'
    ];


    /**
     * Get avatar link
     *
     * @return string
     */
    public function getAvatarUrlAttribute()
    {
        if (!empty($this->attributes['avatar'])) {
            return $this->attributes['avatar'];
        } else {
            return asset('assets/media/avatars/blank.png');
        }
    }

    /**
     * Get avatar link
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

}
