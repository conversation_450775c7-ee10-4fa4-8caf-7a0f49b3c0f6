<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorUnavailabilityRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class DoctorUnavailabilityController extends Controller
{
    /**
     * @var DoctorAvailabilityRepositoryEloquent|string
     */
    public $doctorUnavailabilityRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Doctor availability Controller constructor.
     * @param DoctorAvailabilityRepositoryEloquent $doctorUnavailabilityRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorUnavailabilityRepositoryEloquent $doctorUnavailabilityRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->doctorUnavailabilityRepository = $doctorUnavailabilityRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function create(Request $request)
    {
        try {
            $this->doctorUnavailabilityRepository->create($request);
            return $this->apiResponse->respondWithMessage(trans('messages.unavailability_created'));
        }  catch (Exception $exception) {
            if($exception->getCode() == '409'){
                return $this->apiResponse->respondResourceConflict($exception->getMessage());
            }else{
                return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function view(Request $request)
    {
        try {
            $unavailability = $this->doctorUnavailabilityRepository->view($request);
            return $this->apiResponse->respondWithMessageAndPayload($unavailability, trans('messages.unavailability_fetching'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function delete(Request $request)
    {
        try {
            $this->doctorUnavailabilityRepository->delete($request);
            return $this->apiResponse->respondWithMessage(trans('messages.unavailability_delete'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
