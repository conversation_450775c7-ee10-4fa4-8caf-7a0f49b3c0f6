<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="{{asset('assets/plugins/global/plugins.bundle.js')}}" type="text/javascript"></script>
<script src="{{asset('assets/js/scripts.bundle.js')}}" type="text/javascript"></script>
<!--end::Global Javascript Bundle-->

<!--begin::Custom Javascript(used for this page only)-->
<script src="{{asset('assets/js/custom/authentication/sign-in/general.js')}}"></script>
<!--end::Custom Javascript-->

<!--begin::Vendors Javascript(used for this page only)-->
<script src="{{asset('assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>
<!--end::Vendors Javascript-->
<!--begin::Custom Javascript(used for this page only)-->
<script src="{{asset('assets/js/custom/account/settings/signin-methods.js')}}"></script>
<script src="{{asset('assets/js/custom/account/settings/profile-details.js')}}"></script>
<script src="{{asset('assets/js/custom/account/settings/deactivate-account.js')}}"></script>
<script src="{{asset('assets/js/custom/pages/user-profile/general.js')}}"></script>
<script src="{{asset('assets/js/widgets.bundle.js')}}"></script>
<script src="{{asset('assets/js/custom/widgets.js')}}"></script>
<script src="{{asset('assets/js/custom/apps/chat/chat.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/upgrade-plan.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/create-app.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/offer-a-deal/type.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/offer-a-deal/details.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/offer-a-deal/finance.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/offer-a-deal/complete.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/offer-a-deal/main.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/two-factor-authentication.js')}}"></script>
<script src="{{asset('assets/js/custom/utilities/modals/users-search.js')}}"></script>
<!--end::Custom Javascript-->
<!--end::Javascript-->
<!--begin::Page Scripts(used by this page) -->
<script src="{{ asset('assets/plugins/toastr/build/toastr.min.js') }}" type="text/javascript"></script>

<!-- begin::Global Config(global config for global JS sciprts) -->
<script>

$(window).on("load",function(){
    $('.preloader').delay(1000).fadeOut();;
});

toastr.options = {
  "closeButton": false,
  "debug": false,
  "newestOnTop": false,
  "progressBar": false,
  "positionClass": "toast-top-right",
  "preventDuplicates": false,
  "onclick": null,
  "showDuration": "300",
  "hideDuration": "1000",
  "timeOut": "5000",
  "extendedTimeOut": "1000",
  "showEasing": "swing",
  "hideEasing": "linear",
  "showMethod": "fadeIn",
  "hideMethod": "fadeOut"
}


    @if (Session::has('success'))
    toastr.success("{{ Session::get('success') }}");
    @endif

    @if (Session::has('error'))
    toastr.error("{{ Session::get('error') }}");
    @endif

</script>

@stack('scripts')

@yield('js')
