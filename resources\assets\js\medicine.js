"use strict";

// Class definition
var KTDatatablesServerSide = (function () {
    // Shared variables
    var table;
    var datatable;
    var filterPayment;
    $('input[name="search"]').daterangepicker({
        autoUpdateInput: false,
        autoclose: false,
        locale: {
            cancelLabel: "Clear",
        },
    });
    // Private functions
    var initDatatable = function () {
        datatable = $("#medicine_table").DataTable({
            searchDelay: 500,
            processing: true,
            serverSide: true,
            order: [[5, "desc"]],
            stateSave: false,
            select: {
                style: "multi",
                selector: 'td:first-child input[type="checkbox"]',
                className: "row-selected",
            },
            ajax: {
                url: getListRoute,
                data: function (d) {
                    var dt_params = $("#medicine_table").data("dt_params");
                    if (dt_params) {
                        $.extend(d, dt_params);
                    }
                },
                error: function (jqXHR, exception) {
                    if (jqXHR.status == 401 || jqXHR.status == 419) {
                        window.location.href = login;
                    }
                },
            },
            columns: [
                {
                    data: "DT_RowIndex",
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                },
                { data: "name", name: "name" },
                { data: "doctor_name", name: "doctor_name", orderable:false, searchable:false },
                { data: "approve_status", name: "approve_status" },
                { data: "status", name: "status" },
                {
                    data: "created_date",
                    name: "created_date",
                    searchable: false,
                },
                { data: "action", name: "action", searchable: false, orderable: false },
            ],
            columnDefs: [
                {
                    targets: 1,
                    render: function (data, type, row) {
                        return `
                        <div class="d-flex flex-column">
                            <a href="#" class="text-gray-800 text-hover-primary mb-1">${row.name}</a>
                        </div>`;
                    },
                },
                {
                    targets: 4,
                    render: function (data, type, row) {
                        return `${row.status}` == true
                            ? `<div class="badge badge-light-success">Active</div>`
                            : `<div class="badge badge-light-danger">Inactive</div>`;
                    },
                },
                {
                    targets: 2,
                    className: "d-flex align-items-center",
                    render: function (data, type, row) {
                        if(row.doctor != null){
                        var doctorUrl = `${doctorRoute}`.replace('id', `${row.doctor.id}`);
                        return `<div class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                        <div class="symbol-label">
                            <img src="${row.doctor.avatar_url}" alt="${row.doctor.name}" class="w-100">
                        </div>
                        </div>
                        <div class="d-flex flex-column">
                            <a href=`+doctorUrl+` class="text-gray-800 text-hover-primary mb-1">${row.doctor.name}</a>
                            <span>${row.doctor.username}</span>
                        </div>`;
                        }else{
                            return '-';
                        }
                    },
                },
            ],
        });

        table = datatable.$;
        $('#medicine_table thead tr th').removeClass('d-flex align-items-center');
        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
        datatable.on("draw", function () {
            KTMenu.createInstances();
        });
    };

    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
    var handleSearchDatatable = () => {
        const filterSearch = document.querySelector(
            '[data-kt-user-table-filter="search"]'
        );
        filterSearch.addEventListener("keyup", function (e) {
            datatable.search(e.target.value).draw();
        });
    };

    // Filter Datatable
    var handleFilterDatatable = () => {
        // Select filter options
        const filterForm = document.querySelector(
            '[data-kt-user-table-filter="form"]'
        );
        const filterButton = filterForm.querySelector(
            '[data-kt-user-table-filter="filter"]'
        );
        const selectOptions = filterForm.querySelectorAll("select");

        // Filter datatable on submit
        filterButton.addEventListener("click", function () {
            var filterString = "";

            // Get filter values
            selectOptions.forEach((item, index) => {
                if (item.value && item.value !== "") {
                    if (index == 0) {
                        datatable.column(4).search(item.value).draw();
                    }
                    if (index == 1) {
                        datatable.column(3).search(item.value).draw();
                    }
                }else{
                    if (index == 0) {
                        datatable.column(4).search("").draw();
                    }
                    if (index == 1) {
                        datatable.column(3).search("").draw();
                    }
                }
            });
        });
    };

    // Reset Filter
    var handleResetForm = () => {
        // Select reset button
        const resetButton = document.querySelector(
            '[data-kt-user-table-filter="reset"]'
        );

        // Reset datatable
        resetButton.addEventListener("click", function () {
            // Select filter options
            const filterForm = document.querySelector(
                '[data-kt-user-table-filter="form"]'
            );
            const selectOptions = filterForm.querySelectorAll("select");

            // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
            selectOptions.forEach((select) => {
                $(select).val("").trigger("change");
            });

            // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
            datatable.search("").columns().search("").draw();
        });
    };

    $('input[name="search"]').on(
        "apply.daterangepicker",
        function (ev, picker) {
            $(this).val(
                picker.startDate.format("DD/MM/YYYY") +
                    " - " +
                    picker.endDate.format("DD/MM/YYYY")
            );
            var start_date = picker.startDate.format("YYYY/MM/DD");
            var end_date = picker.endDate.format("YYYY/MM/DD");
            $("#medicine_table").data("dt_params", {
                start_date: start_date,
                end_date: end_date,
            });
            datatable.draw();
        }
    );

    $('input[name="search"]').on(
        "cancel.daterangepicker",
        function (ev, picker) {
            $(this).val("");
            $("#medicine_table").data("dt_params", {
                start_date: null,
                end_date: null,
            });
            datatable.draw();
        }
    );
    var initDropzone = function initDropzone() {
        var myDropzone = new Dropzone("#add_invitation", {
          url: importInvitation,
          // Set the url for your upload script location
          paramName: "file",
          // The name that will be used to transfer the file
          maxFiles: 10,
          maxFilesize: 10,
          // MB
          addRemoveLinks: true,
          headers: {
            'X-CSRF-TOKEN': csrf
          },
          init: function init() {
            this.on('success', function () {
              Swal.fire({
                text: "File Imported Successfully!.",
                icon: "success",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                  confirmButton: "btn fw-bold btn-primary"
                }
              }).then(function () {
                location.reload();
              });
            });
          },
          accept: function accept(file, done) {
            if (file.name == "wow.jpg") {
              done("Naha, you don't.");
            } else {
              done();
              datatable.ajax.reload();
            }
          }
        });
      };

    // Public methods
    return {
        init: function () {
            initDatatable();
            initDropzone();
            handleSearchDatatable();
            handleResetForm();
            handleFilterDatatable();
        },
    };
})();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    KTDatatablesServerSide.init();
});

$(document).on("click", ".changeStatus", function () {
    var url = $(this).attr("data-url");
    var id = $(this).attr("id");
    $.ajax({
        url: url,
        method: "GET",
        data: {
            id: id,
        },
        success: function (data) {
            $("#medicine_table").DataTable().ajax.reload();
        },
    });
});


$(document).on("click", ".approve-status", function () {
    var url = $(this).attr("data-url");
    var name = $(this).attr("data-name");
    var approve_status = $(this).attr("data-status");
    var id = $(this).attr("id");
    if(approve_status == 2){
       var text = "Are you sure you want to approve "+name+"?"
       var confirmButtonText = "Yes, approve!"
    }else{
       var text = "Are you sure you want to disapprove "+name+"?"
       var confirmButtonText = "Yes, disapprove!"
    }
    Swal.fire({
        text: text,
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: confirmButtonText,
        cancelButtonText: "No, cancel",
        customClass: {
            confirmButton: "btn fw-bold btn-danger",
            cancelButton: "btn fw-bold btn-active-light-primary",
        },
    }).then(function (result) {
        if (result.value) {
            $.ajax({
                url: url,
                method: "GET",
                data: {
                    id: id,
                    approve_status: approve_status,
                    name: name
                },
                success: function (data) {
                    $("#medicine_table").DataTable().ajax.reload();
                },
            });
        }
    });
});

