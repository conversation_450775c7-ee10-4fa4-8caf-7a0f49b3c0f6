<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Contracts\Transformable;
use Prettus\Repository\Traits\TransformableTrait;

class DoctorDetail extends Model
{
    use HasFactory, TransformableTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'gender', 'council_number', 'signature', 'is_generalist', 'is_specialist', 'doctor_type', 'is_popular',
        'online_status', 'served_location', 'virtual_consultation', 'physical_consultation', 'home_consultation', 'virtual_consultation_price',
        'physical_consultation_price', 'home_consultation_price', 'service', 'education_summary', 'timezone', 'account_verified_at', 'wallet_balance'
    ];

    protected $appends = [
        'signature_url'
    ];

    /**
     * The doctor details has user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }


    /**
     * Get avatar link
     *
     * @return string
     */
    public function getSignatureUrlAttribute()
    {
        if (!empty($this->attributes['signature'])) {
            return $this->attributes['signature'];
        } else {
            return asset('assets/media/avatars/blank.png');
        }
    }
}
