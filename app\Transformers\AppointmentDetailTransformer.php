<?php

namespace App\Transformers;

use App\Models\DoctorAppointment;
use League\Fractal\TransformerAbstract;

class AppointmentDetailTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = ['patient_detail'];
    protected $hashKey;

    public function __construct($hashKey) {
        $this->hashKey = $hashKey;
    }

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorAppointment $doctorAppointment)
    {
        return [
            'key' => $this->hashKey["hashKey"],
            'patient_id' => $doctorAppointment->patient_id,
            'patient_avatar' => $doctorAppointment->patient->avatar_url,
            'patient_name' => $doctorAppointment->patient->name,
            'doctor_id' => $doctorAppointment->doctor_id,
            'doctor_name' => $doctorAppointment->doctor->name,
            'doctor_avatar' => $doctorAppointment->doctor->avatar_url,
            'doctor_type' => $doctorAppointment->doctor->doctorDetail->doctor_type,
            'appointment_status' => $doctorAppointment->appointment_status,
            'appointment_date' => $doctorAppointment->appointment_date,
            'start_time' => $doctorAppointment->start_time,
            'end_time' => $doctorAppointment->end_time,
            'consultation_type' => $doctorAppointment->consultation_type,
            'clinic_name' => $doctorAppointment->doctor->doctorClinic->clinic_name,
            'apartment_no' => $doctorAppointment->doctor->doctorClinic->apartment_no,
            'clinic_address' => $doctorAppointment->doctor->doctorClinic->clinic_address,
            'clinic_landmark' => $doctorAppointment->doctor->doctorClinic->clinic_landmark,
            'clinic_city' => $doctorAppointment->doctor->doctorClinic->clinic_city,
            'clinic_state' => $doctorAppointment->doctor->doctorClinic->clinic_state,
            'country' => $doctorAppointment->doctor->doctorClinic->country,
            'clinic_dial_code' => $doctorAppointment->doctor->doctorClinic->clinic_dial_code,
            'clinic_mobile' => (string)$doctorAppointment->doctor->doctorClinic->clinic_mobile,
            'booking_id' => $doctorAppointment->unique_id,
            'transaction_id' => isset($doctorAppointment->transaction->request_id) ? $doctorAppointment->transaction->request_id : "",
            'amount' => isset($doctorAppointment->transaction->amount) ? $doctorAppointment->transaction->amount : "",
            'currency' => isset($doctorAppointment->transaction->currency) ? $doctorAppointment->transaction->currency : ""
        ];
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Item
     */
    public function includePatientDetail(DoctorAppointment $doctorAppointment)
    {
        if(!empty($doctorAppointment->patient->patientDetail)) {
            return $this->item($doctorAppointment->patient->patientDetail, new PatientDetailViewTransformer());
        }
    }
}
