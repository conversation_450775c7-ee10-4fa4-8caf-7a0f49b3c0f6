<?php

namespace App\Repositories;

use App\Enums\NotificationType;
use App\Enums\SenderType;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Imports\LabTestImport;
use App\Jobs\Notification;
use App\Models\DoctorAppointment;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\LabTestRepository;
use App\Models\LabTest;
use App\Models\User;
use App\Transformers\LabTestTransformer;
use App\Validators\LabTestValidator;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\DataTables;

/**
 * Class LabTestRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class LabTestRepositoryEloquent extends BaseRepository implements LabTestRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return LabTest::class;
    }

    /**
     * Send Invitation to doctor
     *
     * @return string
     */
    public function import($request)
    {

        return Excel::import(new LabTestImport, $request->file);
    }

    /**
     * List of lab-test
     *
     * @return array
     */
    public function getList($request)
    {
        $speciality = LabTest::with('doctor');

        if (!empty($request->start_date)) {
            $speciality = $speciality->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($speciality)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.lab-test.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.lab-test.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.lab-test.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                    <a class="menu-link px-3 delete" id="' . $row->id . '" data-name="' . $row->name . '" data-url="' . route('admin.lab-test.destroy') . '" data-table="lab-test_table"> Delete</a>
                    </div>
                </div>';
                return $html;
            })->editColumn('approve_status', function ($row) {
                if ($row->approve_status == 1) {
                    $html = '<a href="#" class="badge badge-outline badge-success approve-status" data-name="' . $row->name . '" data-url="' . route('admin.lab-test.change-approve-status') . '" id="' . $row->id . '" data-status="2">
                        Approve
                    </a>
                    <a href="#" class="badge badge-outline badge-warning approve-status" data-name="' . $row->name . '" data-url="' . route('admin.lab-test.change-approve-status') . '" id="' . $row->id . '" data-status="3">
                        Disapprove
                    </a>';
                } else if ($row->approve_status == 2) {
                    $html = '<a href="#" class="badge badge-outline badge-success">
                        Approve
                    </a>';
                } else if ($row->approve_status == 3) {
                    $html = '<a href="#" class="badge badge-outline badge-warning">
                       Disapprove
                    </a>';
                }

                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action', 'approve_status'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();

        return LabTest::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $labtest = LabTest::findOrFail($id);

        return $labtest;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $labtest = LabTest::findOrFail($id);

        $data = $request->all();
        if ($request->has('image')) {
            $data['image'] = self::imageUpload($request->image, 'molema/lab-test');
            self::delete($labtest->getOriginal('image'));
        }
        return $labtest->update($data);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = LabTest::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = LabTest::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function changeApproveStatus($request)
    {
        $labtest = LabTest::findOrFail($request->input('id'));

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($labtest->doctor_id);
        $extra = [
            'notification_type' => NotificationType::LabTestRequest,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'sender_type' => SenderType::Admin,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Add LabTest Request',
            'message' => $request->approve_status == 2 ? "Your labtest " . $request->name . " request has been approved." : "Your labtest " . $request->name . " request has been disapproved.",
            'ref_id' => $labtest->id,
            'ref_type' => 'LabTest'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        $labtest->update(['approve_status' => $request->approve_status]);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = LabTest::findOrFail($request->input('id'));
        $patient->delete();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
