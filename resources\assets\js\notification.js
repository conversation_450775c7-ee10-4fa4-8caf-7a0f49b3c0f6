"use strict";

// Class definition
var KTDatatablesServerSide = (function () {
    // Shared variables
    var table;
    var datatable;
    var filterPayment;
    $('input[name="search"]').daterangepicker({
        autoUpdateInput: false,
        autoclose: false,
        locale: {
            cancelLabel: "Clear",
        },
    });
    // Private functions
    var initDatatable = function () {
        datatable = $("#notification_table").DataTable({
            searchDelay: 500,
            processing: true,
            serverSide: true,
            order: [[3, "desc"]],
            stateSave: false,
            select: {
                style: "multi",
                selector: 'td:first-child input[type="checkbox"]',
                className: "row-selected",
            },
            ajax: {
                url: getListRoute,
                data: function (d) {
                    var dt_params = $("#notification_table").data("dt_params");
                    if (dt_params) {
                        $.extend(d, dt_params);
                    }
                },
                error: function (jqXHR, exception) {
                    if (jqXHR.status == 401 || jqXHR.status == 419) {
                        window.location.href = login;
                    }
                },
            },
            columns: [
                {
                    data: "DT_RowIndex",
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                },
                { data: "title", name: "title" },
                { data: "description", name: "description" },
                {
                    data: "created_date",
                    name: "created_date",
                    searchable: false,
                }
            ],
            columnDefs: [

            ],
        });

        table = datatable.$;
        $('#notification_table thead tr th').removeClass('d-flex align-items-center');
        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
        datatable.on("draw", function () {
            KTMenu.createInstances();
        });
    };

    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
    var handleSearchDatatable = () => {
        const filterSearch = document.querySelector(
            '[data-kt-user-table-filter="search"]'
        );
        filterSearch.addEventListener("keyup", function (e) {
            datatable.search(e.target.value).draw();
        });
    };

    // Filter Datatable
    var handleFilterDatatable = () => {
        // Select filter options
        const filterForm = document.querySelector(
            '[data-kt-user-table-filter="form"]'
        );
        const filterButton = filterForm.querySelector(
            '[data-kt-user-table-filter="filter"]'
        );
        const selectOptions = filterForm.querySelectorAll("select");

        // Filter datatable on submit
        filterButton.addEventListener("click", function () {
            var filterString = "";

            // Get filter values
            selectOptions.forEach((item, index) => {

            });
        });
    };

    // Reset Filter
    var handleResetForm = () => {
        // Select reset button
        const resetButton = document.querySelector(
            '[data-kt-user-table-filter="reset"]'
        );

        // Reset datatable
        resetButton.addEventListener("click", function () {
            // Select filter options
            const filterForm = document.querySelector(
                '[data-kt-user-table-filter="form"]'
            );
            const selectOptions = filterForm.querySelectorAll("select");

            // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
            selectOptions.forEach((select) => {
                $(select).val("").trigger("change");
            });

            // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
            datatable.search("").columns().search("").draw();
        });
    };

    $('input[name="search"]').on(
        "apply.daterangepicker",
        function (ev, picker) {
            $(this).val(
                picker.startDate.format("DD/MM/YYYY") +
                    " - " +
                    picker.endDate.format("DD/MM/YYYY")
            );
            var start_date = picker.startDate.format("YYYY/MM/DD");
            var end_date = picker.endDate.format("YYYY/MM/DD");
            $("#notification_table").data("dt_params", {
                start_date: start_date,
                end_date: end_date,
            });
            datatable.draw();
        }
    );

    $('input[name="search"]').on(
        "cancel.daterangepicker",
        function (ev, picker) {
            $(this).val("");
            $("#notification_table").data("dt_params", {
                start_date: null,
                end_date: null,
            });
            datatable.draw();
        }
    );

    // Public methods
    return {
        init: function () {
            initDatatable();
            handleSearchDatatable();
            handleResetForm();
            handleFilterDatatable();
        },
    };
})();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    KTDatatablesServerSide.init();
});
