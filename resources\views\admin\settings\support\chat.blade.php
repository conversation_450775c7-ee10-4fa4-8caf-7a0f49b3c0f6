@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    {{ Breadcrumbs::render('admin.settings.support') }}

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Layout-->
            <div class="d-flex flex-column flex-lg-row">
                <!--begin::Content-->
                <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
                    <!--begin::Messenger-->
                    <div class="card" id="kt_chat_messenger">
                        <!--begin::Card header-->
                        <div class="card-header" id="kt_chat_messenger_header">
                            <!--begin::Title-->
                            <div class="card-title col-md-10 col-md-offset-1">
                                <!--begin::User-->
                                <div class="d-flex justify-content-center flex-column me-3">
                                    <a href="#" class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1">{{
                                        $chat->subject }} (#{{ $chat->unique_id }})
                                    @if($chat->status == App\Enums\SupportStatus::Pending)
                                    <div class="badge badge-light-danger">Open</div>
                                    @else
                                    <div class="badge badge-light-success">Completed</div>
                                    @endif
                                    </a>
                                    <a href="#" class="fs-6 text-gray-500 me-1 mb-2 lh-1">{{ $chat->description }}</a>
                                </div>
                                <!--end::User-->
                            </div>
                            <!--end::Title-->
                            <!--begin::Card toolbar-->
                            <div class="card-toolbar col-md-1 col-md-offset-2">
                                <!--begin::Menu-->
                                <div class="me-n3">
                                    <a href="{{ route('admin.support.index') }}" class="btn btn-sm btn-light-primary">
                                        <i class="la la-arrow-left"></i> Back
                                    </a>
                                </div>
                                <!--end::Menu-->
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->
                        <!--begin::Card body-->
                        <div class="card-body" id="kt_chat_messenger_body">
                            <!--begin::Messages-->
                            <div class="scroll-y me-n5 pe-5 h-300px h-lg-auto" id="scroll" data-kt-element="messages"
                                data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                                data-kt-scroll-max-height="auto"
                                data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_app_toolbar, #kt_toolbar, #kt_footer, #kt_app_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer"
                                data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_messenger_body"
                                data-kt-scroll-offset="5px">

                                @foreach ($chat->supportChat as $supportChat)
                                <!--begin::Message(in)-->
                                <div
                                    class="d-flex {{ $supportChat->sender_id == Auth::guard('admin')->user()->id ? 'justify-content-end' : 'justify-content-start' }} mb-10">
                                    <!--begin::Wrapper-->
                                    <div
                                        class="d-flex flex-column {{ $supportChat->sender_id == Auth::guard('admin')->user()->id ? 'align-items-end' : 'align-items-start' }}">
                                        <!--begin::User-->
                                        <div class="d-flex align-items-center mb-2">
                                            <!--begin::Avatar-->
                                            <div class="symbol symbol-35px symbol-circle">
                                                <img alt="Pic"
                                                    src="{{ $supportChat->sender_type == 1 ? $supportChat->senderUser->avatar_url : $supportChat->senderAdmin->avatar_url }}" />
                                            </div>
                                            <!--end::Avatar-->
                                            <!--begin::Details-->
                                            <div class="ms-3">
                                                <a href="#"
                                                    class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">{{
                                                    $supportChat->sender_type == 1 ? $supportChat->senderUser->name :
                                                    $supportChat->senderAdmin->name }}</a>
                                                <span class="text-muted fs-7 mb-1">{{
                                                    \Carbon\Carbon::parse($supportChat->created_at)->diffForHumans()
                                                    }}</span>
                                            </div>
                                            <!--end::Details-->
                                        </div>
                                        <!--end::User-->

                                        <!--begin::Text-->
                                        @if ($supportChat->message_type == 'document')
                                        <a href="{{ $supportChat->message }}" target="_new">
                                            @endif
                                            <div class="p-3 rounded {{ $supportChat->sender_id == Auth::guard('admin')->user()->id ? 'bg-light-primary text-end' : 'bg-light-info text-start' }}  text-dark fw-semibold mw-lg-400px"
                                                data-kt-element="message-text" style="{{ $supportChat->message_type == 'document' &&
                                                    (pathinfo($supportChat->message)['extension'] == 'png' ||
                                                        pathinfo($supportChat->message)['extension'] == 'jpg' ||
                                                        pathinfo($supportChat->message)['extension'] == 'jpeg')
                                                        ? " background-image: url($supportChat->message); height:150px;
                                                width:150px; background-size: cover;"
                                                : '' }}">
                                                {{ $supportChat->message_type == 'text' ? $supportChat->message : '' }}
                                                @if ($supportChat->message_type == 'document')
                                                @if (pathinfo($supportChat->message)['extension'] == 'pdf' ||
                                                pathinfo($supportChat->message)['extension'] == 'doc' ||
                                                pathinfo($supportChat->message)['extension'] == 'docx')
                                                <iframe src={{ $supportChat->message }} scrolling="no"
                                                    frameBorder="0" height="100px" width="250px"
                                                    id="frame"></iframe>
                                                <a href="{{ $supportChat->message }}" target="_new">
                                                    <i class="fa fa-eye" aria-hidden="true"
                                                        style="margin-left: 10px;"></i>
                                                </a>
                                                @endif
                                                @endif
                                            </div>
                                            @if ($supportChat->message_type == 'document')
                                        </a>
                                        @endif
                                        <!--end::Text-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                @endforeach
                            </div>
                            <!--end::Messages-->
                        </div>
                        <!--end::Card body-->
                        <!--begin::Card footer-->
                        <div class="card-footer pt-4" id="kt_chat_messenger_footer">
                            {{ Form::open([
                            'route' => ['admin.support.chat.create', $chat->id],
                            'method' => 'post',
                            'class' => 'form',
                            'id' => 'chat-form',
                            'enctype' => 'multipart/form-data',
                            ]) }}
                            {!! Form::hidden('support_id', $chat->id) !!}
                            <!--begin::Input-->
                            <textarea name="message" class="form-control form-control-flush mb-3 test" rows="1"
                                data-kt-element="input" placeholder="Type a message"></textarea>
                                @if($errors->has('message'))
                                    <div id="message-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('message') }}
                                    </div>
                                    @endif
                            <div id="iframeContainer"></div>
                            <!--end::Input-->
                            <!--begin:Toolbar-->
                            <div class="d-flex flex-stack">
                                <!--begin::Actions-->
                                <div class="d-flex align-items-center me-2">
                                    <button class="btn btn-sm btn-icon btn-active-light-primary me-1 " type="button"
                                        data-bs-toggle="tooltip" title="Coming soon">
                                        <i class="bi bi-paperclip fs-3" id="OpenImgUpload"></i>
                                        {!! Form::file('media', ['style' => 'display: none', 'id' => 'imgupload']) !!}
                                        @if ($errors->has('media'))
                                        <div id="media-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('media') }}
                                        </div>
                                        @endif
                                    </button>


                                </div>
                                <!--end::Actions-->
                                <!--begin::Send-->
                                {!! Form::submit('Send', [
                                'data-kt-element' => 'send',
                                'class' => 'btn
                                btn-primary',
                                ]) !!}
                                <!--end::Send-->
                            </div>
                            {!! Form::close() !!}
                            <!--end::Toolbar-->
                        </div>
                        <!--end::Card footer-->
                    </div>
                    <!--end::Messenger-->
                </div>
                <!--end::Content-->
            </div>
            <!--end::Layout-->

        </div>
        <!--end::Content container-->
    </div>
</div>
@endsection
@section('js')

<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\ChatRequest', '#chat-form'); !!}

<script type="text/javascript">
    $('#OpenImgUpload').click(function() {
            $('#imgupload').trigger('click');
        });

        $('#imgupload').change(function() {
            var input = this;
            var url = $(this).val();
            var ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
            var reader = new FileReader();

            reader.onload = function(e) {
                var message = e.target.result;

                var iframe = $('<iframe>', {
                    src: message,
                    scrolling: 'no',
                    width: '150px',
                    height: '150px'
                });

                $('.test').hide();

                // Clear previous iframe
                $('#iframeContainer').empty();

                // Append the iframe to the container
                $('#iframeContainer').append(iframe);
                scrollToBottom();
            }
            reader.readAsDataURL(input.files[0]);

        });



       var height = 0;
        $('#scroll').each(function(i, value){
        height += parseInt($(this).height());
        });

        height += '';
        console.log(height);
        $('div').animate({scrollTop: height}, 100);

</script>
@endsection
