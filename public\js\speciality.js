/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!*******************************************!*\
  !*** ./resources/assets/js/speciality.js ***!
  \*******************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#speciality_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[4, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getListRoute,
        data: function data(d) {
          var dt_params = $("#speciality_table").data("dt_params");
          console.log(dt_params);
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "specialist",
        name: "specialist"
      }, {
        data: "symptoms",
        name: "symptoms"
      }, {
        data: "status",
        name: "status"
      }, {
        data: "created_date",
        name: "created_date",
        searchable: false
      }, {
        data: "action",
        name: "action",
        searchable: false,
        orderable: false
      }],
      columnDefs: [{
        targets: 1,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"symbol symbol-circle symbol-50px overflow-hidden me-3\">\n                        <div class=\"symbol-label\">\n                            <img src=\"".concat(row.image_url, "\" alt=\"").concat(row.specialist, "\" class=\"w-100\">\n                        </div>\n                        </div>\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1 mx-2\">").concat(row.specialist.en, "</a>\n                        </div>(\n                        <div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">").concat(row.specialist.es, "</a>\n                        </div>)");
        }
      }, {
        targets: 2,
        render: function render(data, type, row) {
          return row.symptom.symptoms_name.en;
        }
      }, {
        targets: 3,
        render: function render(data, type, row) {
          return "".concat(row.status) == true ? "<div class=\"badge badge-light-success\">Active</div>" : "<div class=\"badge badge-light-danger\">Inactive</div>";
        }
      }]
    });
    table = datatable.$;
    $('#speciality_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    var filterButton = filterForm.querySelector('[data-kt-user-table-filter="filter"]');
    var selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    filterButton.addEventListener("click", function () {
      var filterString = "";

      // Get filter values
      selectOptions.forEach(function (item, index) {
        if (item.value && item.value !== "") {
          if (index == 0) {
            datatable.column(3).search(item.value).draw();
          }
        }
      });
    });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');

    // Reset datatable
    resetButton.addEventListener("click", function () {
      // Select filter options
      var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
      var selectOptions = filterForm.querySelectorAll("select");

      // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
      selectOptions.forEach(function (select) {
        $(select).val("").trigger("change");
      });

      // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
      datatable.search("").columns().search("").draw();
    });
  };
  $('input[name="search"]').on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
    var start_date = picker.startDate.format("YYYY/MM/DD");
    var end_date = picker.endDate.format("YYYY/MM/DD");
    $("#speciality_table").data("dt_params", {
      start_date: start_date,
      end_date: end_date
    });
    datatable.draw();
  });
  $('input[name="search"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#speciality_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });

  // Public methods
  return {
    init: function init() {
      initDatatable();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#speciality_table").DataTable().ajax.reload();
    }
  });
});
/******/ })()
;