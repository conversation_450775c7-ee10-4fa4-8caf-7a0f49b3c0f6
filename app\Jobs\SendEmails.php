<?php

namespace App\Jobs;

use App\Mail\SendMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendEmails implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user;
    public $email_body;
    public $subject;

    public function __construct($user)
    {
        $this->user = $user;
        $this->email_body = $user['email_body'];
        $this->subject = $user['subject'];
    }

    public function handle()
    {
        Log::info('Sending email to using SendEmails job started');
        Mail::to($this->user['email'])->send(new SendMail($this->user, $this->email_body, $this->subject));
        Log::info('Sending email to using SendEmails job ended');
    }
}