<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Speciality extends Model
{
    use HasFactory, TransformableTrait, SoftDeletes, HasTranslations;

    public $translatable = [
        'specialist'
    ];

    protected $dates = ['deleted_at'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'specialist', 'status', 'image', 'symptoms_id'
    ];

    protected $appends = [
        'created_date', 'image_url'
    ];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * Get Image link
     *
     * @return string
     */
    public function getImageUrlAttribute()
    {
        if (!empty($this->attributes['image'])) {
            return $this->attributes['image'];
        } else {
            return asset('assets/media/avatars/blank.png');
        }
    }

    /**
     * The symptoms_id that belong to the speciality.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function symptom()
    {
        return $this->belongsTo(Symptom::class, 'symptoms_id', 'id')->withTrashed();
    }

    /**
     * @param $query
     * @param $keyword
     * @return mixed
     */
    public function scopeSearchBySymptom($query, $keyword)
    {
        $query->whereHas('symptom', function($query) use ($keyword){
            $query->where('symptoms_name', 'like', '%'.$keyword.'%');
        });
    }
}
