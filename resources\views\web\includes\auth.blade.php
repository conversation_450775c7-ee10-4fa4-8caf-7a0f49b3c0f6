<!DOCTYPE html>
<html>

@include('web.includes.authhead')

<body>
    <!-- Header Section -->
    <div class="header-section full-width">
        <div class="wrapper flex-center">
            <div class="logo">
                <a href="{{ url('/') }}"> <img src="{{ asset('assets/media/website-images/top-logo.svg') }}" alt="logo" /> </a>
            </div>
            <div class="top-right">
                @if(empty(auth()->user()))
                    {{-- <div><a href="#" class="cta-btn">@lang('promotional.nav.register_button')</a></div> --}}
                    <div><a href="{{ route('web.login', ['locale' => app()->getLocale() ?? 'en']) }}" class="cta-btn hovered">@lang('promotional.nav.login_button')</a></div>
                @else
                    <div><a href="{{ route('web.subscription', ['locale' => app()->getLocale() ?? 'en']) }}" class="cta-btn ">@lang('promotional.subscription.title')</a></div>
                @endif
                {{-- <div>
                <select id="selectbox2" class="cta-btn hovered" style="border-radius: ">
                    <option value="en" {{ app()->getLocale() == 'en' ?'selected':'' }}>English</option>
                    <option value="es" {{ app()->getLocale() == 'es' ?'selected':'' }}>Spanish</option>
                </select>
                </div> --}}
                <div class="select-menu" style="margin-left: 15px; min-width: 20px;">
                    <div class="select-btn">
                        <span class="sBtn-text">
                        @if (app()->getLocale() == 'en')
                            <img class="rounded-1" src="{{ asset('assets/media/flags/united-states.svg') }}" alt="" style="width: 20px; height: 15px; border-radius: 5px;">
                        @else
                            <img class="rounded-1" src="{{ asset('assets/media/flags/spain.svg') }}" alt="" style="width: 20px; height: 15px; border-radius: 5px;">
                        @endif
                        </span>
                        <i class="bx bx-chevron-down"></i>
                    </div>
                    <div style="position: relative;
                    inset: 0px 0px auto auto;
                    margin: 0px;
                    transform: translate3d(-90px, 0px, 0px);">
                        <ul class="options">
                            <li class="option">
                                <img class="rounded-1" src="{{ asset('assets/media/flags/united-states.svg') }}" alt="" style="width: 20px; height: 15px; margin-right: 7px; border-radius: 5px;">
                                <span class="option-text"><a href="{{ route('home',  ['locale' => 'en']) }}">English</a></span>
                            </li>
                            <li class="option">
                                <img class="rounded-1" src="{{ asset('assets/media/flags/spain.svg') }}" alt="" style="width: 20px; height: 15px; margin-right: 7px; border-radius: 5px;">
                                <span class="option-text"><a href="{{ route('home',  ['locale' => 'es']) }}">Spanish</a></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
        $currentRouteName = Route::currentRouteName();
        $currentRouteParams = Route::current()->parameters();
    ?>
    <script type="text/javascript">
        $(document).ready(function () {
            $("#selectbox2").click(function () {
                if ($(this).val() == 'es') {
                    window.location.href = "{{ route($currentRouteName, ['locale' => 'es']) }}";
                } if ($(this).val() == 'en') {
                    window.location.href = "{{ route($currentRouteName, ['locale' => 'en']) }}";
                }
            });

            const optionMenu = document.querySelector(".select-menu"),
            selectBtn = optionMenu.querySelector(".select-btn"),
            options = optionMenu.querySelectorAll(".option"),
            sBtn_text = optionMenu.querySelector(".sBtn-text");

            selectBtn.addEventListener("click", () =>
                optionMenu.classList.toggle("active")
            );
        })
    </script>

    <!-- begin:: Page -->
    @yield('content')
    <!-- end:: Page -->
    @include('web.includes.footer')

</body>

</html>
