<?php

namespace App\Exports;

use App\Models\DoctorWalletTransaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class WalletTransactionExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    public $requestData;

    public function __construct(array $requestData)
    {
        $this->requestData = $requestData;
    }

    public function query()
    {
        $walletTransaction = DoctorWalletTransaction::with('transaction', 'transaction.doctor', 'doctorAppointment');

        if(!empty($this->requestData['search'])){
            $keyword = $this->requestData['search'];
            $walletTransaction = $walletTransaction
            ->whereHas('transaction', function ($que) use ($keyword) {
                $que->whereHas('doctor', function ($q) use ($keyword) {
                    $q->searchByName($keyword);
                })->orWhereHas('doctorAppointment', function ($que) use ($keyword) {
                    $que->where('unique_id', 'LIKE', '%' . trim($keyword) . '%');
                })->orWhere('transaction_id', 'LIKE', '%' . trim($keyword) . '%');
            });
        }

        if(!empty($this->requestData['wallet_type'])){
            $walletTransaction = $walletTransaction->where('wallet_type',$this->requestData['wallet_type']);
        }

        if(!empty($this->requestData['doctor_type'])){
            $walletTransaction = $walletTransaction->where('doctor_type', $this->requestData['doctor_type']);
        }

        if(!empty($this->requestData['datepick'])){
            $date = explode(' - ', $this->requestData['datepick']);
            $start_date = date('Y/m/d', strtotime($date[0]));
            $end_date = date('Y/m/d', strtotime($date[1]));
            $walletTransaction = $walletTransaction->whereBetween(\DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$start_date, $end_date]);
        }

        return $walletTransaction;
    }


    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID","Doctor Name","Doctor Type","Appointment ID","Transaction Id","Commission","Molema commission amount","Doctor amount","Wallet type","Created At","Updated At"];
    }

    public function map($appointment): array
    {

        return [
            $appointment->id,
            $appointment->transaction->doctor->name,
            $appointment->doctor_type == 1 ? 'Collaborator' : 'Freelancer',
            $appointment->doctorAppointment->unique_id ?? '-',
            $appointment->transaction->transaction_id,
            $appointment->commission,
            $appointment->transaction->amount - $appointment->wallet_amount,
            $appointment->wallet_amount,
            $appointment->wallet_type == 1 ? 'Earning' : 'Payout',
            $appointment->created_date,
            $appointment->updated_date
        ];
    }
}
