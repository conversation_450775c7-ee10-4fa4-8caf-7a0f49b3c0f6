<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Contracts\Translation\TranslatorTrait;

class DoctorInvitation extends Model
{
    use HasFactory, TranslatorTrait;

    protected $fillable = [
        'user_id',
        'user_type',
        'email',
        'invitation_code',
        'type',
        'status',
        'name'
    ];

    /**
     * The doctor_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    /**
     * The doctor_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class, 'user_id', 'id');
    }

    protected $appends = [
        'created_date'
    ];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }
}
