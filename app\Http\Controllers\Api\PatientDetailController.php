<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\PatientDetailRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\PatientDetailRepositoryEloquent;
use App\Repositories\UserRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PatientDetailController extends Controller
{
    /**
     * @var PatientDetailRepositoryEloquent|string
     */
    public $patientDetailRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Patient Detail Controller constructor.
     * @param PatientDetailRepositoryEloquent $patientDetailRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(PatientDetailRepositoryEloquent $patientDetailRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->patientDetailRepository = $patientDetailRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updatePatientProfile(PatientDetailRequest $request)
    {
        try {
            $user = $this->patientDetailRepository->updatePatientProfile($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            echo $exception->getMessage();
            echo $exception->getLine();
            echo $exception->getFile();

            exit;
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     *
     */
    public function viewMedicalHistory(Request $request)
    {
        try {
            $symptom = $this->patientDetailRepository->viewMedicalHistory($request);
            return $this->apiResponse->respondWithMessageAndPayload($symptom, trans('messages.medical_history_detail'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewPatientProfile(Request $request)
    {
        try {
            $user = $this->patientDetailRepository->viewPatientProfile($request);
            $userDevice = $this->patientDetailRepository->storeDeviceInfo($request);
            //$patientProfile = $this->patientDetailRepository->updatePatientProfile($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function homeScreen(Request $request){
        try {
            $user = $this->patientDetailRepository->homeScreen($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function homeSearch(Request $request){
        try {
            $user = $this->patientDetailRepository->homeSearch($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
