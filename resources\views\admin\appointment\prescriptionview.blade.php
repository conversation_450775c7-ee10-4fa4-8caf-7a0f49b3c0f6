@extends('admin.layouts.app')
@section('content')	
<div id="kt_app_content" class="app-content flex-column-fluid">

	{{ Breadcrumbs::render('admin.appointment.view') }}
	<!--begin::Content container-->
	<div id="kt_app_content_container" class="app-container container-xxl">
		<!--begin::Navbar-->
		<div class="card mb-5 mb-xxl-8">
			<div class="pb-0" style="padding-left: 10px;">
				
				<!--begin::Navs-->
				<ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="view" class="nav-link text-active-primary ms-0 me-10 py-5 active" href="{{route('admin.appointment.view',$appointment->appointment_id)}}">Overview</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="notesview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.notesview',$appointment->appointment_id)}}">Notes</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="prescriptionview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.prescriptionview',$appointment->appointment_id)}}">Prescriptions</a>
					</li>
					<!--end::Nav item-->
					<!--begin::Nav item-->
					<li class="nav-item mt-2">
						<a id="labview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.labview',$appointment->appointment_id)}}">Lab Tests</a>
					</li>
					<!--end::Nav item-->
				</ul>
				<!--begin::Navs-->
			</div>
		</div>
		<!--end::Navbar-->
		<!--begin::Row-->
		<div class="row g-5 g-xxl-8">
			<!--begin::Col-->
			<div class="col-xl-12">
				<div id="kt_app_content_container">
		            <div class="card">
		               
		                <div class="card-body py-4">
		                    <table id="lab-test_table" class="table align-middle table-row-dashed fs-6 gy-5">
		                        <thead>
		                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
		                                <th>Id</th>
		                                <th>Unique Id</th>
		                                <th>Medicine Name</th>
		                                <th>Medicine Type</th>
		                                <th>Medicine Course</th>
		                                <th>Medicine Duration</th>
		                                <th>Medicine Quantity</th>
		                                <th>Medicine Direction</th>
		                                <th>Created Date</th>
		                            </tr>
		                        </thead>
		                        @forelse($appointment as $key => $value)
		                        <?php
		                        $count = count($value->patientPrescriptionMedicine);
		                        ?>
		                        <tbody class="text-gray-600 fw-semibold">
		                        	<td rowspan={{ $count }}>{{$value->id}}</td>
		                        	<td rowspan={{ $count }}>{{$value->unique_id}}</td>
		                        	<td>
		                        		@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
		                        		
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_name}}
			                        		@else
			                        		{{$pvalue->medicine_name}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
			                        <td>
			                        	@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_type}}
			                        		@else
			                        		{{$pvalue->medicine_type}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
			                        <td>
			                        	@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_course}}
			                        		@else
			                        		{{$pvalue->medicine_course}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
			                        <td>
			                        	@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_duration}}
			                        		@else
			                        		{{$pvalue->medicine_duration}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
			                        <td>
			                        	@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_quantity}}
			                        		@else
			                        		{{$pvalue->medicine_quantity}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
			                        <td>
			                        	@forelse($value->patientPrescriptionMedicine as $pkey => $pvalue)
			                        		@if ($loop->last)
			                        		{{$pvalue->medicine_direction}}
			                        		@else
			                        		{{$pvalue->medicine_direction}} |
			                        		@endif
			                        	@empty
			                        	@endforelse
			                        </td>
		                        	<td rowspan={{ $count }}>{{$value->created_date}}</td>
		                        </tbody>
		                        @empty
		                        <tbody class="text-gray-600 fw-semibold">
		                        	<td>No Prescription Found.</td>
		                        </tbody>
		                        @endforelse
		                    </table>
		                    <div class="pagination-custom">
							@php
								echo $appointment->render();
							@endphp
							</div>
		                </div>
		            </div>
		        </div>
			</div>
			<!--end::Col-->
			
		</div>
		<!--end::Row-->
	</div>
	<!--end::Content container-->
</div>
<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript">
	$(document).ready(function() {
	    var current = window.location.pathname.split("/")[4];
	    if(current == "prescriptionview"){
	    	$('#prescriptionview').addClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "labview"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').addClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "view"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').addClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "notesview"){
	    	$('#prescriptionview').removeClass('active');
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').addClass('active');
	    }
	    
	});
</script>
@endsection