<?php

namespace App\Transformers;

use App\Models\DoctorExperience;
use League\Fractal\TransformerAbstract;

class DoctorExperienceTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorExperience $doctorExperience)
    {
        return [
            'employer_name' => $doctorExperience->employer_name,
            'years' => $doctorExperience->years,
            'months' => $doctorExperience->months
        ];
    }
}
