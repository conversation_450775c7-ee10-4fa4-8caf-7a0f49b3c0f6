@extends('admin.layouts.app')
@section('content')
<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <div class="d-flex flex-column flex-column-fluid">
        {{ Breadcrumbs::render('admin.doctor.view') }}
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                <div class="d-flex flex-column flex-lg-row">
                    <div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
                        <div class="card mb-5 mb-xl-8">
                            <div class="card-body">
                                <div class="d-flex flex-center flex-column py-5">
                                    <div class="symbol symbol-100px symbol-circle mb-7">
                                        <img src="{{ $doctor->avatar_url }}" alt="image" />
                                    </div>
                                    <div>
                                        <div class="fs-3 text-gray-800 text-hover-primary fw-bold mb-3">
                                            {{$doctor->first_name}} {{$doctor->last_name}}</div>
                                    </div>
                                    <a href="#"
                                        class="badge badge-lg badge-light-primary d-inline mb-3">{{$doctor->username}}</a>
                                    <div>
                                        @php
                                        $stars_count = $averageRating;

                                        for($i=1; $i<=5; $i++) { if($stars_count>= $i)
                                            {
                                            @endphp
                                            <i class="fa fa-star fa-2x" style="color:orange"></i>
                                            @php
                                            }
                                            else
                                            {
                                            @endphp
                                            <i class="fa fa-star fa-2x" style="color:grey"></i>
                                            @php

                                            }
                                            }
                                            @endphp
                                    </div>


                                </div>
                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold rotate collapsible" data-bs-toggle="collapse"
                                        href="#kt_user_view_details" role="button" aria-expanded="false"
                                        aria-controls="kt_user_view_details">Details
                                        <span class="ms-2 rotate-180">
                                            <span class="svg-icon svg-icon-3">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                                                        fill="currentColor" />
                                                </svg>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <div class="separator"></div>
                                <div id="kt_user_view_details" class="collapse show">
                                    <div class="pb-5 fs-6">
                                        <div class="fw-bold mt-5">Mobile</div>
                                        <div class="text-gray-600">{{$doctor->dial_code}} {{$doctor->mobile}}</div>
                                        <div class="fw-bold mt-5">Language</div>
                                        <div class="text-gray-600">
                                            <a href="#" class="text-gray-600 text-hover-primary">{{($doctor->language ==
                                                1) ? "es":"en"}}</a>
                                        </div>
                                        <div class="fw-bold mt-5">Specialities</div>
                                        <div class="text-gray-600">
                                            {{ implode(',', $specialities) }}
                                        </div>
                                        @if($doctor->doctorDetail)
                                        <div class="fw-bold mt-5">Doctor Type</div>
                                        <div class="text-gray-600">{{($doctor->doctorDetail->doctor_type == 1)?
                                            "Collaborator": "Freelancer"}}</div>
                                        <div class="fw-bold mt-5">Status</div>
                                        <div class="text-gray-600">{{($doctor->status == 1) ? "Active":"Inactive"}}
                                        </div>
                                        <div class="fw-bold mt-5">Served Location</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->served_location ?? "-"}}
                                        </div>
                                        <div class="fw-bold mt-5">Service</div>
                                        <div class="text-gray-600">
                                            @if($doctor->doctorDetail->service)
                                            {{str_replace( array( '[', ']','"'), '',$doctor->doctorDetail->service)}}
                                            @else
                                            -
                                            @endif
                                        </div>
                                        <div class="fw-bold mt-5">Virtual Consultation</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->virtual_consultation ? 'Yes'
                                            : 'No'}}</div>
                                        <div class="fw-bold mt-5">Home Consultation</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->home_consultation ? 'Yes' :
                                            'No'}}</div>
                                        <div class="fw-bold mt-5">Physical Consultation</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->physical_consultation ?
                                            'Yes' : 'No'}}</div>
                                        <div class="fw-bold mt-5">Virtual Consultation Price</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->virtual_consultation_price
                                            ?? 0}} franc</div>
                                        <div class="fw-bold mt-5">Home Consultation Price</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->home_consultation_price ??
                                            0}} franc</div>
                                        <div class="fw-bold mt-5">Physical Consultation Price</div>
                                        <div class="text-gray-600">{{$doctor->doctorDetail->physical_consultation_price
                                            ?? 0}} franc</div>
                                        @endif
                                        <div class="fw-bold mt-5">Signature</div>
                                        <div class="text-gray-600">
                                            <div class="symbol symbol-100px">
                                                <a href="{{ !empty($doctor->doctorDetail->signature_url) ? $doctor->doctorDetail->signature_url : asset('assets/media/avatars/blank.png') }}"
                                                    target="_new">
                                                    <img style="width: 250px; height: 75px"
                                                        src="{{ !empty($doctor->doctorDetail->signature_url) ? $doctor->doctorDetail->signature_url : asset('assets/media/avatars/blank.png') }}"></a>
                                            </div>
                                        </div>
                                        <div class="fw-bold mt-5">Created On</div>
                                        <div class="text-gray-600">{{$doctor->created_date}}</div>
                                        <div class="fw-bold mt-5">Updated On</div>
                                        <div class="text-gray-600">{{$doctor->updated_date}}</div>
                                        <div class="fw-bold mt-5">Status</div>
                                        <div class="text-gray-600">{{($doctor->status == 1) ? "Active":"Inactive"}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-5 mb-xl-8">
                            <div class="card-header border-0">
                                <div class="card-title">
                                    <h3 class="fw-bold m-0">Clinical Details</h3>
                                </div>
                            </div>
                            <div class="card-body pt-2">
                                <div class="fw-bold mt-5">Clinic Name</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_name ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Moible</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->apartment_no ?? "-"}}</div>
                                <div class="fw-bold mt-5">Apartment No</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_address ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Address</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_city ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Landmark</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_landmark ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic City</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_city ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic State</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_state ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Country</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->country ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Open Time</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_open_time ?? "-"}}</div>
                                <div class="fw-bold mt-5">Clinic Close Time</div>
                                <div class="text-gray-600">{{$doctor->doctorClinic->clinic_close_time ?? "-"}}</div>
                            </div>
                        </div>

                        <div class="card mb-5 mb-xl-8">
                            <div class="card-header border-0 mt-6">
                                <div class="card-title flex-column" style="display:contents;">
                                    <h3 class="fw-bold m-0">Wallet Transaction </h3>
                                    <a href="{{route('admin.wallet-transaction.index')}}">
                                        <button class="btn btn-icon btn-active-light-primary w-30px h-30px me-3"
                                            data-bs-toggle="modal" data-bs-target="#kt_modal_update_permission">
                                            <span class="svg-icon svg-icon-3">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M17.5 11H6.5C4 11 2 9 2 6.5C2 4 4 2 6.5 2H17.5C20 2 22 4 22 6.5C22 9 20 11 17.5 11ZM15 6.5C15 7.9 16.1 9 17.5 9C18.9 9 20 7.9 20 6.5C20 5.1 18.9 4 17.5 4C16.1 4 15 5.1 15 6.5Z"
                                                        fill="currentColor"></path>
                                                    <path opacity="0.3"
                                                        d="M17.5 22H6.5C4 22 2 20 2 17.5C2 15 4 13 6.5 13H17.5C20 13 22 15 22 17.5C22 20 20 22 17.5 22ZM4 17.5C4 18.9 5.1 20 6.5 20C7.9 20 9 18.9 9 17.5C9 16.1 7.9 15 6.5 15C5.1 15 4 16.1 4 17.5Z"
                                                        fill="currentColor"></path>
                                                </svg>
                                            </span>
                                        </button></a>
                                </div>
                            </div>
                            <div class="card-body pt-2">
                                <div class="fw-bold mb-2">Total Doctor Commision Amount</div>
                                <div class="text-gray-600 mb-2 fs-5">{{$commissionAmount}}</div>

                            </div>
                        </div>
                    </div>
                    <div class="flex-lg-row-fluid ms-lg-15">

                        <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Documents</h2>
                                </div>
                            </div>
                            @foreach($doctor->doctorDocument as $key => $value)

                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    @if($value->image_id == 1)
                                    Education Certificate:-
                                    @elseif($value->image_id == 2)
                                    Other Document:-
                                    @endif

                                    <h6 class="mb-1">{{$value->image_name}}</h6>
                                    <h6>{{$value->image_description}}</h6>
                                </div>

                                <div class="symbol symbol-100px mb-7">

                                    <iframe
                                        src="{{ !empty($value->image) ? $value->image : asset('assets/media/avatars/blank.png') }}"
                                        scrolling="no" frameBorder="0" height="100px" width="130px" id="frame"></iframe>

                                    <a href="{{ !empty($value->image) ? $value->image : asset('assets/media/avatars/blank.png') }}"
                                        target="_new">
                                        <i class="fa fa-eye" aria-hidden="true" style="margin-left: 10px;"></i></a>

                                </div>
                            </div>
                            @endforeach
                        </div>

                        <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Experiences</h2>
                                </div>
                            </div>
                            <div class="card-body d-flex flex-column pt-0">
                                @forelse($doctor->doctorExperience as $key => $value)
                                <div class="d-flex align-items-center position-relative mb-7">
                                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>

                                    <div class="fw-semibold ms-5">
                                        <a href="#" class="fs-5 fw-bold text-dark text-hover-primary">Employer Name:
                                            {{$value->employer_name}}</a>
                                        <div class="fs-7 text-muted">{{$value->years}} Years {{$value->months}} Months
                                        </div>
                                    </div>

                                </div>
                                @empty
                                <div>No experience found.</div>
                                @endforelse
                            </div>
                        </div>

                        {{-- <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Specialities</h2>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column pt-0">
                                @forelse($doctor->speciality as $key => $value)
                                <div class="d-flex align-items-center position-relative mb-7">
                                    <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px"></div>

                                    <div class="fw-semibold ms-5">
                                        <a href="#"
                                            class="fs-5 fw-bold text-dark text-hover-primary">{{$value->specialist}}</a>

                                    </div>

                                </div>
                                @empty
                                <div>No speciality found.</div>
                                @endforelse
                            </div>

                        </div> --}}


                        <div class="card card-flush mb-6 mb-xl-9">

                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Subscription</h2>
                                </div>
                            </div>

                            <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                                data-bs-target="#kt_account_profile_details" aria-expanded="true"
                                aria-controls="kt_account_profile_details">

                                <div class="card-toolbar m-0">
                                    <ul class="nav nav-stretch fs-5 fw-semibold nav-line-tabs border-transparent"
                                        role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_year_tab" class="nav-link text-active-gray-800 active"
                                                data-bs-toggle="tab" role="tab" href="#ongoing" aria-selected="false"
                                                tabindex="-1">Current Subscription</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#upcoming"
                                                aria-selected="true">Purchased Subscription</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#completed"
                                                aria-selected="true">Used Subscription</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div id="ongoing" class="collapse show">
                                <div class="card-body d-flex flex-column pt-0">
                                    @if(!empty($ongoingSubscription->subscription))
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$ongoingSubscription->subscription->name}}</a>
                                            <div class="fs-7 text-muted">
                                                {{$ongoingSubscription->subscription_start_date}} -
                                                {{$ongoingSubscription->subscription_end_date}}</div>

                                        </div>
                                    </div>
                                    @else
                                    No subscription
                                    @endif
                                </div>
                            </div>
                            <div id="upcoming" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($upcomingSubscription as $upcomingSub)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$upcomingSub->subscription->name}}</a>

                                            <div class="fs-7 text-muted">{{$upcomingSub->subscription_start_date}} -
                                                {{$upcomingSub->subscription_end_date}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    No subscription
                                    @endforelse
                                </div>
                            </div>
                            <div id="completed" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($completedSubscription as $upcomingSub)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$upcomingSub->subscription->name}}</a>

                                            <div class="fs-7 text-muted">{{$upcomingSub->subscription_start_date}} -
                                                {{$upcomingSub->subscription_end_date}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    No subscription
                                    @endforelse
                                </div>
                            </div>
                        </div>


                        <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Available Slots</h2>
                                </div>
                            </div>

                            <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                                data-bs-target="#kt_account_profile_details" aria-expanded="true"
                                aria-controls="kt_account_profile_details">

                                <div class="card-toolbar m-0">
                                    <ul class="nav nav-stretch fs-5 fw-semibold nav-line-tabs border-transparent"
                                        role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_year_tab" class="nav-link text-active-gray-800 active"
                                                data-bs-toggle="tab" role="tab" href="#monday" aria-selected="false"
                                                tabindex="-1">Monday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#tuesday"
                                                aria-selected="true">Tuesday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#wednesday"
                                                aria-selected="true">Wednesday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#thursday"
                                                aria-selected="true">Thursday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#friday"
                                                aria-selected="true">Friday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#saturday"
                                                aria-selected="true">Saturday</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#sunday"
                                                aria-selected="true">Sunday</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div id="monday" class="collapse show">
                                <div class="card-body d-flex flex-column pt-0">

                                    @forelse($available_slot->where('day', 1) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="tuesday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 2) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="wednesday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 3) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="thursday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 4) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="friday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 5) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="saturday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 6) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>
                            <div id="sunday" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($available_slot->where('day', 7) as $slot)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            {{-- <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$slot}}</a> --}}

                                            <div class="fs-7">{{$slot->start_time}} -
                                                {{$slot->end_time}}</div>

                                        </div>

                                    </div>
                                    @empty
                                    Not Available
                                    @endforelse
                                </div>
                            </div>


                        </div>

                        <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column">
                                    <h2 class="mb-1">Unavailable Slots</h2>
                                </div>
                            </div>
                            \
                            <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                                data-bs-target="#kt_account_profile_details" aria-expanded="true"
                                aria-controls="kt_account_profile_details">

                                <div class="card-toolbar m-0">
                                    <ul class="nav nav-stretch fs-5 fw-semibold nav-line-tabs border-transparent"
                                        role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_year_tab" class="nav-link text-active-gray-800 active"
                                                data-bs-toggle="tab" role="tab" href="#week" aria-selected="false"
                                                tabindex="-1">Last 7 days </a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#month" aria-selected="true">Last
                                                30 days</a>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4"
                                                data-bs-toggle="tab" role="tab" href="#year" aria-selected="true">Last
                                                year</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div id="week" class="collapse show">
                                <div class="card-body d-flex flex-column pt-0">

                                    @forelse($unavailable_slot as $key => $value)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$value->unavailable_date}}
                                            </a>

                                            <div class="fs-7 text-muted">
                                                @if($value->leave_status == 0)
                                                {{$value->start_time}} : {{$value->end_time}}
                                                @else
                                                Full Day
                                                @endif</div>

                                        </div>

                                    </div>
                                    @empty
                                    No Leave
                                    @endforelse
                                </div>
                            </div>
                            <div id="month" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($month_unavailable_slot as $key => $value)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$value->unavailable_date}}
                                            </a>

                                            <div class="fs-7 text-muted">
                                                @if($value->leave_status == 0)
                                                {{$value->start_time}} : {{$value->end_time}}
                                                @else
                                                Full Day
                                                @endif</div>

                                        </div>

                                    </div>
                                    @empty
                                    No Leave
                                    @endforelse
                                </div>
                            </div>
                            <div id="year" class="collapse">
                                <div class="card-body d-flex flex-column pt-0">
                                    @forelse($year_unavailable_slot as $key => $value)
                                    <div class="d-flex align-items-center position-relative mb-7">
                                        <div class="position-absolute top-0 start-0 rounded h-100 bg-secondary w-4px">
                                        </div>

                                        <div class="fw-semibold ms-5">
                                            <a href="#"
                                                class="fs-5 fw-bold text-dark text-hover-primary">{{$value->unavailable_date}}
                                            </a>

                                            <div class="fs-7 text-muted">
                                                @if($value->leave_status == 0)
                                                {{$value->start_time}} : {{$value->end_time}}
                                                @else
                                                Full Day
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    @empty
                                    No Leave
                                    @endforelse
                                </div>
                            </div>
                        </div>


                        <div class="card card-flush mb-6 mb-xl-9">
                            <div class="card-header mt-6">
                                <div class="card-title flex-column" style="display:contents;">
                                    <h2 class="mb-1" style="float: left;">Bank Details</h2>
                                    @if(!empty($doctor->patientBankDetail))
                                    <a href="{{route('admin.bank_details.edit',$doctor->patientBankDetail->id)}}">
                                        <span data-bs-toggle="tooltip" data-bs-trigger="hover" aria-label="Edit"
                                            data-bs-original-title="Edit" data-kt-initialized="1">
                                            <span class="svg-icon svg-icon-3">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3"
                                                        d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13099 14.109L2.06399 20.309C1.98815 20.5354 1.97703 20.7787 2.03189 21.0111C2.08674 21.2436 2.2054 21.4561 2.37449 21.6248C2.54359 21.7934 2.75641 21.9115 2.989 21.9658C3.22158 22.0201 3.4647 22.0084 3.69099 21.932H3.68699Z"
                                                        fill="currentColor"></path>
                                                    <path
                                                        d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3ZM4.13499 14.105L9.891 19.861L19.245 10.507L13.489 4.75098L4.13499 14.105Z"
                                                        fill="currentColor"></path>
                                                </svg>
                                            </span>
                                        </span>
                                    </a>
                                    @endif
                                </div>

                            </div>
                            <div class="card-body p-9 pt-0">
                                <div class="tab-content">
                                    <div id="kt_schedule_day_0" class="tab-pane fade show active">

                                        @if(!empty($doctor->patientBankDetail))

                                        <div class="d-flex flex-stack position-relative mt-6">
                                            <div
                                                class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0">
                                            </div>
                                            <div class="fw-semibold ms-5">
                                                <div class="fs-5 mb-2"><b>Account Number:</b>
                                                    {{$doctor->patientBankDetail->account_number ?? "-"}}
                                                </div>
                                                <div class="fs-5 mb-2"><b>Account Address:</b>
                                                    {{$doctor->patientBankDetail->account_address ?? "-"}}
                                                </div>
                                                <div class="fs-5 mb-2"><b>Account Name:</b>
                                                    {{$doctor->patientBankDetail->account_name ?? "-"}}</div>
                                                <div class="fs-5 mb-2"><b>Account Phone:</b>
                                                    {{$doctor->patientBankDetail->account_phone ?? "-"}}</div>
                                                <div class="fs-5 mb-2"><b>Account Type:</b>
                                                    {{$doctor->patientBankDetail->account_type ?? "-"}}</div>
                                                <div class="fs-5"><b>Bank Code:</b>
                                                    {{$doctor->patientBankDetail->bank_code ?? "-"}}</div>
                                            </div>
                                        </div>
                                        @else
                                        <div>No Details Found.</div>
                                        @endif


                                    </div>

                                </div>
                            </div>


                        </div>

                        @if(!empty($doctor->doctordetail->account_verified_at))

                        <ul
                            class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-8">
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab"
                                    href="#kt_user_view_overview_tab">Upcoming</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-kt-countup-tabs="true"
                                    data-bs-toggle="tab" href="#kt_user_view_overview_security">Ongoing</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                    href="#kt_user_view_overview_events_and_logs_tab">Completed</a>
                            </li>

                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="kt_user_view_overview_tab" role="tabpanel">
                                <div class="card card-flush mb-6 mb-xl-9">
                                    <div class="card-header mt-6">
                                        <div class="card-title flex-column">
                                            <h2 class="mb-1">Doctor's Appointments</h2>
                                        </div>

                                    </div>
                                    <div class="card-body p-9 pt-4">
                                        <div class="tab-content">
                                            <div id="kt_schedule_day_0" class="tab-pane fade show active">

                                                @forelse($appointment_upcoming as $key => $value)

                                                <div class="d-flex flex-stack position-relative mt-6">
                                                    <div
                                                        class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0">
                                                    </div>
                                                    <div class="fw-semibold ms-5">
                                                        <div class="fs-7 mb-1">Appointment Date :
                                                            {{$value->appointment_date}}
                                                            <span
                                                                class="fs-7 text-muted text-uppercase">{{$value->start_timed}}
                                                                - {{$value->end_timed}}
                                                            </span>
                                                        </div>
                                                        <a href="#"
                                                            class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment
                                                            Id : #{{$value->unique_id}}</a>
                                                        <div>Consultation Type:
                                                            @if($value->consultation_type == 1)
                                                            Virtual Consultation
                                                            @elseif($value->consultation_type == 2)
                                                            Physical Consultation
                                                            @elseif($value->consultation_type == 3)
                                                            Home Consultation
                                                            @endif
                                                        </div>
                                                        <div class="fs-7 text-muted">Lead by
                                                            <a href="#">{{$value->doctor->name}}</a>
                                                        </div>
                                                    </div>
                                                    <a href="{{route('admin.appointment.view',$value->id)}}"
                                                        class="btn btn-light bnt-active-light-primary btn-sm">View</a>
                                                </div>
                                                @empty
                                                <p>No Appointments</p>
                                                @endforelse

                                                <div class="pagination-custom">
                                                    @php
                                                    echo $appointment_upcoming->links('pagination.custom');
                                                    @endphp
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="kt_user_view_overview_security" role="tabpanel">
                                <div class="card card-flush mb-6 mb-xl-9">
                                    <div class="card-header mt-6">
                                        <div class="card-title flex-column">
                                            <h2 class="mb-1">Doctor's Appointments</h2>
                                        </div>
                                    </div>
                                    <div class="card-body p-9 pt-4">

                                        <div class="tab-content">
                                            <div id="kt_schedule_day_0" class="tab-pane fade show active">

                                                @forelse($appointment_ongoing as $key => $value)
                                                <div class="d-flex flex-stack position-relative mt-6">
                                                    <div
                                                        class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0">
                                                    </div>
                                                    <div class="fw-semibold ms-5">
                                                        <div class="fs-7 mb-1">Appointment Date :
                                                            {{$value->appointment_date}}
                                                            <span
                                                                class="fs-7 text-muted text-uppercase">{{$value->start_timed}}
                                                                - {{$value->end_timed}}</span>
                                                        </div>
                                                        <a href="#"
                                                            class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment
                                                            Id : #{{$value->unique_id}}</a>
                                                        <div>Consultation Type:
                                                            @if($value->consultation_type == 1)
                                                            Virtual Consultation
                                                            @elseif($value->consultation_type == 2)
                                                            Physical Consultation
                                                            @elseif($value->consultation_type == 3)
                                                            Home Consultation
                                                            @endif
                                                        </div>
                                                        <div class="fs-7 text-muted">Lead by
                                                            <a href="#">{{$value->doctor->name}}</a>
                                                        </div>
                                                    </div>
                                                    <a href="{{route('admin.appointment.view',$value->id)}}"
                                                        class="btn btn-light bnt-active-light-primary btn-sm">View</a>
                                                </div>
                                                @empty
                                                <p>No Appointments</p>
                                                @endforelse

                                                <div class="pagination-custom">
                                                    @php
                                                    echo $appointment_ongoing->links('pagination.custom');
                                                    @endphp
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="tab-pane fade" id="kt_user_view_overview_events_and_logs_tab" role="tabpanel">
                                <div class="card card-flush mb-6 mb-xl-9">
                                    <div class="card-header mt-6">
                                        <div class="card-title flex-column">
                                            <h2 class="mb-1">Doctor's Appointments</h2>
                                        </div>

                                    </div>
                                    <div class="card-body p-9 pt-4">

                                        <div class="tab-content">
                                            <div id="kt_schedule_day_0" class="tab-pane fade show active">

                                                @forelse($appointment_completed as $key => $value)
                                                <div class="d-flex flex-stack position-relative mt-6">
                                                    <div
                                                        class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0">
                                                    </div>
                                                    <div class="fw-semibold ms-5">
                                                        <div class="fs-7 mb-1">Appointment Date :
                                                            {{$value->appointment_date}}
                                                            <span
                                                                class="fs-7 text-muted text-uppercase">{{$value->start_timed}}
                                                                - {{$value->end_timed}}</span>
                                                        </div>
                                                        <a href="#"
                                                            class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment
                                                            Id : #{{$value->unique_id}}</a>
                                                        <div>Consultation Type:
                                                            @if($value->consultation_type == 1)
                                                            Virtual Consultation
                                                            @elseif($value->consultation_type == 2)
                                                            Physical Consultation
                                                            @elseif($value->consultation_type == 3)
                                                            Home Consultation
                                                            @endif
                                                        </div>
                                                        <div class="fs-7 text-muted">Lead by
                                                            <a href="#">{{$value->doctor->name}}</a>
                                                        </div>
                                                    </div>
                                                    <a href="{{route('admin.appointment.view',$value->id)}}"
                                                        class="btn btn-light bnt-active-light-primary btn-sm">View</a>
                                                </div>
                                                @empty
                                                <p>No Appointments</p>
                                                @endforelse

                                                <div class="pagination-custom">
                                                    @php
                                                    echo $appointment_completed->links('pagination.custom');
                                                    @endphp
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @else
                        <a href="{{route('admin.doctor.account-verify-doctor',$doctor->id)}}"
                            class="menu-link px-3 btn btn-primary">
                            Approve Doctor
                        </a>

                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
