<?php

namespace App\Jobs;

use App\Http\CommonTraits\UploadMedia;
use App\Models\PatientLabTest;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class LabTestPdf implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UploadMedia;

    public $labTestId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($labTestId)
    {
        $this->labTestId = $labTestId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $labTest = PatientLabTest::with('patientLabTestDescription')->findOrFail($this->labTestId);
        $data = ['labTest' => $labTest];
        $contxt = stream_context_create([
            'ssl' => [
                'verify_peer' => FALSE,
                'verify_peer_name' => FALSE,
                'allow_self_signed' => TRUE,
            ]
        ]);
    
        $pdf = Pdf::setOptions(['isHTML5ParserEnabled' => true, 'isRemoteEnabled' => true]);
        $pdf->getDomPDF()->setHttpContext($contxt);
        $pdf->loadView('pdf.laborder', $data);
        $content = $pdf->download()->getOriginalContent();

        $url = self::pdfUpload($content, 'molema/labtest');

        $labTest->update(['lab_test_pdf' => $url]);

    }
}
