<?php

namespace App\Repositories;

use App\Enums\ReferStatus;
use App\Enums\ReferType;
use App\Enums\SenderType;
use App\Imports\DoctorInvitationImport;
use App\Jobs\SendEmails;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorInvitationRepository;
use App\Models\DoctorInvitation;
use App\Models\EmailTemplate;
use App\Validators\DoctorInvitationValidator;
use Exception;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\DataTables;

/**
 * Class DoctorInvitationRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorInvitationRepositoryEloquent extends BaseRepository implements DoctorInvitationRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorInvitation::class;
    }

    /**
     * Send Invitation to doctor
     *
     * @return string
     */
    public function refer($request){
        $data = $request->all();
        $data['user_id'] = auth()->user()->id;
        $data['user_type'] = SenderType::User;
        $data['invitation_code'] = generateInvitationCode();
        $data['type'] = ReferType::Invited();

        $invitation = DoctorInvitation::where('user_id', auth()->user()->id)->where('email', $request->email)->first();

        if(!empty($invitation)){
            throw new Exception(config('messages.invitation_already_sent'), 409);
        }

        return DoctorInvitation::create($data);
    }

    /**
     * Send Invitation valid
     *
     * @return string
     */
    public function inviteValidate($request){

        $invitation = DoctorInvitation::where('email', $request->email)
            ->where('status', ReferStatus::Approved)
            ->firstOrFail();

        $invitation = $invitation->where('invitation_code', $request->invitation_code)->first();
        if(empty($invitation)){
            throw new Exception(config('messages.verify_invitation_code_error'), 503);
        }
        $invitation->update(['status' => ReferStatus::Completed]);

        DoctorInvitation::where('email', $request->email)->where('id', '!=', $invitation->id)->update(['status' => ReferStatus::Expired]);

        return $invitation;
    }

    /**
     * Send Wishlist to admin
     *
     * @return string
     */
    public function wishList($request){
        $data = $request->all();
        $data['invitation_code'] = generateInvitationCode();
        $data['type'] = ReferType::WaitList();

        return DoctorInvitation::create($data);
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $invitation = DoctorInvitation::with('user', 'admin');

        if (!empty($request->start_date)) {
            $invitation = $invitation->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($invitation)
            ->editColumn('action', function ($row) {

                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>';
                if ($row->status != ReferStatus::Completed) {
                        $html .= '<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                        <div class="menu-item px-3">';

                    if ($row->status != ReferStatus::Approved) {
                        $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.doctor-invitation.change-status-approve') . '" id="' . $row->id . '">
                            Approved
                        </a>';
                    } if ($row->status != ReferStatus::Declined){
                        $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.doctor-invitation.change-status-decline') . '" id="' . $row->id . '">
                            Declined
                        </a>';
                    }
                }else{
                    $html = '';
                };
                return $html;
            })
            ->filterColumn('inviter_name', function ($query, $keyword) {
                return $query->whereHas('user', function ($q) use ($keyword) {
                    $q->searchByName($keyword);;
                });
            })
            ->orderColumn('inviter_name', function ($query, $order) {
                return $query->whereHas('user', function ($q) use ($order) {
                    $q->orderBy('first_name', $order);
                });
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     */
    public function changeStatusApproved($request)
    {
        $invitation = DoctorInvitation::findOrFail($request->input('id'));
        $invitation->update(['status' => ReferStatus::Approved]);

        $emailTemplate = EmailTemplate::where('slug', 'doctor_invitation_mail')->first();

        $emailBody = $emailTemplate->getTranslation('description', app()->getLocale());

        $email_content = str_replace("[[USERNAME]]", $invitation->name, $emailBody);
        $email_content = str_replace("[[CODE]]", $invitation->invitation_code, $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
            'email' => $invitation->email,
            'email_body' => $email_content,
            'subject' => $emailTemplate->getTranslation('title', app()->getLocale())
        ];


        dispatch((new SendEmails($user)));

    }

    /**
     * @param $request
     */
    public function changeStatusDeclined($request)
    {
        $invitation = DoctorInvitation::findOrFail($request->input('id'));
        $invitation->update(['status' => ReferStatus::Declined]);

    }

    /**
     * Send Invitation to doctor
     *
     * @return string
     */
    public function store($request)
    {
        $data = $request->all();
        $data['user_id'] = auth()->user()->id;
        $data['user_type'] = SenderType::Admin;
        $data['invitation_code'] = generateInvitationCode();
        $data['type'] = ReferType::Invited;
        $data['status'] = ReferStatus::Approved;

        $emailTemplate = EmailTemplate::where('slug', 'doctor_invitation_mail')->first();

        $emailBody = $emailTemplate->getTranslation('description', app()->getLocale());

        $email_content = str_replace("[[USERNAME]]", $request->name, $emailBody);
        $email_content = str_replace("[[CODE]]", $data['invitation_code'], $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
                'email' => $request->email,
                'email_body' => $email_content,
                'subject' => $emailTemplate->getTranslation('title', app()->getLocale())
            ];


        dispatch((new SendEmails($user)));

        return DoctorInvitation::create($data);
    }

    /**
     * Send Invitation to doctor
     *
     * @return string
     */
    public function import($request){

        return Excel::import(new DoctorInvitationImport, $request->file);
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
