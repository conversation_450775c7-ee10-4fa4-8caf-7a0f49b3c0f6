<?php

namespace App\Transformers;

use App\Models\Support;
use League\Fractal\TransformerAbstract;

class SupportTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Support $support)
    {
        return [
            'id' => $support->id,
            'user_id' => $support->user_id,
            'user_name' => $support->user->name,
            'user_type' => $support->user->user_type,
            'subject' => $support->subject,
            'description' => $support->description,
            'status' => $support->status,
            'type' => $support->type,
            'ref_id' => $support->ref_id,
            'unique_id' => $support->unique_id,
            'ref_unique_id' => $support->ref_unique_id,
            'created_at' => $support->created_at
        ];
    }
}
