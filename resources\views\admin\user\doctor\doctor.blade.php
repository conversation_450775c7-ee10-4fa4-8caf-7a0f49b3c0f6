@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($doctor))
    {{ Breadcrumbs::render('admin.doctor.edit') }}
    @else
    {{ Breadcrumbs::render('admin.doctor.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($doctor))
                        {{__('Edit Doctor Detail')}}
                        @else
                        {{__('Add Doctor Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.doctor.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i> 
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="profile_details" class="collapse show">
                    @if(!empty($doctor))
                    {{ Form::model($doctor, ['route' => ['admin.doctor.update', $doctor->id], 'method' =>
                    'post','class'=>'form','id'=>'doctor-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$doctor->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.doctor.store',
                    'method'=>'post','class'=>'form','id'=>'doctor-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('First Name', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('first_name',null, ['class' => 'form-control form-control-lg
                                    form-control-solid mb-3 mb-lg-0','id'=>'first_name','placeholder'=>'Enter first name']) !!}
                                    @if($errors->has('first_name'))
                                    <div id="first_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('first_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Last Name', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('last_name',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'last_name','placeholder'=>'Enter last name']) !!}
                                    @if($errors->has('last_name'))
                                    <div id="last_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('last_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('council_number', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('council_number',$doctor->doctorDetail->council_number ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'council_number','placeholder'=>'Enter Council Number']) !!}
                                    @if($errors->has('council_number'))
                                    <div id="council_number-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('council_number') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Mobile Number', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="row">
                                    <!--begin::Col-->

                                    <div class="col-lg-4 fv-row fv-plugins-icon-container">
                                        {!! Form::select('country_code',$countrycode,$doctor->country_code ?? null,['class' => 'form-select form-select-solid', 'data-control' => 'select2','id'=>'country_code']) !!}
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                        @if($errors->has('dial_code'))
                                        <div id="dial_code-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('dial_code') }}
                                        </div>
                                        @endif
                                    </div>
                                   
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-lg-8 fv-row fv-plugins-icon-container">
                                        {!! Form::text('mobile',null, ['class' => 'form-control form-control-lg
                                        form-control-solid','id'=>'mobile','placeholder'=>'Enter mobile']) !!}
                                        @if($errors->has('mobile'))
                                        <div id="mobile-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('mobile') }}
                                        </div>
                                        @endif
                                    </div>
                                    <!--end::Col-->
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Speciality', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::select('speciality[]',$specialities, !empty($doctor->speciality) ? $doctor->speciality : null, ['class' =>
                                    'form-select form-select-solid','id'=>'speciality','data-control' => 'select2',
                                    'data-hide-search'=>"true","multiple" => "Multiple",'data-placeholder' => "Select Specialities"]) !!}
                                    @if($errors->has('speciality'))
                                    <div id="speciality-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('speciality') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @php
                            $served_locations = config('constants.served_location');
                            $certificate_degree = config('constants.certificate_degree');
                            @endphp
                            <div class="col-lg-6">
                                {{ Form::label('served_location', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::select('served_location[]', $served_locations, !empty($doctor->doctorDetail->served_location) ? explode(",",$doctor->doctorDetail->served_location) : null,['id'=>'served_location',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true","multiple" => "Multiple", 'data-placeholder' => "Select Locations"]) !!}

                                    @if($errors->has('served_location'))
                                    <div id="served_location-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('served_location') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Email Address', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('email',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'email','placeholder'=>'Enter email']) !!}
                                    @if($errors->has('email'))
                                    <div id="email-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('email') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @if(empty($doctor))
                            <div class="col-md-3">
                                {{ Form::label('password', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="input-group">
                                    {!! Form::password('password', ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'password','placeholder'=>'Enter password']) !!}
                                    <i type="button" class="fa fa-eye check-eye" style="position: absolute;right: 45px;top: 17px;color: #7c7c7c;z-index: 999;"></i>
                                    <i type="button" class="fa fa-eye-slash uncheck-eye" style="position: absolute;right: 45px;top: 17px;color: #7c7c7c;z-index: 999;"></i> 
                                    @if($errors->has('password'))
                                    <div id="password-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('password') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-3">
                                {{ Form::label('', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                {!! Form::button('<i class="flaticon-refresh"></i> Generate Password', ['class' =>
                                'btn btn-light-primary mt-5','id'=>'generate']) !!}

                            </div>
                            @endif
                             <div class="col-lg-6">
                                {{ Form::label('Services & Procedures', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    <select name="services[]" class="form-control form-control-lg
                                    form-control-solid" placeholder="Add Services" multiple="multiple" id="service">
                                        @if(!empty($doctor->doctorDetail->service))     
                                            @foreach(explode(",",$doctor->doctorDetail->service) as $key => $value)
                                              <option selected="selected">{{str_replace( array( '[', ']','"'), '',$value)}}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    

                                    @if($errors->has('service'))
                                    <div id="service-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('service') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <hr class="mt-5">
                            <div class="d-flex flex-column mb-15 fv-row">
                                <!--begin::Label-->
                                <div class="fs-5 fw-bold form-label mb-3">Experience
                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true" data-bs-content="Add custom fields to the billing invoice." data-kt-initialized="1"></i></div>
                                <!--end::Label-->
                                <!--begin::Table wrapper-->
                                <div class="table-responsive">
                                    <!--begin::Table-->
                                    <div id="kt_create_new_custom_fields_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer"><div class="table-responsive"><table id="kt_create_new_custom_fields" class="table align-middle table-row-dashed fw-semibold fs-6 gy-5 dataTable no-footer">
                                        <!--begin::Table head-->
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0"><th class="pt-0 sorting_disabled" rowspan="1" colspan="1" style="width: 331.9px;">Employer Name</th><th class="pt-0 sorting_disabled" rowspan="1" colspan="1" style="width: 336.237px;">Exerience (Year)</th><th class="pt-0 sorting_disabled" rowspan="1" colspan="1" style="width: 336.237px;">Exerience (Month)</th><th class="pt-0 text-end sorting_disabled" rowspan="1" colspan="1" style="width: 79.8125px;">Remove</th></tr>
                                        </thead>
                                        <!--end::Table head-->
                                        <!--begin::Table body-->
                                        <tbody>

                                        @if(!empty($doctor->doctorExperience))
                                            @foreach($doctor->doctorExperience as $key => $value)
                                                <tr class="odd">
                                                <td>
                                                    <input type="text" id="employee_name" name="employee_name" class="form-control form-control-solid" value="{{$value->employer_name}}">
                                                </td>
                                                <td>
                                                    <input type="text" id="experience_year" name="experience_year" class="form-control form-control-solid" value="{{$value->years}}">
                                                </td>
                                                <td>
                                                    <input type="text" id="experience_month" name="experience_month" class="form-control form-control-solid" value="{{$value->months}}">
                                                </td>
                                                <td class="text-end">
                                                    <button type="button" class="btn btn-icon btn-flex btn-active-light-primary w-30px h-30px me-3" data-kt-action="field_remove">
                                                        <!--begin::Svg Icon | path: icons/duotune/general/gen027.svg-->
                                                        <span class="svg-icon svg-icon-3">
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path>
                                                                <path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path>
                                                                <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path>
                                                            </svg>
                                                        </span>
                                                        <!--end::Svg Icon-->
                                                    </button>
                                                </td>
                                            </tr>
                                            @endforeach
                                        @endif 
                                        <tr class="odd copy_content">
                                            <td>
                                                <input type="text" id="employee_name" name="employee_name" class="form-control form-control-solid" value="">
                                            </td>
                                            <td>
                                                <input type="text" id="experience_year" name="experience_year" class="form-control form-control-solid" value="">
                                            </td>
                                            <td>
                                                <input type="text" id="experience_month" name="experience_month" class="form-control form-control-solid" value="">
                                            </td>
                                            <td class="text-end">
                                                <button type="button" class="btn btn-icon btn-flex btn-active-light-primary w-30px h-30px me-3" data-kt-action="field_remove">
                                                    <!--begin::Svg Icon | path: icons/duotune/general/gen027.svg-->
                                                    <span class="svg-icon svg-icon-3">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path>
                                                            <path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path>
                                                            <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path>
                                                        </svg>
                                                    </span>
                                                    <!--end::Svg Icon-->
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                        <!--end::Table body-->
                                    </table></div><div class="row"><div class="col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start"></div><div class="col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end"></div></div></div>
                                    <!--end:Table-->
                                </div>
                                <!--end::Table wrapper-->
                                <!--begin::Add custom field-->
                                <button type="button" class="btn btn-light-primary me-auto" id="kt_create_new_custom_fields_add">Add new experience</button>
                                <!--end::Add custom field-->
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="d-flex flex-column mb-5 fv-row rounded-3 p-7 border border-dashed border-gray-300">
                                <!--begin::Label-->
                                <div class="fs-5 fw-bold form-label mb-3">Virtual Consultation
                                <i tabindex="0" class="cursor-pointer fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="popover" data-bs-trigger="hover focus" data-bs-html="true" data-bs-delay-hide="1000" data-bs-content="Thresholds help manage risk by limiting the unpaid usage balance a customer can accrue. Thresholds only measure and bill for metered usage (including discounts but excluding tax). <a href='#'>Learn more</a>." data-kt-initialized="1"></i></div>
                                <!--end::Label-->
                                <!--begin::Checkbox-->
                                <label class="form-check form-check-custom form-check-solid">
                                    @if(!empty($doctor->doctorDetail->virtual_consultation))
                                    <input class="form-check-input" id="virtual_consultation" type="checkbox" name="virtual_consultation" checked="checked" value="{{$doctor->doctorDetail->virtual_consultation ?? 0}}">
                                    @else
                                    <input class="form-check-input" id="virtual_consultation" type="checkbox" name="virtual_consultation" value="{{$doctor->doctorDetail->virtual_consultation ?? 0}}">
                                    @endif
                                    <span class="form-check-label text-gray-600 w-xxl-100">
                                        <div>
                                            {!! Form::text('virtual_consultation_price',$doctor->doctorDetail->virtual_consultation_price ?? null, ['class' =>
                                            'form-control form-control-lg
                                            form-control-solid','id'=>'virtual_consultation','placeholder'=>'Enter virtual consultation price']) !!}
                                            @if($errors->has('virtual_consultation'))
                                            <div id="virtual_consultation-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('virtual_consultation') }}
                                            </div>
                                            @endif
                                        </div>
                                    </span>
                                </label>
                                <!--end::Checkbox-->
                            </div>
                            <div class="d-flex flex-column mb-5 fv-row rounded-3 p-7 border border-dashed border-gray-300">
                                <!--begin::Label-->
                                <div class="fs-5 fw-bold form-label mb-3">Home Consultation
                                <i tabindex="0" class="cursor-pointer fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="popover" data-bs-trigger="hover focus" data-bs-html="true" data-bs-delay-hide="1000" data-bs-content="Thresholds help manage risk by limiting the unpaid usage balance a customer can accrue. Thresholds only measure and bill for metered usage (including discounts but excluding tax). <a href='#'>Learn more</a>." data-kt-initialized="1"></i></div>
                                <!--end::Label-->
                                <!--begin::Checkbox-->
                                <label class="form-check form-check-custom form-check-solid">
                                    @if(!empty($doctor->doctorDetail->home_consultation))
                                    <input class="form-check-input" id="home_consultation" type="checkbox" name="home_consultation" checked="checked" value="{{$doctor->doctorDetail->home_consultation ?? 0}}">
                                    @else
                                    <input class="form-check-input" id="home_consultation" type="checkbox" name="home_consultation" value="{{$doctor->doctorDetail->home_consultation ?? 0}}">
                                    @endif
                                    <span class="form-check-label text-gray-600 w-xxl-100">
                                        <div>
                                            {!! Form::text('home_consultation_price',$doctor->doctorDetail->home_consultation_price ?? null, ['class' =>
                                            'form-control form-control-lg
                                            form-control-solid','id'=>'home_consultation','placeholder'=>'Enter home consultation price']) !!}
                                            @if($errors->has('home_consultation'))
                                            <div id="home_consultation-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('home_consultation') }}
                                            </div>
                                            @endif
                                        </div>
                                    </span>
                                </label>
                                <!--end::Checkbox-->
                            </div>
                            <div class="d-flex flex-column mb-5 fv-row rounded-3 p-7 border border-dashed border-gray-300">
                                <!--begin::Label-->
                                <div class="fs-5 fw-bold form-label mb-3">Physical Consultation
                                <i tabindex="0" class="cursor-pointer fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="popover" data-bs-trigger="hover focus" data-bs-html="true" data-bs-delay-hide="1000" data-bs-content="Thresholds help manage risk by limiting the unpaid usage balance a customer can accrue. Thresholds only measure and bill for metered usage (including discounts but excluding tax). <a href='#'>Learn more</a>." data-kt-initialized="1"></i></div>
                                <!--end::Label-->
                                <!--begin::Checkbox-->
                                <label class="form-check form-check-custom form-check-solid">
                                    @if(!empty($doctor->doctorDetail->physical_consultation))
                                    <input class="form-check-input" type="checkbox" id="physical_consultation" name="physical_consultation" checked="checked" value="{{$doctor->doctorDetail->physical_consultation ?? 0}}">
                                    @else
                                    <input class="form-check-input" type="checkbox" id="physical_consultation" name="physical_consultation" value="{{$doctor->doctorDetail->physical_consultation ?? 0}}">
                                    @endif
                                    <span class="form-check-label text-gray-600 w-xxl-100">
                                        <div>
                                            {!! Form::text('physical_consultation_price',$doctor->doctorDetail->physical_consultation_price ?? null, ['class' =>
                                            'form-control form-control-lg
                                            form-control-solid','id'=>'physical_consultation','placeholder'=>'Enter physical consultation price']) !!}
                                            @if($errors->has('physical_consultation'))
                                            <div id="virtual_consultation_price-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('physical_consultation') }}
                                            </div>
                                            @endif
                                        </div>
                                    </span>
                                </label>
                                <!--end::Checkbox-->
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <!--begin::Col-->
                            <div class="col-lg-6">
                                {{ Form::label('Avatar', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6'])
                                }}
                                <div class="mb-5">
                                    <div class="image-input image-input-outline" data-kt-image-input="true"
                                        style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                        <div class="image-input-wrapper w-125px h-125px"
                                            style="background-image: url({{ !empty($doctor->avatar_url) ? $doctor->avatar_url : asset('assets/media/avatars/blank.png') }})">
                                        </div>
                                        <label
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            title="Change avatar">
                                            <i class="bi bi-pencil-fill fs-7"></i>
                                            {!! Form::file('avatar',['accept'=>'.png, .jpg, .jpeg']) !!}
                                            @if($errors->has('avatar'))
                                            <div id="avatar-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('avatar') }}
                                            </div>
                                            @endif
                                            <input type="hidden" name="avatar_remove" />
                                        </label>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            title="Cancel avatar">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            title="Remove avatar">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Signature', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6'])
                                }}
                                <div class="mb-5">
                                    <div class="image-input image-input-outline" data-kt-image-input="true"
                                        style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                        <div class="image-input-wrapper w-125px h-125px"
                                            style="background-image: url({{ !empty($doctor->doctorDetail->signature_url) ? $doctor->doctorDetail->signature_url : asset('assets/media/avatars/blank.png') }})">
                                        </div>
                                        <label
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            title="Change signature">
                                            <i class="bi bi-pencil-fill fs-7"></i>
                                            {!! Form::file('signature',['accept'=>'.png, .jpg, .jpeg']) !!}
                                            @if($errors->has('signature'))
                                            <div id="signature-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('signature') }}
                                            </div>
                                            @endif
                                            <input type="hidden" name="signature_remove" />
                                        </label>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            title="Cancel signature">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            title="Remove signature">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Education Certificate', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6'])
                                }}
                                <div class="mb-5">
                                    {!! Form::select('education_certi_text', $certificate_degree,
                                    !empty($doctor->doctorDocumentEducation) ? $doctor->doctorDocumentEducation->image_name : asset('assets/media/avatars/blank.png'),['id'=>'education_certi_text',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true"]) !!}
                                    <br>
                                    <br>
                                    <div class="image-input image-input-outline" data-kt-image-input="true"
                                        style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                        <div class="image-input-wrapper w-125px h-125px"
                                            style="background-image: url({{ !empty($doctor->doctorDocumentEducation) ? $doctor->doctorDocumentEducation->image : asset('assets/media/avatars/blank.png') }})">
                                        </div>
                                        <label
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            title="Change education certificate">
                                            <i class="bi bi-pencil-fill fs-7"></i>
                                            {!! Form::file('education_certi',['accept'=>'.png, .jpg, .jpeg']) !!}
                                            @if($errors->has('education_certi'))
                                            <div id="education_certi-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('education_certi') }}
                                            </div>
                                            @endif
                                            <input type="hidden" name="education_certi_remove" />
                                        </label>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            title="Cancel education certificate">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            title="Remove education certificate">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Other Document', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6'])
                                }}
                                <div class="mb-5">
                                    {!! Form::text('other_document_title', !empty($doctor->doctorDocumentOther) ? $doctor->doctorDocumentOther->image_name : null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'other_document_title','placeholder'=>'Enter Other Document Title']) !!}
                                    <br>
                                    {!! Form::text('other_document_description', !empty($doctor->doctorDocumentOther) ? $doctor->doctorDocumentOther->image_description : null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'other_document_description','placeholder'=>'Enter Other Document Description']) !!}
                                    <br>
                                    <div class="image-input image-input-outline" data-kt-image-input="true"
                                        style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                        <div class="image-input-wrapper w-125px h-125px"
                                            style="background-image: url({{ !empty($doctor->doctorDocumentOther->image) ? $doctor->doctorDocumentOther->image : asset('assets/media/avatars/blank.png') }})">
                                        </div>
                                        <label
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            title="Change other document">
                                            <i class="bi bi-pencil-fill fs-7"></i>
                                            {!! Form::file('other_document',['accept'=>'.png, .jpg, .jpeg']) !!}
                                            @if($errors->has('other_document'))
                                            <div id="other_document-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('other_document') }}
                                            </div>
                                            @endif
                                            <input type="hidden" name="other_document_remove" />
                                        </label>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            title="Cancel other document">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            title="Remove other document">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Language', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('language', languageArray(), $doctor->language ??
                                    null,['id'=>'language',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true"]) !!}

                                    @if($errors->has('language'))
                                    <div id="language-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('language') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Status', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('status', statusArray(), $doctor->status ?? null,['id'=>'status',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true"]) !!}
                                    @if($errors->has('status'))
                                    <div id="status-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('status') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Clinic Name', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('clinic_name', $doctor->doctorClinic->clinic_name ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_name','placeholder'=>'Enter clinic name']) !!}
                                    @if($errors->has('clinic_name'))
                                    <div id="clinic_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Apt/House No.', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('apartment_no', $doctor->doctorClinic->apartment_no ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'apartment_no','placeholder'=>'Enter house no.']) !!}
                                    @if($errors->has('apartment_no'))
                                    <div id="apartment_no-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('apartment_no') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Address', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('clinic_address',$doctor->doctorClinic->clinic_address ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_address','placeholder'=>'Enter clinic address']) !!}
                                    @if($errors->has('clinic_address'))
                                    <div id="clinic_address-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_address') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('clinic landmark', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('clinic_landmark',$doctor->doctorClinic->clinic_landmark ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_landmark','placeholder'=>'Enter clinic landmark']) !!}
                                    @if($errors->has('clinic_landmark'))
                                    <div id="clinic_landmark-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_landmark') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('clinic city', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('clinic_city',$doctor->doctorClinic->clinic_city ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_city','placeholder'=>'Enter clinic city']) !!}
                                    @if($errors->has('clinic_city'))
                                    <div id="clinic_city-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_city') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-3">
                                {{ Form::label('Province', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('clinic_state',$doctor->doctorClinic->clinic_state ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_state','placeholder'=>'Enter clinic state']) !!}
                                    @if($errors->has('clinic_state'))
                                    <div id="clinic_state-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_state') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('country', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('country',$doctor->doctorClinic->country ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'country','placeholder'=>'Enter country']) !!}
                                    @if($errors->has('country'))
                                    <div id="country-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('country') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('doctor type', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('doctor_type', doctortypeArray() , !empty($doctor->doctorDetail->doctor_type) ? $doctor->doctorDetail->doctor_type : null, ['class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true",'id'=>'doctor_type','placeholder'=>'Enter doctor type']) !!}
                                    @if($errors->has('country'))
                                    <div id="doctor_type-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('doctor_type') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-6">
                            <div class="col-lg-3">
                                {{ Form::label('clinic open time', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('clinic_open_time',$doctor->doctorClinic->clinic_open_time ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_open_time','placeholder'=>'Enter clinic open time']) !!}
                                    @if($errors->has('clinic_open_time'))
                                    <div id="clinic_open_time-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_open_time') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('clinic close time', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('clinic_close_time',$doctor->doctorClinic->clinic_close_time ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'clinic_close_time','placeholder'=>'Enter clinic close time']) !!}
                                    @if($errors->has('clinic_close_time'))
                                    <div id="clinic_close_time-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('clinic_close_time') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Clinic Mobile Number', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="row">
                                    <!--begin::Col-->
                                    <div class="col-lg-4 fv-row fv-plugins-icon-container">
                                        {!! Form::text('clinic_dial_code', $doctor->doctorClinic->clinic_dial_code ?? null, ['class' => 'form-control form-control-lg
                                        form-control-solid','id'=>'clinic_dial_code','placeholder'=>'Enter dial code']) !!}
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                        @if($errors->has('clinic_dial_code'))
                                        <div id="clinic_dial_code-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('clinic_dial_code') }}
                                        </div>
                                        @endif
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-lg-8 fv-row fv-plugins-icon-container">
                                        {!! Form::text('clinic mobile', $doctor->doctorClinic->clinic_mobile ?? null, ['class' => 'form-control form-control-lg
                                        form-control-solid','id'=>'clinic_mobile','placeholder'=>'Enter clinic mobile']) !!}
                                        @if($errors->has('clinic_mobile'))
                                        <div id="clinic_mobile-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('clinic_mobile') }}
                                        </div>
                                        @endif
                                    </div>
                                    <!--end::Col-->
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                            btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
<script type="text/javascript" src="{{asset('js/doctor.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\DoctorRequest', '#doctor-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\DoctorRequest', '#doctor-edit-form'); !!}
<script type="text/javascript" src="{{asset('assets/js/custom/apps/subscriptions/add/advanced.js')}}"></script>
<script type="text/javascript">
    $("#service").select2({
    tags: true,
    placeholder: "Add Services",
    multiple: true,
    tokenSeparators: [',',' ']
})
</script>
@endsection
