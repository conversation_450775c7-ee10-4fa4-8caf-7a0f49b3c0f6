<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DoctorSubscription;
use App\Models\EmailTemplate;
use App\Jobs\SendEmails​;
use Log;

class DoctorSubscriptionReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send Email notifications to doctor so subscription expiration reminder';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info("doctor subsciption reminder");

        $doctorSubscription = DoctorSubscription::whereNotNull('transaction_id')
            ->whereDate('subscription_end_date', '<', date('Y-m-d', strtotime("+5 days")))
            ->whereDate('subscription_end_date', '>', date('Y-m-d'))
            ->groupBy('user_id')
            ->chunk(50, function($doctorSubscription) {
                foreach ($doctorSubscription as $subscription) {
                    $doctorUser = $subscription->doctor;

                    $emailTemplate = EmailTemplate::where('slug', 'subscription_expiration_reminder')->first();
                    $emailBody = $emailTemplate->getTranslation('description', $doctorUser->language);
                    $email_content = str_replace("[[USERNAME]]", ucwords($doctorUser->name), $emailBody);
                    $email_content = str_replace("[[SUBSCRIPTION_ID]]", $subscription->transaction->transaction_id, $email_content);
                    $email_content = str_replace("[[PLAN_NAME]]", $subscription->subscription->name, $email_content);
                    $email_content = str_replace("[[EXPIREY_DATE]]", $subscription->subscription_end_date, $email_content);
                    $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

                    $datediff = strtotime($subscription->subscription_end_date) - strtotime(date('Y-m-d'));
                    $datediff = round($datediff / (60 * 60 * 24));
                    $subject = $emailTemplate->getTranslation('title', $doctorUser->language);
                    $subject = str_replace("[[DAYS]]", $datediff." Days", $subject);

                    $user = [
                        'email' => $doctorUser->email,
                        'email_body' => $email_content,
                        'subject' => $subject
                    ];

                    dispatch((new SendEmails​($user)));
                }
            });

        return 1;
    }
}
