<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\EmailTemplateRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class EmailTemplateController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "email-template";

    /**
     * @var EmailTemplateRepositoryEloquent|string
     */
    public $emailTemplateRepositoryEloquent = "";

    public function __construct(EmailTemplateRepositoryEloquent $emailTemplateRepositoryEloquent)
    {
        parent::__construct();
        $this->emailTemplateRepositoryEloquent = $emailTemplateRepositoryEloquent;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Email template']);
        return view('admin.email-template.index');
    }

    /**
     * Return faq list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->emailTemplateRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit faq list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Patient']);
        try {
            $faq = $this->emailTemplateRepositoryEloquent->edit($request, $id);
            return view('admin.settings.faqs.faq', compact('faq'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.faq.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.faq.index');
        }
    }

    /**
     * Update faq list.
     *
     * @param FaqRequest $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $faq = $this->faqRepository->update($request, $id);
            $request->session()->flash('success', config("messages.faq_updated"));
            return redirect()->route('admin.email-template.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.email-template.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.email-template.index');
        }
    }

}
