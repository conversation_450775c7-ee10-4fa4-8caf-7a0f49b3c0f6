<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;

class DoctorAppointment extends Model
{
    use HasFactory, TransformableTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'patient_id', 'doctor_id', 'appointment_date', 'start_time', 'end_time', 'consultation_type', 'appointment_status', 'payment_status', 'security_code', 'unique_id'
    ];

    protected $appends = ['created_date'];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * The doctor_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')->withTrashed();
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id', 'id')->withTrashed();
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function transaction()
    {
        return $this->hasOne(Transaction::class, 'payment_id');
    }

    public function appointmentRating()
    {
        return $this->hasMany(DoctorRating::class,'appointment_id');
    }

    public function getStartTimedAttribute()
    {
        return \Carbon\Carbon::createFromFormat('H:i:s',$this->start_time)->format('h:i A');
    }

    public function getEndTimedAttribute()
    {
        return \Carbon\Carbon::createFromFormat('H:i:s',$this->end_time)->format('h:i A');
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function patientNoteIllness()
    {
        return $this->hasOne(PatientNote::class, 'appointment_id')->where('subject', 'primary_health_complaint');
    }


}
