<?php

namespace App\Repositories;

use App\Http\Responses\Transformers\CustomArraySerializer;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorAvailabilityRepository;
use App\Models\DoctorAvailability;
use App\Transformers\DoctorAvailabilityTransformer;
use App\Validators\DoctorAvailabilityValidator;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Period\Period;
use Spatie\Period\Precision;
use Spatie\Period\Boundaries;

/**
 * Class DoctorAvailabilityRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorAvailabilityRepositoryEloquent extends BaseRepository implements DoctorAvailabilityRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorAvailability::class;
    }

    /**
     * Set Doctor Availability
     *
     * @return string
     */
    public function create($request)
    {
        $myData = [];
        $user_id = Auth::guard('api')->user()->id;
        if ($request->day) {
            $day = $request->day;
            for ($i=0; $i<count($day); $i++) {
                for ($j=$i+1; $j<count($day); $j++) {
                    $a = Period::make('1970-01-01 ' . $day[$i]['start_time'], '1970-01-01 ' .  $day[$i]['end_time'], Precision::MINUTE, Boundaries::EXCLUDE_END);
                    $b = Period::make('1970-01-01 ' .  $day[$j]['start_time'], '1970-01-01 ' .  $day[$j]['end_time'], Precision::MINUTE, Boundaries::EXCLUDE_END);
                    if ($a->overlapsWith($b)) {
                        throw new Exception(config('messages.availability_overlap'), 409);
                    }
                }
            }
            $doctorSlot = DoctorAvailability::where('user_id', $user_id)->where('slot', $request->slot)->delete();
            foreach ($request->day as $key => $day) {
                array_push($myData, [
                    'user_id' => $user_id,
                    'day' => $day['day'],
                    'start_time' => $day['start_time'],
                    'end_time' => $day['end_time'],
                    'slot' => $request->slot
                ]);
            }
            return DoctorAvailability::insert($myData);
        }
    }

    /**
     * view Doctor Availability
     *
     * @return string
     */
    public function view($request)
    {
        $user_id = Auth::guard('api')->user()->id;
        $doctorAvailability = DoctorAvailability::select('slot', DB::raw('count(*) as total'))->where('user_id', $user_id)->groupBy('slot')->get();

        $available = [];
        foreach ($doctorAvailability as $key => $availability) {
            $available[$key]['slot'] = $availability->slot;
            $doctorSlot = DoctorAvailability::where('user_id', $user_id)->where('slot', $availability->slot)->get();
            foreach ($doctorSlot as $key1 => $slot) {
                $available[$key]['day'][$key1]['day'] = $slot->day;
                $available[$key]['day'][$key1]['start_time'] = $slot->start_time;
                $available[$key]['day'][$key1]['end_time'] = $slot->end_time;
            }
        }
        return $available;
    }

    /**
     * delete Doctor Availability
     *
     * @return string
     */
    public function delete($request)
    {
        $user_id = Auth::guard('api')->user()->id;
        $doctorAvailability = DoctorAvailability::where('user_id', $user_id)->where('day', $request->day)->delete();

        return ['deleted_availability' => $doctorAvailability];
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
