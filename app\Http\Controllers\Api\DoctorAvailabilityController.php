<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\AvailabilityRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorAvailabilityRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class DoctorAvailabilityController extends Controller
{
    /**
     * @var DoctorAvailabilityRepositoryEloquent|string
     */
    public $doctorAvailabilityRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Doctor availability Controller constructor.
     * @param DoctorAvailabilityRepositoryEloquent $doctorAvailabilityRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorAvailabilityRepositoryEloquent $doctorAvailabilityRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->doctorAvailabilityRepository = $doctorAvailabilityRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function create(AvailabilityRequest $request)
    {
        try {
            $availability = $this->doctorAvailabilityRepository->create($request);
            return $this->apiResponse->respondWithMessage(trans('messages.availability_created'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '409') {
                return $this->apiResponse->respondResourceConflict($exception->getMessage());
            } else {
                return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function view(Request $request)
    {
        try {
            $availability = $this->doctorAvailabilityRepository->view($request);
            return $this->apiResponse->respondWithMessageAndPayload($availability, trans('messages.availability_fetching'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function delete(Request $request)
    {
        try {
            $availability = $this->doctorAvailabilityRepository->delete($request);
            return $this->apiResponse->respondWithMessageAndPayload($availability, trans('messages.availability_deleted'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
