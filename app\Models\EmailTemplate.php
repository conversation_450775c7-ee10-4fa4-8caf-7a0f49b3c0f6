<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class EmailTemplate extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = ['name', 'title', 'description', 'slug'];

    protected $appends = [
        'created_date'
    ];

    public $translatable = [
        'name',
        'title',
        'description'
    ];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

}
