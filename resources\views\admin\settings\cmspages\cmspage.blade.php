@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($cms))
    {{ Breadcrumbs::render('admin.settings.cmspages.edit') }}
    @else
    {{ Breadcrumbs::render('admin.settings.cmspages.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($cms))
                        {{__('Edit CMS Detail')}}
                        @else
                        {{__('Add CMS Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.cms.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="cms_details" class="collapse show">
                    @if(!empty($cms))
                    {{ Form::model($cms, ['route' => ['admin.cms.update', $cms->id], 'method' =>
                    'post','class'=>'form','id'=>'cms-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$cms->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.cms.store',
                    'method'=>'post','class'=>'form','id'=>'cms-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Title', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('title',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'title','placeholder'=>'Enter title
                                        name']) !!}
                                        @if($errors->has('title'))
                                        <div id="title-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('title') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Slug', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('slug',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'slug','placeholder'=>'Enter slug
                                        name', 'disabled'=>true]) !!}
                                        @if($errors->has('slug'))
                                        <div id="slug-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('slug') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Content', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::textarea('content',null, ['class' => 'summernote
                                        form-control','id'=>'content kt_docs_tinymce_hidden','placeholder'=>'Enter
                                        Content', 'data-msg'=>'The content field is required'])
                                        !!}
                                        @if($errors->has('content'))
                                        <div id="content-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('content') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::select('status', statusArray(), $cms->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script src="{{ asset('assets/plugins/custom/tinymce/tinymce.bundle.js') }}"></script>
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\CmsRequest', '#cms-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\CmsRequest', '#cms-edit-form'); !!}
<script>
   $(document).ready(function() {
        tinymce.init({
            selector: "textarea", height : "480",
            menubar: false,
            toolbar: ["styleselect fontselect fontsizeselect",
            "undo redo | cut copy paste | bold italic | alignleft aligncenter alignright alignjustify",
            ],
        });
    });
</script>
@endsection
