<?php

namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

class DoctorHomeScreenTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        return [
            'id' => $user->id,
            'user_type' => $user->user_type,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'avatar' => $user->avatar_url ?? "",
            'education_summary' => $user->doctorDetail->education_summary,
            'avg_rating' => $user->doctorRating->avg('rating'),
            'doctor_type' => $user->doctorDetail->doctor_type
        ];
    }
}
