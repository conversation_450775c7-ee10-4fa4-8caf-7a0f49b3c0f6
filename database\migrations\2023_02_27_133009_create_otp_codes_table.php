<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

/**
 * Class CreateOtpCodesTable.
 */
class CreateOtpCodesTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('otp_codes', function(Blueprint $table) {
            $table->increments('id');
            $table->string('code');
            $table->string('type');
            $table->bigInteger('user_id');
            $table->timestamp('expiry_date');
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('otp_codes');
	}
}
