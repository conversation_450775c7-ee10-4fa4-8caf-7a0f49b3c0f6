<?php

namespace App\Http\Requests;
use App\Models\Setting;

use Illuminate\Foundation\Http\FormRequest;

class DoctorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $virtual = Setting::where('slug','virtual-consultation-min-price')->pluck('value')->first() ?? 0;
        $home = Setting::where('slug','home-consultation-min-price')->pluck('value')->first() ?? 0;
        $physical = Setting::where('slug','physical-consultation-min-price')->pluck('value')->first() ?? 0;

        if(!empty(request()->id)){

            return [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => 'required|email|unique:users,email,' . request()->id . ',id,deleted_at,NULL',
                //'dial_code' => 'required|max:4|regex:/^(\+\d{1,3})$/',
                'country_code' => 'required',
                'mobile' => 'required|max:15|regex:/^(\d{6,14})$/|unique:users,mobile,' . request()->id . ',id,deleted_at,NULL',
                'virtual_consultation_price' => 'nullable|numeric|gt:'.$virtual,
                'home_consultation_price' => 'nullable|numeric|gt:'.$home,
                'physical_consultation_price' => 'nullable|numeric|gt:'.$physical,
                'clinic_open_time' => 'nullable|date_format:H:i:s',
                'clinic_close_time' => 'nullable|date_format:H:i:s|after:clinic_open_time',
                'experience_year.*' => 'nullable|integer',
                'experience_month.*' => 'nullable|integer',
                'clinic_dial_code' => 'nullable|max:4|regex:/^(\+\d{1,3})$/',
                'clinic mobile' => 'nullable|max:15|regex:/^(\d{6,14})$/'
            ]; 
        }else{
            return [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => 'required|email|unique:users,email,' . request()->id . ',id,deleted_at,NULL',
                'password' => 'required|min:8',
                //'dial_code' => 'required|max:4|regex:/^(\+\d{1,3})$/',
                'country_code' => 'required',
                'mobile' => 'required|max:15|regex:/^(\d{6,14})$/|unique:users,mobile,' . request()->id . ',id,deleted_at,NULL',
                'virtual_consultation_price' => 'nullable|numeric|gt:'.$virtual,
                'home_consultation_price' => 'nullable|numeric|gt:'.$home,
                'physical_consultation_price' => 'nullable|numeric|gt:'.$physical,
                'clinic_open_time' => 'nullable|date_format:H:i:s',
                'clinic_close_time' => 'nullable|date_format:H:i:s|after:clinic_open_time',
                'experience_year.*' => 'nullable|integer',
                'experience_month.*' => 'nullable|integer',
                'clinic_dial_code' => 'nullable|max:4|regex:/^(\+\d{1,3})$/',
                'clinic mobile' => 'nullable|max:15|regex:/^(\d{6,14})$/'
            ]; 
        }
    }
}
