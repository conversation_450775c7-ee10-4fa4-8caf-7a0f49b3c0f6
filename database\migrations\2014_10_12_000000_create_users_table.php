<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_type')->comment('1 = Patient, 2 = Doctor')->default(1);
            $table->string('first_name', 200)->nullable();
            $table->string('last_name', 200)->nullable();
            $table->string('username',200)->unique();
            $table->string('email', 200)->unique();
            $table->string('country_code')->nullable();
            $table->string('dial_code')->nullable();
            $table->string('mobile')->unique();
            $table->string('password')->nullable();
            $table->string('avatar')->nullable();
            $table->string('language')->comment('1 = es, 2 = en')->default(1);
            $table->boolean('status')->comment('1 = active, 0 = inactive')->default(1);
            $table->boolean('is_first_time_login')->comment('1 = yes, 0 = no')->default(1);
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('mobile_verified_at')->nullable();
            $table->string('provider_name')->nullable();
            $table->string('provider_id')->unique()->nullable();
            $table->integer('steps')->default(0);
            $table->rememberToken();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
