@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($speciality))
    {{ Breadcrumbs::render('admin.speciality.edit') }}
    @else
    {{ Breadcrumbs::render('admin.speciality.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($speciality))
                        {{__('Edit Symptoms Detail')}}
                        @else
                        {{__('Add Symptoms Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.speciality.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="speciality_details" class="collapse show">
                    @if(!empty($speciality))
                    {{ Form::model($speciality, ['route' => ['admin.speciality.update', $speciality->id], 'method' =>
                    'post','class'=>'form','id'=>'speciality-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$speciality->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.speciality.store',
                    'method'=>'post','class'=>'form','id'=>'speciality-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Image', null, ['class' => 'col-lg-4 col-form-label fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="image-input image-input-outline" data-kt-image-input="true"
                                    style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url({{ !empty($speciality->image_url) ?$speciality->image_url: asset('assets/media/avatars/blank.png') }})"></div>
                                    <label
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        title="Change avatar">
                                        <i class="bi bi-pencil-fill fs-7"></i>
                                        {!! Form::file('image',['accept'=>'.png, .jpg, .jpeg']) !!}
                                        @if($errors->has('image'))
                                        <div id="image-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('image') }}
                                        </div>
                                        @endif
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        title="Cancel avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        title="Remove avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                </div>
                                <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Specialist Name En', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('specialist_en', !empty($speciality) ? $speciality->getTranslation('specialist', 'en') : null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'specialist_en','placeholder'=>'Enter specialist english name']) !!}
                                        @if($errors->has('specialist_en'))
                                        <div id="specialist_en-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('specialist_en') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Specialist Name Es', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('specialist_es',!empty($speciality) ? $speciality->getTranslation('specialist', 'es') : null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'specialist_es','placeholder'=>'Enter specialist spanish name']) !!}
                                        @if($errors->has('specialist_es'))
                                        <div id="specialist_es-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('specialist_es') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Symptoms', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('symptoms_id',$symptoms, null, ['class' => 'form-control form-control-lg
                                        form-control-solid','id'=>'symptoms', 'data-control' => 'select2', 'placeholder' => 'Select Symptoms']) !!}
                                        @if($errors->has('symptoms'))
                                        <div id="symptoms-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('symptoms') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('status', statusArray(), $speciality->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\SpecialityRequest', '#speciality-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\SpecialityRequest', '#speciality-edit-form'); !!}
@endsection
