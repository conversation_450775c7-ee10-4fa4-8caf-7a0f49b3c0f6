<div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
    <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
        <span class="svg-icon svg-icon-2 svg-icon-md-1">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor" />
                <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor" />
            </svg>
        </span>
    </div>
</div>
<div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
    <a href="../../demo1/dist/index.html" class="d-lg-none">
    <img class="h-30px" src="{{ asset('assets/media/auth/logo.png') }}" alt="MOLEMA" />
    </a>
</div>
<div class="d-flex align-items-stretch justify-content-between" id="kt_app_header_wrapper">
    <div class="app-navbar flex-shrink-0">

        <div class="app-navbar-item ms-1 ms-md-3">
            <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-30px h-30px w-md-40px h-md-40px" id="kt_activities_toggle">
                <span class="svg-icon svg-icon-2 svg-icon-md-1">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="8" y="9" width="3" height="10" rx="1.5" fill="currentColor" />
                        <rect opacity="0.5" x="13" y="5" width="3" height="14" rx="1.5" fill="currentColor" />
                        <rect x="18" y="11" width="3" height="8" rx="1.5" fill="currentColor" />
                        <rect x="3" y="13" width="3" height="6" rx="1.5" fill="currentColor" />
                    </svg>
                </span>
                @php
                 use App\Models\User;
                    use App\Models\Medicine;
                    use App\Models\LabTest;

                    $doctors = User::whereHas('doctorDetail', function ($q) {
                        $q->whereNull('account_verified_at');
                    })->count();

                    $medicines = Medicine::with('doctor')
                        ->where('approve_status', 1)
                        ->count();

                    $labtest = LabTest::with('doctor')
                        ->where('approve_status', 1)
                        ->count();
                @endphp
                @if($doctors > 0 || $medicines > 0 || $labtest > 0)
                <span class="bullet bullet-dot bg-danger h-8px w-8px position-absolute translate-middle top-0 start-160 animation-blink" style="margin-top: 29px;margin-left: 16px;"></span>
                @endif
            </div>
        </div>

        <!--begin::User menu-->
            @include('admin.includes.notifications')
        <!--end::Notifications-->


        <div class="app-navbar-item ms-1 ms-md-3" id="kt_header_user_menu_toggle">
            <div class="cursor-pointer symbol symbol-30px symbol-md-40px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                <img src="{{ auth()->guard('admin')->user()->avatar_url }}" alt="user" />
            </div>
            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px" data-kt-menu="true">
                <div class="menu-item px-3">
                    <div class="menu-content d-flex align-items-center px-3">
                        <div class="symbol symbol-50px me-5">
                            <img alt="Logo" src="{{ auth()->guard('admin')->user()->avatar_url }}" />
                        </div>
                        <div class="d-flex flex-column">
                            <div class="fw-bold d-flex align-items-center fs-5">{{ auth()->guard('admin')->user()->first_name }} {{ auth()->guard('admin')->user()->last_name }}
                            <span class="badge badge-light-success fw-bold fs-8 px-2 py-1 ms-2">{{ auth()->guard('admin')->user()->username }}</span></div>
                            <a href="#" class="fw-semibold text-muted text-hover-primary fs-7">{{ auth()->guard('admin')->user()->email }}</a>
                        </div>
                    </div>
                </div>
                <div class="separator my-2"></div>
                <div class="menu-item px-5">
                    <a href="{{ route('admin.my-profile') }}" class="menu-link px-5">My Profile</a>
                </div>

                <div class="menu-item px-5">
                    <a href="javascript:void(0)" class="menu-link px-5"
                    onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Sign Out</a>
                    <form id="logout-form" method="POST" action="{{ route('logout') }}" style="display: none;">
                        @csrf
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>
