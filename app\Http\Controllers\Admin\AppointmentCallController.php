<?php

namespace App\Http\Controllers\Admin;

use App\Exports\AppointmentCallExport;
use App\Repositories\AppointmentCallRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AppointmentCallController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "call";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var AppointmentCallRepositoryEloquent|string
     */
    public $appointmentCallRepositoryEloquent = "";

    public function __construct(AppointmentCallRepositoryEloquent $appointmentCallRepositoryEloquent)
    {
        parent::__construct();
        $this->appointmentCallRepositoryEloquent = $appointmentCallRepositoryEloquent;
    }

    /**
     * Return List of Appointment
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Appointment Calls']);
        return view('admin.call.index');
    }



    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->appointmentCallRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * create a new appointments
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Appointment']);
        return view('admin.appointment.appointment');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $appointment = $this->appointmentCallRepositoryEloquent->store($request);
            $request->session()->flash('success', config("messages.appointment_created"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit appointment list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Appointment']);
        try {
            $appointment = $this->appointmentCallRepositoryEloquent->edit($request, $id);
            return view('admin.appointment.appointment', compact('appointment'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Update appointment list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $appointment = $this->appointmentCallRepositoryEloquent->update($request, $id);
            $request->session()->flash('success', config("messages.appointment_updated"));
            return redirect()->route('admin.appointment.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->appointmentCallRepositoryEloquent->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->appointmentCallRepositoryEloquent->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->appointmentCallRepositoryEloquent->destroy($request);
            $request->session()->flash('success', config("messages.appointment_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "call_" . time() . ".xlsx";
        return Excel::download(new AppointmentCallExport($request->all()), $fileName);
    }
}
