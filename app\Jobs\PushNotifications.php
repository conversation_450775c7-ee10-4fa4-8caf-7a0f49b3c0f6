<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\CommonTraits\PushNotification;
use LaravelFCM\Message\PayloadDataBuilder;
use Log;

class PushNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $receiver, $extra, $sender;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sender, $receiver, $extra)
    {
        $this->sender = $sender;
        $this->receiver = $receiver;
        $this->extra = $extra;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::error("======================started in  PushNotifications=======================");
        Log::info("receiver => ".$this->receiver->id);
        Log::info("sender => ".$this->sender);
        Log::info("message => ".$this->extra['message']);
        Log::info("title => ".$this->extra['title']);
        try{
            if ($tokens = PushNotification::getUsersToken([$this->receiver->id])) {
                $dataBuilder = new PayloadDataBuilder();
                $dataBuilder->addData($this->extra);
                $dataBuilder->addData(['click_action' => 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK']);
                //PushNotification::sendNotification($tokens, $this->extra['title'], $this->extra['message'], $this->sender, $dataBuilder);
                PushNotification::sendPushNotification($tokens, $this->extra['title'], $this->extra['message'], $this->sender);
            }
        }catch(\Exception $e){
            Log::error("======================Error in PushNotifications=======================");
            Log::error("Error => ".$e->getMessage());
            Log::error("File => ".$e->getFile());
            Log::error("Line => ".$e->getLine());
            Log::error("======================Error in PushNotifications End=======================");
        }


    }
}
