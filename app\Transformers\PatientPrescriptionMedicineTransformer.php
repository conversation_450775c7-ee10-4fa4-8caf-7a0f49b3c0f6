<?php

namespace App\Transformers;

use App\Models\PatientPrescriptionMedicine;
use League\Fractal\TransformerAbstract;

class PatientPrescriptionMedicineTransformer extends TransformerAbstract
{
    
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientPrescriptionMedicine $patientPrescriptionMedicine)
    {
        return [
            'id' => $patientPrescriptionMedicine->id,
            'medicine_id' => $patientPrescriptionMedicine->medicine_id,
            'medicine_name' => $patientPrescriptionMedicine->medicine_name,
            'medicine_type' => $patientPrescriptionMedicine->medicine_type,
            'medicine_course' => $patientPrescriptionMedicine->medicine_course,
            'medicine_duration' => $patientPrescriptionMedicine->medicine_duration,
            'medicine_quantity' => $patientPrescriptionMedicine->medicine_quantity,
            'medicine_direction' => $patientPrescriptionMedicine->medicine_direction,
            'created_at' => $patientPrescriptionMedicine->created_at
        ];
    }
}
