<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;

class DoctorClinic extends Model
{
    use HasFactory, TransformableTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'clinic_name',
        'apartment_no',
        'clinic_address',
        'clinic_landmark',
        'clinic_city',
        'clinic_state',
        'country',
        'clinic_dial_code',
        'clinic_mobile',
        'clinic_open_time',
        'clinic_close_time',
    ];
}
