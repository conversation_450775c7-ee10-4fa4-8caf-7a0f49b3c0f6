<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\AppointmentRequest;
use App\Http\Requests\UpdateCallStatusRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorAppointmentRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DoctorAppointmentController extends Controller
{
    /**
     * @var DoctorAppointmentRepositoryEloquent|string
     */
    public $doctorAppointmentRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Appointment Page Controller constructor.
     * @param DoctorAppointmentRepositoryEloquent $doctorAppointmentRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorAppointmentRepositoryEloquent $doctorAppointmentRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->doctorAppointmentRepository = $doctorAppointmentRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getTimeSlot(Request $request){
        try {
            $timeSlot = $this->doctorAppointmentRepository->getTimeSlot($request);
            return $this->apiResponse->respondWithMessageAndPayload($timeSlot, trans('messages.time_slot_created'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function createAppointment(AppointmentRequest $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->createAppointment($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_created'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '409') {
                return $this->apiResponse->respondResourceConflict($exception->getMessage());
            }
            \Log::info("Error => ".$exception->getMessage());
            \Log::info("File => ".$exception->getFile());
            \Log::info("Line => ".$exception->getLine());
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateAppointment(AppointmentRequest $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->updateAppointment($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_created'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '409') {
                return $this->apiResponse->respondResourceConflict($exception->getMessage());
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getBookingSummery(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->getBookingSummery($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_created'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function myBooking(Request $request, $name)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->myBooking($request, $name);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.booking_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function doctorBooking(Request $request, $name)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->doctorBooking($request, $name);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.booking_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function silentNotification(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->silentNotification($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.booking_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateOngoing(UpdateCallStatusRequest $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->updateOngoing($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.call_ongoing_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '401') {
                return $this->apiResponse->respondUnauthorized($exception->getMessage());
            } else {
                return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateCompleted(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->updateCompleted($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.call_completed_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function appointmentDetail(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->appointmentDetail($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_detail_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function callList(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->callList($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_detail_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function todayAppointment(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->todayAppointment($request);
            return $this->apiResponse->respondWithMessageAndPayload($appointment, trans('messages.appointment_detail_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
