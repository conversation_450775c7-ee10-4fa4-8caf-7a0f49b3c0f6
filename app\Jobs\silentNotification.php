<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use App\Http\CommonTraits\PushNotification;
use Illuminate\Support\Facades\Log;
use LaravelFCM\Message\PayloadDataBuilder;

class silentNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $receiver, $extra, $sender;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sender, $receiver, $extra)
    {
        $this->sender = $sender;
        $this->receiver = $receiver;
        $this->extra = $extra;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("||====================silentNotification start=====================||");
        if ($tokens = PushNotification::getUsersSilentToken([$this->receiver->id])) {
            Log::info("|| token available");
            $dataBuilder = new PayloadDataBuilder();
            $dataBuilder->addData($this->extra);
            $dataBuilder->addData(['click_action' => 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK']);
            $this->extra['click_action'] = 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK';
            foreach ($this->extra as $key => $value) {
                if (is_numeric($value)) {
                    $this->extra[$key] = (string) $value;
                }
            }
            Log::info("|| databuilder ready => " . json_encode($this->extra));
            PushNotification::sendSilentNotification($tokens, $this->extra);
            Log::info("|| silent push triggered");
        }
    }
}
