<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use App\Http\CommonTraits\PushNotification;
use Illuminate\Support\Facades\Log;
use LaravelFCM\Message\PayloadDataBuilder;

class silentNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $receiver, $extra, $sender;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sender, $receiver, $extra)
    {
        $this->sender = $sender;
        $this->receiver = $receiver;
        $this->extra = $extra;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("||====================silentNotification start=====================||");
        Log::info("|| Receiver ID: " . $this->receiver->id);
        Log::info("|| Sender ID: " . $this->sender->id);
        Log::info("|| Extra Data: " . json_encode($this->extra));

        try {
            $tokens = PushNotification::getUsersSilentToken([$this->receiver->id]);

            if ($tokens && !empty($tokens)) {
                Log::info("|| Tokens available: " . count($tokens));
                Log::info("|| Tokens: " . json_encode($tokens));

                $dataBuilder = new PayloadDataBuilder();
                $dataBuilder->addData($this->extra);
                $dataBuilder->addData(['click_action' => 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK']);
                $this->extra['click_action'] = 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK';

                // Convert numeric values to strings for FCM compatibility
                foreach ($this->extra as $key => $value) {
                    if (is_numeric($value)) {
                        $this->extra[$key] = (string) $value;
                    }
                }

                Log::info("|| Final payload data: " . json_encode($this->extra));

                PushNotification::sendSilentNotification($tokens, $this->extra);
                Log::info("|| Silent notification sent successfully");
            } else {
                Log::error("|| No tokens available for user: " . $this->receiver->id);
                Log::error("|| User device info may be missing or invalid");

                // Check if user device exists
                $userDevice = \App\Models\UserDevice::where('user_id', $this->receiver->id)->first();
                if ($userDevice) {
                    Log::info("|| User device found - ID: " . $userDevice->id);
                    Log::info("|| Push token: " . ($userDevice->push_token ? 'Available' : 'Not Available'));
                    Log::info("|| Device type: " . $userDevice->device_type);
                } else {
                    Log::error("|| No user device record found for user: " . $this->receiver->id);
                }
            }
        } catch (\Exception $e) {
            Log::error("|| Silent notification failed");
            Log::error("|| Error: " . $e->getMessage());
            Log::error("|| File: " . $e->getFile());
            Log::error("|| Line: " . $e->getLine());
            Log::error("|| Trace: " . $e->getTraceAsString());
            throw $e;
        }

        Log::info("||====================silentNotification end=======================||");
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error("|| Silent Notification Job Failed");
        Log::error("|| Error: " . $exception->getMessage());
        Log::error("|| File: " . $exception->getFile());
        Log::error("|| Line: " . $exception->getLine());
        Log::error("|| Receiver ID: " . $this->receiver->id);
        Log::error("|| Sender ID: " . $this->sender->id);
        Log::error("|| Extra Data: " . json_encode($this->extra));
    }
}
