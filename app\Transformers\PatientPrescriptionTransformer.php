<?php

namespace App\Transformers;

use App\Models\PatientPrescription;
use League\Fractal\TransformerAbstract;

class PatientPrescriptionTransformer extends TransformerAbstract
{

    protected array $defaultIncludes = ['patient_prescription_medicine'];

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientPrescription $patientPrescription)
    {
        return [
            'id' => $patientPrescription->id,
            'appointment_id' => $patientPrescription->appointment_id,
            'unique_id' => $patientPrescription->unique_id,
            'prescription_pdf'  => $patientPrescription->prescription_pdf
        ];
    }

    /**
     * @param PatientPrescription $patientPrescription
     * @return \League\Fractal\Resource\Collection
     */
    public function includePatientPrescriptionMedicine(PatientPrescription $patientPrescription)
    {
        return $this->collection($patientPrescription->patientPrescriptionMedicine, new PatientPrescriptionMedicineTransformer());
    }
}
