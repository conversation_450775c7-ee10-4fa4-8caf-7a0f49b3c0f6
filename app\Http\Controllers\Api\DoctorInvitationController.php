<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ReferRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorInvitationRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class DoctorInvitationController extends Controller
{
    /**
     * @var DoctorInvitationRepositoryEloquent|string
     */
    public $doctorInvitationRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Rating Page Controller constructor.
     * @param DoctorInvitationRepositoryEloquent $doctorInvitationRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorInvitationRepositoryEloquent $doctorInvitationRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->doctorInvitationRepositoryEloquent = $doctorInvitationRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function refer(ReferRequest $request)
    {
        try {
            $refer = $this->doctorInvitationRepositoryEloquent->refer($request);
            return $this->apiResponse->respondWithMessageAndPayload($refer, trans('messages.invitation_sent'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '409') {
                return $this->apiResponse->respondResourceConflict(trans('messages.invitation_already_sent'));
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function wishList(ReferRequest $request)
    {
        try {
            $wishList = $this->doctorInvitationRepositoryEloquent->wishList($request);
            return $this->apiResponse->respondWithMessageAndPayload($wishList, trans('messages.invitation_sent'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function inviteValidate(Request $request)
    {
        try {
            $wishList = $this->doctorInvitationRepositoryEloquent->inviteValidate($request);
            return $this->apiResponse->respondWithMessageAndPayload($wishList, trans('messages.invitation_sent'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            if ($exception->getCode() == '503') {
                return $this->apiResponse->respondServiceUnavailable(trans('messages.verify_invitation_code_error'), $exception);
            }
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
