<?php

namespace App\Repositories;

use App\Enums\DoctorType;
use App\Enums\Status;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\SendEmails​;
use App\Models\DoctorClinic;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorDetailRepository;
use App\Models\DoctorDetail;
use App\Models\DoctorDocument;
use App\Models\DoctorExperience;
use App\Models\DoctorSpeciality;
use App\Models\DoctorSubscription;
use App\Models\DoctorSymptom;
use App\Models\Setting;
use App\Models\Speciality;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserDevice;
use App\Transformers\DoctorAllDetailTransformer;
use App\Transformers\DoctorHomeScreenTransformer;
use App\Transformers\DoctorListTransformer;
use App\Transformers\SpecialityDetailTransformer;
use App\Transformers\UserAllDetailTransformer;
use App\Transformers\UserRegisterDetailTransformer;
use App\Validators\DoctorDetailValidator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use App\Models\DoctorAppointment;
use App\Enums\AppointmentStatus;
use App\Enums\Day;
use App\Enums\TransactionPaymentStatus;
use App\Enums\TransactionType;
use App\Models\Countries;
use App\Models\DoctorAvailability;
use App\Models\EmailTemplate;
use App\Models\Transaction;

/**
 * Class DoctorDetailRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorDetailRepositoryEloquent extends BaseRepository implements DoctorDetailRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorDetail::class;
    }

    /**
     * view doctor speciality API
     *
     * @var array
     */
    public function viewDoctorSpeciality($request)
    {
        $speciality = Speciality::where('status', Status::Active);

        if ($request->search) {
            $speciality = $speciality->where('specialist', 'LIKE', '%' . trim($request->search) . '%');
        }

        $speciality = $speciality->paginate(10);

        return fractal($speciality, new SpecialityDetailTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * update doctor speciality with symptoms API
     *
     * @var array
     */
    public function updateDoctorSpeciality($request)
    {
        if (!empty(Auth::guard('api')->user())) {
            $user = Auth::guard('api')->user();
        } else {
            $user = User::findOrFail($request->user_id);
        }
        // update doctor speciality
        $user->speciality()->sync($request->speciality);

        $data = $request->all();
        $data['user_id'] = $user->id;
        $data['service'] = json_encode($request->service);
        $data['education_summary'] = $request->education_summary;
        $userDetails = DoctorDetail::updateOrCreate(['user_id' => $user->id], $data);

        if($user->steps < 1){
            $user->update(['steps' => 1]);
        }

        return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * update doctor education with experience API
     *
     * @var array
     */
    public function updateEducationWithExperience($request)
    {
        if (!empty(Auth::guard('api')->user())) {
            $user = Auth::guard('api')->user();
        } else {
            $user = User::findOrFail($request->user_id);
        }

        $data = $request->all();
        $data['user_id'] = $user->id;
        $userDetails = DoctorDetail::updateOrCreate(['user_id' => $user->id], $data);

        self::updateExperience($request, $user);
        self::updateDocuments($request, $user);

        if($user->steps < 2){
            $user->update(['steps' => 2]);
        }

        return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * update doctor Documents API
     *
     * @var array
     */
    public function updateDocuments($request, $user)
    {
        $myFiles = [];
        if ($request->documents) {
            foreach ($request->documents as $key => $document) {
                $doctorDocument = DoctorDocument::where('user_id', $user->id)->where('image_id', $key+1)->first();
                if (!empty($document['image'])) {
                    $file = self::fileUpload($document['image'], '/doctor/document');
                }else{
                   $file = $doctorDocument->image;
                }
                array_push($myFiles, [
                    'user_id' => $user->id,
                    'image_id' => $key + 1,
                    'image_name' => $document['image_name'] ?? null,
                    'image_description' => $document['image_description'] ?? null,
                    'image' => $file ?? $doctorDocument->image
                ]);
            }
            return DoctorDocument::upsert($myFiles, ['user_id', 'image_id'], ['image_name', 'image_description', 'image']);
        }
    }

    /**
     * update doctor Experience API
     *
     * @var array
     */
    public function updateExperience($request, $user)
    {
        $myExperience = [];
        if ($request->experience) {
            DoctorExperience::where('user_id', $user->id)->delete();
            foreach ($request->experience as $key => $experience) {
                array_push($myExperience, [
                    'user_id' => $user->id,
                    'employer_name' => $experience['employer_name'] ?? null,
                    'years' => $experience['years'] ?? null,
                    'months' => $experience['months'] ?? null,
                ]);
            }
            return DoctorExperience::insert($myExperience);
        }
    }

    /**
     * update doctor consultation type API
     *
     * @var array
     */
    public function updateConsultationType($request)
    {
        if (!empty(Auth::guard('api')->user())) {
            $user = Auth::guard('api')->user();
        } elseif (!empty(Auth::guard('web')->user())) {
            $user = Auth::guard('web')->user();
        } else {
            $user = User::findOrFail($request->user_id);
        }

        $data = $request->all();
        $data['user_id'] = $user->id;
        $userDetails = DoctorDetail::updateOrCreate(['user_id' => $data['user_id']], $data);

        if($user->steps < 3){
            $user->update(['steps' => 3]);
        }

        // if (!empty(Auth::guard('api')->user())) {
            return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        // } else {
        //     Auth::guard('web')->loginUsingId($user->id);
        //     $user = fractal(Auth::guard('web')->user()->fresh(), new UserAllDetailTransformer())->serializeWith(new CustomArraySerializer());
        //     return array('user' => $user, 'access_token' => Auth::guard('web')->user()->createToken('Molema')->accessToken);
        // }
    }

    /**
     * update doctor clinic detail API
     *
     * @var array
     */
    public function updateClinicDetail($request)
    {
        if (!empty(Auth::guard('api')->user())) {
            $user = Auth::guard('api')->user();
        } else {
            $user = User::findOrFail($request->user_id);
        }

        $doctorClinic = DoctorClinic::updateOrCreate(['user_id' => $user->id], $request->all());

        if($user->steps < 4){
            $user->update(['steps' => 4]);
        }

        return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * update doctor education with experience API
     *
     * @var array
     */
    public function updateSignature($request)
    {
        $user = Auth::guard('api')->user();

        $data = $request->all();
        if ($request->has('signature')) {
            $data['signature'] =  self::signatureUpload($request, 'molema/doctor/signature');;
        }
        $userDetails = DoctorDetail::updateOrCreate(['user_id' => $user->id], $data);

        return fractal($user, new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * update doctor clinic detail API
     *
     * @var array
     */
    public function viewDoctorProfile($request)
    {
        return fractal(Auth::guard('api')->user(), new DoctorAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $user = User::with('doctorDetail')->whereHas('doctorDetail')->orderBy('created_at','desc');

        if (!empty($request->start_date)) {
            $user = $user->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($user)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.doctor.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.doctor.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                if ($row->doctorDetail->account_verified_at == null) {
                    $html .= '<a href="#" class="menu-link px-3 accountVerify" data-url="' . route('admin.doctor.account-verify') . '" id="' . $row->id . '">
                        Verified
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.doctor.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.doctor.view', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="view_row">
                            View
                        </a>
                    </div>
                    <div class="menu-item px-3">
                    <a class="menu-link px-3 delete" id="' . $row->id . '" data-name="' . $row->first_name . " " . $row->last_name . '" data-url="' . route('admin.doctor.destroy') . '" data-table="doctor_table"> Delete</a>
                    </div>
                </div>';
                return $html;
            })
            ->filterColumn('name', function ($query, $keyword) {
                return $query->searchByName($keyword);
            })
            ->filterColumn('account_verified', function ($query, $keyword) {
                if($keyword == "Verified"){
                    return $query->whereHas('doctorDetail', function($q){
                        $q->whereNotNull('account_verified_at');
                    });
                }
                else if($keyword == "Unverified"){
                    return $query->whereHas('doctorDetail', function($q){
                        $q->whereNull('account_verified_at');
                    });
                }
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('name', function ($query, $order) {
                $query->orderBy('first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();
        $data['password'] = bcrypt($request->password);
        $data['user_type'] = UserType::Doctor;
        $data['email_verified_at'] = now();
        $data['mobile_verified_at'] = now();
        $data['username'] = generateUserName(UserType::Doctor);
        $data['steps'] = 4;
        if(!empty($request->served_location)){
            $data['served_location'] = implode(',', $request->served_location);
        }

        if($request->country_code){
            $data['dial_code'] = Countries::where('iso',$request->country_code)->pluck('phonecode')->first();
        }
        if ($request->has('avatar')) {
            $data['avatar'] = self::avatarUpload($request, 'molema/user');
        }
        if ($request->has('education_certi')) {
            $data['education_certi'] = self::educationcertiUpload($request, 'molema/user');
        }
        if ($request->has('other_document')) {
            $data['other_document'] = self::otherdocumentUpload($request, 'molema/user');
        }
        if ($request->has('signature')) {
            $data['signature'] = self::signatureUpload($request, 'molema/user');
        }
        if($request->services){
            $data['service'] = '["'.implode('","', $request->services).'"]';
        }
        $pass = $request->password;

        $doctor = User::create($data);
        $data['user_id'] = $doctor->id;

        $speciality_detail = [];
        if(!empty($request->speciality)){
            foreach ($request->speciality as $key => $value) {
                $speciality_detail[] = [
                    'user_id' => $doctor->id,
                    'speciality_id' => $value
                ];
            }
        }

        $document_detail = [];
        if(!empty($request->education_certi)){
            $document_detail = [
                'user_id' => $doctor->id,
                'image_id' => 1,
                'image_name' => $request->education_certi_text,
                'image' => $data['education_certi']
            ];
            DoctorDocument::create($document_detail);
        }

        if(!empty($request->other_document)){
            $document_detail = [
                'user_id' => $doctor->id,
                'image_id' => 2,
                'image_name' => $request->other_document_title,
                'image_description' => $request->other_document_description,
                'image' => $data['other_document']
            ];
            DoctorDocument::create($document_detail);
        }

        $employee_detail = [];
        if(!empty($request->employee_name) && !empty($request->experience_year) & !empty($request->experience_month)){
            if($request->employee_name[0] != null){
                foreach ($request->employee_name as $key => $value) {
                    if($value != null){
                        $employee_detail[] = [
                            'user_id' => $doctor->id,
                            'employer_name' => $value,
                            'years' => !empty($request->experience_year[$key]) ? $request->experience_year[$key] : 0,
                            'months' => !empty($request->experience_month[$key]) ? $request->experience_month[$key] : 0
                        ];
                    }
                }
            }
        }

        DoctorSpeciality::insert($speciality_detail);
        DoctorExperience::insert($employee_detail);
        DoctorDetail::create($data);
        DoctorClinic::create($data);

        return $doctor;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $doctor = User::with('doctorDetail','doctorExperience','doctorClinic','doctorAvailability','speciality','doctorDocumentEducation','doctorDocumentOther')->findOrFail($id);

        return $doctor;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $doctor = User::findOrFail($id);
        $data = $request->all();

        $data['user_type'] = UserType::Doctor;
        $data['username'] = generateUserName(UserType::Doctor);

        if(!empty($request->served_location)){
            $data['served_location'] = implode(',', $request->served_location);
        }

        if ($request->has('avatar')) {
            $data['avatar'] = self::avatarUpload($request, 'molema/user', $doctor->id);
            self::deleteImg($doctor->getOriginal('avatar'));
        }
        if ($request->has('education_certi')) {
            $data['education_certi'] = self::educationcertiUpload($request, 'molema/user', $doctor->id);
            self::deleteImg($doctor->getOriginal('education_certi'));
        }
        if ($request->has('other_document')) {
            $data['other_document'] = self::otherdocumentUpload($request, 'molema/user', $doctor->id);
            self::deleteImg($doctor->getOriginal('other_document'));
        }
        if ($request->has('signature')) {
            $data['signature'] = self::signatureUpload($request, 'molema/user', $doctor->id);
            self::deleteImg($doctor->getOriginal('signature'));
        }
        if($request->country_code){
            $data['dial_code'] = Countries::where('iso',$request->country_code)->pluck('phonecode')->first();
        }
        if($request->services){
            $data['service'] = '["'.implode('","', $request->services).'"]';
        }
        $data['user_id'] = $doctor->id;

        $speciality_detail = [];
        if(!empty($request->speciality)){
            DoctorSpeciality::where('user_id', $doctor->id)->delete();
            foreach ($request->speciality as $key => $value) {
                $speciality_detail[] = [
                    'user_id' => $doctor->id,
                    'speciality_id' => $value
                ];
            }
        }

        $document_detail = [];
        if(!empty($request->education_certi)){
            $education = DoctorDocument::where('user_id',$doctor->id)->where('image_id',1)->first();
            $document_detail = [
                'user_id' => $doctor->id,
                'image_id' => 1,
                'image_name' => $request->education_certi_text,
                'image' => $data['education_certi']
            ];

            if(!empty($education)){
                DoctorDocument::where('user_id',$doctor->id)->where('image_id',1)->update($document_detail);
            }else{
                DoctorDocument::insert($document_detail);
            }
        }


        if(!empty($request->other_document)){
            $other = DoctorDocument::where('user_id',$doctor->id)->where('image_id',2)->first();
            $document_detail = [
                'user_id' => $doctor->id,
                'image_id' => 2,
                'image_name' => $request->other_document_title ?? 'Document',
                'image_description' => $request->other_document_description ?? 'Document',
                'image' => $data['other_document']
            ];
            if(!empty($other)){
                DoctorDocument::where('user_id',$doctor->id)->where('image_id',2)->update($document_detail);
            }else{
                DoctorDocument::insert($document_detail);
            }
        }

        $employee_detail = [];
        if(!empty($request->employee_name) && !empty($request->experience_year) & !empty($request->experience_month)){
            if($request->employee_name[0] != null){
                DoctorExperience::where('user_id',$doctor->id)->delete();
                foreach ($request->employee_name as $key => $value) {
                    if($value != null){
                        $employee_detail[] = [
                            'user_id' => $doctor->id,
                            'employer_name' => $value,
                            'years' => !empty($request->experience_year[$key]) ? $request->experience_year[$key] : 0,
                            'months' => !empty($request->experience_month[$key]) ? $request->experience_month[$key] : 0
                        ];
                    }
                }
            }
        }

        DoctorSpeciality::insert($speciality_detail);
        DoctorExperience::insert($employee_detail);
        $doctorDetail = DoctorDetail::where('user_id',$doctor->id)->first();
        if(!empty($doctorDetail)){
            $doctorDetail->updateOrCreate(['user_id' => $doctor->id], $data);
        }
        $doctorClinic = DoctorClinic::where('user_id',$doctor->id)->first();
        if(!empty($doctorClinic)){
            $doctorClinic->updateOrCreate(['user_id' => $doctor->id], $data);
        }
        $doctor->update($data);

        return $doctor;
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = User::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = User::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function accountVerify($request)
    {
        $doctor = DoctorDetail::where('user_id', $request->input('id'))->first();
        $user = User::findOrFail( $request->input('id'));

        $subscription = DoctorSubscription::create([
            'user_id' => $request->input('id'),
            'subscription_id' => Subscription::where('slug', 'free_trial')->first()->id,
            'transaction_id' => null,
            'subscription_start_date' => date("Y/m/d"),
            'subscription_end_date' => date('Y/m/d', strtotime("+".Subscription::where('slug', 'free_trial')->first()->validity." days"))
        ]);

        $transactions = Transaction::create([
            'doctor_id' => $user->id,
            'transaction_id' => generateTransactionId(TransactionType::getKey(3)),
            'payment_id' => $subscription->id,
            'payment_type' => TransactionType::getKey(3),
            'amount' => 0,
            'currency' => "USD",
            'payment_status' => TransactionPaymentStatus::Complete,
            'transaction_type' => TransactionType::Subscription
        ]);

        $subscription->update(['transaction_id' => $transactions->id]);

        $start_time = '10:00:00';
        $end_time = '18:00:00';

        $myData = [];
        //add doctor availability
        for ($i = 1; $i < 6; $i++) {
            array_push($myData, [
                'user_id' => $request->input('id'),
                'day' => $i,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'slot' => Day::getKey($i)
            ]);
        }

        DoctorAvailability::insert($myData);

        $doctor->update(['account_verified_at' => now(), 'doctor_type' => DoctorType::Collaborator]);

        $emailTemplate = EmailTemplate::where('slug', 'account_verification_mail')->first();

        $emailBody = $emailTemplate->getTranslation('description', $user->language);

        $email_content = str_replace("[[USERNAME]]", $user->name, $emailBody);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
            'email' => $user->email,
            'email_body' => $email_content,
            'subject' => $emailTemplate->getTranslation('title', $user->language)
        ];


        dispatch((new SendEmails​($user)));
    }

    public function accountVerifyDoctor($request,$id)
    {
        $doctor = DoctorDetail::where('user_id', $id)->first();
        $user = User::findOrFail($id);

        $subscription = DoctorSubscription::create([
            'user_id' => $id,
            'subscription_id' => Subscription::where('slug', 'free_trial')->first()->id,
            'transaction_id' => null,
            'subscription_start_date' => date("Y/m/d"),
            'subscription_end_date' => date('Y/m/d', strtotime("+30 days"))
        ]);

        $doctor->update(['account_verified_at' => now(), 'doctor_type' => DoctorType::Collaborator]);

        $emailTemplate = EmailTemplate::where('slug', 'account_verification_mail')->first();

        $emailBody = $emailTemplate->getTranslation('description', $user->language);

        $email_content = str_replace("[[USERNAME]]", $user->name, $emailBody);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
                'email' => $user->email,
                'email_body' => $email_content,
                'subject' => $emailTemplate->getTranslation('title', $user->language)
            ];


        dispatch((new SendEmails​($user)));
    }

    /**
     * @param $request
     */
    public function doctorInvitation($request)
    {
        $setting = Setting::where('slug', 'molema-doctor-sign-up-mode')->first();
        $data['value'] = $request->invitation;

        return $setting->update($data);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $user = User::findOrFail($request->input('id'));
        $data['email'] = $user->email . time();
        $data['mobile'] = $user->mobile . time();
        $data['provider_id'] = null;
        $data['provider_name'] = null;
        DoctorSpeciality::where('user_id', $user->id)->delete();
        DoctorDetail::where('user_id', $user->id)->delete();
        DoctorDocument::where('user_id', $user->id)->delete();
        DoctorClinic::where('user_id', $user->id)->delete();

        $user->update($data);
        return $user->delete();
    }

    /**
     * Store Device Info
     *
     * @param $request
     * @return mixed
     */
    public function storeDeviceInfo($request)
    {
        $data = $request->only('device_id', 'push_token', 'voip_token', 'device_type', 'os_version');
        $data['user_id'] = Auth::guard('api')->user()->id;

        return UserDevice::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    /**
     * Delete Education certification
     *
     * @param $request
     * @return mixed
     */
    public function deleteEducationDocument($request){
        $document =  DoctorDocument::where('user_id', $request->user_id)->where('image_id', $request->image_id)->firstOrFail();
        return $document->delete();
    }

    public function view($request, $id)
    {
        $doctor = User::with('doctorDetail','doctorClinic','doctorExperience','speciality','doctorDocument')->findOrFail($id);

        return $doctor;
    }

    public function booking($request, $id)
    {
        $appointment = DoctorAppointment::whereHas('transaction')->where('doctor_id', $id);
        $upcoming_appointment = clone($appointment);
        $ongoing_appointment = clone($appointment);
        $completed_appointment = clone($appointment);

        $appointment_upcoming = $upcoming_appointment->where('appointment_status', AppointmentStatus::Scheduled)->where('payment_status',1)->orderBy('created_at', 'DESC')->paginate(10);
        $appointment_ongoing = $ongoing_appointment->where('appointment_status', AppointmentStatus::Ongoing)->where('payment_status',1)->orderBy('created_at', 'DESC')->paginate(10);
        $appointment_completed = $completed_appointment->where('appointment_status', AppointmentStatus::Completed)->where('payment_status',1)->orderBy('created_at', 'DESC')->paginate(10);

        return [
            "appointment_upcoming" => $appointment_upcoming,
            "appointment_ongoing" => $appointment_ongoing,
            "appointment_completed" => $appointment_completed
        ];
    }

    /**
     * view specialist doctor
     *
     * @param $request
     * @return mixed
     */
    public function viewSpecialistDoctor($request)
    {
        $searchString = $request->search;

        $users = User::with(['speciality', 'doctorRating', 'speciality.symptom', 'doctorDetail', 'doctorAppointment'])
        ->where('user_type', UserType::Doctor)
        ->where('status', UserStatus::Active);

        if(!empty($request->symptom_name)){
            $symptomName = $request->symptom_name;
            $users = $users->whereHas('speciality.symptom', function ($query) use ($symptomName) {
                    $query->where('symptoms_name', 'like', '%' . $symptomName . '%');
            });
        }

        if(!empty($request->speciality_name)){
            $specialityName = $request->speciality_name;
            $users = $users->whereHas('speciality', function ($query) use ($specialityName) {
                    $query->where('specialist', 'like', '%' . $specialityName . '%');
            });
        }

        $users = $users->where(function ($query) use ($searchString) {
            $query->where('first_name', 'LIKE', '%' . $searchString . '%')
                ->orWhere('last_name', 'LIKE', '%' . $searchString . '%')
                ->orWhereHas('speciality', function ($query) use ($searchString) {
                    $query->where('specialist', 'like', '%' . $searchString . '%');
                })->orWhereHas('speciality.symptom', function ($query) use ($searchString) {
                    $query->where('symptoms_name', 'like', '%' . $searchString . '%');
                });
        });

        if (!empty($request->city_name)) {
            $cityName = $request->city_name;
            $users = $users->whereHas('doctorDetail', function ($query) use ($cityName) {
                $query->whereRaw('FIND_IN_SET("' . $cityName . '", served_location)')->whereNotNull('account_verified_at');
            });
        }
        else{
            $users = $users->whereHas('doctorDetail', function ($query) {
                $query->whereNotNull('account_verified_at');
            });
        }

        if(!empty($request->popular_doctor)){
            $users = $users->withCount('doctorAppointment')->orderBy('doctor_appointment_count', 'DESC')->orderBy('first_name')->paginate(10);
        }
        else{
            $users = $users->select(['users.*', 'doctor_details.doctor_type'])->join('doctor_details', 'doctor_details.user_id', '=', 'users.id')->orderBy('doctor_type')->orderBy('first_name')->paginate(10);
        }

        return fractal($users, new DoctorListTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Doctor About
     *
     * @param $request
     * @return mixed
     */
    public function doctorAbout($request, $id)
    {
        $doctor = User::findOrFail($id);
        return fractal($doctor, new DoctorListTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Doctor About
     *
     * @param $request
     * @return mixed
     */
    public function updateDoctorType($request)
    {
        $doctor = auth()->user();

        $doctorDetail = DoctorDetail::where('user_id', auth()->user()->id)->first();

        $doctorDetail->update(['doctor_type' => $request->doctor_type]);

        return fractal($doctor, new DoctorListTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Doctor About
     *
     * @param $request
     * @return mixed
     */
    public function updateStatus($request)
    {
        $doctor = auth()->user();

        $doctorDetail = DoctorDetail::where('user_id', auth()->user()->id)->first();

        $doctorDetail->update(['online_status' => $request->online_status]);

        return fractal($doctor, new DoctorListTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Doctor About
     *
     * @param $request
     * @return mixed
     */
    public function checkClinicUpdates($request)
    {
        $doctor = auth()->user();

        if(empty($doctor->doctorClinic)){
            return ['is_clinic_address_added' => 0];
        }else{
            return ['is_clinic_address_added' => 1];
        }
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
