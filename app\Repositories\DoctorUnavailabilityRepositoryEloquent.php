<?php

namespace App\Repositories;

use App\Models\DoctorAppointment;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorUnavailabilityRepository;
use App\Models\DoctorUnavailability;
use App\Validators\DoctorUnavailabilityValidator;
use Exception;
use Illuminate\Support\Facades\Auth;
use Spatie\Period\Period;
use Spatie\Period\Precision;
use DB;
use Spatie\Period\Boundaries;

/**
 * Class DoctorUnavailabilityRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorUnavailabilityRepositoryEloquent extends BaseRepository implements DoctorUnavailabilityRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorUnavailability::class;
    }

    /**
     * Set Doctor Unavailability
     *
     * @return string
     */
    public function create($request)
    {
        $myData = [];
        $user_id = Auth::guard('api')->user()->id;
        $bookedTimes = DoctorAppointment::where('appointment_date', $request->unavailable_date)->where('doctor_id', $user_id)->get();
        foreach ($bookedTimes as $booked) {
            foreach ($request->day as $key => $day) {
                $e = Period::make($request->unavailable_date . " " . $day['start_time'], $request->unavailable_date . " " . $day['end_time'], Precision::HOUR);
                $f = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::HOUR);
                if ($e->overlapsWith($f)) {
                    $g = Period::make($request->unavailable_date . " " . $day['start_time'], $request->unavailable_date . " " . $day['end_time'], Precision::MINUTE);
                    $h = Period::make($booked->appointment_date . " " . $booked->start_time, $booked->appointment_date . " " . $booked->end_time, Precision::MINUTE);
                    if ($g->overlapsWith($h)) {
                        throw new Exception(config('messages.appointment_overlap'), 409);
                    }
                }
            }
        }

        if ($request->day) {
            $day = $request->day;
            for ($i=0; $i<count($day); $i++) {
                for ($j=$i+1; $j<count($day); $j++) {
                    $a = Period::make('1970-01-01 ' . $day[$i]['start_time'], '1970-01-01 ' .  $day[$i]['end_time'], Precision::MINUTE, Boundaries::EXCLUDE_END);
                    $b = Period::make('1970-01-01 ' .  $day[$j]['start_time'], '1970-01-01 ' .  $day[$j]['end_time'], Precision::MINUTE, Boundaries::EXCLUDE_END);
                    if ($a->overlapsWith($b)) {
                        throw new Exception(config('messages.availability_overlap'), 409);
                    }
                }
            }
            $doctorSlot = DoctorUnavailability::where('user_id', $user_id)->where('unavailable_date', $request->unavailable_date)->delete();
            foreach ($request->day as $key => $day) {
                array_push($myData, [
                    'user_id' => $user_id,
                    'unavailable_date' => $request->unavailable_date,
                    'start_time' => $day['start_time'],
                    'end_time' => $day['end_time'],
                    'leave_status' => $request->leave_status
                ]);
            }
            return DoctorUnavailability::insert($myData);
        } else {
            $data = $request->all();
            $data['user_id'] = $user_id;
            return DoctorUnavailability::updateOrCreate(['user_id' => $user_id, 'unavailable_date' => $request->unavailable_date], $data);
        }
    }

    /**
     * view Doctor unavailability
     *
     * @return string
     */
    public function view($request)
    {
        $user_id = Auth::guard('api')->user()->id;
        $doctorAvailability = DoctorUnavailability::select('unavailable_date', DB::raw('count(*) as total'))->where('user_id', $user_id)->where('unavailable_date', '>=', date("Y-m-d"))->groupBy('unavailable_date')->get();

        $available = [];
        foreach ($doctorAvailability as $key => $availability) {
            $available[$key]['unavailable_date'] = $availability->unavailable_date;
            $doctorSlot = DoctorUnavailability::where('user_id', $user_id)->where('unavailable_date', $availability->unavailable_date)->where('unavailable_date', '>=', date("Y-m-d"))->get();
            foreach ($doctorSlot as $key1 => $slot) {
                $available[$key]['leave_status'] = $slot->leave_status;
                if ($slot->leave_status == 0) {
                    $available[$key]['day'][$key1]['start_time'] = $slot->start_time;
                    $available[$key]['day'][$key1]['end_time'] = $slot->end_time;
                }
            }
        }
        return $available;
    }

    /**
     * view Doctor unavailability
     *
     * @return string
     */
    public function delete($request)
    {
        return DoctorUnavailability::where('user_id', Auth::guard('api')->user()->id)->where('unavailable_date', $request->unavailable_date)->delete();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
