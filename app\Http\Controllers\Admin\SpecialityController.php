<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\SpecialityRequest;
use App\Models\Symptom;
use App\Repositories\SpecialityRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class SpecialityController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "speciality";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var PatientDetailRepositoryEloquent|string
     */
    public $specialityRepository = "";

    public function __construct(SpecialityRepositoryEloquent $specialityRepository)
    {
        parent::__construct();
        $this->specialityRepository = $specialityRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Speciality']);

        return view('admin.speciality.index');
    }

    /**
     * Return speciality list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->specialityRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Speciality']);
        $symptoms = Symptom::pluck('symptoms_name', 'id')->toArray();
        return view('admin.speciality.speciality', compact('symptoms'));
    }

    /**
     * @param SpecialityRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SpecialityRequest $request)
    {
        try {
            $speciality = $this->specialityRepository->store($request);
            $request->session()->flash('success', config("messages.speciality_created"));
            return redirect()->route('admin.speciality.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit speciality list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Speciality']);
        try {
            $speciality = $this->specialityRepository->edit($request, $id);
            $symptoms = Symptom::pluck('symptoms_name', 'id')->toArray();
            return view('admin.speciality.speciality', compact('speciality', 'symptoms'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.speciality.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.speciality.index');
        }
    }

    /**
     * Update speciality list.
     *
     * @param SpecialityRequest $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(SpecialityRequest $request, $id)
    {
        try {
            $speciality = $this->specialityRepository->update($request, $id);
            $request->session()->flash('success', config("messages.speciality_updated"));
            return redirect()->route('admin.speciality.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.speciality.index');
        } catch (Exception $exception) {
            dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.speciality.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->specialityRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.speciality.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->specialityRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.speciality.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->specialityRepository->destroy($request);
            $request->session()->flash('success', config("messages.speciality_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.speciality.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.speciality.index');
        }
    }

    /**
     * @return Response
     */
    public static function export()
    {
        return Excel::download(new PatientExport, 'speciality.xlsx');
    }
}
