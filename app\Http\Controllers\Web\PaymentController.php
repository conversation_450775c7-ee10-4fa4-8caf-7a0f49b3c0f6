<?php

namespace App\Http\Controllers\Web;

use App\Enums\DoctorType;
use App\Enums\TransactionPaymentStatus;
use App\Enums\TransactionType;
use App\Http\Controllers\Controller;
use App\Models\DoctorAppointment;
use App\Enums\PaymentStatus;
use App\Models\DoctorDetail;
use App\Models\DoctorSubscription;
use App\Models\Subscription;
use App\Models\Transaction;
use Exception;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Cookie\SetCookie;
use GuzzleHttp\TransferStats;
use Illuminate\Support\Facades\Redirect;
use App\Repositories\TransactionRepositoryEloquent;
use App\Repositories\SubscriptionRepositoryEloquent;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * @var TransactionRepositoryEloquent
     */
    protected $transactionRepositoryEloquent;
    protected $subscriptionRepositoryEloquent;

    /**
     * SymptomController constructor.
     * @param TransactionRepositoryEloquent $transactionRepositoryEloquent
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(TransactionRepositoryEloquent $transactionRepositoryEloquent, SubscriptionRepositoryEloquent $subscriptionRepositoryEloquent)
    {
        $this->transactionRepositoryEloquent = $transactionRepositoryEloquent;
        $this->subscriptionRepositoryEloquent = $subscriptionRepositoryEloquent;
    }

    public function purchase(Request $request, $locale = null, $id)
    {
        try {
            $subscription = Subscription::findOrFail($id);
            $client = new Client();

            $subscriptionApply = DoctorSubscription::where('user_id', auth()->user()->id)
                ->orderBy('subscription_end_date', 'DESC')->whereNotNull('transaction_id')->first();


            if (empty($subscriptionApply) || $subscriptionApply->subscription_end_date < date('Y-m-d')) {
                $subscription_start_date = date('Y-m-d');
            } else {
                $subscription_start_date = date('Y-m-d', strtotime($subscriptionApply->subscription_end_date  . ' +1 day'));
            }

            $doctorSubscription = DoctorSubscription::updateOrCreate([
                'user_id' => auth()->user()->id,
                'transaction_id' => null,
            ], [
                'user_id' => auth()->user()->id,
                'subscription_id' => $subscription->id,
                'transaction_id' => null,
                'subscription_start_date' => $subscription_start_date,
                'subscription_end_date' => date('Y-m-d', strtotime($subscription_start_date . ' + ' . $subscription->validity . ' days'))
            ]);


            $requestId = generateRequestId($doctorSubscription->user_id);
            $language = 'en';
            $price = $subscription->price;
            $orderInfo = generateOrderInfo();
            // $price = "24";

            $params = [
                'profile_id' => env('PROFILE_ID'),
                'access_key' => env('ACCESS_CODE'),
                'transaction_uuid' => uniqid(),
                'signed_date_time' => gmdate('Y-m-d\TH:i:s\Z'),
                'signed_field_names' => "profile_id,access_key,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,auth_trans_ref_no,amount,currency,merchant_descriptor,payment_method,override_custom_cancel_page,override_custom_receipt_page",
                'unsigned_field_names' => 'signature,bill_to_forename,bill_to_surname,bill_to_email,bill_to_phone,bill_to_address_line1,bill_to_address_line2,bill_to_address_city,bill_to_address_state,bill_to_address_country,bill_to_address_postal_code,customer_ip_address,merchant_defined_data1,merchant_defined_data2,merchant_defined_data3,merchant_defined_data4',
                'payment_method' => 'card',
                'transaction_type' => 'sale',
                'reference_number' => $requestId,
                'auth_trans_ref_no' => $requestId,
                'amount' => $price,
                'currency' => 'XAF',
                'locale' => $language,
                'merchant_descriptor' => "Misalud",
                'bill_to_address_line1' => "1295 Charleston Rd",
                'bill_to_address_city' => "Mountain View",
                'bill_to_address_country' => "US",
                'bill_to_email' => auth()->user()->email,
                'bill_to_surname' => $subscriptionApply->doctor->last_name,
                'bill_to_forename' => $subscriptionApply->doctor->first_name,
                'bill_to_phone' => "",
                'bill_to_address_line2' => "",
                'bill_to_address_state' => "CA",
                'bill_to_address_postal_code' => "94043",
                'customer_ip_address' => "127.0.0.1",
                'merchant_defined_data1' => "",
                'merchant_defined_data2' => "",
                'merchant_defined_data3' => "",
                'merchant_defined_data4' => "",
                'override_custom_cancel_page' => url('/web/ecobank/fail-response?plateform=web'),
                'override_custom_receipt_page' => url('/web/ecobank/success-response?plateform=web')
            ];
            $params['signature'] = Transaction::sign($params);

            \Log::info("params Body => " . json_encode($params));

            $transaction = Transaction::updateOrCreate([
                'doctor_id' => $doctorSubscription->user_id,
                'payment_status' => TransactionPaymentStatus::Init,
            ], [
                'patient_id' => null,
                'doctor_id' => $doctorSubscription->user_id,
                'request_id' => $requestId,
                'payment_id' => $doctorSubscription->id,
                'order_info' => $orderInfo,
                'secure_hash' => $params['signature'],
                'transaction_id' => generateTransactionId(TransactionType::getKey(3)),
                'payment_type' => TransactionType::Subscription,
                'amount' => $price,
                'currency' => config('constants.currency'),
                'payment_status' => TransactionPaymentStatus::Init,
                'transaction_type' => TransactionType::Subscription,
                'payment_channel' => 1
            ]);

            $doctorDetail =  DoctorDetail::where('user_id', $doctorSubscription->user_id)->update(['doctor_type' => DoctorType::Collaborator]);

            return view('web.confirm', compact('params', 'requestId', 'orderInfo', 'price', 'doctorSubscription'));
        } catch (Exception $e) {
            Log::error("===================================Error in purchase===============================");
            Log::error("Error => " . $e->getMessage());
            Log::error("Line => " . $e->getLine());
            Log::error("File => " . $e->getFile());
            Log::error("=====================================================================================");
            return redirect()->back();
        }
    }

    public function ecobankSuccessResponse(Request $request)
    {
        try {
            $response = $_POST;

            $transaction = Transaction::where([
                'request_id' => $response['req_reference_number'],
            ])->first();

            if (!empty($transaction)) {

                $transactionStatus = TransactionPaymentStatus::Init;
                if (isset($response['decision']) && $response['decision'] == "ACCEPT") {
                    $transactionStatus = TransactionPaymentStatus::Complete;
                } elseif (isset($response['decision']) && $response['decision'] == "DECLINE") {
                    $transactionStatus = TransactionPaymentStatus::Fail;
                }

                if ($transaction->payment_status == TransactionPaymentStatus::Init) {
                    $transaction->update([
                        'payment_status' => $transactionStatus,
                        'response_message' => isset($response['message']) ? $response['message'] : "",
                        'decision_code' => isset($response['decision_return_code']) ? $response['decision_return_code'] : "",
                        'decision' => isset($response['decision']) ? $response['decision'] : "",
                        'channel_transaction_id' => isset($response['transaction_id']) ? $response['transaction_id'] : "",
                    ]);

                    if (isset($response['decision']) && $response['decision'] == "ACCEPT") {
                        if ($transaction->payment_type == TransactionType::Subscription) {
                            $params = (object)[
                                'transaction_id' => $transaction->transaction_id,
                                'subscription_id' => $transaction->doctorSubscription->subscription_id
                            ];
                            $subscription = $this->subscriptionRepositoryEloquent->subscriptionComplete($params);
                        } elseif ($transaction->payment_type == TransactionType::Consultation) {
                            $transaction->doctorAppointment->update(['payment_status' => PaymentStatus::completed]);
                            $params = (object)[
                                'transaction_id' => $transaction->transaction_id,
                                'payment_status' => TransactionPaymentStatus::Complete
                            ];
                            $transactionArray = $this->transactionRepositoryEloquent->updatePaymentStatus($params);
                        }
                    }
                }

                if ($request->get('plateform') == "web") {
                    return redirect('en/web/subscription');
                }
                if (isset($response['decision']) && $response['decision'] == "DECLINE") {
                    if ($request->get('plateform') == "app") {
                        return redirect('web/ecobank-redirection?status=fail');
                    }
                } elseif (isset($response['decision']) && $response['decision'] == "ACCEPT") {
                    if ($request->get('plateform') == "app") {
                        return redirect('web/ecobank-redirection?status=success');
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("===================================Error in ecobankSuccessResponse===============================");
            Log::error("Error => " . $e->getMessage());
            Log::error("Line => " . $e->getLine());
            Log::error("File => " . $e->getFile());
            Log::error("=====================================================================================");
            if ($request->get('plateform') == "app") {
                return redirect('web/ecobank-redirection?status=fail');
            }
        }
    }

    public function ecobankFailedResponse(Request $request)
    {
        try {
            $response = $_POST;


            $transaction = Transaction::where([
                'request_id' => $response['req_reference_number'],
            ])->first();

            if (!empty($transaction)) {

                if ($transaction->payment_status == TransactionPaymentStatus::Init) {
                    $transaction->update([
                        'payment_status' => TransactionPaymentStatus::Fail,
                        'response_message' => isset($response['message']) ? $response['message'] : "",
                        'decision_code' => isset($response['decision_return_code']) ? $response['decision_return_code'] : "",
                        'decision' => isset($response['decision']) ? $response['decision'] : "",
                        'channel_transaction_id' => isset($response['transaction_id']) ? $response['transaction_id'] : "",
                    ]);
                }

                if ($request->get('plateform') == "web") {
                    return redirect('en/web/subscription');
                }
                if ($request->get('plateform') == "app") {
                    return redirect('web/ecobank-redirection?status=fail');
                }
            }
        } catch (\Exception $e) {
            Log::error("===================================Error in ecobankFailedResponse===============================");
            Log::error("Error => " . $e->getMessage());
            Log::error("Line => " . $e->getLine());
            Log::error("File => " . $e->getFile());
            Log::error("=====================================================================================");
            if ($request->get('plateform') == "app") {
                return redirect('web/ecobank-redirection?status=fail');
            }
        }
    }

    public function smswebhook(Request $request){
        Log::info('Incoming Headers:', $request->headers->all());
        Log::info('Incoming Request Body:', $request->all());
        Log::info('Raw Body:', ['raw' => $request->getContent()]);
        return response()->json([
            'headers' => $request->headers->all(),
            'body' => $request->all(),
            'raw' => $request->getContent(),
        ]);
    }

    public function ecobankWebhook(Request $request)
    {
        try {
            \Log::info('==========ecobankWebhook called==============');
            \Log::info("Request Payload => " . json_encode($_POST));
            $response = $_POST;

            $transaction = Transaction::where([
                'request_id' => $response['req_reference_number'],
            ])->first();

            if (!empty($transaction)) {

                if ($transaction->payment_status == TransactionPaymentStatus::Init) {

                    $transactionStatus = TransactionPaymentStatus::Init;
                    if (isset($response['decision']) && $response['decision'] == "ACCEPT") {
                        $transactionStatus = TransactionPaymentStatus::Complete;
                    } elseif (isset($response['decision']) && $response['decision'] == "DECLINE") {
                        $transactionStatus = TransactionPaymentStatus::Fail;
                    }

                    $transaction->update([
                        'payment_status' => $transactionStatus,
                        'response_message' => isset($response['message']) ? $response['message'] : "",
                        'decision_code' => isset($response['decision_return_code']) ? $response['decision_return_code'] : "",
                        'decision' => isset($response['decision']) ? $response['decision'] : "",
                        'channel_transaction_id' => isset($response['transaction_id']) ? $response['transaction_id'] : "",
                    ]);

                    if (isset($response['decision']) && $response['decision'] == "ACCEPT") {
                        if ($transaction->payment_type == TransactionType::Subscription) {
                            $params = (object)[
                                'transaction_id' => $transaction->transaction_id,
                                'subscription_id' => $transaction->doctorSubscription->subscription_id
                            ];
                            $subscription = $this->subscriptionRepositoryEloquent->subscriptionComplete($params);
                        } elseif ($transaction->payment_type == TransactionType::Consultation) {
                            $transaction->doctorAppointment->update(['payment_status' => PaymentStatus::completed]);
                            $params = (object)[
                                'transaction_id' => $transaction->transaction_id,
                                'payment_status' => TransactionPaymentStatus::Complete
                            ];
                            $transactionArray = $this->transactionRepositoryEloquent->updatePaymentStatus($params);
                        }
                    }
                }

                \Log::info('Transaction ' . $transaction->id . ' updated with status: ' . $transactionStatus);
            }
            \Log::info('Transaction not found');
        } catch (\Exception $e) {
            Log::error("===================================Error in ecobankWebhook===============================");
            Log::error("Error => " . $e->getMessage());
            Log::error("Line => " . $e->getLine());
            Log::error("File => " . $e->getFile());
            Log::error("=====================================================================================");
            if ($request->get('plateform') == "app") {
                return redirect('web/ecobank-redirection?status=fail');
            }
        }
    }

    public function pay(Request $request)
    {


        // Replace with your actual URL and cookie name
        $webSourceBaseUrl = 'https://testsecureacceptance.cybersource.com/pay';
        $cookieName = 'cookies';

        $cookieValue = bin2hex(random_bytes(16));

        $cookieJar = new CookieJar();

        $setCookie = new SetCookie([
            'Name' => $cookieName,
            'Value' => $cookieValue,
            'Domain' => $webSourceBaseUrl, // Adjust the domain as needed
            'Path' => '/',
            'Expires' => time() + 10 * 24 * 60 * 60, // 10 days expiration in seconds
            'Secure' => true,
            'HttpOnly' => false,
            'Discard' => false,
        ]);

        $cookieJar->setCookie($setCookie);

        $client = new Client(['cookies' => $cookieJar]);
        $response = $client->post('https://testsecureacceptance.cybersource.com/pay', [
            RequestOptions::FORM_PARAMS => [
                'unsigned_field_names' => '',
                'signed_field_names' => 'access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency',
                'access_key' => env('ACCESS_CODE'),
                'profile_id' => env('MERCHANT_ID'),
                'locale' => 'en',
                'transaction_uuid' => $request->orderInfo,
                'transaction_type' => 'sale',
                'reference_number' => $request->requestId,
                'currency' => config('constants.currency'),
                'amount' => $request->price,
                'signed_date_time' => $request->signed_date_time,
                'signature' => $request->signature,
            ],
            'headers' => [
                'Content-Type' => 'multipart/form-data',
                'Origin' => 'developer.ecobank.com'
            ],
        ]);

        return $response->getBody()->getContents();
    }

    public function jailWebhook(Request $request)
    {
        try {
            Log::info("||===============================jailWebhook init==========================||");
            Log::info("|| Request: " . json_encode($request->all()));
            Log::info("||=========================================================================||");

            return response()->json(['status' => "Success"], 200);
        } catch (\Exception $e) {
            Log::error("||============================Error in jailWebhook========================||");
            Log::error("|| Error: " . $e->getMessage());
            Log::error("|| Line: " . $e->getLine());
            Log::error("|| File: " . $e->getFile());
            Log::error("||========================================================================||");
        }
    }
}
