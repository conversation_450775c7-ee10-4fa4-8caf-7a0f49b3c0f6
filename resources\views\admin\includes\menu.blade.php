<?php
if (empty($activeSidebarMenu)) {
    $activeSidebarMenu = "";
}

if (empty($activeSidebarSubMenu)) {
    $activeSidebarSubMenu = "";
}

$sidebarMenus = [
    'dashboard' => [
        'text' => 'Dashboard',
        'routeName' => 'dashboard',
        'icon' => 'fa-solid fa-table-columns',
        'subMenu' => []
    ],
    'patient' => [
        'text' => 'Patient',
        'routeName' => 'admin.patient.index',
        'icon' => 'fa-solid fa-hospital-user',
        'subMenu' => []
    ],
    'doctor' => [
        'text' => 'Doctor',
        'routeName' => 'admin.doctor.index',
        'icon' => 'fa-solid fa-building-user',
        'subMenu' => []
    ],
    'payout' => [
        'text' => 'Doctor Payout',
        'routeName' => 'admin.payout.index',
        'icon' => 'fa-solid fa-building-user',
        'subMenu' => []
    ],
    'doctor-invitation' => [
        'text' => 'Doctor Invitation',
        'routeName' => 'admin.doctor-invitation.index',
        'icon' => 'fa-solid fa-building-user',
        'subMenu' => []
    ],
    'speciality' => [
        'text' => 'Speciality',
        'routeName' => 'admin.speciality.index',
        'icon' => 'fa-solid fa-person-circle-plus',
        'subMenu' => []
    ],
    'symptom' => [
        'text' => 'Symptoms / Medical History',
        'routeName' => 'admin.symptom.index',
        'icon' => 'fa-solid fa-syringe',
        'subMenu' => []
    ],
    'medicine' => [
        'text' => 'Medicine',
        'routeName' => 'admin.medicine.index',
        'icon' => 'fa-solid fa-medkit',
        'subMenu' => []
    ],
    'lab-test' => [
        'text' => 'Lab Test',
        'routeName' => 'admin.lab-test.index',
        'icon' => 'fa-solid fa-flask-vial',
        'subMenu' => []
    ],
    'appointment' => [
        'text' => 'Appointment',
        'routeName' => 'admin.appointment.index',
        'icon' => 'fa-solid fa-calendar-check',
        'subMenu' => []
    ],
    'call' => [
        'text' => 'Call',
        'routeName' => 'admin.call.index',
        'icon' => 'fa-solid fa-phone-volume',
        'subMenu' => []
    ],
    'subscription' => [
        'text' => 'Subscriptions',
        'routeName' => 'admin.subscription.index',
        'icon' => 'fa-solid fa-money-bill-wheat',
        'subMenu' => []
    ],
    'transaction' => [
        'text' => 'Transactions',
        'routeName' => 'admin.transactions.index',
        'icon' => 'fa-solid fa-sack-dollar',
        'subMenu' => []
    ],
    'wallet-transaction' => [
        'text' => 'Doctor Wallet Transaction',
        'routeName' => 'admin.wallet-transaction.index',
        'icon' => 'fa-solid fa-money-check-dollar',
        'subMenu' => []
    ],

    'support' => [
        'text' => 'Support',
        'routeName' => 'admin.support.index',
        'icon' => 'fa-solid fa-duotone fa-circle-question',
        'subMenu' => []
    ],
    'settings' => [
        'text' => 'Settings',
        'icon' => 'fa-solid fa-gear',
        'subMenu' => [
            'video' => [
                'text' => 'Video Upload',
                'routeName' => 'admin.video.index',
                'icon' => 'fa fa-video-camera'
            ],
            'bank-detail' => [
                'text' => 'Eco Bank Details',
                'routeName' => 'admin.bank-detail.index',
                'icon' => 'fa fa-credit-card-alt'
            ],
            'faq' => [
                'text' => 'FAQs',
                'routeName' => 'admin.faq.index',
                'icon' => 'fa-solid fa-circle-question'
            ],
            'cms' => [
                'text' => 'CMS Pages',
                'routeName' => 'admin.cms.index',
                'icon' => 'fa-solid fa-file'
            ],
            'setting' => [
                'text' => 'Setting Pages',
                'routeName' => 'admin.setting.index',
                'icon' => 'fa-solid fa-wrench'
            ],
        ]
    ],
];
?>

<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
        <!--begin::Logo image-->
        <a href="#">
            <img class="h-50px w-200px app-sidebar-logo-default" src="{{ asset('assets/media/auth/logo-header.png') }}" alt="MOLEMA" />
            <img class="h-25px app-sidebar-logo-minimize" src="{{ asset('assets/media/auth/logo.png') }}" alt="MOLEMA" />
        </a>
        <!--end::Logo image-->

        <!--begin::Sidebar toggle-->
        <div id="kt_app_sidebar_toggle"
            class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate"
            data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body"
            data-kt-toggle-name="app-sidebar-minimize">
            <span class="svg-icon svg-icon-2 rotate-180">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5"
                        d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4071 11.2929C11.0166 11.6834 11.0166 12.3166 11.4071 12.7071L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z"
                        fill="currentColor" />
                    <path
                        d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.40712 11.2929C5.01659 11.6834 5.01659 12.3166 5.40712 12.7071L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z"
                        fill="currentColor" />
                </svg>
            </span>
        </div>
        <!--end::Sidebar toggle-->
    </div>

    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5"
            data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto"
            data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
            data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
            <div class="menu menu-column menu-rounded menu-sub-indention px-3" id="#kt_app_sidebar_menu"
                data-kt-menu="true" data-kt-menu-expand="false">

                @foreach ($sidebarMenus as $key => $menu)
                @php
                $active = ($key == $activeSidebarMenu) ? 'active' : '';
                $subMenu = !empty($menu['subMenu']) ? 'data-kt-menu-trigger=click' : '';
                $subMenuClass = !empty($menu['subMenu']) ? 'show' : '';
                $href = (!empty($menu['routeName'])) ? route($menu['routeName']) : 'javascript:void(0);';
                @endphp


                @if(empty($menu['subMenu']))
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link {{ $active }}" href="{{$href}}">
                        <span class="menu-icon">
                            <i class="{{ $menu['icon'] ?? '' }}"></i>
                        </span>
                        <span class="menu-title">{{ $menu['text'] }}</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                @else
                <div {{ $subMenu }} class="menu-item menu-accordion @php echo (!empty($active)) ? $subMenuClass :''; @endphp">
                    <span class="menu-link {{ $active }}">
                        <span class="menu-icon">
                            <i class="{{ $menu['icon'] ?? '' }}"></i>
                        </span>
                        <span class="menu-title">{{ $menu['text'] }}</span>
                        <span class="menu-arrow"></span>
                    </span>

                    @foreach ($menu['subMenu'] as $innerKey => $innerMenu)
                    <div class="menu-sub menu-sub-accordion">
                        <div class="menu-item">
                            <a class="menu-link @php echo ($innerKey == $activeSidebarSubMenu) ? 'active' : ''; @endphp"
                                href="@php echo (!empty($innerMenu['routeName'])) ? route($innerMenu['routeName']) : 'javascript:;'; @endphp">
                                <span class="menu-bullet">
                                    <i class="{{ $innerMenu['icon'] ?? '' }}"></i>
                                </span>
                                <span class="menu-title">{{ $innerMenu['text'] }}</span>
                            </a>
                        </div>
                    </div>
                    @endforeach

                </div>
                @endif
                @endforeach

            </div>
        </div>
    </div>
</div>
