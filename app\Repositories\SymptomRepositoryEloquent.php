<?php

namespace App\Repositories;

use App\Enums\Status;
use App\Enums\SymptomHistory;
use App\Http\CommonTraits\UploadMedia;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\SymptomRepository;
use App\Models\Symptom;
use App\Transformers\SymptomTransformer;
use App\Validators\SymptomValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Class SymptomRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class SymptomRepositoryEloquent extends BaseRepository implements SymptomRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Symptom::class;
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $speciality = Symptom::query();

        if (!empty($request->start_date)) {
            $speciality = $speciality->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($speciality)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.symptom.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.symptom.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.symptom.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                    <a class="menu-link px-3 delete" id="' . $row->id . '" data-name="' . $row->symptoms_name . '" data-url="' . route('admin.symptom.destroy') . '" data-table="symptom_table"> Delete</a>
                    </div>
                </div>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();

        $data['symptoms_name'] = [
            'en' => $request->symptoms_name_en ?? null,
            'es' => $request->symptoms_name_es ?? null,
        ];

        if ($request->has('image')) {
            $data['image'] = self::imageUpload($request->image, 'molema/symptom');
        }

        return Symptom::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $symptom = Symptom::findOrFail($id);

        return $symptom;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $symptom = Symptom::findOrFail($id);

        $data = $request->all();

        $data['symptoms_name'] = [
            'en' => $request->symptoms_name_en ?? $symptom->symptoms_name->en,
            'es' => $request->symptoms_name_es ?? $symptom->symptoms_name->es,
        ];

        if ($request->has('image')) {
            $data['image'] = self::imageUpload($request->image, 'molema/symptom');
        }
        return $symptom->update($data);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = Symptom::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = Symptom::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = Symptom::findOrFail($request->input('id'));
        $patient->delete();
    }

    /**
     * @param $request
     */
    public function viewSymptom($request)
    {
        $speciality = Symptom::where('status', Status::Active)->where('is_history', SymptomHistory::Symptoms);
        if($request->search){
            $speciality = $speciality->where('symptoms_name', 'LIKE', '%' . trim($request->search) . '%');
        }
        $speciality = $speciality->paginate(40);
        return fractal($speciality, new SymptomTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
