<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;

class LabTest extends Model
{
    use HasFactory, TransformableTrait;

     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'code', 'status', 'doctor_id', 'approve_status'];

    protected $appends = ['created_date'];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')->withTrashed();
    }
}
