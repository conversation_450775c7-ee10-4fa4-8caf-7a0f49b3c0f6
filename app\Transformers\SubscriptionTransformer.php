<?php

namespace App\Transformers;

use App\Models\Subscription;
use League\Fractal\TransformerAbstract;

class SubscriptionTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Subscription $subscription)
    {
        return [
            'name' => $subscription->name,
            'description' => $subscription->description,
            'slug' => $subscription->slug,
            'validity' => $subscription->validity,
            'price' => $subscription->price,
            'status' => $subscription->status
        ];
    }
}
