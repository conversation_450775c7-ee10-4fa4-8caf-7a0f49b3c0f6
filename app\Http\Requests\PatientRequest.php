<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if(!empty(request()->id)){
            return [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => 'required|email|unique:users,email,' . request()->id . ',id,deleted_at,NULL',
                //'dial_code' => 'required|max:4|regex:/^(\+\d{1,3})$/',
                'country_code' => 'required',
                'mobile' => 'required|max:15|regex:/^(\d{6,14})$/|unique:users,mobile,' . request()->id . ',id,deleted_at,NULL',
                'age' => 'required|numeric|min:18',
                'weight' => 'required|numeric',
                'height' => 'required|numeric'
            ];
        }else{
            return [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => 'required|email|unique:users,email,' . request()->id . ',id,deleted_at,NULL',
                //'dial_code' => 'required|max:4|regex:/^(\+\d{1,3})$/',
                'country_code' => 'required',
                'mobile' => 'required|max:15|regex:/^(\d{6,14})$/|unique:users,mobile,' . request()->id . ',id,deleted_at,NULL',
                'age' => 'required|numeric|min:18',
                'weight' => 'required|numeric',
                'height' => 'required|numeric',
                'password' => 'required|min:8',
            ];
        }
    }
}
