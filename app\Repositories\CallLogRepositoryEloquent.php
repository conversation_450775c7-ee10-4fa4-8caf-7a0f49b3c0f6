<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\CallLogRepository;
use App\Entities\CallLog;
use App\Validators\CallLogValidator;

/**
 * Class CallLogRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class CallLogRepositoryEloquent extends BaseRepository implements CallLogRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return CallLog::class;
    }

    

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
    
}
