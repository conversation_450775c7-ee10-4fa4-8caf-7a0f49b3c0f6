<?php

namespace App\Http\Controllers\Admin;

use App\Exports\AppointmentExport;
use App\Repositories\DoctorAppointmentRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class DoctorAppointmentController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "appointment";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var DoctorAppointmentRepositoryEloquent|string
     */
    public $doctorAppointmentRepository = "";

    public function __construct(DoctorAppointmentRepositoryEloquent $doctorAppointmentRepository)
    {
        parent::__construct();
        $this->doctorAppointmentRepository = $doctorAppointmentRepository;
    }

    /**
     * Return List of Appointment
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Appointment']);
        return view('admin.appointment.index');
    }



    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->doctorAppointmentRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * create a new appointments
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Appointment']);
        return view('admin.appointment.appointment');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->store($request);
            $request->session()->flash('success', config("messages.appointment_created"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit appointment list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Appointment']);
        try {
            $appointment = $this->doctorAppointmentRepository->edit($request, $id);
            return view('admin.appointment.appointment', compact('appointment'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Edit appointment list.
     *
     * @return mixed
     */
    public function view(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            $appointment = $this->doctorAppointmentRepository->view($request, $id);
            $appointmentprescription  = $this->doctorAppointmentRepository->prescriptionview($request, $id);
            return view('admin.appointment.newview', compact('appointment','appointmentprescription'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    public function supportview(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            return $this->doctorAppointmentRepository->support($request, $id);
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    public function calllogsview(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            return $this->doctorAppointmentRepository->callLogs($request, $id);
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }


    /**
     * notesview appointment list.
     *
     * @return mixed
     */
    public function notesview(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            $appointment = $this->doctorAppointmentRepository->view($request, $id);
            $appointmentnotes = $this->doctorAppointmentRepository->notesview($request, $id);
            $support = $this->doctorAppointmentRepository->support($request, $id);
            $calls = $this->doctorAppointmentRepository->callLogs($request, $id);
            return view('admin.appointment.notesview', compact('appointment','appointmentnotes','support','calls'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * labview appointment list.
     *
     * @return mixed
     */
    public function labview(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            $appointment = $this->doctorAppointmentRepository->view($request, $id);
            $appointmentlab = $this->doctorAppointmentRepository->labview($request, $id);
            $support = $this->doctorAppointmentRepository->support($request, $id);
            $calls = $this->doctorAppointmentRepository->callLogs($request, $id);
            return view('admin.appointment.labview', compact('appointment','appointmentlab','support','calls'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * prescriptionview appointment list.
     *
     * @return mixed
     */
    public function prescriptionview(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Appointment']);
        try {
            $appointment = $this->doctorAppointmentRepository->prescriptionview($request, $id);
            return view('admin.appointment.prescriptionview', compact('appointment'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Update appointment list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $appointment = $this->doctorAppointmentRepository->update($request, $id);
            $request->session()->flash('success', config("messages.appointment_updated"));
            return redirect()->route('admin.appointment.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->doctorAppointmentRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->doctorAppointmentRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->doctorAppointmentRepository->destroy($request);
            $request->session()->flash('success', config("messages.appointment_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "appointment_" . time() . ".xlsx";
        return Excel::download(new AppointmentExport($request->all()), $fileName);
    }
}
