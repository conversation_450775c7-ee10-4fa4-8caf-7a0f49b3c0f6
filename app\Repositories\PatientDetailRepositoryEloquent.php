<?php

namespace App\Repositories;

use App\Enums\PopularDoctor;
use App\Enums\UserType;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\PatientDetailRepository;
use App\Models\PatientDetail;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Models\DoctorAppointment;
use App\Models\DoctorDetail;
use App\Models\DoctorSpeciality;
use App\Models\Speciality;
use App\Models\Symptom;
use App\Models\User;
use App\Models\UserDevice;
use App\Transformers\DoctorListTransformer;
use App\Transformers\PatientAllDetailTransformer;
use App\Transformers\SpecialityTransformer;
use App\Transformers\SymptomTransformer;
use App\Validators\PatientDetailValidator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use App\Http\CommonTraits\UploadMedia;
use App\Enums\AppointmentStatus;
use App\Enums\DoctorType;
use App\Enums\Status;
use App\Enums\SymptomHistory;
use App\Enums\TransactionPaymentStatus;
use App\Enums\UserStatus;
use App\Transformers\DoctorAppointmentTransformer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\SendEmails;
use App\Transformers\DoctorHomeScreenTransformer;
use App\Models\Transaction;
use App\Models\Countries;
use App\Models\EmailTemplate;
use App\Models\Video;
use App\Transformers\VideoTransformer;
/**
 * Class PatientDetailRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class PatientDetailRepositoryEloquent extends BaseRepository implements PatientDetailRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return PatientDetail::class;
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function updatePatientProfile($request)
    {
        $data = $request->all();

        $data['user_id'] = Auth::guard('api')->user()->id;

        $data['height'] = encrypt($request->height) ?? encrypt(null);
        $data['weight'] = encrypt($request->weight) ?? encrypt(null);
        $data['age'] = encrypt($request->age) ?? encrypt(null);

        if (!empty($request->health_issue)) {
            $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, $request->other_issue, 'other')));
        } else if (!empty($request->other_issue)) {
            $data['health_issue']['other'] = encrypt(json_encode($request->other_issue));
        } elseif (empty($request->health_issue) && empty($request->other_issue)) {
            $data['health_issue'] = encrypt(null);
        }

        $patientDetail = PatientDetail::updateOrCreate(['user_id' => Auth::guard('api')->user()->id], $data);

        return fractal(Auth::guard('api')->user(), new PatientAllDetailTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * View patient profile detail
     *
     * @return array
     */
    public function viewPatientProfile($request)
    {
        return fractal(Auth::guard('api')->user(), new PatientAllDetailTransformer())->serializeWith(new CustomArraySerializer());

    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $user = User::OnlyPatient();

        if (!empty($request->start_date)) {
            $user = $user->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($user)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.patient.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.patient.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.patient.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.patient.view', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="view_row">
                            View
                        </a>
                    </div>
                    <div class="menu-item px-3">
                    <a class="menu-link px-3 delete" id="' . $row->id . '" data-name="' . $row->first_name . " " . $row->last_name . '" data-url="' . route('admin.patient.destroy') . '" data-table="patient_table"> Delete</a>
                    </div>
                </div>';
                return $html;
            })
            ->filterColumn('name', function ($query, $keyword) {
                return $query->searchByName($keyword);
            })
            ->filterColumn('verified', function ($query, $keyword) {
                return $query->searchByVerified($keyword);
            })
            ->filterColumn('mobile', function ($query, $keyword) {
                return $query->searchByMobile($keyword);
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->orderColumn('name', function ($query, $order) {
                $query->orderBy('first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();
        $data['password'] = bcrypt($request->password);
        $data['user_type'] = UserType::Patient;
        $data['email_verified_at'] = now();
        $data['mobile_verified_at'] = now();
        $data['username'] = generateUserName(UserType::Patient);
        $data['timezone'] = "UTC";
        if ($request->has('avatar')) {
            $data['avatar'] = self::avatarUpload($request, 'molema/user');
        }
        $pass = $request->password;
        if (!empty($request->health_issue) && !empty($request->other_issue)) {
            $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, $request->other_issue, 'other')));
        } elseif (!empty($request->other_issue) && empty($request->health_issue)) {
            $data['health_issue']['other'] = encrypt($request->other_issue);
        } elseif (!empty($request->health_issue) && empty($request->other_issue)) {
            $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, null, 'other')));
        } elseif (empty($request->health_issue) && empty($request->other_issue)) {
            $data['health_issue'] = encrypt(null);
        }
        if ($request->country_code) {
            $data['dial_code'] = Countries::where('iso', $request->country_code)->pluck('phonecode')->first();
        }
        $data['height'] = encrypt($request->height) ?? encrypt(null);
        $data['weight'] = encrypt($request->weight) ?? encrypt(null);
        $data['age'] = encrypt($request->age) ?? encrypt(null);

        $patient = User::create($data);

        $data['user_id'] = $patient->id;
        $patientDetail = PatientDetail::create($data);

        $emailTemplate = EmailTemplate::where('slug', 'patient_registration_success_by_admin')->first();

        $emailBody = $emailTemplate->getTranslation('description', app()->getLocale());

        $email_content = str_replace("[[USERNAME]]", $request->name, $emailBody);
        $email_content = str_replace("[[PASSWORD]]", $request->password, $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);

        $user = [
                'email' => $request->email,
                'email_body' => $email_content,
                'subject' => $emailTemplate->getTranslation('title', app()->getLocale())
            ];


        dispatch((new SendEmails($user)));

        return $patient;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $patient = User::with('patientDetail')->findOrFail($id);

        return $patient;
    }

    public function view($request, $id)
    {
        $patient = User::with('patientDetail')->findOrFail($id);

        return $patient;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $patient = User::findOrFail($id);

        $data = $request->all();

        if ($request->has('avatar')) {
            $data['avatar'] = self::avatarUpload($request, 'molema/user', $patient->id);
            self::deleteImg($patient->getOriginal('avatar'));
        }

        if (!empty($request->health_issue) && !empty($request->other_issue)) {
            $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, $request->other_issue, 'other')));
        } elseif (!empty($request->other_issue) && empty($request->health_issue)) {
            $data['health_issue']['other'] = encrypt($request->other_issue);
        } elseif (!empty($request->health_issue) && empty($request->other_issue)) {
            $data['health_issue'] = encrypt(json_encode(Arr::prepend($request->health_issue, null, 'other')));
        } elseif (empty($request->health_issue) && empty($request->other_issue)) {
            $data['health_issue'] = encrypt(null);
        }
        if ($request->country_code) {
            $data['dial_code'] = Countries::where('iso', $request->country_code)->pluck('phonecode')->first();
        }
        $data['height'] = encrypt($request->height) ?? encrypt(null);
        $data['weight'] = encrypt($request->weight) ?? encrypt(null);
        $data['age'] = encrypt($request->age) ?? encrypt(null);

        $patient->update($data);
        $data['user_id'] = $patient->id;


        $patientDetail = PatientDetail::where('user_id', $patient->id)->first();
        if (!empty($patientDetail)) {
            $patientDetail->updateOrCreate(['user_id' => $patient->id], $data);
        } else {
            $patientDetail = PatientDetail::create($data);
        }

        return $patient;
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = User::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = User::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }


    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = User::findOrFail($request->input('id'));

        $data['email'] = $patient->email . time();
        $data['mobile'] = $patient->mobile . time();
        $data['provider_id'] = null;
        $data['provider_name'] = null;
        PatientDetail::where('user_id', $patient->id)->delete();
        $patient->update($data);
        $patient->delete();
    }

    /**
     * Store Device Info
     *
     * @param $request
     * @return mixed
     */
    public function storeDeviceInfo($request)
    {
        $data = $request->only('device_id', 'push_token', 'voip_token', 'device_type', 'os_version');
        $data['user_id'] = Auth::guard('api')->user()->id;

        return UserDevice::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function viewMedicalHistory($request)
    {
        $symptom = Symptom::select('id', 'symptoms_name', 'image')->where('is_history', true)->get();

        return fractal($symptom, new SymptomTransformer())->serializeWith(new CustomArraySerializer());
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function homeScreen($request)
    {

        // Popular Doctor
        $popularDoctor = self::getPopularDoctor($request);

        // youtube video
        $video = self::getVideoLink($request);

        //reward transaction
        $rewardCount = Transaction::where('patient_id', auth()->user()->id)
        ->where('payment_status', TransactionPaymentStatus::Reward)->count();

        // Popular Speciality
        $popularSpeciality = self::getPopularSpeciality($request, $popularDoctor['popularDoctors']);

        // Popular Symptoms
        $popularSymptom = self::getPopularSymptoms($request);

        return [
            'notification_indicator' => auth()->user()->notification_indicator,
            'reward_count' => $rewardCount,
            'videos' => fractal($video, new VideoTransformer())->serializeWith(new CustomArraySerializer()),
            'popularDoctor' => fractal($popularDoctor['doctor'], new DoctorHomeScreenTransformer())->serializeWith(new CustomArraySerializer()),
            'popularSpeciality' => fractal($popularSpeciality, new SpecialityTransformer())->serializeWith(new CustomArraySerializer()),
            'popularSymptom' => fractal($popularSymptom, new SymptomTransformer())->serializeWith(new CustomArraySerializer())
        ];
    }

    /**
     * Get popular Doctor
     *
     * @param $request
     * @return array
     */
    public function getPopularDoctor($request)
    {
        $totalPopularDoctor = 10;

        $popularDoctor = DoctorDetail::where('doctor_type', DoctorType::Collaborator)->whereNotNull('account_verified_at');
        if (!empty($request->city_name)) {
            $popularDoctor = $popularDoctor->whereRaw('FIND_IN_SET("' . $request->city_name . '", served_location)');
        }
        $popularDoctor = $popularDoctor->inRandomOrder()->limit($totalPopularDoctor)->get()->pluck('user_id')->toArray();

        $popularDoctors = $popularDoctor;
        //view admin selected popular doctor and remaining more consultation done by doctor
        if (count($popularDoctor) < $totalPopularDoctor) {
            $remaining = $totalPopularDoctor - count($popularDoctor);
            $cityName = $request->city_name;
            $highestBookedDoctor = DoctorAppointment::query();
            if (!empty($request->city_name)) {
                $highestBookedDoctor = $highestBookedDoctor->whereHas('doctor.doctorDetail', function ($query) use ($cityName) {
                    $query->whereRaw('FIND_IN_SET("' . $cityName . '", served_location)')->whereNotNull('account_verified_at');
                });
            }
            $highestBookedDoctor = $highestBookedDoctor->whereNotIn('doctor_id', $popularDoctor)
                ->groupBy('doctor_id')
                ->selectRaw('count(*) as total, doctor_id')
                ->orderBy('total', 'DESC')
                ->limit($remaining)
                ->get()
                ->pluck('doctor_id')
                ->toArray();

            $popularDoctors = array_merge($popularDoctor, $highestBookedDoctor);
        }

        $doctor = User::where('status', UserStatus::Active)->whereIn('id', $popularDoctors)->get();

        return ['doctor' => $doctor, 'popularDoctors' => $popularDoctors];
    }

    /**
     * Get popular Speciality
     *
     * @param $request
     * @param $popularDoctors
     * @return array
     */
    public function getPopularSpeciality($request, $popularDoctors)
    {

        $totalPopularSpeciality = 6;
        $speciality = DoctorAppointment::groupBy('doctor_id')->selectRaw('count(*) as total, doctor_id')->orderBy('total', 'DESC')->limit($totalPopularSpeciality)->get()->pluck('doctor_id')->toArray();
        $doctor = User::with('speciality')->whereIn('id', $popularDoctors)->get();
        $special = [];
        foreach ($doctor as $user) {
            foreach ($user->speciality as $speciality) {
                $special[] = $speciality->id;
            }
        }
        $uniqueSpecial = array_unique($special);

        $popularSpeciality = $uniqueSpecial;
        if (count($uniqueSpecial) < $totalPopularSpeciality) {
            $remaining = $totalPopularSpeciality - count($uniqueSpecial);
            $remainingSpeciality = Speciality::where('status', Status::Active)->whereNotIn('id', $special)->inRandomOrder()->limit($remaining)->get()->pluck('id')->toArray();
            $popularSpeciality = array_merge($uniqueSpecial, $remainingSpeciality);
        }

        $popSpeciality = Speciality::where('status', Status::Active)->whereIn('id', $popularSpeciality)->inRandomOrder()->limit($totalPopularSpeciality)->get();
        return $popSpeciality;
    }

    public function getVideoLink($request){

        $video = Video::where('slug', 'patient-home')->orderBy('priority')->get();

        return $video;
    }

    /**
     * Get popular Symptom
     *
     * @param $request
     * @return array
     */
    public function getPopularSymptoms($request)
    {
        $totalPopularSymptoms = 8;
        $popSymptom = Symptom::where('is_history', SymptomHistory::Symptoms)->where('status', Status::Active)->inRandomOrder()->limit($totalPopularSymptoms)->get();

        return $popSymptom;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function homeSearch($request)
    {
        switch ($request->type) {
            case 'pharmacy':
                $data = [];
                break;
            case 'doctors':
                $data = self::DoctorSearch($request);
                break;
            case 'all':
                $data = self::allSearch($request);
                break;
        }
        return $data;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function allSearch($request)
    {
        $doctors = self::DoctorSearch($request);

        $pharmacy = self::pharmacySearch($request);

        return ['doctors' => $doctors, 'pharmacy' => $pharmacy];
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function doctorSearch($request)
    {
        $searchString = $request->search;

        $doctors = User::with(['speciality', 'doctorRating', 'speciality.symptom'])
            ->where('user_type', UserType::Doctor)
            ->where('status', UserStatus::Active)
            ->where(function ($query) use ($searchString) {
                $query->where('first_name', 'LIKE', '%' . $searchString . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $searchString . '%')
                    ->orWhereHas('speciality', function ($query) use ($searchString) {
                        $query->where('specialist', 'like', '%' . $searchString . '%');
                    })->orWhereHas('speciality.symptom', function ($query) use ($searchString) {
                        $query->where('symptoms_name', 'like', '%' . $searchString . '%');
                    });
            });
        if (!empty($request->city_name)) {
            $cityName = $request->city_name;
            $doctors = $doctors->whereHas('doctorDetail', function ($query) use ($cityName) {
                $query->whereRaw('FIND_IN_SET("' . $cityName . '", served_location)')->whereNotNull('account_verified_at');
            });
        } else {
            $doctors = $doctors->whereHas('doctorDetail', function ($query) {
                $query->whereNotNull('account_verified_at');
            });
        }

        if ($request->type == 'all') {
            $doctors = $doctors->limit(3)->get();
            return fractal($doctors, new DoctorListTransformer())->serializeWith(new CustomArraySerializer());
        } else {
            $doctors = $doctors->paginate(10);
            return fractal($doctors, new DoctorListTransformer())->serializeWith(new PaginationArraySerializer());
        }
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function pharmacySearch($request)
    {
        $searchString = $request->search;

        $pharmacy = [];

        return $pharmacy;
    }

    /**
     * Get doctor appoint list
     *
     * @return string
     */
    public function booking($request, $id)
    {
        $appointment = DoctorAppointment::whereHas('transaction')->where('patient_id', $id);
        $upcoming_appointment = clone($appointment);
        $ongoing_appointment = clone($appointment);
        $completed_appointment = clone($appointment);

        $appointment_upcoming = $upcoming_appointment->where('appointment_status', AppointmentStatus::Scheduled)->where('payment_status', 1)->orderBy('created_at', 'DESC')->paginate(10);
        $appointment_ongoing = $ongoing_appointment->where('appointment_status', AppointmentStatus::Ongoing)->where('payment_status', 1)->orderBy('created_at', 'DESC')->paginate(10);
        $appointment_completed = $completed_appointment->where('appointment_status', AppointmentStatus::Completed)->where('payment_status', 1)->orderBy('created_at', 'DESC')->paginate(10);

        return [
            "appointment_upcoming" => $appointment_upcoming,
            "appointment_ongoing" => $appointment_ongoing,
            "appointment_completed" => $appointment_completed
        ];
    }

    public function transaction($request, $id)
    {
        $transaction = Transaction::where('patient_id', $id)->orderBy('created_at', 'DESC')->paginate(10);
        return $transaction;
    }


    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
