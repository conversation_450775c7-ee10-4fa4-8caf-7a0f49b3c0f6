<?php

namespace App\Transformers;

use App\Models\SupportChat;
use League\Fractal\TransformerAbstract;

class SupportChatTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(SupportChat $chat)
    {
        return [
            'id' => $chat->id,
            'support_id' => $chat->support_id,
            'sender_id' => $chat->sender_id,
            'sender_type' => $chat->sender_type,
            'message_type' => $chat->message_type,
            'message' => $chat->message,
            'is_read' => $chat->is_read,
            'created_at' => $chat->created_at
        ];
    }
}
