<?php

namespace App\Http\Controllers\Admin;

use App\Enums\SupportStatus;
use App\Models\Support;
use App\Repositories\SupportRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class SupportController extends Controller
{
    /**
     * @var SupportRepositoryEloquent
     */
    protected $supportRepository;

    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "support";

    /**
     * SupportController constructor.
     * @param SupportRepositoryEloquent $supportRepository
     */
    public function __construct(SupportRepositoryEloquent $supportRepository)
    {
        $this->supportRepository = $supportRepository;
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Support']);
        $supportCount = Support::where('status', SupportStatus::Pending)->count();
        return view('admin.settings.support.index', compact('supportCount'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function getList(Request $request)
    {
        try {
            return $this->supportRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request, $id)
    {
        try {
            $this->supportRepository->changeStatusActive($request, $id);
            $request->session()->flash('success', config("messages.support_ticket_open"));
            return redirect()->route('admin.support.index', $id);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.support.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request, $id)
    {
        try {
            $this->supportRepository->changeStatusDeactive($request, $id);
            $request->session()->flash('success', config("messages.support_ticket_close"));
            return redirect()->route('admin.support.index', $id);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.support.index');
        }
    }

    /**
     * Edit support list.
     *
     * @return mixed
     */
    public function chat(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Support Chat']);
        try {
            $chat = $this->supportRepository->chat($request, $id);
            return view('admin.settings.support.chat', compact('chat'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.support.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.support.index');
        }
    }

    /**
     * Edit support list.
     *
     * @return mixed
     */
    public function createChat(Request $request, $id)
    {
        try {
            $chat = $this->supportRepository->createChat($request, $id);
            return redirect()->route('admin.support.chat', $id);
        } catch (ModelNotFoundException $exception) {
            dd(1);
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.support.index');
        } catch (Exception $exception) {
            dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.support.index');
        }
    }
}
