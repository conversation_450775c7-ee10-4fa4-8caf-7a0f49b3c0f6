<?php

namespace App\Transformers;

use App\Models\AppointmentCall;
use League\Fractal\TransformerAbstract;

class CallDetailTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(AppointmentCall $appointmentCall)
    {
        return [
            "id"=> $appointmentCall->id,
            "channel_name"=> $appointmentCall->channel_name,
            "initiator_id"=> $appointmentCall->initiator_id,
            "receiver_id"=> $appointmentCall->receiver_id,
            "call_date"=> $appointmentCall->call_date,
            "start_time"=> $appointmentCall->start_time,
            "calculated_seconds"=> $appointmentCall->calculated_seconds,
            "created_at"=> $appointmentCall->created_at,
            "appointment_id"=> $appointmentCall->appointment_id
        ];
    }
}
