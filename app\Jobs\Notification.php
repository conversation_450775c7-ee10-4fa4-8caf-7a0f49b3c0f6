<?php

namespace App\Jobs;

use App\Enums\SenderType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\CommonTraits\PushNotification;
use App\Models\Notification as ModelsNotification;
use Illuminate\Support\Facades\Log;
use LaravelFCM\Message\PayloadDataBuilder;

class Notification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $receiver, $extra, $sender;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sender, $receiver, $extra)
    {
        $this->sender = $sender;
        $this->receiver = $receiver;
        $this->extra = $extra;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //Save Doctor Notification
        $data = [
            'sender_id' => $this->sender->id,
            'receiver_id' => $this->receiver->id,
            'sender_type' => $this->extra['sender_type'] ?? SenderType::User,
            'receiver_type' => $this->extra['receiver_type'] ?? SenderType::User,
            'title' => $this->extra['title'],
            'description' => $this->extra['message'],
            'ref_id' => $this->extra['ref_id'],
            'ref_type' => $this->extra['ref_type'],
            'notification_type' => $this->extra['notification_type'],
        ];
        ModelsNotification::create($data);

        if ($tokens = PushNotification::getUsersToken([$this->receiver->id])) {
            $dataBuilder = new PayloadDataBuilder();
            $dataBuilder->addData($this->extra);
            // $dataBuilder->addData(['click_action' => 'com.molema.health.app.FLUTTER_NOTIFICATION_CLICK']);
            // PushNotification::sendNotification($tokens, $this->extra['title'], $this->extra['message'], $this->sender, $dataBuilder);
            PushNotification::sendPushNotification($tokens, $this->extra['title'], $this->extra['message'], $this->sender);
        }
    }
}
