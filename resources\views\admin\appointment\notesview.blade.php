@extends('admin.layouts.app')
@section('content')
		<!--begin::Main-->
		<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
			<!--begin::Content wrapper-->
			<div class="d-flex flex-column flex-column-fluid">
				{{ Breadcrumbs::render('admin.appointment.view') }}
				<!--begin::Content-->
				<div id="kt_app_content" class="app-content flex-column-fluid">
					<!--begin::Content container-->
					<div id="kt_app_content_container" class="app-container container-xxl">
						<!--begin::Layout-->
						<div class="d-flex flex-column flex-lg-row">
							<!--begin::Sidebar-->
							<div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
								<!--begin::Card-->
								<div class="card mb-5 mb-xl-8">
									<!--begin::Card body-->
									<div class="card-body">
										<!--begin::Details toggle-->
										<div class="d-flex flex-stack fs-4 py-3">
											<div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_user_view_details" role="button" aria-expanded="false" aria-controls="kt_user_view_details">Details
											<span class="ms-2 rotate-180">
												<!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
												<span class="svg-icon svg-icon-3">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z" fill="currentColor" />
													</svg>
												</span>
												<!--end::Svg Icon-->
											</span></div>
										</div>
										<div class="separator"></div>
										<div id="kt_user_view_details" class="collapse show">
											<div class="pb-5 fs-6">
											@if($appointment->patient)
												<div class="fw-bold mt-5">Patient Name</div>
												<div class="text-gray-600">{{$appointment->patient->name}}</div>
												@endif
												<!--begin::Details item-->
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Appointment Date</div>
												<div class="text-gray-600">{{$appointment->appointment_date}}</div>
												<!--begin::Details item-->
												<!--begin::Details item-->
												@if($appointment->doctor)
												<div class="fw-bold mt-5">Doctor Name</div>
												<div class="text-gray-600">{{$appointment->doctor->name}}</div>
												@endif
												<!--begin::Details item-->
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Start Time</div>
												<div class="text-gray-600">{{$appointment->start_time}}</div>
												<!--begin::Details item-->
												<!--begin::Details item-->
												<div class="fw-bold mt-5">End Time</div>
												<div class="text-gray-600">{{$appointment->end_time}}</div>
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Consultation Type</div>
												<div class="text-gray-600">
													@if($appointment->consultation_type == 1)
														Virtual Consultation
													@elseif($appointment->consultation_type == 2)
														Physical Consultation
													@elseif($appointment->consultation_type == 3)
														Home Consultation
													@endif
												</div>
												<!--begin::Details item-->
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Appointment Status</div>
												<div class="text-gray-600">
													@if($appointment->appointment_status == 1)
													Scheduled
													@elseif($appointment->appointment_status == 2)
													Ongoing
													@elseif($appointment->appointment_status == 3)
													Completed
													@endif
												</div>
												<!--begin::Details item-->
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Payment Status</div>
												<div class="text-gray-600">
													@if($appointment->payment_status == 0)
													Incomplete
													@elseif($appointment->payment_status == 1)
													Completed
													@endif
												</div>
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Created On</div>
												<div class="text-gray-600">{{$appointment->created_date}}</div>
											</div>
										</div>
										<!--end::Details content-->
									</div>
									<!--end::Card body-->
								</div>
								<!--end::Card-->
								<div class="card mb-5 mb-xl-8">
							<!--begin::Card header-->
							<div class="card-header border-0">
								<div class="card-title">
									<h3 class="fw-bold m-0">Patient Address</h3>
								</div>
							</div>
							<!--end::Card header-->
							<!--begin::Card body-->
							<div class="card-body pt-2">
								@if(!empty($appointment->patient) && !empty($appointment->patient->patientDetail))
									<div class="fw-bold mt-5">Patient Apartment No</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->apartment_no ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Address</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->address ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Landmark</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->landmark ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient City</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->city ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient State</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->state ?? "-"}}</div>
									<div class="fw-bold mt-5">Patient Country</div>
									<div class="text-gray-600">{{$appointment->patient->patientDetail->country ?? "-"}}</div>
								@endif
							</div>
						</div>
						<!--end::Card-->
						<div class="card mb-5 mb-xl-8">
							<!--begin::Card header-->
							<div class="card-header border-0">
								<div class="card-title">
									<h3 class="fw-bold m-0">Clinical Details</h3>
								</div>
							</div>
							<!--end::Card header-->
							<!--begin::Card body-->
							<div class="card-body pt-2">
								@if(!empty($appointment->doctor) && !empty($appointment->doctor->doctorClinic))
									<div class="fw-bold mt-5">Clinic Name</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_name ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Moible</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->apartment_no ?? "-"}}</div>
									<div class="fw-bold mt-5">Apartment No</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_address ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Address</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_city ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Landmark</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_landmark ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic City</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_city ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic State</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_state ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Country</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->country ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Open Time</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_open_time ?? "-"}}</div>
									<div class="fw-bold mt-5">Clinic Close Time</div>
									<div class="text-gray-600">{{$appointment->doctor->doctorClinic->clinic_close_time ?? "-"}}</div>
								@endif
							</div>
						</div>
							</div>
							<div>
								<!--end::Sidebar-->
								<!--begin::Content-->
								<div class="flex-lg-row-fluid ms-lg-15 col-xl-12">

									<ul style="background-color:white;padding-left: 30px;" class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
										
										<!--begin::Nav item-->
										<li class="nav-item mt-2">
											<a id="prescriptionview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.view',$appointment->id)}}">Prescriptions</a>
										</li>
										<!--end::Nav item-->
										<!--begin::Nav item-->
										<li class="nav-item mt-2">
											<a id="labview" class="nav-link text-active-primary ms-0 me-10 py-5" href="{{route('admin.appointment.labview',$appointment->id)}}">Lab Tests</a>
										</li>
										<!--end::Nav item-->
										<!--begin::Nav item-->
										<li class="nav-item mt-2">
											<a id="notesview" class="nav-link text-active-primary ms-0 me-10 py-5 active" href="{{route('admin.appointment.notesview',$appointment->id)}}">Notes</a>
										</li>
										<!--end::Nav item-->
									</ul>
			
									<!--begin:::Tab content-->
									<div class="tab-content" id="myTabContent">
										
										<!--begin:::Tab pane-->
										<div class="tab-pane fade show active" id="kt_user_view_overview_events_and_logs_tab" role="tabpanel">
											<div class="card">
								                <div class="card-body py-4">
								                    <table id="lab-test_table" class="table align-middle table-row-dashed fs-6 gy-5">
								                        <thead>
								                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
								                                <th>Id</th>
								                                <th>Subject</th>
								                                <th>Notes</th>
								                                <th>Created Date</th>
								                                
								                            </tr>
								                        </thead>
								                        @forelse($appointmentnotes as $key => $value)
								                        <tbody class="text-gray-600 fw-semibold">
								                        	<td>{{$key+1}}</td>
								                        	<td>{{$value->subject}}</td>
								                        	<td>{{$value->notes}}</td>
								                        	<td>{{$value->created_date}}</td>
								                        </tbody>
								                        @empty
								                        <tbody class="text-gray-600 fw-semibold">
								                        	<td>No Notes Found.</td>
								                        </tbody>
								                        @endforelse
								                    </table>
								                    <div class="pagination-custom">
													@php
														echo $appointmentnotes->render();
													@endphp
													</div>
								                </div>
								            </div>
										</div>
										<!--end:::Tab pane-->
									</div>
									<!--end:::Tab content-->
								</div>
								<!--end::Content-->

								<div class="flex-lg-row-fluid ms-lg-15 col-xl-12 mt-10">
	                        <div id="kt_app_content" class="app-content flex-column-fluid">
	                            <div id="kt_app_content_container" class="container-xxl p-0">
	                                <div class="card">
	                                    <div class="card-header border-0 pt-6">

	                                        <div class="card-title">
	                                            <div class="d-flex align-items-center position-relative my-1">
	                                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
	                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
	                                                        xmlns="http://www.w3.org/2000/svg">
	                                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
	                                                            transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
	                                                        <path
	                                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
	                                                            fill="currentColor" />
	                                                    </svg>
	                                                </span>
	                                                <input type="text" data-kt-user-table-filter="search"
	                                                    class="form-control form-control-solid w-250px ps-14" placeholder="Search ticket" />
	                                            </div>
	                                        </div>

	                                        <div class="card-toolbar">
	                                            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
	                                                <input id="datepick" class="form-control form-control-solid w-250px ps-14" placeholder="Pick date"
	                                                name="search" />
	                                            </div>

	                                        </div>

	                                    </div>

	                                    <div class="card-body py-4">

	                                        <table id="appointment_ticket_table" class="table align-middle table-row-dashed fs-6 gy-5">
	                                            <thead>
	                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
	                                                    <th>Id</th>
	                                                    <th>User Name</th>
	                                                    <th>Subject</th>
	                                                    <th>Description</th>
	                                                    <th>Status</th>
	                                                    <th>Unique Id</th>
	                                                    <th>Created Date</th>
	                                                </tr>
	                                            </thead>
	                                            <tbody class="text-gray-600 fw-semibold">
	                                            </tbody>
	                                        </table>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                    </div>

	                    <div class="flex-lg-row-fluid ms-lg-15 col-xl-12 mt-10">
	                        <div id="kt_app_content" class="app-content flex-column-fluid">
	                            <div id="kt_app_content_container" class="container-xxl p-0">
	                                <div class="card">
	                                    <div class="card-header border-0 pt-6">

	                                        <div class="card-title">
	                                            <div class="d-flex align-items-center position-relative my-1">
	                                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
	                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
	                                                        xmlns="http://www.w3.org/2000/svg">
	                                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
	                                                            transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
	                                                        <path
	                                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
	                                                            fill="currentColor" />
	                                                    </svg>
	                                                </span>
	                                                <input type="text" data-kt-user-table-filter="search1"
	                                                    class="form-control form-control-solid w-250px ps-14" placeholder="Search Log" />
	                                            </div>
	                                        </div>

	                                        <div class="card-toolbar">
	                                            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
	                                                <input id="datepick" class="form-control form-control-solid w-250px ps-14" placeholder="Pick date"
	                                                name="search1" />
	                                            </div>

	                                        </div>

	                                    </div>

	                                    <div class="card-body py-4">

	                                        <table id="call_logs_table" class="table align-middle table-row-dashed fs-6 gy-5">
	                                            <thead>
	                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
	                                                    <th>Id</th>
	                                                    <th>Channel Name</th>
	                                                    <th>Initiator</th>
	                                                    <th>Receiver</th>
	                                                    <th>Date time</th>
	                                                    <th>Duration</th>
	                                                    <th>Created Date</th>
	                                                </tr>
	                                            </thead>
	                                            <tbody class="text-gray-600 fw-semibold">
	                                            </tbody>
	                                        </table>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
							</div>
							

						</div>
						<!--end::Layout-->
						
					</div>
					<!--end::Content container-->
				</div>
				<!--end::Content-->
			</div>
			<!--end::Content wrapper-->

			</div>
			<!--end::Footer-->
		</div>
		<!--end:::Main-->
		<!--begin::Javascript-->

		<!--end::Custom Javascript-->
		<!--end::Javascript-->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript">
	$(document).ready(function() {
	    var current = window.location.pathname.split("/")[4];
	    if(current == "view"){
	    	$('#view').addClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "labview"){
	    	$('#view').removeClass('active');
	    	$('#labview').addClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "view"){
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').removeClass('active');
	    }else if(current == "notesview"){
	    	$('#view').removeClass('active');
	    	$('#labview').removeClass('active');
	    	$('#notesview').addClass('active');
	    }
	    
	});
</script>
@endsection

@section('css')
<link href="{{asset('assets/plugins/custom/datatables/datatables.bundle.css')}}" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css"
    href="{{ asset('assets/plugins/custom/daterangepicker/daterangepicker.css') }}" />
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('assets/plugins/custom/daterangepicker/moment.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('assets/plugins/custom/daterangepicker/daterangepicker.min.js') }}">
</script>
<script type="text/javascript">
var appointment_id = {!! json_encode($appointment->id) !!};
var getListRoute = '{{ route('admin.appointment.supportview',"appointment_id") }}';
var getCallListRoute = '{{ route('admin.appointment.calllogsview',"appointment_id") }}';
getListRoute = getListRoute.replace('appointment_id', appointment_id);
getCallListRoute = getCallListRoute.replace('appointment_id', appointment_id);
</script>
<script src="{{asset('assets/plugins/custom/datatables/datatables.bundle.js')}}" type="text/javascript"></script>
<script type="text/javascript" src="{{asset('js/appointmentticket.js')}}"></script>
<script type="text/javascript" src="{{asset('js/calllogs.js')}}"></script>
<script type="text/javascript" src="{{ asset('js/alert.js')}}"></script>
@endsection
