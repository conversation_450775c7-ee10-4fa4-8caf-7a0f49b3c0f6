<?php

namespace App\Transformers;

use App\Enums\UserType;
use App\Models\User;
use League\Fractal\TransformerAbstract;

class UserAllDetailTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        $users = [
            'id' => $user->id,
            'user_type' => $user->user_type,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'dial_code' => $user->dial_code,
            'mobile' => $user->mobile,
            'steps' => $user->steps,
            'status' => $user->status,
            'avatar' => $user->avatar_url ?? "",
            'created_at' => $user->created_at->timestamp
        ];

        if ($user->user_type == UserType::Doctor) {
            $users['physicalConsultation'] = !empty($user->doctorDetail) ? $user->doctorDetail->physical_consultation : 0;
        }

        return $users;
    }
}
