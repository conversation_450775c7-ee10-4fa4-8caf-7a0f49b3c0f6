<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('patient_id')->nullable();
            $table->string('doctor_id')->nullable();
            $table->string('request_id')->nullable();
            $table->string('payment_id');
            $table->string('order_info')->nullable();
            $table->string('secure_hash')->nullable();
            $table->string('transaction_id');
            $table->string('payment_type');
            $table->string('amount');
            $table->string('currency');
            $table->tinyInteger('payment_status')->comment('1 = init, 1 = complete, 2 = fail')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
}
