@extends('admin.layouts.app')
@section('content')
		<!--begin::Main-->
		<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
			<!--begin::Content wrapper-->
			<div class="d-flex flex-column flex-column-fluid">
				{{ Breadcrumbs::render('admin.patient.view') }}
				<!--begin::Content-->
				<div id="kt_app_content" class="app-content flex-column-fluid">
					<!--begin::Content container-->
					<div id="kt_app_content_container" class="app-container container-xxl">
						<!--begin::Layout-->
						<div class="d-flex flex-column flex-lg-row">
							<!--begin::Sidebar-->
							<div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
								<!--begin::Card-->
								<div class="card mb-5 mb-xl-8">
									<!--begin::Card body-->
									<div class="card-body">
										<!--begin::Summary-->
										<!--begin::User Info-->
										<div class="d-flex flex-center flex-column py-5">
											<!--begin::Avatar-->
											<div class="symbol symbol-100px symbol-circle mb-7">
												<img src="{{ $patient->avatar_url }}" alt="image" />
											</div>
											<!--end::Avatar-->
											<!--begin::Name-->

											<!--end::Name-->
											<!--begin::Position-->
											<div>
												<!--begin::Badge-->
												<div class="fs-3 text-gray-800 text-hover-primary fw-bold mb-3">{{$patient->first_name}} {{$patient->last_name}}</div>
												<!--begin::Badge-->
											</div>
											<a class="badge badge-lg badge-light-primary d-inline mb-5">{{$patient->username}}</a>
											<!--end::Position-->
											<!--begin::Info-->

										</div>
										<!--end::User Info-->
										<!--end::Summary-->
										<!--begin::Details toggle-->
										<div class="d-flex flex-stack fs-4 py-3">
											<div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_user_view_details" role="button" aria-expanded="false" aria-controls="kt_user_view_details">Details
											<span class="ms-2 rotate-180">
												<!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
												<span class="svg-icon svg-icon-3">
													<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z" fill="currentColor" />
													</svg>
												</span>
												<!--end::Svg Icon-->
											</span></div>
										</div>
										<!--end::Details toggle-->
										<div class="separator"></div>
										<!--begin::Details content-->
										<div id="kt_user_view_details" class="collapse show">
											<div class="pb-5 fs-6">
												<!--begin::Details item-->
												<div class="fw-bold mt-5">Mobile</div>
											<div class="text-gray-600">{{$patient->dial_code}} {{$patient->mobile}}</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Language</div>
											<div class="text-gray-600">
												<a href="#" class="text-gray-600 text-hover-primary">{{($patient->language == 1) ? "es":"en"}}</a>
											</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											@if($patient->patientDetail)
											<div class="fw-bold mt-5">Gender</div>
											<div class="text-gray-600">{{($patient->patientDetail->gender == 1)? "Male": "Female"}}</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Status</div>
											<div class="text-gray-600">{{($patient->status == 1) ? "Active":"Inactive"}}</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Address</div>
											<div class="text-gray-600">{{$patient->patientDetail->apartment_no}},{{$patient->patientDetail->address}}</div>
											<div class="fw-bold mt-5">Country</div>
											<div class="text-gray-600">{{$patient->patientDetail->country}}</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Height</div>
											<div class="text-gray-600">{{decrypt($patient->patientDetail->height)}} ft</div>
											<div class="fw-bold mt-5">Weight</div>
											<div class="text-gray-600">
												<a href="#" class="text-gray-600 text-hover-primary">{{decrypt($patient->patientDetail->weight)}} kg</a>
											</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Age</div>
											<div class="text-gray-600">{{decrypt($patient->patientDetail->age)}} Yrs</div>
											<!--begin::Details item-->
											<!--begin::Details item-->
											<div class="fw-bold mt-5">Landmark</div>
											<div class="text-gray-600">{{$patient->patientDetail->landmark}}</div>
											<div class="fw-bold mt-5">City</div>
											<div class="text-gray-600">{{$patient->patientDetail->city}}</div>
											<div class="fw-bold mt-5">State</div>
											<div class="text-gray-600">{{$patient->patientDetail->state}}</div>
											@endif
											<div class="fw-bold mt-5">Created On</div>
											<div class="text-gray-600">{{$patient->created_date}}</div>
											<div class="fw-bold mt-5">Updated On</div>
											<div class="text-gray-600">{{$patient->updated_date}}</div>

											<!--begin::Details item-->
											<!--begin::Details item-->

											<div class="fw-bold mt-5">Status</div>
											<div class="text-gray-600">{{($patient->status == 1) ? "Active":"Inactive"}}</div>
											<!--begin::Details item-->
											<!--begin::Details item-->

											<div class="fw-bold mt-5">Social Id</div>
											<div class="text-gray-600">{{$patient->provider_id ?? "-"}}</div>
											<div class="fw-bold mt-5">Social Type</div>
											<div class="text-gray-600">{{$patient->provider_name ?? "-"}}</div>


											<div class="fw-bold mt-5">Health Issue</div>
											<div>
												@forelse($healthIssue as $key => $value)
													@if($loop->last)
												        {{$value}}
												    @else
												        {{$value}},
												    @endif
												@empty
												-
												@endforelse
											</div>

											<div class="fw-bold mt-5">Other Issue</div>
											<div class="text-gray-600">{{$otherIssue ?? "-"}}</div>

												<!--begin::Details item-->
											</div>
										</div>
										<!--end::Details content-->
									</div>
									<!--end::Card body-->
								</div>
								<!--end::Card-->
							</div>
							<!--end::Sidebar-->
							<!--begin::Content-->
							<div class="flex-lg-row-fluid ms-lg-15">

								<div class="card card-flush mb-6 mb-xl-9">
									<!--begin::Card header-->
									<div class="card-header mt-6">
										<!--begin::Card title-->
										<div class="card-title flex-column">
											<h2>Transactions</h2>
										</div>
										<!--end::Card title-->
									</div>
									<!--end::Card header-->
									<!--begin::Card body-->
									<div class="card-body d-flex flex-column pt-2">
										@forelse($transaction as $key => $value)
										<!--begin::Time-->
										<div class="d-flex flex-stack position-relative mt-6">
											<!--begin::Bar-->
											<div class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0"></div>
											<!--end::Bar-->
											<!--begin::Info-->
											<div class="fw-semibold ms-5">
												<!--begin::Time-->
												<div class="fs-7">Doctor : <b>{{$value->doctor->name}}</b>
												</div>
												<!--end::Time-->
												<!--begin::Title-->
												@if($value->transaction_type == 1)
												<a class="fs-5 fw-bold text-dark text-hover-primary mb-2">Transaction Id : #{{$value->transaction_id}} | Transaction Type : <span class="badge badge-light-primary">Consultation</span></a>
												@elseif($value->transaction_type == 2)
												<a class="fs-5 fw-bold text-dark text-hover-primary mb-2">Transaction Id : #{{$value->transaction_id}} | Transaction Type : <span class="badge badge-light-primary">Pharmacy</span></a>
												@elseif($value->transaction_type == 3)
												<a class="fs-5 fw-bold text-dark text-hover-primary mb-2">Transaction Id : #{{$value->transaction_id}} | Transaction Type : <span class="badge badge-light-primary">Subscription</span></a>
												@endif
												<!--end::Title-->
												<!--begin::User-->
												<div class="fs-7 text-muted">Request Id : {{$value->request_id}}
												</div>
												<div class="fs-7 text-muted">Amount: {{$value->amount}} {{$value->currency}}
												</div>
												<!--end::User-->
											</div>
											<!--end::Info-->
											<!--begin::Action-->
											<div>
											@if($value->payment_status == 1)
											<a class="btn btn-warning bnt-active-light-warning btn-sm">Init</a>
											@elseif($value->payment_status == 2)
											<a class="btn btn-success bnt-active-light-success btn-sm">Complete</a>
											@elseif($value->payment_status == 3)
											<a class="btn btn-danger bnt-active-light-danger btn-sm">Failed</a>
											@endif
											<!--end::Action-->
										</div>
										</div>
										@empty
										<p>No Transactions</p>
										@endforelse

										<div class="pagination-custom">
										@php
											echo $transaction->links('pagination.custom');
										@endphp
										</div>
									</div>
									<!--end::Card body-->
								</div>
								<!--begin:::Tabs-->
								<ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-8">
									<!--begin:::Tab item-->
									<li class="nav-item">
										<a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_user_view_overview_tab">Upcoming</a>
									</li>
									<!--end:::Tab item-->
									<!--begin:::Tab item-->
									<li class="nav-item">
										<a class="nav-link text-active-primary pb-4" data-kt-countup-tabs="true" data-bs-toggle="tab" href="#kt_user_view_overview_security">Ongoing</a>
									</li>
									<!--end:::Tab item-->
									<!--begin:::Tab item-->
									<li class="nav-item">
										<a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_user_view_overview_events_and_logs_tab">Completed</a>
									</li>
									<!--end:::Tab item-->

								</ul>
								<!--end:::Tabs-->
								<!--begin:::Tab content-->
								<div class="tab-content" id="myTabContent">
									<!--begin:::Tab pane-->
									<div class="tab-pane fade show active" id="kt_user_view_overview_tab" role="tabpanel">
										<!--begin::Card-->
										<div class="card card-flush mb-6 mb-xl-9">
											<!--begin::Card header-->
											<div class="card-header mt-6">
												<!--begin::Card title-->
												<div class="card-title flex-column">
													<h2 class="mb-1">Patient's Appointments</h2>
													<!-- <div class="fs-6 fw-semibold text-muted">2 upcoming meetings</div> -->
												</div>
												<!--end::Card title-->

											</div>
											<!--end::Card header-->
											<!--begin::Card body-->
											<div class="card-body p-9 pt-4">
												<!--begin::Dates-->
												<!--end::Dates-->
												<!--begin::Tab Content-->
												<div class="tab-content">
													<!--begin::Day-->
													<div id="kt_schedule_day_0" class="tab-pane fade show active">

														@forelse($appointment_upcoming as $key => $value)

														<!--begin::Time-->
														<div class="d-flex flex-stack position-relative mt-6">
															<!--begin::Bar-->
															<div class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0"></div>
															<!--end::Bar-->
															<!--begin::Info-->
															<div class="fw-semibold ms-5">
																<!--begin::Time-->
																<div class="fs-7 mb-1">Appointment Date : {{$value->appointment_date}}
																	<span class="fs-7 text-muted text-uppercase">{{$value->start_timed}} - {{$value->end_timed}}
																	</span>
																</div>
																<!--end::Time-->
																<!--begin::Title-->
																<a href="#" class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment Id : #{{$value->unique_id}}</a>
																<div>Consultation Type:
																	@if($value->consultation_type == 1)
																	Virtual Consultation
																	@elseif($value->consultation_type == 2)
																	Physical Consultation
																	@elseif($value->consultation_type == 3)
																	Home Consultation
																	@endif
																</div>
																<!--end::Title-->
																<!--begin::User-->
																<div class="fs-7 text-muted">Lead by
																<a href="#">{{$value->doctor->name}}</a></div>
																<!--end::User-->
															</div>
															<!--end::Info-->
															<!--begin::Action-->
															<a href="{{route('admin.appointment.view',$value->id)}}" class="btn btn-light bnt-active-light-primary btn-sm">View</a>
															<!--end::Action-->
														</div>
														@empty
    													<p>No Appointments</p>
														@endforelse

														<div class="pagination-custom">
														@php
															echo $appointment_upcoming->links('pagination.custom');
														@endphp
														</div>

														<!--end::Time-->
													</div>
													<!--end::Day-->

												</div>
												<!--end::Tab Content-->
											</div>
											<!--end::Card body-->
										</div>
										<!--end::Card-->

									</div>
									<!--end:::Tab pane-->
									<!--begin:::Tab pane-->
									<div class="tab-pane fade" id="kt_user_view_overview_security" role="tabpanel">
										<!--begin::Card-->
										<div class="card card-flush mb-6 mb-xl-9">
											<!--begin::Card header-->
											<div class="card-header mt-6">
												<!--begin::Card title-->
												<div class="card-title flex-column">
													<h2 class="mb-1">Patient's Appointments</h2>
													<!-- <div class="fs-6 fw-semibold text-muted">2 upcoming meetings</div> -->
												</div>
												<!--end::Card title-->

											</div>
											<!--end::Card header-->
											<!--begin::Card body-->
											<div class="card-body p-9 pt-4">

												<!--begin::Tab Content-->
												<div class="tab-content">
													<!--begin::Day-->
													<div id="kt_schedule_day_0" class="tab-pane fade show active">

														@forelse($appointment_ongoing as $key => $value)
														<!--begin::Time-->
														<div class="d-flex flex-stack position-relative mt-6">
															<!--begin::Bar-->
															<div class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0"></div>
															<!--end::Bar-->
															<!--begin::Info-->
															<div class="fw-semibold ms-5">
																<!--begin::Time-->
																<div class="fs-7 mb-1">Appointment Date : {{$value->appointment_date}}
																<span class="fs-7 text-muted text-uppercase">{{$value->start_timed}} - {{$value->end_timed}}</span></div>
																<!--end::Time-->
																<!--begin::Title-->
																<a href="#" class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment Id : #{{$value->unique_id}}</a>
																<div>Consultation Type:
																	@if($value->consultation_type == 1)
																	Virtual Consultation
																	@elseif($value->consultation_type == 2)
																	Physical Consultation
																	@elseif($value->consultation_type == 3)
																	Home Consultation
																	@endif
																</div>
																<!--end::Title-->
																<!--begin::User-->
																<div class="fs-7 text-muted">Lead by
																<a href="#">{{$value->doctor->name}}</a></div>
																<!--end::User-->
															</div>
															<!--end::Info-->
															<!--begin::Action-->
															<a href="{{route('admin.appointment.view',$value->id)}}" class="btn btn-light bnt-active-light-primary btn-sm">View</a>
															<!--end::Action-->
														</div>
														@empty
    													<p>No Appointments</p>
														@endforelse

														<div class="pagination-custom">
														@php
														echo $appointment_ongoing->links('pagination.custom');
														@endphp
														</div>

														<!--end::Time-->
													</div>
													<!--end::Day-->

												</div>
												<!--end::Tab Content-->
											</div>
											<!--end::Card body-->
										</div>
										<!--end::Card-->
									</div>
									<!--end:::Tab pane-->
									<!--begin:::Tab pane-->
									<div class="tab-pane fade" id="kt_user_view_overview_events_and_logs_tab" role="tabpanel">
										<!--begin::Card-->
										<!--begin::Card-->
										<div class="card card-flush mb-6 mb-xl-9">
											<!--begin::Card header-->
											<div class="card-header mt-6">
												<!--begin::Card title-->
												<div class="card-title flex-column">
													<h2 class="mb-1">Patient's Appointments</h2>
													<!-- <div class="fs-6 fw-semibold text-muted">2 upcoming meetings</div> -->
												</div>
												<!--end::Card title-->

											</div>
											<!--end::Card header-->
											<!--begin::Card body-->
											<div class="card-body p-9 pt-4">

												<!--begin::Tab Content-->
												<div class="tab-content">
													<!--begin::Day-->
													<div id="kt_schedule_day_0" class="tab-pane fade show active">

														@forelse($appointment_completed as $key => $value)
														<!--begin::Time-->
														<div class="d-flex flex-stack position-relative mt-6">
															<!--begin::Bar-->
															<div class="position-absolute h-100 w-4px bg-secondary rounded top-0 start-0"></div>
															<!--end::Bar-->
															<!--begin::Info-->
															<div class="fw-semibold ms-5">
																<!--begin::Time-->
																<div class="fs-7 mb-1">Appointment Date : {{$value->appointment_date}}
																<span class="fs-7 text-muted text-uppercase">{{$value->start_timed}} - {{$value->end_timed}}</span></div>
																<!--end::Time-->
																<!--begin::Title-->
																<a href="#" class="fs-5 fw-bold text-dark text-hover-primary mb-2">Appointment Id : #{{$value->unique_id}}</a>
																<div>Consultation Type:
																	@if($value->consultation_type == 1)
																	Virtual Consultation
																	@elseif($value->consultation_type == 2)
																	Physical Consultation
																	@elseif($value->consultation_type == 3)
																	Home Consultation
																	@endif
																</div>
																<!--end::Title-->
																<!--begin::User-->
																<div class="fs-7 text-muted">Lead by
																<a href="#">{{$value->doctor->name}}</a></div>
																<!--end::User-->
															</div>
															<!--end::Info-->
															<!--begin::Action-->
															<a href="{{route('admin.appointment.view',$value->id)}}" class="btn btn-light bnt-active-light-primary btn-sm">View</a>
															<!--end::Action-->
														</div>
														@empty
    													<p>No Appointments</p>
														@endforelse

														<div class="pagination-custom">
														@php
														echo $appointment_completed->links('pagination.custom');
														@endphp
														</div>

														<!--end::Time-->
													</div>
													<!--end::Day-->

												</div>
												<!--end::Tab Content-->
											</div>
											<!--end::Card body-->
										</div>
										<!--end::Card-->
										<!--end::Card-->
									</div>
									<!--end:::Tab pane-->
								</div>
								<!--end:::Tab content-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Layout-->
					</div>
					<!--end::Content container-->
				</div>
				<!--end::Content-->
			</div>
			<!--end::Content wrapper-->

			</div>
			<!--end::Footer-->
		</div>
		<!--end:::Main-->
		<!--begin::Javascript-->

		<!--end::Custom Javascript-->
		<!--end::Javascript-->
@endsection
