<?php

namespace App\Http\Requests;

use App\Http\CommonTraits\ApiValidationMessageFormat;
use Illuminate\Foundation\Http\FormRequest;

class NotesRequest extends FormRequest
{
    use ApiValidationMessageFormat;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'appointment_id' => 'required',
        ];
    }
}
