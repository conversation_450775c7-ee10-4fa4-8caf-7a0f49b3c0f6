<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;

class PatientLabTest extends Model
{
    use HasFactory, TransformableTrait;

     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['appointment_id', 'unique_id', 'lab_test_id', 'lab_test_name', 'lab_test_pdf'];

    /**
     * The labtest has description.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function patientLabTestDescription()
    {
        return $this->hasMany(PatientLabTestDescription::class, 'patient_lab_test_id');
    }

    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function appointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'appointment_id', 'id');
    }
}
