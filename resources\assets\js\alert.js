"use strict";
// Class definition
var KTSweetAlert2Demo = (function () {
    // Demos

    var initDemos = function () {
        $("body").on("click", ".delete", function () {
            var id = $(this).attr("id");
            var url = $(this).attr("data-url");
            var name = $(this).attr("data-name");
            var table = $(this).attr("data-table");
            Swal.fire({
                text: "Are you sure you want to delete " + name + "?",
                icon: "warning",
                showCancelButton: true,
                buttonsStyling: false,
                confirmButtonText: "Yes, delete!",
                cancelButtonText: "No, cancel",
                customClass: {
                    confirmButton: "btn fw-bold btn-danger",
                    cancelButton: "btn fw-bold btn-active-light-primary",
                },
            }).then(function (result) {
                if (result.value) {
                    $.ajax({
                        url: url,
                        mehtod: "get",
                        data: { id: id },
                        success: function () {
                            Swal.fire({
                                text: "You have deleted " + name + "!.",
                                icon: "success",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn fw-bold btn-primary",
                                },
                            }).then(function () {
                                // Remove current row
                                $('#'+table).DataTable().row($(parent)).remove().draw();
                            });
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            Swal.fire({
                                text: name + " was not deleted.",
                                icon: "error",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn fw-bold btn-primary",
                                },
                            });
                        },
                    });
                } else if (result.dismiss === "cancel") {
                    Swal.fire({
                        text: name + " was not deleted.",
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Ok, got it!",
                        customClass: {
                            confirmButton: "btn fw-bold btn-primary",
                        },
                    });
                }
            });
        });
    };

    return {
        // Init
        init: function () {
            initDemos();
        },
    };
})();

// Class Initialization
jQuery(document).ready(function () {
    KTSweetAlert2Demo.init();
});
