<?php

namespace App\Transformers;

use App\Models\PatientLabTestDescription;
use League\Fractal\TransformerAbstract;

class PatientLabTestDescriptionTransformer extends TransformerAbstract
{
    
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientLabTestDescription $patientLabTestDescription)
    {
        return [
            'id' => $patientLabTestDescription->id,
            'lab_test_id' => $patientLabTestDescription->lab_test_id,
            'lab_test_name' => $patientLabTestDescription->lab_test_name,
            'created_at' => $patientLabTestDescription->created_at
        ];
    }
}
