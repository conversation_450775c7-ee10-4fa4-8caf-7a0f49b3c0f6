<?php

namespace App\Transformers;

use App\Models\Speciality;
use League\Fractal\TransformerAbstract;

class SpecialityDetailTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Speciality $speciality)
    {
        return [
            'id' => $speciality->id,
            'specialist' => $speciality->specialist,
            'symptoms' => $speciality->symptom->symptoms_name
        ];
    }
}
