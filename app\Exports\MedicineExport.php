<?php

namespace App\Exports;

use App\Models\Medicine;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class MedicineExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function query()
    {
        return Medicine::query();
    }

    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID", "name", "status", "Created At"];
    }

    public function map($medicine): array
    {
        return [
            $medicine->id,
            $medicine->name,
            ($medicine->status == 1) ? "Active" : "Inactive",
            $medicine->created_at
        ];
    }
}
