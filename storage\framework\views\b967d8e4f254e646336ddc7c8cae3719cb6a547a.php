
<?php $__env->startSection('content'); ?>

<div class="d-flex flex-column flex-root" id="kt_app_root">
    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
            <div class="d-flex flex-center flex-lg-start flex-column">
                <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px"
                        src="<?php echo e(asset('assets/media/auth/logo.png')); ?>" alt="" />
                <!--end::Logo-->
                <!--begin::Title-->
                <h2 class="text-blue fw-normal font-blue m-5">Tu salud está en tus manos</h2>
                <!--end::Title-->
            </div>
        </div>
        <div class="d-flex flex-center w-lg-50 p-10">
            <div class="card rounded-3 w-md-550px">
                <div class="card-body d-flex flex-column p-10 p-lg-20 pb-lg-10">
                    <div class="d-flex flex-center flex-column-fluid pb-15 pb-lg-20">
                        <?php echo Form::open(['class'=>'form w-100 admin-login-form','id'=>'admin-login-form kt_sign_in_form']); ?>

                        <div class="text-center mb-11">
                            <h1 class="text-dark fw-bolder mb-3"><?php echo app('translator')->get('auth.signIn'); ?></h1>
                        </div>
                        <?php if($errors->has('email')): ?>
                        <div id="email-error" class="error invalid-feedback" style="display: block;">
                            <?php echo e($errors->first('email')); ?>

                        </div>
                        <?php elseif($errors->has('password')): ?>
                        <div id="password-error" class="error invalid-feedback" style="display: block;">
                            <?php echo e($errors->first('password')); ?>

                        </div>
                        <?php endif; ?>
                        <div class="fv-row mb-8">
                            <?php echo Form::email('email', old('email'), $attributes =
                            ['class'=>'form-control','autocomplete'=>'off','placeholder'=> trans('auth.email')]); ?>

                        </div>
                        <div class="fv-row mb-3">
                            <div class="input-group">
                                <?php echo Form::password('password', ['class' => 'form-control','placeholder'=>trans('auth.password'), 'id'=>'password']); ?>

                                <i type="button" class="fa fa-eye check-eye" style="position: absolute;right: 17px;top: 17px;color: #7c7c7c;z-index: 999;"></i>
                                <i type="button" class="fa fa-eye-slash uncheck-eye" style="position: absolute;right: 17px;top: 17px;color: #7c7c7c;z-index: 999;"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                            <div>
                                <label class="kt-checkbox">
                                    <?php echo Form::checkbox('remember',true, old('remember') ? 'checked' : ''); ?>

                                    <?php echo app('translator')->get('auth.remember_me'); ?>
                                    <span></span>
                                </label>
                            </div>
                            <?php if(str_contains(url()->current(), 'admin')): ?>
                            <a href="<?php echo e(route('password.request')); ?>" id="kt_login_forgot" class="link-primary">Forgot
                                Password ?</a>
                            <?php endif; ?>
                        </div>

                        <div class="d-grid mb-10">
                            <?php echo Form::submit(trans('auth.signIn'),['id'=>'kt_login_signin_submit','class'=>'btn
                            kt-login__btn-primary btn-blue']); ?>

                        </div>
                        <?php echo Form::close(); ?>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<script type="text/javascript" src="<?php echo e(asset('vendor/jsvalidation/js/jsvalidation.js')); ?>"></script>
<?php echo JsValidator::formRequest('App\Http\Requests\Auth\AdminLoginRequest', '.admin-login-form'); ?>

<script>

$(document).ready(function(){

function show() {
     var p = document.getElementById('password');
     p.setAttribute('type', 'text');
 }

 function hide() {
     var p = document.getElementById('password');
     p.setAttribute('type', 'password');
 }

 var pwShown = 0;

 $('.uncheck-eye').hide();
 $('.cmfuncheck-eye').hide();

 $('.check-eye').click(function(e){
     e.preventDefault();
     if (pwShown == 0) {
         pwShown = 1;
         show();
         $('.uncheck-eye').show();
         $('.check-eye').hide();
     } else {
         pwShown = 0;
         hide();
         $('.uncheck-eye').hide();
         $('.check-eye').show();
     }
 });

 $('.uncheck-eye').click(function(e){
     e.preventDefault();
     if (pwShown == 0) {
         pwShown = 1;
         show();
         $('.uncheck-eye').show();
         $('.check-eye').hide();
     } else {
         pwShown = 0;
         hide();
         $('.uncheck-eye').hide();
         $('.check-eye').show();
     }
 });

});

</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.includes.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp\www\Molema\resources\views/admin/auth/login.blade.php ENDPATH**/ ?>