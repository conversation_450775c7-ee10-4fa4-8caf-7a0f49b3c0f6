<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Contracts\Translation\TranslatorTrait;

class Support extends Model
{
    use HasFactory, TranslatorTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id', 'subject', 'description', 'status', 'type', 'ref_id', 'unique_id', 'ref_unique_id'];

    protected $appends = [
        'created_date'
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function doctorAppointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'ref_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function supportChat()
    {
        return $this->hasMany(SupportChat::class, 'support_id', 'id');
    }

    /**
     * Get Created Date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

}
