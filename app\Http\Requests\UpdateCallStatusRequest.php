<?php

namespace App\Http\Requests;

use App\Enums\ConsultationType;
use App\Http\CommonTraits\ApiValidationMessageFormat;
use App\Models\DoctorAppointment;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCallStatusRequest extends FormRequest
{
    use ApiValidationMessageFormat;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $docAppoint = DoctorAppointment::findOrFail(request()->appointment_id);
        return [
            'appointment_id' => 'required',
            'security_code' => $docAppoint->consultation_type !== ConsultationType::VirtualConsultation ? 'required' : ''
        ];
    }
}
