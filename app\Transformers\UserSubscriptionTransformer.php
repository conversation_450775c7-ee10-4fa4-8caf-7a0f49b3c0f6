<?php

namespace App\Transformers;

use App\Models\DoctorSubscription;
use App\Models\Subscription;
use League\Fractal\TransformerAbstract;

class UserSubscriptionTransformer extends TransformerAbstract
{

    /**
     * List of resources to automatically include
     *
     * @var array
     */
    protected array $defaultIncludes = [
        'subscription'
    ];

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorSubscription $doctorSubscription)
    {
        return [
            'user_id' => $doctorSubscription->user_id,
            'subscription_id' => $doctorSubscription->subscription_id,
            'transaction_id' => $doctorSubscription->transaction_id,
            'subscription_start_date' => $doctorSubscription-> subscription_start_date,
            'subscription_end_date' => $doctorSubscription->subscription_end_date
        ];
    }

    /**
     * @param PatientPrescription $patientPrescription
     * @return \League\Fractal\Resource\Collection
     */
    public function includeSubscription(DoctorSubscription $doctorSubscription)
    {
        return $this->item($doctorSubscription->subscription, new SubscriptionTransformer());
    }
}
