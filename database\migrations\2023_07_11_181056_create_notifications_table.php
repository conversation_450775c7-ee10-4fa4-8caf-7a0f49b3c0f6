<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('sender_id');
            $table->bigInteger('receiver_id');
            $table->bigInteger('sender_type')->default('1');
            $table->bigInteger('receiver_type')->default('1');
            $table->bigInteger('ref_id')->nullable();
            $table->string('ref_type')->nullable();
            $table->bigInteger('notification_type');
            $table->string('title')->default('');
            $table->string('description', 1000)->default('');
            $table->boolean('read')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}
