<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('doctor_appointments', function(Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('patient_id');
            $table->bigInteger('doctor_id');
            $table->date('appointment_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->tinyInteger('consultation_type')->comment('1 = virtual consultation, 2 = physical consultation, 3 = home consultation')->default(1);
            $table->tinyInteger('appointment_status')->comment('1 = scheduled, 2 = confirmed, 3 = completed')->default(1);
            $table->tinyInteger('payment_status')->comment('1 = complete, 0 = incomplete')->default(0);
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('doctor_appointments');
	}
};
