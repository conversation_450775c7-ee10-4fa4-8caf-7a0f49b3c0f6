@extends('web.includes.app')
@section('content')
<div id="kt_app_content" class="app-content flex-column-fluid">
    <!--begin::Content container-->
    <div id="kt_app_content_container" class="app-container container-xxl">
        <!--begin::Pricing card-->
        <div class="card" id="kt_pricing">
            <!--begin::Card body-->
            <div class="card-body p-lg-17">
                <!--begin::Plans-->
                <div class="d-flex flex-column">
                    <!--begin::Heading-->
                    <div class="mb-13 text-center">
                        <h1 class="fs-2hx fw-bold mb-5"> @lang('promotional.subscription.title')</h1>
                    </div>

                    <div class="card-body d-flex flex-column pt-2">

                        <!--end::Heading-->
                        @foreach ($doctorSubscription as $docSub)

                        <div class="overflow-auto pb-5">
                            <!--begin::Record-->
                            <div
                                class="d-flex align-items-center border border-dashed border-gray-300 rounded min-w-750px px-7 py-3 mb-5">
                                <!--begin::Title-->
                                <td>
                                    <div class="min-w-300px pe-2">
                                        <div class="fs-6 fw-semibold text-gray-400" style="color: gray !important">
                                            @lang('promotional.subscription.heading.subscription_plan')</div>
                                        <div class="fw-bold text-gray-700">{{ $docSub->subscription->name }}</div>
                                    </div>
                                </td>

                                <td>
                                    <div class="min-w-300px pe-2">
                                        <div class="fs-6 fw-semibold text-gray-400" style="color: gray !important">
                                            @lang('promotional.subscription.heading.effective_timeline')</div>
                                        <div class="fw-bold text-gray-700 badge badge-light text-muted">{{
                                            $docSub->subscription_start_date }} to {{
                                            $docSub->subscription_end_date }}</div>
                                    </div>

                                </td>

                                <td>
                                    <div class="min-w-250px pe-2">
                                        <div class="fs-6 fw-semibold text-gray-400" style="color: gray !important">
                                            @lang('promotional.subscription.heading.purchase_date')</div>
                                        <div class="fw-bold text-gray-700 badge badge-light text-muted">{{
                                            $docSub->updated_at }}</div>
                                    </div>

                                </td>
                                <!--end::Label-->
                                <!--begin::Progress-->
                                <div class="min-w-200px pe-2">
                                    @if($docSub->transaction_id != null)
                                    @if($docSub->subscription_end_date < date('Y-m-d')) <div
                                        class="badge badge-light-danger">@lang('promotional.subscription.expired')</div>
                                @else
                                <div class="badge badge-light-success">@lang('promotional.subscription.subscribed')</div>
                                @endif
                                @else
                                <a href="{{ route('admin.subscription.purchase', ['locale' =>app()->getLocale(), 'id' => $docSub->subscription_id]) }}"
                                    class="cta-btn hovered" style="background: #d3d3d33d">@lang('promotional.subscription.resume_button')</a>
                                @endif
                            </div>
                            <!--end::Progress-->
                        </div>
                        <!--end::Record-->
                    </div>
                    @endforeach

                    <div class="pagination-custom">
                        @php
                        echo $doctorSubscription->links('pagination.custom');
                        @endphp
                    </div>
                </div>
                <!--begin::Row-->
                <div class="mb-13 text-center">
                    <h1 class="fs-2hx fw-bold mb-5">@lang('promotional.subscription.plan.heading')</h1>
                    <div class="text-gray-400 fw-semibold fs-5">@lang('promotional.subscription.plan.description')
                        <a href="#" class="link-primary fw-bold"><EMAIL></a>.
                    </div>
                </div>

                <div class="row g-10">
                    @foreach ($subscriptions as $subscription)
                    <!--begin::Col-->
                    <div class="col-xl-4">
                        <div class="d-flex h-100 align-items-center">
                            <!--begin::Option-->
                            <div
                                class="w-100 d-flex flex-column flex-center rounded-3 bg-light bg-opacity-75 py-15 px-10">
                                <!--begin::Heading-->
                                <div class="mb-7 text-center">
                                    <!--begin::Title-->
                                    <h1 class="text-dark mb-5 fw-bolder">{{ $subscription->name }}</h1>
                                    <!--end::Title-->
                                    <!--begin::Description-->
                                    <div class="text-gray-400 fw-semibold mb-5">
                                        {{ $subscription->description }}
                                    </div>
                                    <!--end::Description-->
                                    <!--begin::Price-->
                                    <div class="text-center">
                                        <span class="mb-2 text-primary">$</span>
                                        <span class="fs-3x fw-bold text-primary" data-kt-plan-price-month="39"
                                            data-kt-plan-price-annual="399">{{
                                            $subscription->price }}</span>
                                        <span class="fs-7 fw-semibold opacity-50">/
                                            <span data-kt-element="period">{{ $subscription->validity
                                                }}</span></span>
                                    </div>
                                    <!--end::Price-->
                                </div>
                                <!--end::Heading-->

                                <!--begin::Select-->
                                <a href="{{ route('admin.subscription.purchase', ['locale' =>app()->getLocale(),'id' => $subscription->id]) }}"
                                    class="cta-btn hovered">@lang('promotional.subscription.plan.purchase_button')</a>
                                <!--end::Select-->
                            </div>
                            <!--end::Option-->
                        </div>
                    </div>
                    <!--end::Col-->
                    @endforeach


                </div>
                <!--end::Row-->
            </div>
            <!--end::Plans-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Pricing card-->
</div>
<!--end::Content container-->
@endsection
