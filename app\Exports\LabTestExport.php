<?php

namespace App\Exports;

use App\Models\LabTest;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class LabTestExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function query()
    {
        return LabTest::query();
    }

    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID", "name", "code", "status", "Created At"];
    }

    public function map($labtest): array
    {
        return [
            $labtest->id,
            $labtest->name,
            $labtest->code,
            ($labtest->status == 1) ? "Active" : "Inactive",
            $labtest->created_at
        ];
    }
}
