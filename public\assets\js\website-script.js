$(document).ready(function () {
  /* Slick Slider */

  if ($(".popular-logos").length > 0) {
    $(".popular-logos").slick({
      dots: false,
      infinite: true,
      arrows: false,
      speed: 1200,
      slidesToShow: 8,
      slidesToScroll: 1,
      autoplay: true,
      responsive: [
        { breakpoint: 1500, settings: { slidesToShow: 6, slidesToScroll: 1 } },
        { breakpoint: 1240, settings: { slidesToShow: 5, slidesToScroll: 1 } },
        { breakpoint: 992, settings: { slidesToShow: 4, slidesToScroll: 1 } },
        { breakpoint: 768, settings: { slidesToShow: 3, slidesToScroll: 1 } },
        { breakpoint: 500, settings: { slidesToShow: 2, slidesToScroll: 1 } },
      ],
    });
  }
  if ($(".authBoxMain").length > 0) {
    $(".authBoxMain").slick({
      dots: false,
      infinite: true,
      arrows: false,
      speed: 1500,
      slidesToShow: 3,
      slidesToScroll: 1,
      vertical: true,
      autoplay: true,
      autoplaySpeed: 4000,
      responsive: [
        { breakpoint: 767, settings: { slidesToShow: 2, slidesToScroll: 1 } },
      ],
    });
  }
  $(".back-to-top a").on("click", function (e) {
    e.preventDefault();
    $("html,body").animate(
      {
        scrollTop: 0,
      },
      1200
    );
  });
});

$(window).scroll(function () {
  var scroll = $(window).scrollTop();
  var top_height = $(".header-section").outerHeight();
  if (scroll > top_height) {
    $(".header-section").addClass("header-fixed");
  } else {
    $(".header-section").removeClass("header-fixed");
  }
});
