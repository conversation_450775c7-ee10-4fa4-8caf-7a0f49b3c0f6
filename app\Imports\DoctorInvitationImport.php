<?php

namespace App\Imports;

use App\Enums\ReferStatus;
use App\Enums\ReferType;
use App\Models\Admin;
use App\Models\DoctorInvitation;
use Maatwebsite\Excel\Concerns\ToModel;

class DoctorInvitationImport implements ToModel
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new DoctorInvitation([
            'user_id' => auth()->user()->id,
            'user_type' => 2,
            'name' => $row[0],
            'email' => $row[1],
            'invitation_code' => generateInvitationCode(),
            'type' => ReferType::Invited(),
            'status' => ReferStatus::Initiated(),
        ]);
    }
}
