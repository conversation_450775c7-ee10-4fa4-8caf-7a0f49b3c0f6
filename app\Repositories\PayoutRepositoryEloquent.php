<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\PayoutRepository;
use App\Models\DoctorWalletTransaction;
use App\Jobs\SendEmails​;
use App\Entities\Payout;
use App\Models\DoctorDetail;
use App\Enums\WalletType;
use App\Models\User;
use Yajra\DataTables\DataTables;

/**
 * Class PayoutRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class PayoutRepositoryEloquent extends BaseRepository implements PayoutRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Payout::class;
    }

    /**
     * return Payout doctor list with wallet balance
     *
     * @return void
     */
    public function getPayoutList($request){
        $payoutList = User::with('doctorDetail')
            ->OnlyDoctor()
            ->where(DoctorDetail::select('wallet_balance')
                ->whereColumn('users.id', 'doctor_details.user_id'), '>', 0
            )
            ->orderBy(DoctorDetail::select('wallet_balance')
                ->whereColumn('users.id', 'doctor_details.user_id'), 'desc'
            );

        return DataTables::of($payoutList)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';



                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.payout.release', ['id' => $row->id]) . '" class="btn btn-primary" data-kt-docs-table-filter="edit_row">
                            Payout
                        </a>
                    </div>
                </div>';

                $html = '<a href="' . route('admin.payout.release', ['id' => $row->id]) . '" class="btn btn-primary btn-sm" data-kt-docs-table-filter="edit_row">
                            Payout
                        </a>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * return Doctor payout wallet balance
     *
     * @return void
     */
    public function getWalletBalance($id){
        $wallet = DoctorDetail::select('wallet_balance')->where('user_id', $id)
            ->first();

        return $wallet->wallet_balance;

    }

    /**
     * return total due payout of all doctors
     *
     * @return void
     */
    public function getTotalPayoutDue(){
        return DoctorDetail::sum('wallet_balance');
    }

    /**
     * release doctor payout due
     *
     * @return void
     */
    public function releasePayout($request){
        $doctorWallet = DoctorDetail::where('user_id', $request['id'])->first();

        // Release payout using UCO Bank API
        $transactionsId = $doctorWallet->uesr_id; // UCO Bank transaction id will take place here

        // Update Doctor wallet balance
        $outstandingBalance = doubleval($doctorWallet->wallet_balance) - doubleval($request['payout_amount']);
        DoctorDetail::where('user_id', $request['id'])->update([
            'wallet_balance' => $outstandingBalance
        ]);


        // Create doctor wallet transaction record
        $data = [];
        $data['transaction_id'] = $transactionsId;
        $data['unique_id'] = generateRequestId($doctorWallet->user_id);
        $data['doctor_type'] = $doctorWallet->doctor_type;
        $data['wallet_type'] = WalletType::Payout;
        $data['wallet_amount'] = doubleval($request['payout_amount']);
        DoctorWalletTransaction::create($data);

        $user = [
            'email' => $doctorWallet->user->email,
            'email_body' => 'Your pending payout of '.doubleval($request['payout_amount']).' is released by '.config("app.name").' admin',
            'subject' => 'Payout Release'
        ];

        dispatch((new SendEmails​($user)));

        return true;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
