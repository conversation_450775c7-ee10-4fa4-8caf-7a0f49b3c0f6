<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Prettus\Repository\Traits\TransformableTrait;
use Spa<PERSON>\Translatable\HasTranslations;

class Symptom extends Model
{
    use HasFactory, TransformableTrait, SoftDeletes, HasTranslations;

    public $translatable = [
        'symptoms_name'
    ];

    protected $dates = ['deleted_at'];


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'symptoms_name', 'status', 'image', 'is_history'
    ];

    protected $appends = [
        'created_date', 'image_url'
    ];

    /**
     * Get Image link
     *
     * @return string
     */
    public function getImageUrlAttribute()
    {
        if (!empty($this->attributes['image'])) {
            return $this->attributes['image'];
        } else {
            return asset('assets/media/avatars/blank.png');
        }
    }

    /**
     * Get Created Date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * The Speciality has symptom.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function speciality()
    {
        return $this->hasOne(Speciality::class, 'symptoms_id')->withTrashed();
    }

}
