@extends('admin.includes.auth')
@section('content')
<div class="d-flex flex-column flex-root" id="kt_app_root">
    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
            <div class="d-flex flex-center flex-lg-start flex-column">
                <!--begin::Logo-->
                <a href="#" class="mb-7">
                    <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px"
                        src="{{ asset('assets/media/auth/logo.png') }}" alt="" />
                </a>
                <!--end::Logo-->
                <!--begin::Title-->
                <h2 class="text-blue fw-normal font-blue m-5">Tu salud está en tus manos</h2>
                <!--end::Title-->
            </div>
        </div>
        <div class="d-flex flex-center w-lg-50 p-10">
            <div class="card rounded-3 w-md-550px">
                <div class="card-body d-flex flex-column p-10 p-lg-20 pb-lg-10">
                    <div class="d-flex flex-center flex-column-fluid pb-15 pb-lg-20">
                        {!! Form::open(['route' => ['password.update'], 'class'=>'form w-100 admin-reset-password-form','id'=>'admin-reset-password-form
                        kt_new_password_form']) !!}
                        {!! Form::hidden('token',$request->route('token')) !!}
                        <div class="text-center mb-10">
                            <h1 class="text-dark fw-bolder mb-3">Setup New Password</h1>
                            <div class="text-gray-500 fw-semibold fs-6">Have you already reset the password ?
                                <a href="{{ route('login') }}"
                                    class="link-primary fw-bold">Sign in</a>
                            </div>
                        </div>
                        @if($errors->has('email'))
                        <div id="email-error" class="error invalid-feedback" style="display: block;">
                            {{ $errors->first('email') }}
                        </div>
                        @elseif($errors->has('password'))
                        <div id="password-error" class="error invalid-feedback" style="display: block;">
                            {{ $errors->first('password') }}
                        </div>
                        @endif
                        <div class="fv-row mb-8">
                            {!! Form::email('email', old('email'), $attributes = ['class'=>'form-control','autocomplete'=>'off','placeholder'=>'Email']) !!}
                        </div>
                        <div class="fv-row mb-8" data-kt-password-meter="true">
                            <div class="mb-1">
                                <div class="position-relative mb-3">
                                    {!! Form::password('password', ['class' => 'form-control bg-transparent', 'autocomplete'=>'off', 'placeholder'=>'Password']) !!}
                                    <span
                                        class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2"
                                        data-kt-password-meter-control="visibility">
                                        <i class="bi bi-eye-slash fs-2"></i>
                                        <i class="bi bi-eye fs-2 d-none"></i>
                                    </span>
                                </div>
                                <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2">
                                    </div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2">
                                    </div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2">
                                    </div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                                </div>
                            </div>
                            <div class="text-muted">Use 8 or more characters with a mix of letters, numbers &
                                symbols.</div>
                        </div>
                        <div class="fv-row mb-8">
                            {!! Form::password('password_confirmation', ['class' => 'form-control bg-transparent','placeholder'=>'Repeat Password']) !!}
                        </div>
                        <div class="d-grid mb-10">
                            {!! Form::submit('Reset Password',['id'=>'kt_new_password_submit','class'=>'btn btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\AdminResetPasswordRequest', '.admin-reset-password-form'); !!}
@endsection
