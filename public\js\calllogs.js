/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!*****************************************!*\
  !*** ./resources/assets/js/calllogs.js ***!
  \*****************************************/


// Class definition
var KTDatatablesServerSide = function () {
  // Shared variables
  var table;
  var datatable;
  var filterPayment;
  $('input[name="search1"]').daterangepicker({
    autoUpdateInput: false,
    autoclose: false,
    locale: {
      cancelLabel: "Clear"
    }
  });
  // Private functions
  var initDatatable = function initDatatable() {
    datatable = $("#call_logs_table").DataTable({
      searchDelay: 500,
      processing: true,
      serverSide: true,
      order: [[3, "desc"]],
      stateSave: false,
      select: {
        style: "multi",
        selector: 'td:first-child input[type="checkbox"]',
        className: "row-selected"
      },
      ajax: {
        url: getCallListRoute,
        data: function data(d) {
          var dt_params = $("#call_logs_table").data("dt_params");
          if (dt_params) {
            $.extend(d, dt_params);
          }
        },
        error: function error(jqXHR, exception) {
          if (jqXHR.status == 401 || jqXHR.status == 419) {
            window.location.href = login;
          }
        }
      },
      columns: [{
        data: "DT_RowIndex",
        name: "DT_RowIndex",
        orderable: false,
        searchable: false
      }, {
        data: "channel_name",
        name: "channel_name"
      }, {
        data: "initiator_id",
        name: "initiator_id"
      }, {
        data: "receiver_id",
        name: "receiver_id"
      }, {
        data: "call_date",
        name: "call_date"
      }, {
        data: "calculated_seconds",
        name: "calculated_seconds"
      }, {
        data: "created_date",
        name: "created_date",
        searchable: false
      }],
      columnDefs: [{
        targets: 2,
        className: "d-flex align-items-center",
        render: function render(data, type, row) {
          return "<div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">".concat(row.initiator.name, "</a>\n                            <span>").concat(row.initiator.username, "</span>\n                        </div>");
        }
      }, {
        targets: 3,
        render: function render(data, type, row) {
          return "<div class=\"d-flex flex-column\">\n                            <a href=\"#\" class=\"text-gray-800 text-hover-primary mb-1\">".concat(row.receiver.name, "</a>\n                            <span>").concat(row.receiver.username, "</span>\n                        </div>");
        }
      }, {
        targets: 5,
        render: function render(data, type, row) {
          if (row.calculated_seconds == null) {
            return "Not Available";
          } else {
            return "".concat(row.calculated_seconds);
          }
        }
      }]
    });
    table = datatable.$;
    $('#call_logs_table thead tr th').removeClass('d-flex align-items-center');
    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
    datatable.on("draw", function () {
      KTMenu.createInstances();
    });
  };

  // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
  var handleSearchDatatable = function handleSearchDatatable() {
    var filterSearch = document.querySelector('[data-kt-user-table-filter="search1"]');
    filterSearch.addEventListener("keyup", function (e) {
      datatable.search(e.target.value).draw();
    });
  };

  // Filter Datatable
  var handleFilterDatatable = function handleFilterDatatable() {
    // Select filter options
    var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
    // const filterButton = filterForm.querySelector(
    //     '[data-kt-user-table-filter="filter"]'
    // );
    //const selectOptions = filterForm.querySelectorAll("select");

    // Filter datatable on submit
    // filterButton.addEventListener("click", function () {
    //     var filterString = "";

    //     // Get filter values
    //     selectOptions.forEach((item, index) => {
    //         if (item.value && item.value !== "") {
    //             if (index == 0) {
    //                 datatable.column(3).search(item.value).draw();
    //             }
    //             if (index == 1) {
    //                 datatable.column(2).search(item.value).draw();
    //             }
    //         }
    //     });
    // });
  };

  // Reset Filter
  var handleResetForm = function handleResetForm() {
    // Select reset button
    var resetButton = document.querySelector('[data-kt-user-table-filter="reset"]');
    if (resetButton) {
      // Reset datatable
      resetButton.addEventListener("click", function () {
        // Select filter options
        var filterForm = document.querySelector('[data-kt-user-table-filter="form"]');
        var selectOptions = filterForm.querySelectorAll("select");

        // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items
        selectOptions.forEach(function (select) {
          $(select).val("").trigger("change");
        });

        // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()
        datatable.search("").columns().search("").draw();
      });
    }
  };
  $('input[name="search1"]').on("apply.daterangepicker", function (ev, picker) {
    $(this).val(picker.startDate.format("DD/MM/YYYY") + " - " + picker.endDate.format("DD/MM/YYYY"));
    var start_date = picker.startDate.format("YYYY/MM/DD");
    var end_date = picker.endDate.format("YYYY/MM/DD");
    $("#call_logs_table").data("dt_params", {
      start_date: start_date,
      end_date: end_date
    });
    datatable.draw();
  });
  $('input[name="search1"]').on("cancel.daterangepicker", function (ev, picker) {
    $(this).val("");
    $("#call_logs_table").data("dt_params", {
      start_date: null,
      end_date: null
    });
    datatable.draw();
  });

  // Public methods
  return {
    init: function init() {
      initDatatable();
      handleSearchDatatable();
      handleResetForm();
      handleFilterDatatable();
    }
  };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
  KTDatatablesServerSide.init();
});
$(document).on("click", ".changeStatus", function () {
  var url = $(this).attr("data-url");
  var id = $(this).attr("id");
  $.ajax({
    url: url,
    method: "GET",
    data: {
      id: id
    },
    success: function success(data) {
      $("#call_logs_table").DataTable().ajax.reload();
    }
  });
});
/******/ })()
;