<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\LabTestRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class LabTestController extends Controller
{
    /**
     * @var LabTestRepositoryEloquent|string
     */
    public $labTestRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param LabTestRepositoryEloquent $labTestRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(LabTestRepositoryEloquent $labTestRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->labTestRepositoryEloquent = $labTestRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }


}
