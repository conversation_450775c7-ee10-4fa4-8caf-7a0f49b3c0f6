<?php


namespace App\Http\CommonTraits;


use App\Http\Responses\ApiFormattedResponse;
use App\Http\Responses\Transformers\ValidationTransformers;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

trait ApiValidationMessageFormat
{
    /**
     * Handle a failed validation attempt.
     *
     * @param \Illuminate\Contracts\Validation\Validator $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        $apiResponse = new ApiFormattedResponse();
        method_exists($this, 'message') ? $this->container->call([
            $this,
            'message'
        ]) : 'The given data was invalid.';

        throw new HttpResponseException($apiResponse->respondValidationError(
            $this->response($validator->errors()->getMessages()),
            ""
        ));
    }

    /**
     * iterate and give valid validation response
     *
     * @param $errors
     * @return array
     *
     */
    public function response($errors)
    {
        $validationResponseTransformer = new ValidationTransformers();
        return $validationResponseTransformer->response($errors);
    }
}
