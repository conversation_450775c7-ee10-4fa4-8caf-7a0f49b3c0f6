const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js('resources/js/app.js', 'public/js').postCss('resources/css/app.css', 'public/css', [
    require('tailwindcss'),
    require('autoprefixer'),
]);

mix.js('resources/assets/js/alert.js', 'public/js');
mix.js('resources/assets/js/patient.js', 'public/js');
mix.js('resources/assets/js/doctor.js', 'public/js');
mix.js('resources/assets/js/speciality.js', 'public/js');
mix.js('resources/assets/js/symptom.js', 'public/js');
mix.js('resources/assets/js/faq.js', 'public/js');
mix.js('resources/assets/js/cms.js', 'public/js');
mix.js('resources/assets/js/support.js', 'public/js');
mix.js('resources/assets/js/setting.js', 'public/js');
mix.js('resources/assets/js/medicine.js', 'public/js');
mix.js('resources/assets/js/lab-test.js', 'public/js');
mix.js('resources/assets/js/appointment.js', 'public/js');
mix.js('resources/assets/js/appointmentticket.js', 'public/js');
mix.js('resources/assets/js/calllogs.js', 'public/js');
mix.js('resources/assets/js/call.js', 'public/js');
mix.js('resources/assets/js/transaction.js', 'public/js');
mix.js('resources/assets/js/refer.js', 'public/js');
mix.js('resources/assets/js/wallet-transaction.js', 'public/js');
mix.js('resources/assets/js/pharmacytransactions.js', 'public/js');
mix.js('resources/assets/js/subscriptiontransactions.js', 'public/js');
mix.js('resources/assets/js/appointmenttransactions.js', 'public/js');
mix.js('resources/assets/js/subscription.js', 'public/js');
mix.js('resources/assets/js/subscription-user.js', 'public/js');
mix.js('resources/assets/js/video.js', 'public/js');
mix.js('resources/assets/js/email-template.js', 'public/js');
mix.js('resources/assets/js/notification.js', 'public/js');
mix.js('resources/assets/js/log.js', 'public/js');
