<!--begin::Activities drawer-->
<div id="kt_activities" class="bg-body" data-kt-drawer="true" data-kt-drawer-name="activities" data-kt-drawer-activate="true"
    data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'300px', 'lg': '800px'}" data-kt-drawer-direction="end"
    data-kt-drawer-toggle="#kt_activities_toggle" data-kt-drawer-close="#kt_activities_close">
    <div class="card shadow-none border-0 rounded-0">
        <!--begin::Header-->
        <div class="card-header" id="kt_activities_header" style="background-image:url('/assets/media/misc/menu-header-bg.jpg');   background-repeat: no-repeat;
        background-size: 100% 100%;">
            <h3 class="card-title text-white fw-semibold px-9 mt-10 mb-6">Approval Requests
                 </h3>
            <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-icon btn-active-light-primary me-n5"
                    id="kt_activities_close">
                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                transform="rotate(-45 6 17.3137)" fill="currentColor" />
                            <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                transform="rotate(45 7.41422 6)" fill="currentColor" />
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </button>
            </div>
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body position-relative" id="kt_activities_body">
            <!--begin::Content-->
            <div id="kt_activities_scroll" class="position-relative scroll-y me-n5 pe-5" data-kt-scroll="true"
                data-kt-scroll-height="auto" data-kt-scroll-wrappers="#kt_activities_body"
                data-kt-scroll-dependencies="#kt_activities_header, #kt_activities_footer" data-kt-scroll-offset="5px">
                <!--begin::Timeline items-->
                @php
                    use App\Models\User;
                    use App\Models\Medicine;
                    use App\Models\LabTest;

                    $doctors = User::whereHas('doctorDetail', function ($q) {
                        $q->whereNull('account_verified_at');
                    })->get();

                    $medicines = Medicine::with('doctor')
                        ->where('approve_status', 1)
                        ->get();

                    $labtest = LabTest::with('doctor')
                        ->where('approve_status', 1)
                        ->get();
                @endphp
                @if ($doctors->count() > 0 || $medicines->count() > 0 || $labtest->count() > 0)


                    <div class="timeline">
                        <!--begin::Timeline item-->
                        <div class="timeline-item">
                            <!--begin::Timeline line-->
                            <div class="timeline-content mb-10 mt-n1">

                                @forelse($doctors as $key => $value)
                                    <h3 class="card-title fw-bold" style="color: #1419b7b8 !important;">Doctor Approval Requests</h3>
                                    <div class="overflow-auto">
                                        <div class="card card-flush">
                                            <!--begin::Card body-->
                                            <div class="card-body p-0">
                                                <!--begin::Table container-->
                                                <div class="table-responsive">
                                                    <!--begin::Table-->
                                                    <div id="kt_profile_overview_table_wrapper"
                                                        class="dataTables_wrapper dt-bootstrap4 no-footer">
                                                        <div class="table-responsive">
                                                            <table id="kt_profile_overview_table"
                                                                class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold dataTable no-footer">
                                                                <tbody class="fs-6">
                                                                    <tr class="odd">
                                                                        <td>
                                                                            <!--begin::User-->
                                                                            <div class="d-flex align-items-center">
                                                                                <!--begin::Wrapper-->
                                                                                <div class="me-5 position-relative">
                                                                                    <!--begin::Avatar-->
                                                                                    <div
                                                                                        class="symbol symbol-35px symbol-circle">
                                                                                        <img alt="Pic"
                                                                                            src="{{ $value->avatar_url }}">
                                                                                    </div>
                                                                                    <!--end::Avatar-->
                                                                                </div>
                                                                                <!--end::Wrapper-->
                                                                                <!--begin::Info-->
                                                                                <div
                                                                                    class="d-flex flex-column justify-content-center">
                                                                                    <a href=""
                                                                                        class="fs-6 text-gray-800 text-hover-primary">{{ $value->name }}</a>
                                                                                    <div
                                                                                        class="fw-semibold text-gray-400">
                                                                                        {{ $value->username }}</div>
                                                                                </div>
                                                                                <!--end::Info-->
                                                                            </div>
                                                                            <!--end::User-->
                                                                        </td>
                                                                        <td>{{ $value->email }}</td>
                                                                        <td>{{ $value->dial_code }} {{ $value->mobile }}
                                                                        </td>
                                                                        <td>
                                                                            {{ $value->created_date }}
                                                                        </td>
                                                                        <td class="text-end">
                                                                            <a href="{{ route('admin.doctor.view', $value->id) }}"
                                                                                class="btn btn-light btn-sm">View</a>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!--end::Table-->
                                                </div>
                                                <!--end::Table container-->
                                            </div>
                                            <!--end::Card body-->
                                        </div>
                                    </div>
                                @empty
                                @endforelse
                            </div>
                            <!--end::Timeline details-->
                        </div>
                        <!--end::Timeline content-->

                        <!--begin::Timeline line-->
                        <div class="timeline-content mb-10 mt-n1">

                            @if ($medicines->count() > 0)
                                <h3 class="card-title fw-bold" style="color: #1419b7b8 !important;">Medicine Approval Requests</h3>

                                <div class="overflow-auto">
                                    <div class="card card-flush">
                                        <!--begin::Card body-->
                                        <div class="card-body p-0">
                                            <!--begin::Table container-->
                                            <div class="table-responsive">
                                                <!--begin::Table-->
                                                <div id="kt_profile_overview_table_wrapper"
                                                    class="dataTables_wrapper dt-bootstrap4 no-footer">
                                                    <div class="table-responsive">
                                                        <table id="kt_profile_overview_table"
                                                            class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold dataTable no-footer">
                                                            <tbody class="fs-6">
                                                                <tr
                                                                    class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                                    <th class="min-w-100px sorting" rowspan="1"
                                                                        colspan="1" style="width: 140.125px;">Name
                                                                    </th>
                                                                    <th class="pe-3 min-w-100px sorting_disabled"
                                                                        rowspan="1" colspan="1"
                                                                        aria-label="Product ID"
                                                                        style="width: 146.688px;">Doctor Name</th>
                                                                    <th class="pe-3 min-w-150px sorting" tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 159.484px;">Date Added</th>
                                                                    <th class="pe-3 min-w-50px sorting" tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 107.047px;">Status</th>

                                                                    <th class="pe-3 min-w-50px sorting" tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 107.047px;">Action</th>

                                                                </tr>
                                                                @forelse($medicines as $key => $value)
                                                                    <tr class="even">
                                                                        <td>
                                                                            <div class="d-flex flex-column">
                                                                                <a href="#"
                                                                                    class="text-gray-800 text-hover-primary mb-1">{{ $value->name }}</a>
                                                                            </div>
                                                                        </td>

                                                                        <td class="d-flex align-items-center">
                                                                            <div
                                                                                class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                                                                                <div class="symbol-label">
                                                                                    <img src="{{ $value->doctor->avatar_url }}"
                                                                                        alt="{{ $value->doctor->username }}"
                                                                                        class="w-100">
                                                                                </div>
                                                                            </div>
                                                                            <div class="d-flex flex-column">
                                                                                <a href="http://127.0.0.1:8000/admin/doctor/61/view"
                                                                                    class="text-gray-800 text-hover-primary mb-1">{{ $value->doctor->name }}</a>
                                                                                <span>{{ $value->doctor->username }}</span>
                                                                            </div>
                                                                        </td>
                                                                        <td>{{ $value->created_date }}</td>
                                                                        <td><a href="#"
                                                                                class="badge badge-outline badge-info">
                                                                                Requested
                                                                            </a></td>
                                                                        <td>
                                                                            <a href="{{ route('admin.medicine.index') }}"
                                                                                class="btn btn-light btn-sm">View</a>
                                                                        </td>
                                                                    </tr>
                                                                @empty
                                                                @endforelse
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!--end::Table-->
                                            </div>
                                            <!--end::Table container-->
                                        </div>
                                        <!--end::Card body-->
                                    </div>
                                </div>
                            @endif

                        </div>
                        <!--end::Timeline details-->

                        <!--begin::Timeline line-->
                        <div class="timeline-content mb-10 mt-n1">


                            @if ($labtest->count() > 0)
                                <h3 class="card-title fw-bold" style="color: #1419b7b8 !important;">Lab Test Approval Requests</h3>

                                <div class="overflow-auto">
                                    <div class="card card-flush">
                                        <!--begin::Card body-->
                                        <div class="card-body p-0">
                                            <!--begin::Table container-->
                                            <div class="table-responsive">
                                                <!--begin::Table-->
                                                <div id="kt_profile_overview_table_wrapper"
                                                    class="dataTables_wrapper dt-bootstrap4 no-footer">
                                                    <div class="table-responsive">
                                                        <table id="kt_profile_overview_table"
                                                            class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold dataTable no-footer">
                                                            <tbody class="fs-6">
                                                                <tr
                                                                    class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">

                                                                    <th class="min-w-100px sorting" rowspan="1"
                                                                        colspan="1" style="width: 140.125px;">Name
                                                                    </th>
                                                                    <th class="pe-3 min-w-100px sorting_disabled"
                                                                        rowspan="1" colspan="1"
                                                                        aria-label="Product ID"
                                                                        style="width: 146.688px;">Doctor Name</th>
                                                                    <th class="pe-3 min-w-150px sorting"
                                                                        tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 159.484px;">Date Added</th>
                                                                    <th class="pe-3 min-w-50px sorting" tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 107.047px;">Status</th>

                                                                    <th class="pe-3 min-w-50px sorting" tabindex="0"
                                                                        aria-controls="kt_table_widget_5_table"
                                                                        rowspan="1" colspan="1"
                                                                        style="width: 107.047px;">Action</th>

                                                                </tr>
                                                                @forelse($labtest as $key => $value)
                                                                    <tr class="even">
                                                                        <td>
                                                                            <div class="d-flex flex-column">
                                                                                <a href="#"
                                                                                    class="text-gray-800 text-hover-primary mb-1">{{ $value->name }}</a>
                                                                            </div>
                                                                        </td>

                                                                        <td class="d-flex align-items-center">
                                                                            <div
                                                                                class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                                                                                <div class="symbol-label">
                                                                                    <img src="{{ $value->doctor->avatar_url }}"
                                                                                        alt="{{ $value->doctor->username }}"
                                                                                        class="w-100">
                                                                                </div>
                                                                            </div>
                                                                            <div class="d-flex flex-column">
                                                                                <a href="http://127.0.0.1:8000/admin/doctor/61/view"
                                                                                    class="text-gray-800 text-hover-primary mb-1">{{ $value->doctor->name }}</a>
                                                                                <span>{{ $value->doctor->username }}</span>
                                                                            </div>
                                                                        </td>
                                                                        <td>{{ $value->created_date }}</td>
                                                                        <td><a href="#"
                                                                                class="badge badge-outline badge-info">
                                                                                Requested
                                                                            </a></td>
                                                                        <td>
                                                                            <a href="{{ route('admin.medicine.index') }}"
                                                                                class="btn btn-light btn-sm">View</a>
                                                                        </td>
                                                                    </tr>
                                                                @empty
                                                                @endforelse
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!--end::Table-->
                                            </div>
                                            <!--end::Table container-->
                                        </div>
                                        <!--end::Card body-->
                                    </div>
                                </div>
                            @endif
                        </div>
                        <!--end::Timeline details-->
                    </div>
                    <!--end::Timeline content-->
                @else
                <div style="  display: flex;
                justify-content: center;
                align-items: center;
                height: 90vh; width: 900px;">
                    <img src="{{ asset('assets/media/illustrations/sketchy-1/21.png') }}" width="250px"
                        height="250px">
                </div>
                @endif
            </div>
            <!--end::Timeline item-->
        </div>
        <!--end::Timeline items-->
    </div>
    <!--end::Content-->
</div>
<!--end::Body-->
</div>
</div>
<!--end::Activities drawer-->
