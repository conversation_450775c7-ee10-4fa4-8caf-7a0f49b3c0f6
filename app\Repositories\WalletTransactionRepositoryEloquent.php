<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\WalletTransactionRepository;
use App\Entities\WalletTransaction;
use App\Enums\WalletType;
use App\Models\DoctorWalletTransaction;
use App\Validators\WalletTransactionValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Class WalletTransactionRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class WalletTransactionRepositoryEloquent extends BaseRepository implements WalletTransactionRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorWalletTransaction::class;
    }

    /**
     * List of call
     *
     * @return array
     */
    public function getList($request)
    {
        $transaction = DoctorWalletTransaction::select(['doctor_wallet_transactions.*', 'users.first_name'])->with('transaction', 'transaction.doctor', 'doctorAppointment')
        ->join('transactions','doctor_wallet_transactions.transaction_id','=', 'transactions.id')
        ->join('users','transactions.doctor_id','=', 'users.id');

        if (!empty($request->start_date)) {
            $transaction = $transaction->whereBetween(DB::raw("DATE_FORMAT(doctor_wallet_transactions.created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($transaction)
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->filterColumn('doctor_name', function ($query, $keyword) {
                return $query->whereHas('transaction', function ($que) use ($keyword) {
                    $que->whereHas('doctor', function ($q) use ($keyword) {
                        $q->searchByName($keyword);
                    });
                });
            })
            ->filterColumn('appointment_id', function ($query, $keyword) {
                return $query->whereHas('doctorAppointment', function ($que) use ($keyword) {
                    $que->where('unique_id', 'LIKE', '%' . trim($keyword) . '%');
                });
            })
            ->orderColumn('doctor_name', function ($query, $order) {
                return $query->orderBy('first_name', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
