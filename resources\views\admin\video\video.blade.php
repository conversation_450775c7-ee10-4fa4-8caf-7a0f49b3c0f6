@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($video))
    {{ Breadcrumbs::render('admin.video.edit') }}
    @else
    {{ Breadcrumbs::render('admin.video.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($video))
                        {{__('Edit videos Detail')}}
                        @else
                        {{__('Add videos Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.video.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="video_details" class="collapse show">
                    @if(!empty($video))
                    {{ Form::model($video, ['route' => ['admin.video.update', $video->id], 'method' =>
                    'post','class'=>'form','id'=>'video-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$video->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.video.store',
                    'method'=>'post','class'=>'form','id'=>'video-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <span
                        class="bullet bullet-dot bg-danger me-2"></span>Please use youtube embaded link for video link
                        for more detail please check <b>https://support.google.com/youtube/answer/171780?hl=en</b>
                        <div class="row mb-6">
                            {{ Form::label('video link', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('link',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'link','placeholder'=>'Enter video link']) !!}
                                        @if($errors->has('link'))
                                        <div id="link-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('link') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('video slug', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('slug', videoArray(), $video->slug ??
                                        null,['id'=>'slug',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                        @if($errors->has('slug'))
                                        <div id="slug-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('slug') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Status', null, ['class' => 'col-lg-4 col-form-label fw-semibold
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::select('status', statusArray(), $video->status ??
                                        null,['id'=>'status',
                                        'class' => 'form-control form-control-lg form-control-solid form-select
                                        form-select-solid', 'data-control' => 'select2',
                                        'data-hide-search'=>"true"]) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('priority', null, ['class' => 'col-lg-4 col-form-label fw-semibold required
                            fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('priority',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'priority','placeholder'=>'Enter videos priority']) !!}
                                        @if($errors->has('priority'))
                                        <div id="priority-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('priority') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\VideoRequest', '#video-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\VideoRequest', '#video-edit-form'); !!}
@endsection
