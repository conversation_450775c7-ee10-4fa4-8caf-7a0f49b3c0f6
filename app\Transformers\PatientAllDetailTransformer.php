<?php

namespace App\Transformers;

use App\Models\PatientDetail;
use App\Models\User;
use League\Fractal\TransformerAbstract;

class PatientAllDetailTransformer extends TransformerAbstract
{

    protected array $defaultIncludes = ['patient_detail'];

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        return [
            'id' => $user->id,
            'user_type' => $user->user_type,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'dial_code' => $user->dial_code,
            'mobile' => $user->mobile,
            'status' => $user->status,
            'language' => $user->language,
            'avatar' => $user->avatar_url ?? "",
            'created_at' => $user->created_at->timestamp
        ];
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Item
     */
    public function includePatientDetail(User $user)
    {
        if(!empty($user->patientDetail)) {
            return $this->item($user->patientDetail, new PatientDetailTransformer());
        }
    }
}
