<?php

namespace App\Http\Controllers\Admin;

use App\Repositories\PayoutRepositoryEloquent;
use App\Repositories\EcoBankRepositoryEloquent;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;

class PayoutController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "payout";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var PayoutRepositoryEloquent|string
     */
    public $PayoutRepositoryEloquent = "";

    /**
     * @var EcoBankRepositoryEloquent|string
     */
    public $EcoBankRepositoryEloquent = "";

    public function __construct(PayoutRepositoryEloquent $PayoutRepositoryEloquent, EcoBankRepositoryEloquent $EcoBankRepositoryEloquent)
    {
        parent::__construct();
        $this->PayoutRepositoryEloquent = $PayoutRepositoryEloquent;
        $this->EcoBankRepositoryEloquent = $EcoBankRepositoryEloquent;
    }

    /**
     * Return view for Payouts
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Payout']);
        $totalPayout = $this->PayoutRepositoryEloquent->getTotalPayoutDue();
        return view('admin.payout.index', compact('totalPayout'));
    }

    /**
     * Return List of Payouts
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getList(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Payout']);
        $payouts = $this->PayoutRepositoryEloquent->getPayoutList($request);
        return $payouts;
    }

    /**
     * Release due payout of doctor
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function releasePayment($id, Request $request){
        try{
            if($request->isMethod('post')){
                $release = $this->EcoBankRepositoryEloquent->doctorPayoutRelease($request->all());
                if($release){
                    return redirect('admin/payout/');
                }
            }else{
                $doctorDetail = User::with('doctorDetail')
                    ->OnlyDoctor()
                    ->where('id',$id)
                    ->first();
                $walletBalance = $this->PayoutRepositoryEloquent->getWalletBalance($id);
                return view('admin.payout.release_payment', compact('id', 'walletBalance', 'doctorDetail'));
            }
        }
        catch(Exception $e){
            dd($e);
        }
    }
}
