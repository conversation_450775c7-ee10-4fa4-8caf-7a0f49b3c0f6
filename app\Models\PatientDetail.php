<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Contracts\Transformable;
use Prettus\Repository\Traits\TransformableTrait;

class PatientDetail extends Model
{
    use HasFactory, TransformableTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'height', 'weight', 'age', 'gender', 'apartment_no', 'address', 'landmark', 'city', 'state', 'country', 'timezone', 'health_issue'
    ];

    protected $encrypt = ['height', 'weight', 'age', 'health_issue', 'apartment_no','address', 'landmark', 'city', 'state', 'country', 'timezone'];

}
