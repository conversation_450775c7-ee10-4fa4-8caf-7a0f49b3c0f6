@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    {{ Breadcrumbs::render('admin.my-profile') }}

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="card mb-5 mb-xl-10">

                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <div class="card-toolbar m-0">
                        <!--begin::Tab nav-->
                        <ul class="nav nav-stretch fs-5 fw-semibold nav-line-tabs border-transparent" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a id="kt_referrals_year_tab" class="nav-link text-active-gray-800 active" data-bs-toggle="tab" role="tab" href="#profile_details" aria-selected="false" tabindex="-1">Profile Details</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a id="kt_referrals_2019_tab" class="nav-link text-active-gray-800 me-4" data-bs-toggle="tab" role="tab" href="#change_password" aria-selected="true">Change Password</a>
                            </li>
                        </ul>
                        <!--end::Tab nav-->
                    </div>
                </div>
                <div id="profile_details" class="collapse show">
                    {{ Form::model($admin, ['route' => 'admin.update.profile', 'method'=>'post','id'=>'update-profile-form', 'enctype' => 'multipart/form-data']) }}
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Avatar', null, ['class' => 'col-lg-4 col-form-label fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="image-input image-input-outline" data-kt-image-input="true"
                                    style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url({{ !empty($admin->avatar_url) ?$admin->avatar_url: asset('assets/media/avatars/blank.png') }})"></div>
                                    <label
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        title="Change avatar">
                                        <i class="bi bi-pencil-fill fs-7"></i>
                                        {!! Form::file('avatar',['accept'=>'.png, .jpg, .jpeg']) !!}
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        title="Cancel avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                    <span
                                        class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        title="Remove avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                </div>
                                <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('First Name', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('first_name', $admin->first_name ?? '', ['class' => 'form-control form-control-lg form-control-solid mb-3 mb-lg-0','id'=>'first_name','placeholder'=>'Enter first name']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Last Name', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('last_name', $admin->last_name ?? '', ['class' => 'form-control form-control-lg form-control-solid','id'=>'last_name','placeholder'=>'Enter last name']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Email Address', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('email', $admin->email ?? '', ['class' => 'form-control form-control-lg form-control-solid','id'=>'email','placeholder'=>'Enter email','disabled'=>'disabled']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Mobile Number', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::text('mobile', $admin->mobile ?? '', ['class' => 'form-control form-control-lg form-control-solid','id'=>'mobile','placeholder'=>'Enter mobile']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'update-profile','class'=>'btn btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <div id="change_password" class="collapse">
                    {{ Form::model($admin, ['route' => 'admin.update.password', 'method'=>'post','id'=>'change-password-form']) }}
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Current Password', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::password('current_password',['class' => 'form-control form-control-lg form-control-solid mb-3 mb-lg-0','id'=>'current_password','placeholder'=>'Enter current password']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('New Password', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::password('password',['class' => 'form-control form-control-lg form-control-solid','id'=>'password','placeholder'=>'Enter new password']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Confirm Password', null, ['class' => 'col-lg-4 col-form-label required fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-6 fv-row">
                                        {!! Form::password('password_confirmation',['class' => 'form-control form-control-lg form-control-solid','id'=>'password_confirmation','placeholder'=>'Enter confirm password']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'change-password','class'=>'btn btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
<script type="text/javascript" src="{{asset('js/patient.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\UpdateAdminProfileRequest', '#update-profile-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\UpdateAdminPasswordRequest', '#change-password-form'); !!}
@endsection
