<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Prettus\Repository\Traits\TransformableTrait;

class Transaction extends Model
{
    use HasFactory, TransformableTrait;

     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'patient_id', 'doctor_id', 'request_id', 'payment_id', 'order_info', 'secure_hash', 'transaction_id', 'payment_type', 'amount', 'currency', 'payment_status', 'transaction_type', 'payment_channel', 'reward_file', 'response_message', 'decision_code', 'decision', 'channel_transaction_id'
    ];

    protected $appends = [
        'created_date'
    ];

    /**
     * Get Created Date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }


    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id', 'id')->withTrashed();
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')->withTrashed();
    }

    public function doctorAppointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'payment_id', 'id');
    }

    public function doctorSubscription()
    {
        return $this->belongsTo(DoctorSubscription::class, 'payment_id', 'id');
    }

    public function doctorWalletTransaction()
    {
        return $this->hasMany(DoctorWalletTransaction::class, 'transaction_id', 'id');
    }

    public function model()
    {
        return Transaction::class;
    }

    public static function sign($params) {
        return Transaction::signData(Transaction::buildDataToSign($params), env('SECURE_SECRATE'));
    }

    public static function signData($data, $secretKey) {
        return base64_encode(hash_hmac("sha256", $data, $secretKey, true));
    }

    public static function buildDataToSign($params) {
        $signedFieldNames = explode(",", $params["signed_field_names"]);
        foreach ($signedFieldNames as $field) {
           $dataToSign[] = $field . "=" . $params[$field];
        }

        return implode(",", $dataToSign);
    }

}
