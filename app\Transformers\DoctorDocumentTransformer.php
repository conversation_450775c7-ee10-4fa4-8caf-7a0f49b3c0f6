<?php

namespace App\Transformers;

use App\Models\DoctorDocument;
use League\Fractal\TransformerAbstract;

class DoctorDocumentTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorDocument $doctorDocument)
    {
        return [
            'image_id' => $doctorDocument->image_id,
            'image_name' => $doctorDocument->image_name,
            'image_description' => $doctorDocument->image_description,
            'image' => $doctorDocument->image
        ];
    }
}
