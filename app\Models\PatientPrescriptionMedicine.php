<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Contracts\Translation\TranslatorTrait;

class PatientPrescriptionMedicine extends Model
{
    use HasFactory, TranslatorTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['patient_prescription_id', 'medicine_id', 'medicine_name', 'medicine_type', 'medicine_course', 'medicine_duration', 'medicine_quantity', 'medicine_direction'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function patientPrescription()
    {
        return $this->belongsTo(PatientPrescription::class, 'patient_prescription_id', 'id');
    }
}
