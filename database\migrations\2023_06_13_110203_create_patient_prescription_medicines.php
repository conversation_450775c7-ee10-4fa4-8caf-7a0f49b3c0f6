<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreatePatientPrescriptionMedicines extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('patient_prescription_medicines', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('patient_prescription_id');
			$table->string('medicine_id');
			$table->string('medicine_name');
			$table->string('medicine_type');
			$table->string('medicine_course');
			$table->string('medicine_duration');
			$table->string('medicine_quantity');
			$table->string('medicine_direction')->nullable();
			$table->softDeletes();
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('patient_prescription_medicines');
    }
}
