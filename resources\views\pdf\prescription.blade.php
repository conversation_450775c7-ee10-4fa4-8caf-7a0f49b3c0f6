<!DOCTYPE html>
<html>
<head>
    <style type="text/css">
        @font-face {
            font-family: "Segoe UI Regular";
            font-style: normal;
            font-weight: normal;
        }

        body {
            font-family: Elegance, sans-serif;
        }

    </style>
</head>
<body style="margin: -43px;">
    <div style="position: relative;">
        <div style=" background-color: aliceblue;padding: 25px;">
            <div style="align-items: center;justify-content: center;">
                <img style="margin-left: auto; width:12%; height:8%;" src="{{asset('assets/media/auth/logo.png')}}" />
                <div style="font-size: 14px;float: right;">
                    <span>Date: {{$prescription->created_at->toDateString()}}</span>
                </div>
            </div>

            <div style="width: 100%;">
                <p style="font-size: 13px;align-items: center;line-height: 1;width: 100%;">
                    <b>{{ $prescription->appointment->patient->name }}</b>
                    <b style="float:right">{{ $prescription->appointment->doctor->name }}</b>
                </p>

                <p style="font-size: 13px;align-items: center;line-height: 1;width: 100%;">
                    <span>{{ $prescription->appointment->patientNoteIllness->notes ?? '' }}</span>
                    <span style="float:right">{{ $prescription->appointment->doctor->doctorDetail->education_summary }}</span>
                </p>

                <p style="font-size: 13px;align-items: center;line-height: 1;width: 100%;">
                    <span>Age: {{ $prescription->appointment->patient->patientDetail->age }} Years</span></span>
                    <span style="float: right; text-align:right"><span>
                            <span>{{ $prescription->appointment->doctor->doctorClinic->apartment_no }},
                                {{ $prescription->appointment->doctor->doctorClinic->clinic_address }}<br> {{ $prescription->appointment->doctor->doctorClinic->clinic_landmark }}, {{ $prescription->appointment->doctor->doctorClinic->clinic_city }}
                                <br>{{ $prescription->appointment->doctor->doctorClinic->clinic_state }},
                                {{ $prescription->appointment->doctor->doctorClinic->country }}<br>
                                Contact: {{ $prescription->appointment->doctor->doctorClinic->clinic_dial_code }}
                                {{ $prescription->appointment->doctor->doctorClinic->clinic_mobile }}</span>
                        </span>
                    </span>
                </p>
                <p style="font-size: 13px;align-items: center;line-height: 1;width: 100%;">
                    <span>Prescription Id: {{ $prescription->unique_id }}</span>
                    <span style="float:right"></span>
                </p>
            </div>
        </div>

        <p style="border-top: none;width: 100%;float:left;color: #707070;align-items: center;line-height: 1;">
            <span style="margin-left:25px;">
                <span style="font-size: 13px;"><b>Prescription</b></span>
            </span>
            <span style="float:right;margin-right:25px;font-size: 12px;">
                Counselling :{{ $prescription->appointment->unique_id }}
            </span>
        </p>


        <div style="padding: 50px 25px 40px 25px;">
            <table style="border-collapse: collapse;width: 100%;border: 2px solid aliceblue;margin-right: 25px;">
                <thead>
                    <tr>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">NAME</th>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">TYPE</th>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">COURSE</th>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">DURATION</th>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">QUANTITY</th>
                        <th style="background: aliceblue;font-size: 12px;padding: 10px;">DIRECTION</th>
                    </tr>
                </thead>
                <tbody>
                    @if(!empty($prescription))
                    @foreach ($prescription->patientPrescriptionMedicine as $medicine)
                    <tr>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">{{ $medicine->medicine_name }}</td>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">{{ $medicine->medicine_type }}</td>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">
                            @php $stringBuffer = ''; @endphp
                            @foreach(explode('_', $medicine->medicine_course) as $key => $value)
                            @php
                            $val = explode('-', $value);
                            $timingIdentifier = $val[0];
                            $quantity = $val[1];
                            $whenToTake = $val[2] == '1' ? 'Before Food' : 'After Food';
                            @endphp
                                @if ($quantity != '0')
                                    @switch ($timingIdentifier)
                                        @case('B')
                                        {{ "Breakfast-".$quantity."-".$whenToTake }}<br>
                                        @break

                                        @case('L')
                                        {{ "Lunch-".$quantity."-".$whenToTake }}<br>
                                        @break

                                        @case('D')
                                        {{ "Dinner-".$quantity."-".$whenToTake }}
                                        @break
                                    @endswitch
                                @endif
                            @endforeach
                        </td>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">{{ $medicine->medicine_duration }}</td>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">{{ $medicine->medicine_quantity }}</td>
                        <td style="font-size: 11px;padding: 10px;color: #000;opacity: 0.6;border-bottom: solid 1px aliceblue;text-align: center;">{{$medicine->medicine_direction??'-'}}</td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <div style="bottom:0;width:100%;left: 0;margin: 25px;padding-top:20px;">
            <b>{{ $prescription->appointment->doctor->name }}</b>
            <br>
            <img width="100" height="100" src="{{ $prescription->appointment->doctor->doctorDetail->signature }}" />
        </div>
    </div>
</body>
</html>


