<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\Translation\Command\TranslationTrait;

class DoctorAvailability extends Model
{
    use HasFactory, TranslationTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'day', 'start_time', 'end_time', 'slot'
    ];
}
