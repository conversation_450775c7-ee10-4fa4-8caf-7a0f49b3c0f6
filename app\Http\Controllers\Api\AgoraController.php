<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\silentNotification;
use App\Models\AgoraToken;
use App\Models\AppointmentCall;
use App\Models\User;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Willywes\AgoraSDK\RtcTokenBuilder;

class AgoraController extends Controller
{

    public function getInitiatorToken()
    {
        $channel_name = 'molema';
        $initiatorUid = getUID($channel_name);
        return getToken($channel_name, $initiatorUid);
    }

    public function getReceiverToken()
    {
        $channel_name = 'molema';
        $receiverUid = getUID($channel_name);
        return getToken($channel_name, $receiverUid);
    }

    public function storeCallData(Request $request)
    {
        return AppointmentCall::create($request->all());
    }

    public function CallNotification(Request $request)
    {

        try {
            $sender = auth()->user();
            $receiver = User::findOrFail($request->receiver_id);

            $message = $sender->name . " is calling you.";

            $extra = array();

            $extra['sender_name'] = $sender->name;
            $extra['sender_avatar'] = $sender->avatar_url;
            $extra['sender_id'] = $sender->id;
            $extra['receiver_id'] = $receiver->id;
            $extra['channel_name'] = $request->channel_name;
            $extra['receiver_token'] = self::getReceiverToken();

            $notificationArr = array();

            $notificationArr['channel_name'] = $request->channel_name;
            $notificationArr['sender_token'] = self::getInitiatorToken();

            $data['channel_name'] = 'molema';
            $data['initiator_id'] = $sender->id;
            $data['receiver_id'] = $receiver->id;
            $data['call_date'] = date("Y-m-d");
            $data['start_time'] = now();
            $data['calculated_seconds'] = null;

            $appointmentCall = AppointmentCall::create($data);

            $senderAgoraToken = AgoraToken::create([
                'appointment_call_id' => $appointmentCall->id,
                'user_id' =>  $sender->id,
                'uid' => $notificationArr['sender_token']
            ]);

            $receiverAgoraToken = AgoraToken::create([
                'appointment_call_id' => $appointmentCall->id,
                'user_id' =>  $receiver->id,
                'uid' => $extra['receiver_token']
            ]);

            dispatch(new silentNotification($sender, $receiver, $extra));

            return $notificationArr;
        } catch (Exception $exception) {
            dd($exception);
        }
    }
}
