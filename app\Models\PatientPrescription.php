<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Contracts\Translation\TranslatorTrait;

class PatientPrescription extends Model
{
    use HasFactory, TranslatorTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['appointment_id', 'unique_id', 'prescription_pdf'];

    /**
     * The prescription has medicine.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function patientPrescriptionMedicine()
    {
        return $this->hasMany(PatientPrescriptionMedicine::class, 'patient_prescription_id');
    }

    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    public function getUpdatedDateAttribute()
    {
        return !empty($this->updated_at) ? $this->updated_at->format("Y-m-d"): '-';
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function appointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'appointment_id', 'id');
    }

}
