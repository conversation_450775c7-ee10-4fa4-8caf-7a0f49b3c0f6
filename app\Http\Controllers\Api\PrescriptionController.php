<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\MedicineRequest;
use App\Http\Requests\PrescriptionListRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\PatientPrescriptionRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PrescriptionController extends Controller
{
    /**
     * @var PatientPrescriptionRepositoryEloquent|string
     */
    public $patientPrescriptionRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param PatientPrescriptionRepositoryEloquent $patientPrescriptionRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(PatientPrescriptionRepositoryEloquent $patientPrescriptionRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->patientPrescriptionRepository = $patientPrescriptionRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function medicineList(Request $request)
    {
        try {
            $medicine = $this->patientPrescriptionRepository->medicineList($request);
            return $this->apiResponse->respondWithMessageAndPayload($medicine, trans('messages.prescription_add_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addMedicine(MedicineRequest $request)
    {
        try {
            $medicine = $this->patientPrescriptionRepository->addMedicine($request);
            return $this->apiResponse->respondWithMessageAndPayload($medicine, trans('messages.medicine_add_success'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addPrescription(Request $request)
    {
        try {
            $prescription = $this->patientPrescriptionRepository->addPrescription($request);
            return $this->apiResponse->respondWithMessage(trans('messages.prescription_add_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getPrescriptions(PrescriptionListRequest $request)
    {
        try {
            $prescription = $this->patientPrescriptionRepository->getPrescriptions($request);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_prescription_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewPrescription(Request $request, $id)
    {
        try {
            $prescription = $this->patientPrescriptionRepository->viewPrescription($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_prescription_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

}
