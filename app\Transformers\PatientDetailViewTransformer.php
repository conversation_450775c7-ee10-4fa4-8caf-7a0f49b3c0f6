<?php

namespace App\Transformers;

use App\Models\PatientDetail;
use App\Models\Symptom;
use League\Fractal\TransformerAbstract;

class PatientDetailViewTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientDetail $patientDetail)
    {
        $issues = (array)json_decode(decrypt($patientDetail->health_issue));

        $arr = [];
        foreach($issues as $issue){
            if(!empty($issue)){
                $arr[] = Symptom::select('symptoms_name')->find($issue)->symptoms_name ?? '';
            }
        }


        return [
            'id' => $patientDetail->id,
            'height' => decrypt($patientDetail->height),
            'weight' => decrypt($patientDetail->weight),
            'age' => decrypt($patientDetail->age),
            'gender' => $patientDetail->gender,
            // 'apartment_no' => !empty($patientDetail->apartment_no) ? decrypt($patientDetail->apartment_no) : "",
            // 'address' => !empty($patientDetail->address) ? decrypt($patientDetail->address) : "",
            // 'landmark' => !empty($patientDetail->landmark) ? decrypt($patientDetail->landmark) : "",
            // 'city' => !empty($patientDetail->city) ? decrypt($patientDetail->city) : "",
            // 'state' => !empty($patientDetail->state) ? decrypt($patientDetail->state) : "",
            // 'country' => !empty($patientDetail->country) ? decrypt($patientDetail->country) : "",
            // 'timezone' => !empty($patientDetail->timezone) ? decrypt($patientDetail->timezone) : "",
            'apartment_no' => $patientDetail->apartment_no,
            'address' => $patientDetail->address,
            'landmark' => $patientDetail->landmark,
            'city' => $patientDetail->city,
            'state' => $patientDetail->state,
            'country' => $patientDetail->country,
            'timezone' => $patientDetail->timezone,
            'health_issue' => $arr,
            'other_issue' => !empty($issues['other']) ? $issues['other'] : ''
        ];
    }
}
