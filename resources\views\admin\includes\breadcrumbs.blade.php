@php
$html='';
$activePathTitle='';
@endphp

@foreach ($breadcrumbs as $breadcrumb)
@if ($breadcrumb->url && $loop->first)
@php
$html.='
<li class="breadcrumb-item">
<a href="'.$breadcrumb->url.'" class="text-muted text-hover-primary">
    '.$breadcrumb->title.'
</a></li>';
$activePathTitle=$breadcrumb->title;
@endphp
@elseif ($breadcrumb->url && !$loop->last)
@php
$html.='<li class="breadcrumb-item">
<span class="bullet bg-gray-400 w-5px h-2px"></span><li>
<li class="breadcrumb-item text-muted">
<a href="'.$breadcrumb->url.'" class="text-muted text-hover-primary ml-5">
    '.$breadcrumb->title.'
</a></li>';
@endphp
@else
@php
$html.='<li class="breadcrumb-item">
<span class="bullet bg-gray-400 w-5px h-2px"></span></li>
<li class="breadcrumb-item text-muted">
<a href="javascript:void(0)" class="text-muted text-hover-primary">
    '. $breadcrumb->title .'
</a>
</li>';
$activePathTitle=$breadcrumb->title;
@endphp
@endif
@endforeach

<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
        <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
            <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                {!! $activePathTitle ?? '' !!}</h1>
            <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    {!! $html??'' !!}
            </ul>
        </div>
    </div>
</div>
