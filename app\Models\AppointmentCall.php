<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Symfony\Contracts\Translation\TranslatorTrait;

class AppointmentCall extends Model
{

    use HasFactory, TranslatorTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'channel_name',
        'appointment_id',
        'initiator_id',
        'receiver_id',
        'call_date',
        'start_time',
        'calculated_seconds',
    ];

    protected $appends = ['created_date'];

    /**
     * Get created date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * The doctor has ratings.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function agoraToken()
    {
        return $this->hasMany(AgoraToken::class, 'appointment_call_id');
    }

    /**
     * The doctor has call logs.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function callLogs()
    {
        return $this->hasMany(CallLog::class, 'appointment_call_id');
    }

    /**
     * The appointment_id that belong to the doctor appointment.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function doctorAppointment()
    {
        return $this->belongsTo(DoctorAppointment::class, 'appointment_id', 'id');
    }

    /**
     * The initiator that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function initiator()
    {
        return $this->belongsTo(User::class, 'initiator_id', 'id')->withTrashed();
    }

    /**
     * The receiver that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id', 'id')->withTrashed();
    }

}
