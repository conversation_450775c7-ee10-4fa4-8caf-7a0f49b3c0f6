<?php

namespace App\Http\Controllers\Admin;

use App\Exports\PatientExport;
use App\Http\Controllers\Admin\Controller;
use App\Http\Requests\CreatePatientRequest;
use App\Http\Requests\PatientRequest;
use App\Http\Requests\UserRegisterRequest;
use App\Models\Symptom;
use App\Models\User;
use App\Repositories\PatientDetailRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\DataTables;
use App\Models\Countries;

class PatientController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "patient";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var PatientDetailRepositoryEloquent|string
     */
    public $patientDetailRepository = "";

    public function __construct(PatientDetailRepositoryEloquent $patientDetailRepository)
    {
        parent::__construct();
        $this->patientDetailRepository = $patientDetailRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Patient']);
        return view('admin.user.patient.index');
    }

    /**
     * Return player list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->patientDetailRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Patient']);
        $healthIssue = Symptom::where('is_history', 1)->pluck('symptoms_name', 'id')->toArray();
        $countrycode = Countries::orderBy('id','asc')->pluck('iso','iso');
        return view('admin.user.patient.patient', compact('healthIssue','countrycode'));
    }

    /**
     * @param PatientRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(PatientRequest $request)
    {
        try {
            $patient = $this->patientDetailRepository->store($request);
            $request->session()->flash('success', config("messages.patient_created"));
            return redirect()->route('admin.patient.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit patient list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Patient']);
        try {
            $patient = $this->patientDetailRepository->edit($request, $id);
            $healthIssue = Symptom::where('is_history', 1)->pluck('symptoms_name', 'id')->toArray();
            $issue = $patient->patientDetail ? (array)json_decode(decrypt($patient->patientDetail->health_issue)) : [];
            $countrycode = Countries::orderBy('id','asc')->pluck('iso','iso');
            $otherIssue = $issue['other'] ?? '';
            return view('admin.user.patient.patient', compact('patient', 'healthIssue', 'issue', 'otherIssue','countrycode'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.patient.index');
        } catch (Exception $exception) {dd($exception->getMessage());
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }


    public function view(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Patient']);
        try {
            $patient = $this->patientDetailRepository->view($request, $id);
            $transaction = $this->patientDetailRepository->transaction($request, $id);
            $appointment = $this->patientDetailRepository->booking($request, $id);
            $appointment_upcoming = $appointment['appointment_upcoming'];
            $appointment_ongoing = $appointment['appointment_ongoing'];
            $appointment_completed = $appointment['appointment_completed'];

            $healthIssue = [];
            $issue = [];
            $otherIssue = '';
            if(!empty($patient->patientDetail) && !empty(decrypt($patient->patientDetail->health_issue))){
                $issue = (array)json_decode(decrypt($patient->patientDetail->health_issue));
                $healthIssue = Symptom::whereIn('id',(array)json_decode(decrypt($patient->patientDetail->health_issue)))->where('is_history', 1)->pluck('symptoms_name')->toArray();
                $otherIssue = $issue['other'] ?? '';
            }

            return view('admin.user.patient.view', compact('patient','appointment_upcoming','appointment_ongoing','appointment_completed','transaction','issue','otherIssue','healthIssue'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.patient.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }

    /**
     * Update patient list.
     *
     * @param PatientRequest $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(PatientRequest $request, $id)
    {
        try {
            $patient = $this->patientDetailRepository->update($request, $id);
            $request->session()->flash('success', config("messages.patient_updated"));
            return redirect()->route('admin.patient.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.patient.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->patientDetailRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->patientDetailRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->patientDetailRepository->destroy($request);
            $request->session()->flash('success', config("messages.patient_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.patient.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.patient.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "patient_" . time() . ".xlsx";
        return Excel::download(new PatientExport($request->all()), $fileName);
    }


}
