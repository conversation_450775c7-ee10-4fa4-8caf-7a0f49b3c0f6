<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserDevice;
use App\Enums\DeviceType;
use App\Enums\NotificationType;
use App\Notifications\VoipNotification;
use App\Jobs\silentNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class TestVoipNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'voip:test {receiver_id : The user ID to send test notification to} {sender_id : The user ID sending the notification}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test VoIP notification to debug issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $receiverId = $this->argument('receiver_id');
        $senderId = $this->argument('sender_id');

        $this->info("Testing VoIP notification");
        $this->line("Receiver ID: {$receiverId}");
        $this->line("Sender ID: {$senderId}");
        $this->line("=" . str_repeat("=", 50));

        // Get users
        $receiver = User::with('userDevice')->find($receiverId);
        $sender = User::find($senderId);

        if (!$receiver) {
            $this->error("Receiver not found with ID: {$receiverId}");
            return 1;
        }

        if (!$sender) {
            $this->error("Sender not found with ID: {$senderId}");
            return 1;
        }

        $this->info("Receiver: {$receiver->name} ({$receiver->email})");
        $this->info("Sender: {$sender->name} ({$sender->email})");

        if (!$receiver->userDevice) {
            $this->error("❌ Receiver has no device record");
            return 1;
        }

        $device = $receiver->userDevice;
        $this->info("Device Type: " . ($device->device_type == DeviceType::IOS ? 'iOS' : 'Android'));

        // Prepare test data
        $extra = [
            'notification_type' => NotificationType::IncomingCall,
            'appointment_id' => 999999, // Test appointment ID
            'booking_id' => 'TEST-' . time(),
            'channel_name' => 'test-channel-' . time(),
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url ?? '',
            'patient_id' => $receiver->id,
            'doctor_id' => $sender->id,
            'appointment_call_id' => 999999,
            'initiator_id' => $sender->id,
            'receiver_id' => $receiver->id,
            'doctor_type' => 'general'
        ];

        $this->line("\n📱 Sending test notification...");

        try {
            if ($device->device_type == DeviceType::IOS) {
                $this->testIosNotification($receiver, $sender, $device, $extra);
            } else {
                $this->testAndroidNotification($receiver, $sender, $device, $extra);
            }

            $this->info("✅ Test notification sent successfully!");
            $this->warn("Check the application logs for detailed information");
            
        } catch (\Exception $e) {
            $this->error("❌ Test notification failed:");
            $this->error("Error: " . $e->getMessage());
            $this->error("File: " . $e->getFile());
            $this->error("Line: " . $e->getLine());
            return 1;
        }

        return 0;
    }

    private function testIosNotification($receiver, $sender, $device, $extra)
    {
        $this->line("🍎 Testing iOS VoIP notification...");

        if (empty($device->voip_token)) {
            $this->warn("⚠️  No VoIP token available, testing with push token fallback");
            
            if (empty($device->push_token)) {
                throw new \Exception("No VoIP token or push token available for iOS device");
            }
            
            $this->line("📤 Dispatching silent notification as fallback...");
            dispatch(new silentNotification($sender, $receiver, $extra));
            return;
        }

        $this->line("📤 Sending VoIP notification...");
        $this->line("VoIP Token: " . substr($device->voip_token, 0, 20) . "...");

        // Create test VoIP notification
        $voipNotification = new VoipNotification(
            $receiver->name,
            'test-uid-' . time(),
            $sender->id,
            $sender->name,
            $extra
        );

        Notification::send($device, $voipNotification);
        $this->info("VoIP notification sent to iOS device");
    }

    private function testAndroidNotification($receiver, $sender, $device, $extra)
    {
        $this->line("🤖 Testing Android silent notification...");

        if (empty($device->push_token)) {
            throw new \Exception("No push token available for Android device");
        }

        $this->line("📤 Dispatching silent notification...");
        $this->line("Push Token: " . substr($device->push_token, 0, 20) . "...");

        dispatch(new silentNotification($sender, $receiver, $extra));
        $this->info("Silent notification dispatched for Android device");
    }
}
