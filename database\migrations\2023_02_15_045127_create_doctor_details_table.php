<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDoctorDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('doctor_details', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->tinyInteger('gender')->comment('1 = male, 2 = female, 3 = other')->nullable();
            $table->string('education_summary')->nullable();
            $table->string('council_number')->nullable();
            $table->string('signature')->nullable();
            $table->tinyInteger('doctor_type')->comment('1 = Collaborator, 2 = Freelancer')->default(2);
            $table->tinyInteger('online_status')->comment('1 = available, 2 = busy, 3 = offline')->default(1);
            $table->tinyInteger('is_popular')->comment('0 = not popular, 1 = popular')->default(0);
            $table->string('served_location')->nullable();
            $table->tinyInteger('virtual_consultation')->comment('0 = unavailable, 1 = available')->default(0);
            $table->tinyInteger('physical_consultation')->comment('0 = unavailable, 1 = available')->default(0);
            $table->tinyInteger('home_consultation')->comment('0 = unavailable, 1 = available')->default(0);
            $table->bigInteger('virtual_consultation_price')->nullable();
            $table->bigInteger('physical_consultation_price')->nullable();
            $table->bigInteger('home_consultation_price')->nullable();
            $table->text('service')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('doctor_details');
    }
}
