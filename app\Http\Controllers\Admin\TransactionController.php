<?php

namespace App\Http\Controllers\Admin;

use App\Enums\PaymentStatus;
use App\Enums\TransactionPaymentStatus;
use App\Enums\TransactionType;
use App\Exports\TransactionExport;
use App\Models\Transaction;
use App\Repositories\TransactionRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class TransactionController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "transaction";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var TransactionRepositoryEloquent|string
     */
    public $transactionRepositoryEloquent = "";

    public function __construct(TransactionRepositoryEloquent $transactionRepositoryEloquent)
    {
        parent::__construct();
        $this->transactionRepositoryEloquent = $transactionRepositoryEloquent;
    }

    /**
     * Return List of Appointment
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Transactions']);
        $transactionCount = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionAppointmentCount = Transaction::where('transaction_type', TransactionType::Consultation)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionPharmacyCount = Transaction::where('transaction_type', TransactionType::Pharmacy)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionSubscriptionCount = Transaction::where('transaction_type', TransactionType::Subscription)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');

        $totalTransaction = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->count();
        return view('admin.transactions.index', compact('transactionCount', 'transactionAppointmentCount', 'transactionPharmacyCount', 'transactionSubscriptionCount', 'totalTransaction'));
    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function countAmount(Request $request)
    {
        $transactionCount = Transaction::where('payment_status', TransactionPaymentStatus::Complete);
        $transactionAppointmentCount = Transaction::where('transaction_type', TransactionType::Consultation)->where('payment_status', TransactionPaymentStatus::Complete);
        $transactionPharmacyCount = Transaction::where('transaction_type', TransactionType::Pharmacy)->where('payment_status', TransactionPaymentStatus::Complete);
        $transactionSubscriptionCount = Transaction::where('transaction_type', TransactionType::Subscription)->where('payment_status', TransactionPaymentStatus::Complete);

        $totalTransaction = Transaction::where('payment_status', TransactionPaymentStatus::Complete);

        if(!empty($request->start_date)){
            $transactionCount =  $transactionCount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
            $transactionAppointmentCount =  $transactionAppointmentCount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
            $transactionPharmacyCount =  $transactionPharmacyCount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
            $transactionSubscriptionCount =  $transactionSubscriptionCount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
            $totalTransaction =  $totalTransaction->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        $data['transactionCount'] = number_format($transactionCount->sum('amount'), 2, '.', '');
        $data['transactionAppointmentCount'] = number_format($transactionAppointmentCount->sum('amount'), 2, '.', '');
        $data['transactionPharmacyCount'] = number_format($transactionPharmacyCount->sum('amount'), 2, '.', '');
        $data['transactionSubscriptionCount'] = number_format($transactionSubscriptionCount->sum('amount'), 2, '.', '');
        $data['totalTransaction'] = $totalTransaction->count();
        return $data;
    }


    /**
     * Return List of Appointment
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function appointmentTransaction()
    {
        config(['globalconfig.APP_TITLE' => 'Appointment Transaction']);
        $transactionCount = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionAppointmentCount = Transaction::where('transaction_type', TransactionType::Consultation)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionPharmacyCount = Transaction::where('transaction_type', TransactionType::Pharmacy)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionSubscriptionCount = Transaction::where('transaction_type', TransactionType::Subscription)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');

        $totalTransaction = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->count();
        return view('admin.transactions.appointment_transaction', compact('transactionCount', 'transactionAppointmentCount', 'transactionPharmacyCount', 'transactionSubscriptionCount', 'totalTransaction'));
    }

    /**
     * Return List of Pharmacy Transaction
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function pharmacyTransaction()
    {
        config(['globalconfig.APP_TITLE' => 'Pharmacy Transaction']);
        $transactionCount = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionAppointmentCount = Transaction::where('transaction_type', TransactionType::Consultation)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionPharmacyCount = Transaction::where('transaction_type', TransactionType::Pharmacy)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionSubscriptionCount = Transaction::where('transaction_type', TransactionType::Subscription)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');

        $totalTransaction = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->count();
        return view('admin.transactions.pharmacy_transaction', compact('transactionCount', 'transactionAppointmentCount', 'transactionPharmacyCount', 'transactionSubscriptionCount', 'totalTransaction'));
    }

    /**
     * Return List of Subscription Transaction
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function subscriptionTransaction()
    {
        config(['globalconfig.APP_TITLE' => 'Subscription Transaction']);

        $transactionCount = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionAppointmentCount = Transaction::where('transaction_type', TransactionType::Consultation)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionPharmacyCount = Transaction::where('transaction_type', TransactionType::Pharmacy)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');
        $transactionSubscriptionCount = Transaction::where('transaction_type', TransactionType::Subscription)->where('payment_status', TransactionPaymentStatus::Complete)->sum('amount');

        $totalTransaction = Transaction::where('payment_status', TransactionPaymentStatus::Complete)->count();
        return view('admin.transactions.subscription_transaction', compact('transactionCount', 'transactionAppointmentCount', 'transactionPharmacyCount', 'transactionSubscriptionCount',  'totalTransaction'));
    }

    /**
     * Return List of Appointment
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function transactionIndex()
    {
        config(['globalconfig.APP_TITLE' => 'Appointment']);
        return view('admin.transactions.transaction');
    }

    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->transactionRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function appointmentTransactionGetList(Request $request)
    {
        try {
            return $this->transactionRepositoryEloquent->appointmentTransactionGetList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function pharmacyTransactionGetList(Request $request)
    {
        try {
            return $this->transactionRepositoryEloquent->pharmacyTransactionGetList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return appointment list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function subscriptionTransactionGetList(Request $request)
    {
        try {
            return $this->transactionRepositoryEloquent->subscriptionTransactionGetList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * create a new appointments
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Appointment']);
        return view('admin.appointment.appointment');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $appointment = $this->transactionRepositoryEloquent->store($request);
            $request->session()->flash('success', config("messages.appointment_created"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit appointment list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit Appointment']);
        try {
            $appointment = $this->transactionRepositoryEloquent->edit($request, $id);
            return view('admin.appointment.appointment', compact('appointment'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Update appointment list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $appointment = $this->transactionRepositoryEloquent->update($request, $id);
            $request->session()->flash('success', config("messages.appointment_updated"));
            return redirect()->route('admin.appointment.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusReward(Request $request)
    {
        try {
            $this->transactionRepositoryEloquent->changeStatusReward($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->transactionRepositoryEloquent->destroy($request);
            $request->session()->flash('success', config("messages.appointment_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.appointment.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.appointment.index');
        }
    }

    /**
     * Import Reward
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function importRewardFile(Request $request)
    {
        try {
            $this->transactionRepositoryEloquent->importRewardFile($request);
            $request->session()->flash('success', config("messages.file_import_success"));
            return redirect()->route('admin.transactions.index');
        } catch (Exception $exception) {dd($exception->getMessage());
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "transaction_" . time() . ".xlsx";
        return Excel::download(new TransactionExport($request->all()), $fileName);
    }
}
