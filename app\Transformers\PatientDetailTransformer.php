<?php

namespace App\Transformers;

use App\Models\PatientDetail;
use League\Fractal\TransformerAbstract;
use Exception;
use Log;

class PatientDetailTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(PatientDetail $patientDetail)
    {
        $issues = [];
        if(!empty($patientDetail->health_issue)){
            $issues = (array)json_decode(decrypt($patientDetail->health_issue));
        }

        $arr = [];
        foreach($issues as $issue){
            $arr[] = !empty($issue) ? $issue : '';
        }

        try {
            return [
                'id' => $patientDetail->id,
                'height' => !empty($patientDetail->height) ? decrypt($patientDetail->height) : "",
                'weight' => !empty($patientDetail->weight) ? decrypt($patientDetail->weight) : "",
                'age' => !empty($patientDetail->age) ? decrypt($patientDetail->age) : "",
                'gender' => $patientDetail->gender,
                // 'apartment_no' => !empty($patientDetail->apartment_no) ? decrypt($patientDetail->apartment_no) : "",
                // 'address' => !empty($patientDetail->address) ? decrypt($patientDetail->address) : "",
                // 'landmark' => !empty($patientDetail->landmark) ? decrypt($patientDetail->landmark) : "",
                // 'city' => !empty($patientDetail->city) ? decrypt($patientDetail->city) : "",
                // 'state' => !empty($patientDetail->state) ? decrypt($patientDetail->state) : "",
                // 'country' => !empty($patientDetail->country) ? decrypt($patientDetail->country) : "",
                // 'timezone' => !empty($patientDetail->timezone) ? decrypt($patientDetail->timezone) : "",
                'apartment_no' => $patientDetail->apartment_no,
                'address' => $patientDetail->address,
                'landmark' => $patientDetail->landmark,
                'city' => $patientDetail->city,
                'state' => $patientDetail->state,
                'country' => $patientDetail->country,
                'timezone' => $patientDetail->timezone,
                'health_issue' => $arr,
                'other_issue' => !empty($issues['other']) ? $issues['other'] : ''
            ];
        } catch (Exception $e) {
            Log::error("||================= new Error in PatientDetailTransformer============||");
            Log::error("|| Error => ".$e->getMessage());
            Log::error("|| File => ".$e->getFile());
            Log::error("|| Line => ".$e->getLine());
            Log::error("||================= Error in PatientDetailTransformer completed============||");
        }

    }
}
