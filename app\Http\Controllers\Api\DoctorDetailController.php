<?php

namespace App\Http\Controllers\Api;

use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateClinicDetailRequest;
use App\Http\Requests\UpdateConsultationTypeRequest;
use App\Http\Requests\UpdateDoctorSpecialityWithSymptomRequest;
use App\Http\Requests\UpdateEducationWithExperienceRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\DoctorDetailRepositoryEloquent;
use App\Repositories\UserRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DoctorDetailController extends Controller
{
    /**
     * @var DoctorDetailRepositoryEloquent|string
     */
    public $doctorDetailRepository = "";

    /**
     * @var UserRepositoryEloquent|string
     */
    public $userRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Doctor Detail Controller constructor.
     * @param DoctorDetailRepositoryEloquent $doctorDetailRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(DoctorDetailRepositoryEloquent $doctorDetailRepository, ApiFormattedResponse $apiFormattedResponse, UserRepositoryEloquent $userRepository)
    {
        $this->doctorDetailRepository = $doctorDetailRepository;
        $this->userRepository = $userRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewDoctorSpeciality(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->viewDoctorSpeciality($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.speciality_details'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UpdateDoctorSpecialityWithSymptomRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateDoctorSpeciality(UpdateDoctorSpecialityWithSymptomRequest $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateDoctorSpeciality($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param UpdateEducationWithExperienceRequest $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateEducationWithExperience(UpdateEducationWithExperienceRequest $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateEducationWithExperience($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateConsultationType(UpdateConsultationTypeRequest $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateConsultationType($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
            // if (!empty(Auth::guard('web')->user())) {
            //     if (!empty(Auth::guard('web')->user()->email_verified_at) && !empty(Auth::guard('web')->user()->mobile_verified_at)) {
            //         if (Auth::guard('web')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
            //             if (empty(Auth::guard('web')->user()->doctorDetail->account_verified_at)) {
            //                 return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
            //             }
            //         }
            //         if (Auth::guard('web')->user()->status == true) {
            //             $userDevice = $this->userRepository->storeDeviceInfo($request);
            //             if (Auth::guard('web')->user()->is_first_time_login) {
            //                 Auth::guard('web')->user()->update(['is_first_time_login' => false]);
            //             }
            //         } else {
            //             return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
            //         }
            //         return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.update_profile_success'));
            //     } else {
            //         return $this->apiResponse->respondForbidden(trans('messages.verify_email_error'));
            //     }
            // }
            // if (!empty(Auth::guard('api')->user())) {
            //     if (!empty(Auth::guard('api')->user()->email_verified_at) && !empty(Auth::guard('api')->user()->mobile_verified_at)) {
            //         if (Auth::guard('api')->user()->user_type == UserType::Doctor && Auth::guard('web')->user()->steps >= 3 && Auth::guard('web')->user()->doctorDetail->physical_consultation == 0) {
            //             if (empty(Auth::guard('api')->user()->doctorDetail->account_verified_at)) {
            //                 return $this->apiResponse->respondCustomExceptionError(trans('messages.account_unverified'), 405);
            //             }
            //         }
            //         if (Auth::guard('api')->user()->status == true) {
            //             $userDevice = $this->userRepository->storeDeviceInfo($request);
            //             if (Auth::guard('api')->user()->is_first_time_login) {
            //                 Auth::guard('api')->user()->update(['is_first_time_login' => false]);
            //             }
            //         } else {
            //             return $this->apiResponse->respondForbidden(trans('messages.user_is_inactive'));
            //         }
            //         return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
            //     } else {
            //         return $this->apiResponse->respondForbidden(trans('messages.verify_email_error'));
            //     }
            // }
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateClinicDetail(UpdateClinicDetailRequest $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateClinicDetail($request);
            return $this->apiResponse->respondWithMessageAndPayload(['user' => $user], trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateSignature(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateSignature($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewDoctorProfile(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->viewDoctorProfile($request);
            $userDevice = $this->doctorDetailRepository->storeDeviceInfo($request);
            $patientProfile = $this->doctorDetailRepository->updateSignature($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function deleteEducationDocument(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->deleteEducationDocument($request);
            return $this->apiResponse->respondWithMessage(trans('messages.document_delete_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewSpecialistDoctor(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->viewSpecialistDoctor($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.document_delete_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function doctorAbout(Request $request, $id)
    {
        try {
            $user = $this->doctorDetailRepository->doctorAbout($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.document_delete_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateDoctorType(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateDoctorType($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.update_collabrator'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function checkClinicUpdates(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->checkClinicUpdates($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updateStatus(Request $request)
    {
        try {
            $user = $this->doctorDetailRepository->updateStatus($request);
            return $this->apiResponse->respondWithMessageAndPayload($user, trans('messages.profile_retrieved_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
