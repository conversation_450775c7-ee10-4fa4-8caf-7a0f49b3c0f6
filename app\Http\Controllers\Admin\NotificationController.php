<?php

namespace App\Http\Controllers\Admin;

use App\Enums\SenderType;
use App\Models\Notification;
use App\Repositories\NotificationRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "notification";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var NotificationRepositoryEloquent|string
     */
    public $notificationRepositoryEloquent = "";

    public function __construct(NotificationRepositoryEloquent $notificationRepositoryEloquent)
    {
        parent::__construct();
        $this->notificationRepositoryEloquent = $notificationRepositoryEloquent;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Patient']);

        Notification::where('receiver_type', SenderType::Admin)->where('read', 0)->update(['read' => true]);

        return view('admin.notification.index');
    }

    /**
     * Return player list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->notificationRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }
}
