.btn-blue {
    background-color: #0071BC !important;
    border-color: #0071BC !important;
    color: #ffffff !important;
    font-weight: 400 !important;
}

.font-blue {
    color: #0071BC !important;
}
.pagination-custom svg{
    max-width: 17px;
}
.pagination-custom{
    margin-top: 25px;
}
.pagination-custom nav>div:first-child{
    display: none;
}
.pagination-custom span[aria-current="page"] {
    width: 30px;
    display: inline-block;
}
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    background-image: url('../media/svg/avatars/loader.gif');
    background-repeat: no-repeat;
    background-color: #FFF;
    background-position: center;
    background-size: 10% 15%;
 }
