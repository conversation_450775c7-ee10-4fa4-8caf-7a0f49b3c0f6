<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('doctor_ratings', function(Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('patient_id');
            $table->bigInteger('appointment_id');
            $table->bigInteger('doctor_id');
            $table->float('rating');
            $table->string('notes')->nullable();
            $table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('doctor_ratings');
	}
};
