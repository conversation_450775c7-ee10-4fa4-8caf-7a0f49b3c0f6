<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\AdminRepository;
use App\Models\Admin;
use App\Http\CommonTraits\UploadMedia;
use App\Validators\AdminValidator;
use Illuminate\Support\Facades\Auth;

/**
 * Class AdminRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class AdminRepositoryEloquent extends BaseRepository implements AdminRepository
{
    use UploadMedia;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Admin::class;
    }

    /**
     * @param $request
     * @return mixed
     */
    public function updateProfile($request)
    {
        $admin = Auth::guard('admin')->user();
        $data = $request->only('first_name', 'last_name');

        if ($request->has('avatar')) {
            $data['avatar'] = self::upload($request, 'molema/admin', $admin->id);
        }

        return $admin->update($data);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function changePassword($request)
    {
        $admin = Auth::guard('admin')->user();

        return $admin->update(['password' => bcrypt($request->password)]);
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
