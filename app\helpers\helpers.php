<?php

use App\Enums\DoctorInvitation;
use App\Enums\SymptomHistory;
use App\Enums\UserGender;
use App\Enums\UserLanguage;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Enums\DoctorType;
use App\Enums\Video;
use App\Models\AppointmentCall;
use App\Models\DoctorAppointment;
use App\Models\DoctorInvitation as ModelsDoctorInvitation;
use App\Models\OtpCode;
use App\Models\PatientPrescription;
use App\Models\Support;
use App\Models\Transaction;
use Willywes\AgoraSDK\RtcTokenBuilder;
use Illuminate\Support\Str;

if (!function_exists('generateUniqueOtpCode'))
{
    function generateUniqueOtpCode() : int
    {
        do {
            $code = rand(100000, 999999);
        }
        while (OtpCode::where('code', $code)->exists());

        return $code;
    }
}

if (!function_exists('generateInvitationCode'))
{
    function generateInvitationCode() : string
    {
        do {
            $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
            $code = substr(str_shuffle($str_result), 0, 6);
        }
        while (ModelsDoctorInvitation::where('invitation_code', $code)->exists());

        return $code;
    }
}

if (!function_exists('generateUniquePrescriptionId'))
{
    function generateUniquePrescriptionId() : string
    {
        do {
            $code = rand(100000, 999999);
        }
        while (PatientPrescription::where('unique_id', 'PR'.$code)->exists());

        return 'PR'.$code;
    }
}

if (!function_exists('generateUniqueAppointmentId'))
{
    function generateUniqueAppointmentId() : string
    {
        do {
            $code = rand(100000, 999999);
        }
        while (PatientPrescription::where('unique_id', 'DA'.$code)->exists());

        return 'DA'.$code;
    }
}


if (!function_exists('generateUniqueSupportTicket'))
{
    function generateUniqueSupportTicket() : string
    {
        do {
            $code = rand(100000, 999999);
        }
        while (Support::where('unique_id', 'ST'.$code)->exists());

        return 'ST'.$code;
    }
}


if (!function_exists('generateSecurityCodeAppointment'))
{
    function generateSecurityCodeAppointment() : int
    {
        return rand(100000, 999999);
    }
}

//generate request id for eco bank account
if (!function_exists('generateRequestId'))
{
    function generateRequestId($patient_id) : string
    {
        do {
            $requestId = time();
        }
        while (Transaction::where('request_id', $patient_id.$requestId)->exists());

        return $patient_id.$requestId;
    }
}

//generate order info for eco bank account
if (!function_exists('generateOrderInfo'))
{
    function generateOrderInfo() : string
    {
        do {
            $orderInfo = (string) Str::uuid();

        }
        while (Transaction::where('order_info', $orderInfo)->exists());

        return $orderInfo;
    }
}

//generate transaction id for eco bank account
if (!function_exists('generateTransactionId'))
{
    function generateTransactionId($transaction_type) : string
    {
        $transaction_type =  Str::upper(substr($transaction_type, 0, 3));
        do {
            $transactionId = rand(100000, 999999);
        }
        while (Transaction::where('transaction_id', 'TR'.$transaction_type.$transactionId)->exists());

        return 'TR'.$transaction_type.$transactionId;
    }
}

if (!function_exists('generateUserName'))
{
    function generateUserName($user_type)
    {
        if ($user_type == UserType::Patient) {
            $prefix = "MP";
        } else {
            $prefix = "MD";
        }
        $ts = time();

        return $prefix . $ts;
    }
}

if (!function_exists('statusBadge')) {
    /**
     * Get HTML design for status badge
     * @param $status
     * @return string
     */
    function statusBadge($status)
    {
        return $status == true ? '<div class="badge badge-light-success">Active</div>' : '<div class="badge badge-light-danger">Inactive</div>';
    }
}

if (!function_exists('statusArray')) {
    /**
     * Get status array
     * @return string
     */
    function statusArray()
    {
        return UserStatus::asSelectArray();
    }
}

if (!function_exists('videoArray')) {
    /**
     * Get video array
     * @return string
     */
    function videoArray()
    {
        return Video::asSelectArray();
    }
}

if (!function_exists('doctorInvitation')) {
    /**
     * Get doctor invitation array
     * @return string
     */
    function doctorInvitation()
    {
        return DoctorInvitation::asSelectArray();
    }
}

if (!function_exists('languageArray')) {
    /**
     * Ge language array
     * @return string
     */
    function languageArray()
    {
        return UserLanguage::asSelectArray();
    }
}

if (!function_exists('doctortypeArray')) {
    /**
     * Ge language array
     * @return string
     */
    function doctortypeArray()
    {
        return DoctorType::asSelectArray();
    }
}

if (!function_exists('genderArray')) {
    /**
     * Ge gender array
     * @return string
     */
    function genderArray()
    {
        return UserGender::asSelectArray();
    }
}

if (!function_exists('historyArray')) {
    /**
     * Ge gender array
     * @return string
     */
    function historyArray()
    {
        return SymptomHistory::asSelectArray();
    }
}

if (!function_exists('getUID')) {
    /**
     * Generate UID
     * @return string
     */
    function getUID($channel_name) {
        $uid = rand(0, 999999);
        $call = AppointmentCall::where('channel_name', $channel_name)->first();
        if(!empty($call)){
            if (!$call->agoraToken->isEmpty()){
            foreach($call->agoraToken as $token)
                if ($token->uid === $uid) {
                    return getUID($channel_name);
                }else{
                    return $uid;
                }
            }
        }
        return $uid;
    }
}

if (!function_exists('getToken')) {
    /**
     * Generate Token
     * @return string
     */
    function getToken($channel_name, $uid) {
        $expirationTimeInSeconds = 3600;
        $currentTimestamp = time();
        $privilegeExpiredTs = $currentTimestamp + $expirationTimeInSeconds;
        return RtcTokenBuilder::buildTokenWithUid(
            env('AGORA_APPID'),
            env('AGORA_CERTIFICATE'),
            $channel_name,
            $uid,
            RtcTokenBuilder::RolePublisher,
            $privilegeExpiredTs
          );
    }
}

