<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Http\Responses\ApiFormattedResponse;

class CheckUserStatus
{
    public $apiResponse = "";

    /**
     * Patient Detail Controller constructor.
     * @param PatientDetailRepositoryEloquent $patientDetailRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(ApiFormattedResponse $apiFormattedResponse)
    {
        $this->apiResponse = $apiFormattedResponse;
    }
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth('api')->check() && auth('api')->user()->status === 0) {
            auth('api')->user()->userDevice->update(['device_id' => null, 'push_token' => null, 'voip_token' => null]);
            auth('api')->user()->token()->delete();
            return $this->apiResponse->respondWithMessage(trans('messages.logout_success'));
        }

        return $next($request);
    }
}
