<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\EcoBankRepository;
use App\Entities\EcoBank;
use App\Enums\ConsultationType;
use App\Enums\PaymentStatus;
use App\Enums\SenderType;
use App\Enums\TransactionPaymentStatus;
use App\Enums\TransactionType;
use App\Models\DoctorWalletTransaction;
use App\Jobs\SendEmails;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Models\AppointmentCall;
use App\Models\BankDetail;
use App\Enums\WalletType;
use App\Models\DoctorAppointment;
use App\Models\DoctorDetail;
use App\Models\Log as ModelsLog;
use App\Models\Transaction;
use App\Models\User;
use App\Transformers\TransactionTransformer;
use App\Validators\EcoBankValidator;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use App\Models\EmailTemplate;

/**
 * Class EcoBankRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class EcoBankRepositoryEloquent extends BaseRepository implements EcoBankRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Transaction::class;
    }

    /**
     * Generate agora token
     *
     * @return string
     */
    public function getToken()
    {
        $client = new Client();

        Log::info("getToken function called");
        Log::info(env('CYBS_BASE_URL'));

        $response = $client->post(env('CYBS_BASE_URL') . '/corporateapi/user/token', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Origin' => 'developer.ecobank.com'
            ],
            'json' => [
                'userId' => 'iamaunifieddev103',
                'password' => '$2a$10$Wmame.Lh1FJDCB4JJIxtx.3SZT0dP2XlQWgj9Q5UAGcDLpB0yRYCC'
            ]
        ]);

        $body = $response->getBody();
        $content = $body->getContents();

        Log::info("Token Body => " . $body);

        return json_decode($body)->token;
    }

    /**
     * Card Payment patient to molema api
     *
     * @return string
     */
    public function cardPayment($request)
    {
        Log::info("cardPayment called ");
        try {

            $client = new Client();

            $appointmentPayment = DoctorAppointment::findOrFail($request->appointment_id);

            $requestId = generateRequestId($appointmentPayment->patient_id);

            $doctorAbout = DoctorDetail::where('user_id', $appointmentPayment->doctor_id);
            if ($appointmentPayment->consultation_type == ConsultationType::VirtualConsultation) {
                $price = $doctorAbout->pluck('virtual_consultation_price')->first();
            } else if ($appointmentPayment->consultation_type == ConsultationType::PhysicalConsultation) {
                $price = $doctorAbout->pluck('physical_consultation_price')->first();
            } else {
                $price =  $doctorAbout->pluck('home_consultation_price')->first();
            }
            $doctorLocale = User::select('language')->findOrFail($appointmentPayment->doctor_id);
            $language = $doctorLocale->language == 1 ? 'en' : 'fr';
            $orderInfo = generateOrderInfo();
            $currency = config('constants.currency');
            // $price = "22";

            $params = [
                'profile_id' => env('PROFILE_ID'),
                'access_key' => env('ACCESS_CODE'),
                'transaction_uuid' => uniqid(),
                'signed_date_time' => gmdate('Y-m-d\TH:i:s\Z'),
                'signed_field_names' => "profile_id,access_key,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,auth_trans_ref_no,amount,currency,merchant_descriptor,payment_method,override_custom_cancel_page,override_custom_receipt_page",
                'unsigned_field_names' => 'signature,bill_to_forename,bill_to_surname,bill_to_email,bill_to_phone,bill_to_address_line1,bill_to_address_line2,bill_to_address_city,bill_to_address_state,bill_to_address_country,bill_to_address_postal_code,customer_ip_address,merchant_defined_data1,merchant_defined_data2,merchant_defined_data3,merchant_defined_data4',
                'payment_method' => 'card',
                'transaction_type' => 'sale',
                'reference_number' => $requestId,
                'auth_trans_ref_no' => $requestId,
                'amount' => $price,
                'currency' => $currency,
                'locale' => $language,
                'merchant_descriptor' => "Misalud",
                'bill_to_address_line1' => "1295 Charleston Rd",
                'bill_to_address_city' => "Mountain View",
                'bill_to_address_country' => "US",
                'bill_to_email' => auth()->user()->email,
                'bill_to_surname' => $appointmentPayment->patient->name,
                'bill_to_forename' => $appointmentPayment->patient->first_name,
                'bill_to_phone' => "",
                'bill_to_address_line2' => "",
                'bill_to_address_state' => "CA",
                'bill_to_address_postal_code' => "94043",
                'customer_ip_address' => "127.0.0.1",
                'merchant_defined_data1' => "",
                'merchant_defined_data2' => "",
                'merchant_defined_data3' => "",
                'merchant_defined_data4' => "",
                'override_custom_cancel_page' => url('/web/ecobank/fail-response?plateform=app'),
                'override_custom_receipt_page' => url('/web/ecobank/success-response?plateform=app')
            ];
            $params['signature'] = Transaction::sign($params);
            \Log::info("params Body => " . json_encode($params));

            $transaction = Transaction::create([
                'patient_id' => $appointmentPayment->patient_id,
                'doctor_id' => $appointmentPayment->doctor_id,
                'request_id' => $requestId,
                'payment_id' => $appointmentPayment->id,
                'order_info' => $orderInfo,
                'secure_hash' => $params['signature'],
                'transaction_id' => generateTransactionId(TransactionType::getKey(1)),
                'payment_type' => TransactionType::Consultation,
                'amount' => $price,
                'currency' => $currency,
                'payment_status' => TransactionPaymentStatus::Init,
                'transaction_type' => TransactionType::Consultation,
                'payment_channel' => 1
            ]);

            $reqLog = [
                'requestId' => $requestId,
                'productCode' => $request['id'],
                'amount' => $price,
                'currency' => $currency,
                'locale' => $language,
                'orderInfo' => $orderInfo,
            ];

            ModelsLog::create([
                'user_id' => auth()->user()->id,
                'user_type' => SenderType::Admin,
                'transaction_id' => $transaction->transaction_id,
                'req_log' => json_encode($reqLog),
                'res_log' => json_encode($params),
                'log_type' => 'card-payment',
            ]);

            return [
                'body' => $params,
                'transaction' => $transaction,
                'appointment_id' => $request->appointment_id,
                'locale' => $language,
                'paymentUrl' => url(env('CYBS_BASE_URL') . '/pay'),
                'returnUrl' => url("web/ecobank-redirection"),
                'merchantDetails' => [
                    'accessCode' => env('ACCESS_CODE'),
                    'merchantID' => env('MERCHANT_ID'),
                    'secureSecret' => env('SECURE_SECRATE'),
                    'profileID' => env('PROFILE_ID'),
                ]
            ];
        } catch (\Exception $e) {
            Log::error("||================= Error in cardPayment============||");
            Log::error("|| Error => " . $e->getMessage());
            Log::error("|| File => " . $e->getFile());
            Log::error("|| Line => " . $e->getLine());
            Log::error("||================= cardPayment error completed============||");
        }
    }

    /**
     * Add Doctor Bank Detail api
     *
     * @return string
     */
    public function addBankDetail($request)
    {
        $data = $request->all();
        $data['user_id'] = auth()->user()->id;
        return BankDetail::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    /**
     * Add Doctor Bank Detail api
     *
     * @return string
     */
    public function viewBankDetail($request)
    {
        $bankDetails = BankDetail::where('user_id', auth()->user()->id)->first();
        return $bankDetails;
    }

    /**
     * Add Doctor Bank Detail api
     *
     * @return string
     */
    public function updateAdminBankDetail($request)
    {
        $data = $request->all();

        $data['user_id'] = auth()->user()->id;
        $data['user_type'] = SenderType::Admin;

        return BankDetail::updateOrCreate(['user_type' => SenderType::Admin], $data);
    }

    /**
     * Eco Bank webhook
     *
     * @return string
     */
    public function ecoBankWebhook($request)
    {
        Log::info('-------------------------Eco Bank Call-------------------------');
        Log::info(json_encode($request->all(), true));
    }

    /**
     * Doctor Payout release using Eco Bank APIs
     *
     * @return string
     */
    public function doctorPayoutRelease($request)
    {
        $doctorWallet = DoctorDetail::where('user_id', $request['id'])->first();
        $bankDetails = BankDetail::where('user_id', $request['id'])->first();

        // Release payout using UCO Bank API
        $doctorLocale = User::select('language')->findOrFail($request['id']);
        $language = $doctorLocale->language == 1 ? 'en' : 'fr';
        $orderInfo = generateOrderInfo();
        $price = $request['payout_amount'];
        $price = 20;
        $requestId = generateRequestId($request['id']);
        $currency = config('constants.currency');
        // $client = new Client();

        $secureHashString = $requestId . $request['id'] . $price . $currency . $language . $orderInfo . 'https://webhook.site/02594cfc-9843-44c7-bf39-76003284f0cf' . env('ACCESS_CODE') . env('MERCHANT_ID') . env('SECURE_SECRATE');

        $secureHash = (hash('sha512', $secureHashString));

        // $token = self::getToken();

        // $reqLog = [
        //     'requestId' => $requestId,
        //     'productCode' => $request['id'],
        //     'amount' => $price,
        //     'currency' => $currency,
        //     'locale' => $language,
        //     'orderInfo' => $orderInfo,
        // ];

        // $response = $client->post(env('CYBS_BASE_URL').'/corporateapi/merchant/Signature', [
        //     'headers' => [
        //         'Authorization' => 'Bearer ' . $token,
        //         'Content-Type' => 'application/json',
        //         'Accept' => 'application/json',
        //         'Origin' => 'developer.ecobank.com',
        //         'Referer' => 'developer.ecobank.com'
        //     ],
        //     'json' => [
        //         'paymentDetails' => [
        //             'requestId' => $requestId,
        //             'productCode' => $request['id'],
        //             'amount' => $price,
        //             'currency' => $currency,
        //             'locale' => $language,
        //             'orderInfo' => $orderInfo,
        //             'returnUrl' => route("ecoBankWebhook"),
        //         ],
        //         'merchantDetails' => [
        //             'accessCode' => env('ACCESS_CODE'),
        //             'merchantID' => env('MERCHANT_ID'),
        //             'secureSecret' => env('SECURE_SECRATE'),
        //         ],
        //         'secureHash' => $secureHash,

        //     ],
        // ]);

        // if($bankDetails->bank_type == 1){
        //     $extension = [
        //         "request_id" => "2325",
        //         "request_type" => "INTERBANK",
        //         "param_list" => "[{\"key\"=>\"destinationBankCode\", \"value\"=>\"ASB\"},{\"key\"=>\"senderName\", \"value\"=>\"BEN\"},{\"key\"=>\"senderAddress\", \"value\"=>\"23 Accra Central\"},{\"key\"=>\"senderPhone\", \"value\"=>\"************\"},{\"key\"=>\"beneficiaryAccountNo\",\"value\"=>\"************\"},{\"key\"=>\"beneficiaryName\", \"value\"=>\"Owen\"},{\"key\"=>\"beneficiaryPhone\", \"value\"=>\"************\"},{\"key\"=>\"transferReferenceNo\", \"value\"=>\"QWE345Y4\"},{\"key\"=>\"amount\", \"value\"=>\"10\"},{\"key\"=>\"ccy\", \"value\"=>\"GHS\"},{\"key\"=>\"transferType\", \"value\"=>\"spot\"}]",
        //         "amount" => 10,
        //         "currency" => "GHS",
        //         "status" => "",
        //         "rate_type" => "spot"
        //     ];
        // }else{
        //     $extension = [
        //         "request_id" => "2323",
        //         "request_type" => "domestic",
        //         "param_list" => "[{\"key\":\"creditAccountNo\", \"value\":\"*************\"},{\"key\":\"debitAccountBranch\", \"value\":\"ACCRA\"},{\"key\":\"debitAccountType\", \"value\":\"Corporate\"},{\"key\":\"creditAccountBranch\", \"Accra\":\"GHS\"},{\"key\":\"creditAccountType\", \"value\":\"Corporate\"},{\"key\":\"amount\", \"value\":\"10\"},{\"key\":\"ccy\", \"value\":\"GHS\"}]",
        //         "amount" => 10,
        //         "currency" => "GHS",
        //         "status" => "",
        //         "rate_type" => "spot"
        //     ];
        // }

        // $paymentResponse = $client->post(env('CYBS_BASE_URL').'/corporateapi/merchant/payment', [
        //     'headers' => [
        //         'Authorization' => 'Bearer ' . $token,
        //         'Content-Type' => 'application/json',
        //         'Accept' => 'application/json',
        //         'Origin' => 'developer.ecobank.com'
        //     ],
        //     'json' => [
        //         'paymentHeader' => [
        //             "clientid" => "EGHTelc000043",
        //             "batchsequence" => "1",
        //             "batchamount" => 520,
        //             "transactionamount" => 520,
        //             "batchid" => "EG1593490",
        //             "transactioncount" => 6,
        //             "batchcount" => 6,
        //             "transactionid" => "E12T443308",
        //             "debittype" => "Multiple",
        //             "affiliateCode" => "EGH",
        //             "totalbatches" => "1",
        //             "execution_date" => "2020-06-01 00:00:00"
        //         ],
        //         "extension" => [$extension],
        //         'secureHash' => '398d4f285cc33e12f035da19fa9d954be35afaf66816531c4f1a1aedd3c6f132a85c62b23ca12d7b9a99bf5a84fc69b66738289a70e8f8115e90ffaa060f4026'
        //     ]
        // ]);

        // $body = $response->getBody()->getContents();

        $transaction = Transaction::create([
            'doctor_id' => $request['id'],
            'request_id' => $requestId,
            'payment_id' => $request['id'],
            'order_info' => $orderInfo,
            'secure_hash' => $secureHash,
            'transaction_id' => generateTransactionId(TransactionType::getKey(4)),
            'payment_type' => TransactionType::DoctorPayout,
            'amount' => $price,
            'currency' => $currency,
            'payment_status' => TransactionPaymentStatus::Complete,
            'transaction_type' => TransactionType::DoctorPayout,
            'payment_channel' => 1
        ]);

        // Update Doctor wallet balance
        $outstandingBalance = doubleval($doctorWallet->wallet_balance) - doubleval($request['payout_amount']);
        DoctorDetail::where('user_id', $request['id'])->update([
            'wallet_balance' => $outstandingBalance
        ]);


        // Create doctor wallet transaction record
        $walletTransaction = new DoctorWalletTransaction();
        $walletTransaction->transaction_id = $transaction->id;
        $walletTransaction->unique_id = generateRequestId($doctorWallet->user_id);
        $walletTransaction->doctor_type = $doctorWallet->doctor_type;
        $walletTransaction->wallet_type = WalletType::Payout;
        $walletTransaction->wallet_amount = doubleval($request['payout_amount']);
        $walletTransaction->reference_no = $request['reference_no'];
        $walletTransaction->note = $request['note'];
        $walletTransaction->save();

        // ModelsLog::create([
        //     'user_id' => auth()->user()->id,
        //     'user_type' => SenderType::Admin,
        //     'transaction_id' => $transaction->transaction_id,
        //     'req_log' => json_encode($reqLog),
        //     'res_log' => json_encode($body),
        //     'log_type' => 'payout/release',
        // ]);

        //Send Transaction email notification to Patient
        $emailTemplate = EmailTemplate::where('slug', 'doctor_payout_release')->first();
        $emailBody = $emailTemplate->getTranslation('description', $doctorWallet->user->language);
        $email_content = str_replace("[[USERNAME]]", ucwords($doctorWallet->user->name), $emailBody);
        $email_content = str_replace("[[PAYOUT_ID]]", $walletTransaction->unique_id, $email_content);
        $email_content = str_replace("[[RELEASE_DATE]]", $walletTransaction->created_at, $email_content);
        $email_content = str_replace("[[AMOUNT]]", $walletTransaction->wallet_amount, $email_content);
        $email_content = str_replace("[[REFERENCE_NO]]", $walletTransaction->reference_no, $email_content);
        $email_content = str_replace("[[NOTE]]", $walletTransaction->note, $email_content);
        $email_content = str_replace("[[PRODUCT_NAME]]", config('app.name'), $email_content);
        $user = [
            'email' => $doctorWallet->user->email,
            'email_body' => $email_content,
            'subject' => $emailTemplate->getTranslation('title', $doctorWallet->user->language)
        ];

        dispatch((new SendEmails($user)));

        return true;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
