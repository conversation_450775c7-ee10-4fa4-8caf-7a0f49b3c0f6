@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    @if(isset($patient))
    {{ Breadcrumbs::render('admin.patient.edit') }}
    @else
    {{ Breadcrumbs::render('admin.patient.create') }}
    @endif

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        @if(isset($patient))
                        {{__('Edit Patient Detail')}}
                        @else
                        {{__('Add Patient Detail')}}
                        @endif
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.patient.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="profile_details" class="collapse show">
                    @if(!empty($patient))
                    {{ Form::model($patient, ['route' => ['admin.patient.update', $patient->id], 'method' =>
                    'post','class'=>'form','id'=>'patient-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$patient->id) !!}
                    @else
                    {{ Form::open(['route' => 'admin.patient.store',
                    'method'=>'post','class'=>'form','id'=>'patient-form','enctype' => 'multipart/form-data']) }}
                    @endif
                    <div class="card-body border-top p-9">
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('First Name', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('first_name',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'first_name','placeholder'=>'Enter first name']) !!}
                                    @if($errors->has('first_name'))
                                    <div id="first_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('first_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Last Name', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('last_name',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'last_name','placeholder'=>'Enter last name']) !!}
                                    @if($errors->has('last_name'))
                                    <div id="last_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('last_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Email Address', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('email',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'email','placeholder'=>'Enter email']) !!}
                                    @if($errors->has('email'))
                                    <div id="email-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('email') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Mobile Number', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="row">
                                    <!--begin::Col-->
                                    <div class="col-lg-4 fv-row fv-plugins-icon-container">
                                        {!! Form::select('country_code',$countrycode,$patient->country_code ?? null,['class' => 'form-select form-select-solid', 'data-control' => 'select2','id'=>'country_code']) !!}
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                        @if($errors->has('dial_code'))
                                        <div id="dial_code-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('dial_code') }}
                                        </div>
                                        @endif
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-lg-8 fv-row fv-plugins-icon-container">
                                        {!! Form::text('mobile', $patient->mobile ?? null, ['class' => 'form-control form-control-lg
                                        form-control-solid','id'=>'mobile','placeholder'=>'Enter mobile']) !!}
                                        @if($errors->has('mobile'))
                                        <div id="mobile-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('mobile') }}
                                        </div>
                                        @endif
                                    </div>
                                    <!--end::Col-->
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-2">
                                {{ Form::label('House No', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('apartment_no',$patient->patientDetail->apartment_no ?? null,
                                    ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'appartment_no','placeholder'=>'Enter house number']) !!}
                                    @if($errors->has('appartment_no'))
                                    <div id="appartment_no-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('appartment_no') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-4">
                                {{ Form::label('Address', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('address',$patient->patientDetail->address ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'address','placeholder'=>'Enter address']) !!}
                                    @if($errors->has('address'))
                                    <div id="address-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('address') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('landmark', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <!--begin::Input group-->
                                <div class="mb-5">
                                    {!! Form::text('landmark',$patient->patientDetail->landmark ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'landmark','placeholder'=>'Enter landmark']) !!}
                                    @if($errors->has('landmark'))
                                    <div id="landmark-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('landmark') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">

                            <div class="col-lg-6">
                                {{ Form::label('city', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('city',$patient->patientDetail->city ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'city','placeholder'=>'Enter city']) !!}
                                    @if($errors->has('city'))
                                    <div id="city-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('city') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('country', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('country',$patient->patientDetail->country ?? 'Equatorial Guinea', ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'country','placeholder'=>'Enter country']) !!}
                                    @if($errors->has('country'))
                                    <div id="country-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('country') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <!--begin::Col-->
                            <div class="col-lg-3">
                                {{ Form::label('Avatar', null, ['class' => 'col-lg-12 col-form-label fw-semibold fs-6'])
                                }}
                                <div class="mb-5">
                                    <div class="image-input image-input-outline" data-kt-image-input="true"
                                        style="background-image: url('/assets/media/svg/avatars/blank.svg')">
                                        <div class="image-input-wrapper w-125px h-125px"
                                            style="background-image: url({{ !empty($patient->avatar_url) ?$patient->avatar_url: asset('assets/media/avatars/blank.png') }})">
                                        </div>
                                        <label
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            title="Change avatar">
                                            <i class="bi bi-pencil-fill fs-7"></i>
                                            {!! Form::file('avatar',['accept'=>'.png, .jpg, .jpeg']) !!}
                                            @if($errors->has('avatar'))
                                            <div id="avatar-error" class="fv-plugins-message-container invalid-feedback"
                                                style="display: block;">
                                                {{ $errors->first('avatar') }}
                                            </div>
                                            @endif
                                            <input type="hidden" name="avatar_remove" />
                                        </label>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            title="Cancel avatar">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                        <span
                                            class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            title="Remove avatar">
                                            <i class="bi bi-x fs-2"></i>
                                        </span>
                                    </div>
                                    <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('Height', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('height',!empty($patient->patientDetail) ? decrypt($patient->patientDetail->height) : null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'height','placeholder'=>'Enter height']) !!}
                                    @if($errors->has('height'))
                                    <div id="height-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('height') }}
                                    </div>
                                    @endif
                                </div>
                                {{ Form::label('Weight', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('weight',!empty($patient->patientDetail) ? decrypt($patient->patientDetail->weight) : null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'weight','placeholder'=>'Enter weight']) !!}
                                    @if($errors->has('weight'))
                                    <div id="weight-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('weight') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('Gender', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('gender', genderArray(), $patient->patientDetail->gender ??
                                    null,['id'=>'gender',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true",'placeholder'=>'Select gender', 'data-placeholder' => "Select Gender"]) !!}
                                    @if($errors->has('gender'))
                                    <div id="gender-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('gender') }}
                                    </div>
                                    @endif
                                </div>
                                {{ Form::label('Age', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('age',!empty($patient->patientDetail) ? decrypt($patient->patientDetail->age) : null, ['class' => 'form-control
                                    form-control-lg
                                    form-control-solid','id'=>'age','placeholder'=>'Enter Age']) !!}
                                    @if($errors->has('age'))
                                    <div id="age-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('age') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-3">
                                {{ Form::label('Language', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('language', languageArray(), $patient->language ??
                                    null,['id'=>'language',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true", 'data-placeholder' => "Select Language"]) !!}

                                    @if($errors->has('language'))
                                    <div id="language-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('language') }}
                                    </div>
                                    @endif
                                </div>
                                {{ Form::label('Status', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::select('status', statusArray(), $patient->status ?? null,['id'=>'status',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true", 'data-placeholder' => "Select Status"]) !!}
                                    @if($errors->has('status'))
                                    <div id="status-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('status') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-6">
                            <div class="col-md-3">
                                {{ Form::label('health_issue', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="input-group">
                                    {!! Form::select('health_issue[]', $healthIssue, $issue ?? [],['id'=>'status',
                                    'class' => 'form-select form-select-solid', 'data-control' => 'select2',
                                    'data-hide-search'=>"true", 'multiple'=>"multiple", 'data-placeholder' => "Select Health Issues"]) !!}
                                    @if($errors->has('health_issue'))
                                    <div id="health_issue-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('health_issue') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-3">
                                {{ Form::label('other_issue', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="input-group">
                                    {!! Form::text('other_issue', $otherIssue ?? null,
                                    ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'health_issue','placeholder'=>'Enter other issue']) !!}
                                    @if($errors->has('health_issue'))
                                    <div id="health_issue-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('health_issue') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @if(empty($patient))
                            <div class="col-md-3">
                                {{ Form::label('password', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="input-group">
                                    {!! Form::password('password', ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'password','placeholder'=>'Enter password']) !!}
                                    <i type="button" class="fa fa-eye check-eye" style="position: absolute;right: 45px;top: 17px;color: #7c7c7c;z-index: 999;"></i>
                                    <i type="button" class="fa fa-eye-slash uncheck-eye" style="position: absolute;right: 45px;top: 17px;color: #7c7c7c;z-index: 999;"></i>
                                    @if($errors->has('password'))
                                    <div id="password-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('password') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-3">
                                {{ Form::label('', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6 pt-8']) }}
                                {!! Form::button('<i class="flaticon-refresh"></i> Generate Password', ['class' =>
                                'btn btn-light-primary','id'=>'generate']) !!}

                            </div>
                            @endif
                        </div>
                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                            btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
<script type="text/javascript" src="{{asset('js/patient.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\PatientRequest', '#patient-form'); !!}
{!! JsValidator::formRequest('App\Http\Requests\PatientRequest', '#patient-edit-form'); !!}
@endsection
