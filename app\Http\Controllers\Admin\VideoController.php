<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Video as EnumsVideo;
use App\Http\Requests\VideoRequest;
use App\Models\Video;
use App\Repositories\VideoRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class VideoController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "video";

    /**
     * @var VideoRepositoryEloquent|string
     */
    public $videoRepositoryEloquent = "";

    public function __construct(VideoRepositoryEloquent $videoRepositoryEloquent)
    {
        parent::__construct();
        $this->videoRepositoryEloquent = $videoRepositoryEloquent;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Video']);
        return view('admin.video.index');
    }

    /**
     * Return video Wallet Transaction.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->videoRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Video']);
        return view('admin.video.video');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(VideoRequest $request)
    {
        try {
            $videoCount = Video::where('slug', EnumsVideo::PatientHome)->count();
            $videoWebCount = Video::where('slug', EnumsVideo::WebsiteHome)->count();
            if($videoCount >= 5){
                $request->session()->flash('error', config("messages.app_video_max_limit"));
                return redirect()->route('admin.video.index');
            }
            elseif($videoCount >= 2){
                $request->session()->flash('error', config("messages.web_video_max_limit"));
                return redirect()->route('admin.video.index');
            }
            else{
                $video = $this->videoRepositoryEloquent->store($request);
                $request->session()->flash('success', config("messages.video_created"));
                return redirect()->route('admin.video.index');
            }
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit video list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit video']);
        try {
            $video = $this->videoRepositoryEloquent->edit($request, $id);
            return view('admin.video.video', compact('video'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.video.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.video.index');
        }
    }

    /**
     * Update video list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(VideoRequest $request, $id)
    {
        try {
            $video = $this->videoRepositoryEloquent->update($request, $id);
            $request->session()->flash('success', config("messages.video_updated"));
            return redirect()->route('admin.video.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.video.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.video.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->videoRepositoryEloquent->changeStatusActive($request);
            $request->session()->flash('success', config("messages.video_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.video.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->videoRepositoryEloquent->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.video_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.video.index');
        }
    }

    /**
     * Delete video list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->videoRepositoryEloquent->destroy($request);
            $request->session()->flash('success', config("messages.video_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.video.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.video.index');
        }
    }
}
