<?php

namespace App\Exports;

use App\Models\AppointmentCall;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class AppointmentCallExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    public $requestData;

    public function __construct(array $requestData)
    {
        $this->requestData = $requestData;
    }

    public function query()
    {
        $appointment = AppointmentCall::with('initiator', 'receiver', 'doctorAppointment');

        if(!empty($this->requestData['search'])){
            $keyword = $this->requestData['search'];
            $appointment = $appointment->whereHas('initiator', function ($q) use ($keyword) {
                $q->searchByName($keyword);;
            })->orWhereHas('receiver', function ($q) use ($keyword) {
                $q->searchByName($keyword);
            })->orWhere('channel_name',  'LIKE', '%' . trim($keyword) . '%');
        }

        if(!empty($this->requestData['datepick'])){
            $date = explode(' - ', $this->requestData['datepick']);
            $start_date = date('Y/m/d', strtotime($date[0]));
            $end_date = date('Y/m/d', strtotime($date[1]));
            $appointment = $appointment->whereBetween(\DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$start_date, $end_date]);
        }

        return $appointment;
    }


    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID","Appointment ID","Initiator Name","Receiver Name","Channel Name","Call Date","Call Time", "Seconds","Created At","Updated At"];
    }

    public function map($appointment): array
    {

        return [
            $appointment->id,
            $appointment->doctorAppointment->unique_id ?? '-',
            $appointment->initiator->name,
            $appointment->receiver->name,
            $appointment->channel_name,
            $appointment->call_date,
            $appointment->start_time,
            $appointment->calculated_seconds,
            $appointment->created_date,
            $appointment->updated_date
        ];
    }
}
