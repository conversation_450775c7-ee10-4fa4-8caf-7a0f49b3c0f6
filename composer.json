{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "apility/laravel-fcm": "^1.5", "barryvdh/laravel-dompdf": "^2.0", "bensampo/laravel-enum": "^4.2", "davejamesmiller/laravel-breadcrumbs": "^5.3", "doctrine/dbal": "^3.6", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.15", "google/cloud": "^0.203.2", "guzzlehttp/guzzle": "^7.5", "harishdpatel/apiresponse": "dev-master", "intervention/image": "^2.7", "kreait/laravel-firebase": "^4.2", "laravel-notification-channels/apn": "^3.8", "laravel/breeze": "1.9.2", "laravel/framework": "^8.75", "laravel/passport": "^10.4", "laravel/sanctum": "^2.15", "laravel/socialite": "^5.6", "laravel/tinker": "^2.5", "laravelcollective/html": "^6.4", "league/flysystem-aws-s3-v3": "~1.0", "league/fractal": "^0.20.1", "maatwebsite/excel": "^3.1", "prettus/l5-repository": "^2.8", "proengsoft/laravel-jsvalidation": "^4.7", "spatie/laravel-fractal": "^5.8", "spatie/laravel-permission": "^5.9", "spatie/laravel-translatable": "*", "spatie/period": "^1.6", "twilio/sdk": "^6.44", "willywes/agora-sdk-php": "^0.1.3", "yajra/laravel-datatables-oracle": "^9.21"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"exclude-from-classmap": ["vendor\\edamov\\pushok\\src\\Request.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "NotificationChannels\\Apn\\": "app/Overrides", "Pushok\\": "app/Overrides", "Pushok\\Payload\\": "app/Overrides"}, "files": ["app/helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}