<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeExport;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use DB;
use Auth;
use Config;

class DoctorExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{

    public function __construct(array $requestData)
    {
        $this->requestData = $requestData;
    }

    public function query()
    {
        $user = new User();

        $user = $user->where('user_type',2);

        if(!empty($this->requestData['search'])){
            $user = $user->where('first_name' , 'LIKE' , '%'.$this->requestData['search'].'%')->orWhere('last_name' , 'LIKE' , '%'.$this->requestData['search'].'%')->orWhere('email' , 'LIKE' , '%'.$this->requestData['search'].'%');
        }

        if(!empty($this->requestData['patientstatus']) && ($this->requestData['patientstatus'] == 1)){
            $user = $user->where('status',$this->requestData['patientstatus']);
        }

        if(!empty($this->requestData['patientverification']) && ($this->requestData['patientverification'] == "Verified")){
            $user = $user->where('email_verified_at', '!=', null)->where('mobile_verified_at', '!=', null);
        }

        if(!empty($this->requestData['patientverification']) && ($this->requestData['patientverification'] == "Unverified")){
            $user = $user->where('email_verified_at', null)->orWhere('mobile_verified_at', null);
        }

        if(!empty($this->requestData['datepick'])){
            $date = explode('-', $this->requestData['datepick']);
            if($date[0] == $date[1]){
                $user = $user->whereDate('created_at',$date[0]);
            }else{
                $user = $user->whereDate('created_at','>',$date[0])->whereDate('created_at','<',$date[1]);
            }

        }

        return $user;
    }


    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID","User Type", "First Name","Last Name","User Name","Email", "Dial Code", "Mobile","Language","Status","Council Number","Served Location","Service","Doctor Type","Virtual Consultation Price","Physical Consultation Price","Home Consultation Price","Created At","Updated At"];
    }

    public function map($user): array
    {
        if($user->doctorDetail && $user->doctorDetail->doctor_type == 1){
            $doctorType = 'Collaborator';
        }else if($user->doctorDetail && $user->doctorDetail->doctor_type == 2){
            $doctorType = 'Freelancer';
        }else{
            $doctorType = '';
        }

        return [
            $user->id,
            ($user->user_type == 1) ? "Patient" : "Doctor",
            $user->first_name,
            $user->last_name,
            $user->username,
            $user->email,
            $user->dial_code,
            $user->mobile,
            ($user->language == 1) ? "es" : "en",
            ($user->status == 1) ? "Active" : "Inactive",
            ($user->doctorDetail) ? $user->doctorDetail->council_number : "",
            ($user->doctorDetail) ? $user->doctorDetail->served_location : "",
            ($user->doctorDetail) ? $user->doctorDetail->service : "",
            $doctorType,
            ($user->doctorDetail) ? $user->doctorDetail->virtual_consultation_price : "",
            ($user->doctorDetail) ? $user->doctorDetail->physical_consultation_price : "",
            ($user->doctorDetail) ? $user->doctorDetail->home_consultation_price : "",
            $user->created_date,
            $user->updated_date
        ];
    }
}
