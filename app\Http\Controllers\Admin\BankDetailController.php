<?php

namespace App\Http\Controllers\Admin;

use App\Enums\SenderType;
use App\Models\BankDetail;
use App\Repositories\EcoBankRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class BankDetailController extends Controller
{

    /**
     * @var EcoBankRepository
     */
    protected $ecoBankRepositoryEloquent;

    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "settings";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "bank-detail";

    /**
     * bank-detailPagesController constructor.
     *
     * @param EcoBankRepositoryEloquent $ecoBankRepositoryEloquent
     */
    public function __construct(EcoBankRepositoryEloquent $ecoBankRepositoryEloquent)
    {
        $this->ecoBankRepositoryEloquent = $ecoBankRepositoryEloquent;
        parent::__construct();
    }


    public function bankDetail(Request $request){
        config(['globalconfig.APP_TITLE' => 'Bank Details']);
        try {
            $bankDetail = BankDetail::where('user_type', SenderType::Admin)->first();
            return view('admin.settings.bank-detail.detail', compact('bankDetail'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.bank-detail.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.bank-detail.index');
        }
    }

    public function updateAdminBankDetail(Request $request){
        try {
            $this->ecoBankRepositoryEloquent->updateAdminBankDetail($request);
            $request->session()->flash('success', config("messages.bank_detail_updated"));
           return redirect()->route('admin.bank-detail.index');
       } catch (ModelNotFoundException $exception) {
           $request->session()->flash('error', config("messages.record_not_found"));
           return redirect()->route('admin.bank-detail.index');
       } catch (Exception $exception) {
           $request->session()->flash('error', config("messages.something_went_wrong"));
           return redirect()->route('admin.bank-detail.index');
       }
    }
}
