<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class NotificationType extends Enum
{
    const DoctorRating = 'doctor_rating';
    const PrescriptionAdd = 'prescription_added';
    const LabTestAdd = 'lab_test_added';
    const AppointmentPaymentSuccess = 'appointment_payment_success';
    const DoctorWalletCredit = 'doctor_wallet_credit';
    const AppointmentBooked = 'appointment_booked';
    const PatientChat = 'p_chat';
    const DoctorChat = 'd_chat';
    const Chat = 'chat';
    const CloseConsultation = 'close_consultation';
    const Support = 'support';
    const SupportResolve = 'support_resolve';
    const LabTestReportUpload = 'lab_test_report_upload';
    const LabTestRequest = 'lab_test_request';
    const MedicineRequest = 'medicine_request';
    const DiscontinuedCall = 'discontinued_call';
    const BusyCall = 'busy_call';
    const IncomingCall = "incoming_call";
}
