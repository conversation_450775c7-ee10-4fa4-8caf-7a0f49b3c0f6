@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">
    {{ Breadcrumbs::render('admin.bank_details.edit') }}
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        {{__('Edit Bank Detail')}}
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('admin.doctor.index') }}" class="btn btn-sm btn-light-primary">
                            <i class="la la-arrow-left">&nbsp;{{__('Back')}}</i>
                        </a>
                    </div>
                </div>
                <div id="profile_details" class="collapse show">

                    {{ Form::model($bank, ['route' => ['admin.bank_details.edit', $bank->id], 'method' =>
                    'post','class'=>'form','id'=>'bank-edit-form','enctype' => 'multipart/form-data']) }}
                    {!! Form::hidden('id',$bank->id) !!}

                    <div class="card-body border-top p-9">
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Account Number', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('account_number',null, ['class' => 'form-control form-control-lg
                                    form-control-solid mb-3 mb-lg-0','id'=>'account_number','placeholder'=>'Enter Account Number']) !!}
                                    @if($errors->has('account_number'))
                                    <div id="account_number-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('account_number') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Account Name', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('account_name',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'account_name','placeholder'=>'Enter account name']) !!}
                                    @if($errors->has('account_name'))
                                    <div id="account_name-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('account_name') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('account_type', null, ['class' => 'col-lg-12 col-form-label
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('account_type',$doctor->doctorDetail->account_type ?? null, ['class' =>
                                    'form-control form-control-lg
                                    form-control-solid','id'=>'account_type','placeholder'=>'Enter Account Type']) !!}
                                    @if($errors->has('account_type'))
                                    <div id="account_type-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('account_type') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Account Phone', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('account_phone',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'account_phone','placeholder'=>'Enter account phone']) !!}
                                    @if($errors->has('account_phone'))
                                    <div id="account_phone-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('account_phone') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="row gx-10 mb-0">
                            <div class="col-lg-6">
                                {{ Form::label('Account Address', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('account_address',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'account_address','placeholder'=>'Enter account address']) !!}
                                    @if($errors->has('account_address'))
                                    <div id="account_address-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('account_address') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                {{ Form::label('Bank Code', null, ['class' => 'col-lg-12 col-form-label required
                                fw-semibold fs-6']) }}
                                <div class="mb-5">
                                    {!! Form::text('bank_code',null, ['class' => 'form-control form-control-lg
                                    form-control-solid','id'=>'bank_code','placeholder'=>'Enter account name']) !!}
                                    @if($errors->has('bank_code'))
                                    <div id="bank_code-error" class="fv-plugins-message-container invalid-feedback"
                                        style="display: block;">
                                        {{ $errors->first('bank_code') }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn
                            btn-primary']) !!}
                        </div>
                        {!! Form::close() !!}
                    </div>
                    <!--end::Content-->
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\BankDetailRequest', '#bank-edit-form'); !!}
<script type="text/javascript" src="{{asset('assets/js/custom/apps/subscriptions/add/advanced.js')}}"></script>
@endsection
