<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePatientDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('patient_details', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id');
            $table->string('height', 10)->nullable();
            $table->string('weight', 10)->nullable();
            $table->integer('age')->nullable();
            $table->tinyInteger('gender')->comment('1 = male, 2 = female, 3 = other')->nullable();
            $table->string('apartment_no',200)->nullable();
            $table->string('address',200)->nullable();
            $table->string('landmark', 200)->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('timezone')->nullable();
            $table->string('health_issue')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('patient_details');
    }
}
