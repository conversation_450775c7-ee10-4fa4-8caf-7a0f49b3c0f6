<?php

namespace App\Repositories;

use App\Enums\SenderType;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\NotificationRepository;
use App\Models\Notification;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Models\User;
use App\Transformers\NotificationAllDetailTransformer;
use App\Validators\NotificationValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Class NotificationRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class NotificationRepositoryEloquent extends BaseRepository implements NotificationRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Notification::class;
    }

    /**
     * List of medicine
     *
     * @return array
     */
    public function getList($request)
    {
        $notification = Notification::with('support')->where('receiver_type', SenderType::Admin);

        if (!empty($request->start_date)) {
            $notification = $notification->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($notification)
            ->editColumn('title', function ($row) {
                $html = '<a href="' . route('admin.support.chat', ['id' => $row->ref_id]) . '">'.$row->support->subject.'</a> - '. $row->title;
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['title'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * View notification List
     *
     * @return string
     */
    public function viewNotifications($request){
        $notification = Notification::with('doctorAppointment')->where('receiver_id', auth()->user()->id)->orderBy('created_at', 'DESC')->paginate(10);
        $updateNotificationRead = User::findOrFail(auth()->user()->id);
        $updateNotificationRead->update(['notification_indicator' => false]);
        return fractal($notification, new NotificationAllDetailTransformer())->serializeWith(new PaginationArraySerializer());
    }


    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

}
