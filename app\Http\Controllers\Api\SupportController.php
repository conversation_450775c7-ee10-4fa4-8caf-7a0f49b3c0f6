<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ChatRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\SupportRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class SupportController extends Controller
{
    /**
     * @var SupportRepositoryEloquent
     */
    protected $supportRepository;

    /**
     * @var ApiFormattedResponse
     */
    protected $apiResponse;

    /**
     * FaqController constructor.
     * @param SupportRepositoryEloquent $supportRepository
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(SupportRepositoryEloquent $supportRepository, ApiFormattedResponse $apiResponse)
    {
        $this->supportRepository = $supportRepository;
        $this->apiResponse = $apiResponse;
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        try {
            $this->supportRepository->store($request);
            return $this->apiResponse->respondWithMessage(trans('messages.support_created'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function patientDoctorSupportList(Request $request)
    {
        try {
            $support = $this->supportRepository->patientDoctorSupportList($request);
            return $this->apiResponse->respondWithMessageAndPayload($support, trans('messages.support_created'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function doctorSupportList(Request $request)
    {
        try {
            $support = $this->supportRepository->doctorSupportList($request);
            return $this->apiResponse->respondWithMessageAndPayload($support, trans('messages.support_created'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function patientSupportList(Request $request)
    {
        try {
            $support = $this->supportRepository->patientSupportList($request);
            return $this->apiResponse->respondWithMessageAndPayload($support, trans('messages.support_created'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function markResolve(Request $request, $id)
    {
        try {
            $support = $this->supportRepository->markResolve($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($support, trans('messages.support_resolved'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function createSupportChat(ChatRequest $request)
    {
        try {
            $support = $this->supportRepository->createSupportChat($request);
            return $this->apiResponse->respondWithMessageAndPayload($support, trans('messages.support_created'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

       /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function supportChatList(Request $request, $id){
        try {
            $chat = $this->supportRepository->supportChatList($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($chat, trans('messages.update_profile_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

}
