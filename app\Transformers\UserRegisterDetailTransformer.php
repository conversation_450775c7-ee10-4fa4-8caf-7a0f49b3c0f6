<?php

namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

class UserRegisterDetailTransformer extends TransformerAbstract
{
    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        return [
            'id' => $user->id,
            'user_type' => $user->user_type,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'country_code' => $user->country_code,
            'dial_code' => $user->dial_code,
            'mobile' => $user->mobile,
            'status' => $user->status,
            'avatar' => $user->avatar_url ?? "",
            'language' => $user->language,
            'steps' => $user->steps,
            'created_at' => $user->created_at->timestamp
        ];
    }
}
