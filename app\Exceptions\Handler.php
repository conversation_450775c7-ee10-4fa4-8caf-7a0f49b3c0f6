<?php

namespace App\Exceptions;

use App\Http\Responses\ApiFormattedResponse;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;
use Illuminate\Support\Facades\Log;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        // $this->renderable(function (NotFoundHttpException $e, $request) {
        //     $apiResponse = new ApiFormattedResponse();
        //     if ($request->is('api/*')) {
        //         return $apiResponse->respondNotFound();
        //     }
        // });

        // $this->renderable(function (\Illuminate\Auth\AuthenticationException $e, $request) {
        //     $apiResponse = new ApiFormattedResponse();
        //     if ($request->is('api/*')) {
        //         return $apiResponse->respondUnauthorized('Unauthorized request. Please login and try again.');
        //     }
        // });

        // $this->renderable(function (ModelNotFoundException $e, $request) {
        //     $apiResponse = new ApiFormattedResponse();
        //     if ($request->is('api/*')) {
        //         return $apiResponse->respondMethodNotAllowed("This method is not supported for this route. Please try with valid method.");
        //     }
        // });

        // $this->renderable(function (Exception $e, $request) {
        //     $apiResponse = new ApiFormattedResponse();
        //     return $apiResponse->respondCustomExceptionError($e->getMessage(), 500);
        // });

        // $this->renderable(function (Exception $e, $request) {
            // $apiResponse = new ApiFormattedResponse();
            // return $apiResponse->respondCustomExceptionError($e->getMessage(), 500);
            // Log::error("||================= new Error to store============||");
            // Log::error("|| Error => ".$e->getMessage());
            // Log::error("|| File => ".$e->getFile());
            // Log::error("|| Line => ".$e->getLine());
            // Log::error("||================= Error to store completed============||");

        // });
    }
}
