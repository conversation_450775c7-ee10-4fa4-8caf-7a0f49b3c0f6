<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LabTestRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\PatientLabTestRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PatientLabTestController extends Controller
{
    /**
     * @var PatientLabTestRepositoryEloquent|string
     */
    public $patientLabTestRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param PatientLabTestRepositoryEloquent $patientLabTestRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(PatientLabTestRepositoryEloquent $patientLabTestRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->patientLabTestRepositoryEloquent = $patientLabTestRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function labTestList(Request $request)
    {
        try {
            $labTest = $this->patientLabTestRepositoryEloquent->labTestList($request);
            return $this->apiResponse->respondWithMessageAndPayload($labTest, trans('messages.patient_lab_test_get_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addLaboratory(LabTestRequest $request)
    {
        try {
            $medicine = $this->patientLabTestRepositoryEloquent->addLaboratory($request);
            return $this->apiResponse->respondWithMessageAndPayload($medicine, trans('messages.lab_test_add_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addPatientLabTest(Request $request)
    {
        try {
            $labTest = $this->patientLabTestRepositoryEloquent->addPatientLabTest($request);
            return $this->apiResponse->respondWithMessage(trans('messages.patient_lab_test_add_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getPatientLabtests(Request $request)
    {
        try {
            $prescription = $this->patientLabTestRepositoryEloquent->getPatientLabtests($request);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_lab_test_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewPatientLabTest(Request $request, $id)
    {
        try {
            $prescription = $this->patientLabTestRepositoryEloquent->viewPatientLabTest($request, $id);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_lab_test_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function uploadReports(Request $request)
    {
        try {
            $prescription = $this->patientLabTestRepositoryEloquent->uploadReports($request);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_lab_test_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewReports(Request $request)
    {
        try {
            $prescription = $this->patientLabTestRepositoryEloquent->viewReports($request);
            return $this->apiResponse->respondWithMessageAndPayload($prescription, trans('messages.patient_lab_test_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
