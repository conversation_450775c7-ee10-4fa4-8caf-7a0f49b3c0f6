<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SpecialityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {   return [
            'specialist_en' => 'required|unique:specialities,specialist->en,'.request()->id,
            // 'specialist_es' => 'required|unique:specialities,specialist->es,'.request()->id,
            'symptoms_id' => 'required'
        ];
    }
}
