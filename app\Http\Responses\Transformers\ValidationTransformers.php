<?php


namespace App\Http\Responses\Transformers;


class ValidationTransformers
{
    /**
     * response message
     *
     * @param array $errors
     * @return array
     */
    public function response($errors)
    {
        $transformedMessage = '';
        foreach ($errors as $field => $message) {
            $transformedMessage = method_exists($this, 'message') ? $this->container->call([
                $this,
                'message'
            ]) : $message[0];
            break;
        }

        return $transformedMessage;
    }
}