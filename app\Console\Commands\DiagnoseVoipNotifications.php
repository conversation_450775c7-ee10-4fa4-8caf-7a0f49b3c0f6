<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserDevice;
use App\Enums\DeviceType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DiagnoseVoipNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'voip:diagnose {user_id? : The user ID to diagnose}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose VoIP notification issues for users';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        if ($userId) {
            $this->diagnoseUser($userId);
        } else {
            $this->diagnoseAllUsers();
        }

        return 0;
    }

    private function diagnoseUser($userId)
    {
        $this->info("Diagnosing VoIP notifications for user ID: {$userId}");
        $this->line("=" . str_repeat("=", 60));

        $user = User::with('userDevice')->find($userId);

        if (!$user) {
            $this->error("User not found with ID: {$userId}");
            return;
        }

        $this->info("User: {$user->name} ({$user->email})");
        $this->info("User Type: " . ($user->user_type == 1 ? 'Patient' : 'Doctor'));

        if (!$user->userDevice) {
            $this->error("❌ No device record found for this user");
            $this->warn("Solution: User needs to register their device through the app");
            return;
        }

        $device = $user->userDevice;
        $this->info("Device ID: {$device->device_id}");
        $this->info("Device Type: " . ($device->device_type == DeviceType::IOS ? 'iOS' : 'Android'));
        $this->info("OS Version: {$device->os_version}");

        // Check tokens
        $this->line("\n📱 Token Status:");
        
        if ($device->device_type == DeviceType::IOS) {
            if (!empty($device->voip_token)) {
                $this->info("✅ VoIP Token: Available");
                $this->line("   Token: " . substr($device->voip_token, 0, 20) . "...");
            } else {
                $this->error("❌ VoIP Token: Missing");
                $this->warn("   Solution: App needs to register for VoIP notifications");
            }
        }

        if (!empty($device->push_token)) {
            $this->info("✅ Push Token: Available");
            $this->line("   Token: " . substr($device->push_token, 0, 20) . "...");
        } else {
            $this->error("❌ Push Token: Missing");
            $this->warn("   Solution: App needs to register for push notifications");
        }

        // Check configuration
        $this->line("\n⚙️  Configuration Check:");
        
        if ($device->device_type == DeviceType::IOS) {
            $this->checkApnConfiguration();
        } else {
            $this->checkFcmConfiguration();
        }

        // Check queue status
        $this->line("\n🔄 Queue Status:");
        $this->checkQueueStatus();

        $this->line("\n" . str_repeat("=", 60));
    }

    private function diagnoseAllUsers()
    {
        $this->info("Diagnosing VoIP notifications for all users");
        $this->line("=" . str_repeat("=", 60));

        $usersWithoutDevices = User::whereDoesntHave('userDevice')->count();
        $iosUsersWithoutVoip = UserDevice::where('device_type', DeviceType::IOS)
            ->where(function($q) {
                $q->whereNull('voip_token')->orWhere('voip_token', '');
            })->count();
        $usersWithoutPush = UserDevice::where(function($q) {
            $q->whereNull('push_token')->orWhere('push_token', '');
        })->count();

        $this->info("📊 Summary:");
        $this->line("Users without device records: {$usersWithoutDevices}");
        $this->line("iOS users without VoIP tokens: {$iosUsersWithoutVoip}");
        $this->line("Users without push tokens: {$usersWithoutPush}");

        if ($usersWithoutDevices > 0) {
            $this->warn("⚠️  Some users don't have device records");
        }

        if ($iosUsersWithoutVoip > 0) {
            $this->warn("⚠️  Some iOS users don't have VoIP tokens");
        }

        if ($usersWithoutPush > 0) {
            $this->warn("⚠️  Some users don't have push tokens");
        }

        $this->checkApnConfiguration();
        $this->checkFcmConfiguration();
        $this->checkQueueStatus();
    }

    private function checkApnConfiguration()
    {
        $this->line("🍎 APN Configuration:");
        
        $keyId = env('APN_KEY_ID');
        $teamId = env('APN_TEAM_ID');
        $bundleId = env('APN_APP_BUNDLE_ID');
        $keyPath = storage_path('app/pem/AuthKey_W4LYFB48MX.p8');

        if ($keyId) {
            $this->info("✅ APN Key ID: {$keyId}");
        } else {
            $this->error("❌ APN Key ID: Missing");
        }

        if ($teamId) {
            $this->info("✅ APN Team ID: {$teamId}");
        } else {
            $this->error("❌ APN Team ID: Missing");
        }

        if ($bundleId) {
            $this->info("✅ APN Bundle ID: {$bundleId}");
        } else {
            $this->error("❌ APN Bundle ID: Missing");
        }

        if (file_exists($keyPath)) {
            $this->info("✅ APN Key File: Found");
        } else {
            $this->error("❌ APN Key File: Missing at {$keyPath}");
        }
    }

    private function checkFcmConfiguration()
    {
        $this->line("🔥 FCM Configuration:");
        
        $serverKey = env('FCM_SERVER_KEY');
        $senderId = env('FCM_SENDER_ID');
        $credentialsPath = base_path('firebase-credentials.json');

        if ($serverKey && $serverKey !== 'Your FCM server key') {
            $this->info("✅ FCM Server Key: Available");
        } else {
            $this->error("❌ FCM Server Key: Missing or default");
        }

        if ($senderId && $senderId !== 'Your sender id') {
            $this->info("✅ FCM Sender ID: {$senderId}");
        } else {
            $this->error("❌ FCM Sender ID: Missing or default");
        }

        if (file_exists($credentialsPath)) {
            $this->info("✅ Firebase Credentials: Found");
        } else {
            $this->error("❌ Firebase Credentials: Missing at {$credentialsPath}");
        }
    }

    private function checkQueueStatus()
    {
        $queueConnection = config('queue.default');
        $this->line("Queue Connection: {$queueConnection}");

        if ($queueConnection === 'sync') {
            $this->warn("⚠️  Queue is set to 'sync' - jobs run immediately");
            $this->warn("   Consider using 'database' or 'redis' for better performance");
        } else {
            $this->info("✅ Queue Connection: {$queueConnection}");
            $this->warn("   Make sure queue workers are running: php artisan queue:work");
        }
    }
}
