<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\TransactionRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    /**
     * @var TransactionRepositoryEloquent
     */
    protected $transactionRepositoryEloquent;

    /**
     * @var ApiFormattedResponse
     */
    protected $apiResponse;

    /**
     * SymptomController constructor.
     * @param TransactionRepositoryEloquent $transactionRepositoryEloquent
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(TransactionRepositoryEloquent $transactionRepositoryEloquent, ApiFormattedResponse $apiResponse)
    {
        $this->transactionRepositoryEloquent = $transactionRepositoryEloquent;
        $this->apiResponse = $apiResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function transactionHistory(Request $request)
    {
        try {
            $transactions = $this->transactionRepositoryEloquent->transactionHistory($request);
            return $this->apiResponse->respondWithMessageAndPayload($transactions, trans('messages.transaction_fetching'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function walletHistory(Request $request)
    {
        try {
            $bankDetail = $this->transactionRepositoryEloquent->walletHistory($request);
            return $this->apiResponse->respondWithMessageAndPayload($bankDetail, trans('messages.bank_detail_add_success'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function checkPaymentStatus(Request $request)
    {
        try {
            $bankDetail = $this->transactionRepositoryEloquent->checkPaymentStatus($request);
            return $this->apiResponse->respondWithMessageAndPayload($bankDetail, trans('messages.transaction_fetching'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function updatePaymentStatus(Request $request)
    {
        try {
            $bankDetail = $this->transactionRepositoryEloquent->updatePaymentStatus($request);
            return $this->apiResponse->respondWithMessageAndPayload($bankDetail, trans('messages.payment_status_update_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
