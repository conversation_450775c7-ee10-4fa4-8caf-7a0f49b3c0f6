<?php

namespace App\Exports;

use App\Models\DoctorAppointment;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class AppointmentExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{

    public $requestData;

    public function __construct(array $requestData)
    {
        $this->requestData = $requestData;
    }

    public function query()
    {
        $appointment = DoctorAppointment::with('patient', 'doctor', 'transaction');

        if(!empty($this->requestData['search'])){
            $keyword = $this->requestData['search'];
            $appointment = $appointment->whereHas('transaction', function ($q) use ($keyword) {
                $q->where('transaction_id', $keyword);;
            })->orWhereHas('patient', function ($q) use ($keyword) {
                $q->searchByName($keyword);;
            })->orWhereHas('doctor', function ($q) use ($keyword) {
                $q->searchByName($keyword);;
            });
        }


        if(!empty($this->requestData['consultation_type'])){
            $appointment = $appointment->where('consultation_type',$this->requestData['consultation_type']);
        }

        if(!empty($this->requestData['appointment_status'])){
            $appointment = $appointment->where('appointment_status', $this->requestData['appointment_status']);
        }


        if(!empty($this->requestData['datepick'])){
            $date = explode(' - ', $this->requestData['datepick']);
            $start_date = date('Y/m/d', strtotime($date[0]));
            $end_date = date('Y/m/d', strtotime($date[1]));
            $appointment = $appointment->whereHas('transaction', function($que) use($start_date, $end_date){
                $que->whereBetween(\DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$start_date, $end_date]);
            });
        }

        return $appointment;
    }


    /**
     * Write code on Method
     *
     * @return response()
     */
    public function headings(): array
    {
        return ["ID","Patient Name", "Appointment ID","Doctor Name","Appointment Date","Call Time", "Consultation Type", "Appointment Status", "Transaction Id", "Transaction Date","Created At","Updated At"];
    }

    public function map($appointment): array
    {
        if($appointment->consultation_type == 1){
            $consultation_type = 'Virtual Consultation';
        }else if($appointment->consultation_type == 2){
            $consultation_type = 'Physical Consultation';
        }else{
            $consultation_type = 'Home Consultation';
        }

        if($appointment->appointment_status == 1){
            $appointment_status = 'Scheduled';
        }else if($appointment->appointment_status == 2){
            $appointment_status = 'Confirmed';
        }else{
            $appointment_status = 'Completed';
        }

        return [
            $appointment->id,
            $appointment->patient->name,
            $appointment->unique_id,
            $appointment->doctor->name,
            $appointment->appointment_date,
            $appointment->start_time."-".$appointment->end_time,
            $consultation_type,
            $appointment_status,
            $appointment->transaction->transaction_id,
            $appointment->transaction->created_date,
            $appointment->created_date,
            $appointment->updated_date
        ];
    }
}
