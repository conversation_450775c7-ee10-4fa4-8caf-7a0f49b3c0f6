<?php

namespace App\Transformers;

use App\Models\DoctorClinic;
use League\Fractal\TransformerAbstract;

class DoctorClinicTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(DoctorClinic $doctorClinic)
    {
        return [
            'clinic_name' => $doctorClinic->clinic_name,
            'apartment_no' => $doctorClinic->apartment_no,
            'clinic_address' => $doctorClinic->clinic_address,
            'clinic_landmark' => $doctorClinic->clinic_landmark,
            'clinic_city' => $doctorClinic->clinic_city,
            'clinic_state' => $doctorClinic->clinic_state,
            'country' => $doctorClinic->country,
            'clinic_dial_code' => $doctorClinic->clinic_dial_code,
            'clinic_mobile' => (string)$doctorClinic->clinic_mobile,
            'clinic_open_time' => $doctorClinic->clinic_open_time,
            'clinic_close_time' => $doctorClinic->clinic_close_time,
        ];
    }
}
