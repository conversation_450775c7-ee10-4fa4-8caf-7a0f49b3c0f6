<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\NotesRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\PatientNoteRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PatientNoteController extends Controller
{
    /**
     * @var PatientNoteRepositoryEloquent|string
     */
    public $patientNoteRepositoryEloquent = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param PatientNoteRepositoryEloquent $patientNoteRepositoryEloquent
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(PatientNoteRepositoryEloquent $patientNoteRepositoryEloquent, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->patientNoteRepositoryEloquent = $patientNoteRepositoryEloquent;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function addNotes(Request $request)
    {
        try {
            $notes = $this->patientNoteRepositoryEloquent->addNotes($request);
            return $this->apiResponse->respondWithMessageAndPayload($notes, trans('messages.notes_add_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) { dd($exception);
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function viewNotes(NotesRequest $request)
    {
        try {
            $notes = $this->patientNoteRepositoryEloquent->viewNotes($request);
            return $this->apiResponse->respondWithMessageAndPayload($notes, trans('messages.notes_get_success'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
