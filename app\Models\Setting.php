<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = ['key', 'value', 'slug'];

    protected $appends = [
        'created_date', 'updated_date'
    ];

    /**
     * Get Created Date
     *
     * @return string
     */
    public function getCreatedDateAttribute()
    {
        return !empty($this->created_at) ? $this->created_at->format("Y-m-d"): '-';
    }

    /**
     * Get Updated Date
     *
     * @return string
     */
    public function getUpdatedDateAttribute()
    {
        return !empty($this->updated_at) ? $this->updated_at->format("Y-m-d"): '-';
    }
}
