<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\CmsPageRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class CmsPagesController extends Controller
{
    /**
     * @var CmsPageRepositoryEloquent|string
     */
    public $cmsPageRepository = "";

    /**
     * @var ApiFormattedResponse|string
     */
    public $apiResponse = "";

    /**
     * Cms Page Controller constructor.
     * @param CmsPageRepositoryEloquent $cmsPageRepository
     * @param ApiFormattedResponse $apiFormattedResponse
     */
    public function __construct(CmsPageRepositoryEloquent $cmsPageRepository, ApiFormattedResponse $apiFormattedResponse)
    {
        $this->cmsPageRepository = $cmsPageRepository;
        $this->apiResponse = $apiFormattedResponse;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getPage(Request $request, $slug)
    {
        try {
            $page = $this->cmsPageRepository->getPage($slug);
            return $this->apiResponse->respondWithMessageAndPayload($page, trans('messages.get_cms_data'));
        } catch (ModelNotFoundException $exception) {
            return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getFaq(Request $request)
    {
        try {
            $page = $this->cmsPageRepository->getFaq($request);
            return $this->apiResponse->respondWithMessageAndPayload($page, trans('messages.get_faq_data'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
