<?php

namespace App\Jobs;

use App\Http\CommonTraits\UploadMedia;
use App\Models\PatientPrescription;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrescriptionPdf implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UploadMedia;

    public $prescriptionId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($prescriptionId)
    {
        $this->prescriptionId = $prescriptionId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $prescription = PatientPrescription::with('patientPrescriptionMedicine', 'appointment')->findOrFail($this->prescriptionId);
        $data = ['prescription' => $prescription];
        $contxt = stream_context_create([
            'ssl' => [
                'verify_peer' => FALSE,
                'verify_peer_name' => FALSE,
                'allow_self_signed' => TRUE,
            ]
        ]);
    
        $pdf = Pdf::setOptions(['isHTML5ParserEnabled' => true, 'isRemoteEnabled' => true]);
        $pdf->getDomPDF()->setHttpContext($contxt);
        $pdf->loadView('pdf.prescription', $data);
        $content = $pdf->download()->getOriginalContent();

        $url = self::pdfUpload($content, 'molema/prescription');

        $prescription->update(['prescription_pdf' => $url]);
    }
}


