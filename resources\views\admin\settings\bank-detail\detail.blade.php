@extends('admin.layouts.app')
@section('content')
<div class="d-flex flex-column flex-column-fluid">

    {{ Breadcrumbs::render('admin.settings.bank.detail') }}

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Basic info-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                    data-bs-target="#kt_account_profile_details" aria-expanded="true"
                    aria-controls="kt_account_profile_details">
                    <!--begin::Card title-->
                    <div class="card-title m-0">
                        {{__('Eco Bank Detail')}}
                    </div>
                </div>
                <!--begin::Card header-->
                <!--begin::Content-->
                <div id="bank_details" class="collapse show">
                    {{ Form::model($bankDetail, ['route' => ['admin.bank-detail.update'], 'method' =>'post','class'=>'form','id'=>'bank-detail-form','enctype' => 'multipart/form-data']) }}
                    <div class="card-body border-top p-9">
                        <div class="row mb-6">
                            {{ Form::label('Account Number', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('account_number',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'account_number','placeholder'=>'Enter account number'
                                        ]) !!}
                                        @if($errors->has('account_number'))
                                        <div id="account_number-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('account_number') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Account Name', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('account_name',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'account_name','placeholder'=>'Enter account name'
                                        ]) !!}
                                        @if($errors->has('account_name'))
                                        <div id="account_name-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('account_name') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Account Type', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('account_type',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'account_type','placeholder'=>'Enter account type'
                                        ]) !!}
                                        @if($errors->has('account_type'))
                                        <div id="account_type-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('account_type') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Account Phone', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('account_phone',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'account_phone','placeholder'=>'Enter account phone'
                                        ]) !!}
                                        @if($errors->has('account_phone'))
                                        <div id="account_phone-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('account_phone') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Account Address', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('account_address',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'account_address','placeholder'=>'Enter account address'
                                        ]) !!}
                                        @if($errors->has('account_address'))
                                        <div id="account_address-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('account_address') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-6">
                            {{ Form::label('Account Branch Code', null, ['class' => 'col-lg-4 col-form-label required
                            fw-semibold fs-6']) }}
                            <div class="col-lg-8">
                                <div class="row">
                                    <div class="col-lg-12 fv-row">
                                        {!! Form::text('bank_code',null, ['class' => 'form-control form-control-lg
                                        form-control-solid mb-3 mb-lg-0','id'=>'bank_code','placeholder'=>'Enter account brnach'
                                        ]) !!}
                                        @if($errors->has('bank_code'))
                                        <div id="bank_code-error" class="fv-plugins-message-container invalid-feedback"
                                            style="display: block;">
                                            {{ $errors->first('bank_code') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="reset" class="btn btn-light btn-active-light-primary me-2">Discard</button>
                        {!! Form::submit('Save Changes',['id'=>'kt_account_profile_details_submit','class'=>'btn submit
                        btn-primary']) !!}
                    </div>
                    {!! Form::close() !!}
                </div>
                <!--end::Basic info-->
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('App\Http\Requests\AdminBankDetailRequest', '#bank-detail-form'); !!}
@endsection
