<?php

namespace App\Repositories;

use App\Enums\DeviceType;
use App\Enums\NotificationType;
use App\Enums\UserType;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Jobs\PushNotifications;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\AppointmentCallRepository;
use App\Models\AppointmentCall;
use App\Jobs\silentNotification;
use App\Models\AgoraToken;
use App\Models\CallLog;
use App\Models\DoctorAppointment;
use App\Models\User;
use App\Notifications\VoipNotification;
use App\Transformers\AppointmentCallTransformer;
use App\Transformers\CallLogTransformer;
use App\Validators\AppointmentCallValidator;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Ya<PERSON>ra\DataTables\Contracts\DataTable;
use Yajra\DataTables\DataTables;

/**
 * Class AppointmentCallRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class AppointmentCallRepositoryEloquent extends BaseRepository implements AppointmentCallRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return AppointmentCall::class;
    }

    public function getInitiatorToken($channel_name)
    {
        $initiatorUid = getUID($channel_name);

        return ['initiator_uid' => $initiatorUid, 'token' => getToken($channel_name, $initiatorUid)];
    }

    public function getReceiverToken($channel_name)
    {
        $receiverUid = getUID($channel_name);

        return ['receiver_uid' => $receiverUid, 'token' => getToken($channel_name, $receiverUid)];
    }

    public function callNotification($request)
    {
        Log::info("||======================callNotification start=====================");
        Log::info("||");
        Log::info("|| RequestData => " . json_encode($request->all()));

        $sender = auth()->user();
        $receiver = User::findOrFail($request->receiver_id);
        
        $appointment = DoctorAppointment::whereHas('transaction')->findOrFail($request->appointment_id);

        $channel_name = 'molema' . rand(0, 999999);

        $extra = [
            'notification_type' => NotificationType::IncomingCall,
            'appointment_id' => $appointment->id,
            'booking_id' => $appointment->unique_id,
            'channel_name' => $channel_name,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->user_type == UserType::Patient ? $sender->id : $receiver->id,
            'doctor_id' => $receiver->user_type == UserType::Doctor ? $receiver->id : $sender->id,
        ];
        $receiverDetail = self::getReceiverToken($channel_name);
        $extra['receiver_token'] = $receiverDetail['token'];
        $extra['receiver_uid'] = $receiverDetail['receiver_uid'];

        $dialerDetail = self::getInitiatorToken($channel_name);

        // $doctorUser = User::findOrFail($extra['doctor_id']);
        $doctorUser = User::with('doctorDetail')->findOrFail($extra['doctor_id']); 

        $notificationArr = [
            'booking_id' => $appointment->unique_id,
            'channel_name' => $channel_name,
            'patient_id' => $extra['patient_id'],
            'doctor_id' => $extra['doctor_id'],
            'dialer_token' => $dialerDetail['token'],
            'dialer_uid' => $dialerDetail['initiator_uid'],
            'receiver_uid' => $extra['receiver_uid'],
            'receiver_token' => $extra['receiver_token'],
            'doctor_type' => $doctorUser->doctorDetail->doctor_type
        ];

        $data = [
            'channel_name' => $channel_name,
            'appointment_id' => $appointment->id,
            'initiator_id' => $sender->id,
            'receiver_id' => $receiver->id,
            'call_date' => date("Y-m-d"),
            'start_time' => now(),
            'calculatedSeconds' => null,
        ];

        $appointmentCall = AppointmentCall::create($data);

        $request->merge([
            'appointment_call_id' => $appointmentCall->id,
            'technical_error' => 200,
            'error_message' => 'call started by dialer',
            'status' => 'Initiated'
        ]);
        Self::updateStatus($request);

        $extra['appointment_call_id'] = $appointmentCall->id;
        $extra['initiator_id'] = $appointmentCall->initiator_id;
        $extra['receiver_id'] = $appointmentCall->receiver_id;
        $extra['doctor_type'] = $doctorUser->doctorDetail->doctor_type;

        $senderAgoraToken = AgoraToken::create([
            'appointment_call_id' => $appointmentCall->id,
            'user_id' =>  $sender->id,
            'uid' => $notificationArr['dialer_token']
        ]);

        $receiverAgoraToken = AgoraToken::create([
            'appointment_call_id' => $appointmentCall->id,
            'user_id' =>  $receiver->id,
            'uid' => $extra['receiver_token']
        ]);

        if ($receiver->userDevice->device_type == DeviceType::IOS) {
            Log::info("|| IOS Device");
            if (!empty($receiver->userDevice->voip_token)) {
                Log::info("|| void token available");
                ob_start();
                try {
                    Notification::send($receiver->userDevice, new VoipNotification($receiver->name, $receiverDetail['receiver_uid'], $sender->id, $sender->name, $extra));
                } finally {
                    ob_end_clean();
                }
            } else {
                Log::info("|| voip token not available");
                if ($receiver->user_type == UserType::Doctor) {
                    throw new Exception(config('messages.doctor_user_unavailable'), 404);
                } else {
                    throw new Exception(config('messages.patient_user_unavailable'), 404);
                }
            }
        } else {
            Log::info("|| non-IOS Device");
            if (!empty($receiver->userDevice->push_token)) {
                Log::info("|| push token available");
                dispatch(new silentNotification($sender, $receiver, $extra));
            } else {
                Log::info("|| push token not available");
                if ($receiver->user_type == UserType::Doctor) {
                    throw new Exception(config('messages.doctor_user_unavailable'), 404);
                } else {
                    throw new Exception(config('messages.patient_user_unavailable'), 404);
                }
            }
        }
        $responseArray = ['agora' => $notificationArr, 'appointment_call' => fractal($appointmentCall, new AppointmentCallTransformer())->serializeWith(new CustomArraySerializer())];
        Log::info("||======================callNotification end=======================");
        return $responseArray;
    }

    public function updateStatus($request)
    {

        $database = app('firebase.database');
        $appointmentCallId = $request->appointment_call_id;
        $appointmentCall = AppointmentCall::findOrFail($appointmentCallId);
        $callLog = CallLog::where('appointment_call_id', $appointmentCallId)->orderBy('created_at', 'DESC')->first();

        $sender = User::findOrFail($appointmentCall->initiator_id);
        $receiver = User::findOrFail($appointmentCall->receiver_id);

        if ($request->status == 'Busy') {
            $extra = [
                'notification_type' => NotificationType::BusyCall,
                'sender_name' => $sender->name,
                'sender_avatar' => $sender->avatar_url,
                'title' => 'Missed Call',
                'message' => $sender->name . " tried calling you when you were busy on another call.",
                'ref_id' => $appointmentCallId,
                'ref_type' => 'AppointmentCall'
            ];

            dispatch(new PushNotifications($sender, $receiver, $extra));
        }

        if ($request->status == 'Discontinued') {
            $extra = [
                'notification_type' => NotificationType::DiscontinuedCall,
            ];
            if ($receiver->userDevice->device_type == DeviceType::IOS) {
                if (!empty($receiver->userDevice->voip_token)) {
                    ob_start();
                    try {
                        Notification::send($receiver->userDevice, new VoipNotification($receiver->name, null, $sender->id, $sender->name, $extra));
                    } finally {
                        ob_end_clean();
                    }
                } else {
                    throw new Exception(config('messages.user_unavailable'), 503);
                }
            } else {
                if (!empty($receiver->userDevice->push_token)) {
                    dispatch(new silentNotification($sender, $receiver, $extra));
                } else {
                    throw new Exception(config('messages.user_unavailable'), 503);
                }
            }
        }

        if ($request->status == 'Received' && $callLog->status !== 'Initiated') {
            throw new Exception(config('messages.not_receive_call'), 403);
        }

        if ($request->status == 'Accepted' && $callLog->status !== 'Received') {
            throw new Exception(config('messages.not_accept_call'), 403);
        }

        if ($request->status == 'Ended') {
            $appointmentCall->update(['calculated_seconds' => $request->second]);
        }

        $reference = $database->getReference('/' . $appointmentCallId)->set([
            'appointment_call_id' => $appointmentCallId,
            'technical_error' => $request->technical_error ?? null,
            'error_message' => $request->error_message ?? null,
            'status' => $request->status,
        ]);

        $value = $reference->getValue();

        $senderAgoraToken = $appointmentCall->callLogs()->create([
            'session_id' => $request->session_id ?? null,
            'technical_error' => $request->technical_error ?? null,
            'error_message' => $request->error_message ?? null,
            'status' => $request->status,
        ]);

        return fractal($senderAgoraToken, new CallLogTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * List of call
     *
     * @return array
     */
    public function getList($request)
    {
        $appointmentCall = AppointmentCall::select(['appointment_calls.*', 'initiators.first_name as initiators_first_name', 'receivers.first_name as receivers_first_name'])
            ->with('initiator', 'receiver', 'doctorAppointment')
            ->join('users as initiators', 'appointment_calls.initiator_id', '=', 'initiators.id')
            ->join('users as receivers', 'appointment_calls.receiver_id', '=', 'receivers.id');

        if (!empty($request->start_date)) {
            $appointmentCall = $appointmentCall->whereBetween(DB::raw("DATE_FORMAT(call_date,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($appointmentCall)
            ->filterColumn('initiator_name', function ($query, $keyword) {
                return $query->whereHas('initiator', function ($q) use ($keyword) {
                    $q->searchByName($keyword);
                });
            })
            ->filterColumn('receiver_name', function ($query, $keyword) {
                return $query->whereHas('receiver', function ($q) use ($keyword) {
                    $q->searchByName($keyword);
                });
            })
            ->orderColumn('initiator_name', function ($query, $order) {
                $query->orderBy('initiators_first_name', $order);
            })
            ->orderColumn('receiver_name', function ($query, $order) {
                $query->orderBy('receivers_first_name', $order);
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();

        return AppointmentCall::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $call = AppointmentCall::findOrFail($id);

        return $call;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $call = AppointmentCall::findOrFail($id);

        $data = $request->all();
        if ($request->has('image')) {
            $data['image'] = self::imageUpload($request->image, 'molema/call');
            self::delete($call->getOriginal('image'));
        }
        return $call->update($data);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $user = AppointmentCall::findOrFail($request->input('id'));
        $user->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $user = AppointmentCall::findOrFail($request->input('id'));
        $user->update(['status' => false]);
    }

    /**
     * @param $request
     */
    public function destroy($request)
    {
        $patient = AppointmentCall::findOrFail($request->input('id'));
        $patient->delete();
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
