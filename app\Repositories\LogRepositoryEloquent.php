<?php

namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\LogRepository;
use App\Models\Log;
use App\Validators\LogValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Class LogRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class LogRepositoryEloquent extends BaseRepository implements LogRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Log::class;
    }

    /**
     * get list of log
     *
     * @return array
     */
    public function getList($request)
    {
        $log = Log::query();

        if (!empty($request->start_date)) {
            $log = $log->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($log)
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
