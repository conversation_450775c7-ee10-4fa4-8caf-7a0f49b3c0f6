<?php

namespace App\Http\Controllers\Admin;

use App\Models\Setting;
use App\Exports\DoctorExport;
use App\Repositories\DoctorDetailRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use App\Models\Speciality;
use App\Http\Requests\DoctorRequest;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\DoctorRating;
use App\Models\Countries;
use App\Models\DoctorAvailability;
use App\Models\DoctorUnavailability;
use App\Models\DoctorWalletTransaction;
use App\Models\DoctorSubscription;
use App\Models\BankDetail;

class DoctorController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "doctor";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var DoctorDetailRepositoryEloquent|string
     */
    public $doctorDetailRepository = "";

    public function __construct(DoctorDetailRepositoryEloquent $doctorDetailRepository)
    {
        parent::__construct();
        $this->doctorDetailRepository = $doctorDetailRepository;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'Doctor']);
        $settingPage = Setting::where('slug', 'molema-doctor-sign-up-mode')->first();
        return view('admin.user.doctor.index', compact('settingPage'));
    }

    /**
     * Return player list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->doctorDetailRepository->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add doctor']);
        $countrycode = Countries::orderBy('id', 'asc')->pluck('iso', 'iso');
        $specialities = Speciality::where('status', 1)->pluck('specialist', 'id');
        return view('admin.user.doctor.doctor', compact('specialities', 'countrycode'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(DoctorRequest $request)
    {
        try {
            $doctor = $this->doctorDetailRepository->store($request);
            $request->session()->flash('success', config("messages.doctor_created"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit doctor list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit doctor']);
        try {
            $doctor = $this->doctorDetailRepository->edit($request, $id);
            $countrycode = Countries::orderBy('id', 'asc')->pluck('iso', 'iso');
            $specialities = Speciality::where('status', 1)->pluck('specialist', 'id');
            return view('admin.user.doctor.doctor', compact('doctor', 'specialities', 'countrycode'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * Update doctor list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(DoctorRequest $request, $id)
    {
        try {
            $doctor = $this->doctorDetailRepository->update($request, $id);
            $request->session()->flash('success', config("messages.doctor_updated"));
            return redirect()->route('admin.doctor.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * Update doctor status.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->doctorDetailRepository->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->doctorDetailRepository->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    public function view(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'View Doctor']);
        try {
            $doctor = $this->doctorDetailRepository->view($request, $id);
            $rating = DoctorRating::where('doctor_id', $id)->get();
            $averageRating = $rating->avg('rating');
            $appointment = $this->doctorDetailRepository->booking($request, $id);
            $appointment_upcoming = $appointment['appointment_upcoming'];
            $appointment_ongoing = $appointment['appointment_ongoing'];
            $appointment_completed = $appointment['appointment_completed'];

            $available_slot = DoctorAvailability::where('user_id', $id)->orderBy('day', 'asc')->get();
            $unavailable_slot = DoctorUnavailability::where('user_id', $id)->where('unavailable_date', '>=', date('Y-m-d', strtotime('-7 days')))->get();
            $month_unavailable_slot = DoctorUnavailability::where('user_id', $id)->where('unavailable_date', '>=', date('Y-m-d', strtotime('-30 days')))->get();
            $year_unavailable_slot = DoctorUnavailability::where('user_id', $id)->where('unavailable_date', '>=', date('Y-m-d', strtotime('-1 year')))->get();

            $ongoingSubscription = DoctorSubscription::where('user_id', $id)->where('subscription_start_date', '<=', date('Y-m-d'))->where('subscription_end_date', '>=', date('Y-m-d'))->first();
            $completedSubscription = DoctorSubscription::where('user_id', $id)->where('subscription_end_date', '<=', date('Y-m-d'))->get();
            $upcomingSubscription = DoctorSubscription::where('user_id', $id)->where('subscription_start_date', '>=', date('Y-m-d'))->get();

            $commissionAmount = number_format(DoctorWalletTransaction::whereHas('doctorAppointment', function ($q) use ($id) {
                $q->where('doctor_id', $id);
            })->sum('wallet_amount'), 2, '.', '');
            $specialities = [];
            foreach ($doctor->speciality as $speciality) {
                $specialities[] = $speciality->specialist;
            }
            return view('admin.user.doctor.view', compact('doctor', 'appointment_upcoming', 'appointment_ongoing', 'appointment_completed', 'averageRating', 'available_slot', 'unavailable_slot', 'month_unavailable_slot', 'year_unavailable_slot', 'commissionAmount', 'specialities', 'ongoingSubscription', 'completedSubscription', 'upcomingSubscription'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function accountVerify(Request $request)
    {
        try {
            $this->doctorDetailRepository->accountVerify($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.user-management.index');
        }
    }

    public function accountVerifyDoctor(Request $request, $id)
    {
        try {
            $this->doctorDetailRepository->accountVerifyDoctor($request, $id);
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function doctorInvitation(Request $request)
    {
        try {
            $this->doctorDetailRepository->doctorInvitation($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->doctorDetailRepository->destroy($request);
            $request->session()->flash('success', config("messages.doctor_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.doctor.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request)
    {
        $fileName = "doctor_" . time() . ".xlsx";
        return Excel::download(new DoctorExport($request->all()), $fileName);
    }

    public function bankedit(Request $request, $id)
    {

        try {
            $bank = BankDetail::where('id', $id)->first();
            if ($request->isMethod('post')) {
                $bank->update($request->all());
                $request->session()->flash('success', "Bank Details Updated Successfully");
            }
            return view('admin.user.doctor.bankdetail', compact('bank'));
        } catch (Exception $e) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.doctor.index');
        }
    }
}
