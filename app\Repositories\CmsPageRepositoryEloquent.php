<?php

namespace App\Repositories;

use App\Enums\Status;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\CmsPageRepository;
use App\Models\CmsPage;
use App\Models\Faq;
use App\Transformers\FaqDetailTransformer;
use App\Validators\CmsPageValidator;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Class CmsPageRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class CmsPageRepositoryEloquent extends BaseRepository implements CmsPageRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return CmsPage::class;
    }

    /**
     * Update patient profile detail
     *
     * @return array
     */
    public function getList($request)
    {
        $cmsPage = CmsPage::query();
        if (!empty($request->start_date)) {
            $cmsPage = $cmsPage->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }

        return DataTables::of($cmsPage)
            ->editColumn('action', function ($row) {
                $html = '<a href="#" class="btn btn-light btn-active-light-primary btn-sm" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                    Actions
                    <span class="svg-icon svg-icon-5 m-0">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                <path d="M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z" fill="currentColor" fill-rule="nonzero" transform="translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)"></path>
                            </g>
                        </svg>
                    </span>
                </a>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">';

                if ($row->status == 0) {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.cms.change-status-active') . '" id="' . $row->id . '">
                        Active
                    </a>';
                } else {
                    $html .= '<a href="#" class="menu-link px-3 changeStatus" data-url="' . route('admin.cms.change-status-deactive') . '" id="' . $row->id . '">
                        InActive
                    </a>';
                }

                $html .= '</div>
                    <div class="menu-item px-3">
                        <a href="' . route('admin.cms.edit', ['id' => $row->id]) . '" class="menu-link px-3" data-kt-docs-table-filter="edit_row">
                            Edit
                        </a>
                    </div>
                    <div class="menu-item px-3">
                    <a class="menu-link px-3 delete" id="' . $row->id . '" data-name="' . $row->question . '" data-url="' . route('admin.cms.destroy') . '" data-table="cms_table"> Delete</a>
                    </div>
                </div>';
                return $html;
            })
            ->orderColumn('created_date', function ($query, $order) {
                $query->orderBy('created_at', $order);
            })
            ->rawColumns(['action'])
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function store($request)
    {
        $data = $request->all();

        return CmsPage::create($data);
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function edit($request, $id)
    {
        $cmsPage = CmsPage::findOrFail($id);

        return $cmsPage;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function viewPrivacyPolicy($request)
    {
        $cmsPage = CmsPage::where('slug', 'privacy-policy')->first();

        return $cmsPage;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function viewAboutUs($request)
    {
        $cmsPage = CmsPage::where('slug', 'about-us')->first();

        return $cmsPage;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function viewTermsAndCondition($request)
    {
        $cmsPage = CmsPage::where('slug', 'terms-and-conditions')->first();

        return $cmsPage;
    }

    /** @param $request
     * @param $id
     * @return array
     */
    public function viewDeleteAccountInstruction($request)
    {
        $cmsPage = CmsPage::where('slug', 'delete-account-instruction')->first();

        return $cmsPage;
    }

    /**
     * @param array $request
     * @param $id
     * @return mixed
     */
    public function update($request, $id)
    {
        $cmsPage = CmsPage::findOrFail($id);

        $data = $request->all();

        return $cmsPage->update($data);
    }

    /**
     * @param $request
     */
    public function changeStatusActive($request)
    {
        $cmsPage = CmsPage::findOrFail($request->input('id'));
        $cmsPage->update(['status' => true]);
    }

    /**
     * @param $request
     */
    public function changeStatusDeactive($request)
    {
        $cmsPage = CmsPage::findOrFail($request->input('id'));
        $cmsPage->update(['status' => false]);
    }


    /**
     * @param $request
     */
    public function destroy($request)
    {
        $cmsPage = CmsPage::findOrFail($request->input('id'));
        $cmsPage->delete();
    }

    /**
     * @param $request
     */
    public function getPage($slug)
    {
        return CmsPage::where('slug', $slug)->where('status', Status::Active)->firstOrFail();
    }

    /**
     * @param $request
     */
    public function getFaq($request)
    {
        $faq = Faq::where('status', Status::Active)->paginate(10);

        return fractal($faq, new FaqDetailTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
