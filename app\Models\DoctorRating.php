<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorRating extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id',
        'doctor_id',
        'rating',
        'notes',
    ];

    /**
     * The doctor_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')->withTrashed();
    }

    /**
     * The patient_id that belong to the users.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id', 'id')->withTrashed();
    }

    public function notification()
    {
        return $this->morphToMany(Notification::class, 'ref_type');
    }
}
