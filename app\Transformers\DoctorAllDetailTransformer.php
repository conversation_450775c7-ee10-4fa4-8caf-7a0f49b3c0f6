<?php

namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

class DoctorAllDetailTransformer extends TransformerAbstract
{
    /**
     * List of resources to automatically include
     *
     * @var array
     */
    protected array $defaultIncludes = [
        'doctor_detail', 'doctor_document', 'doctor_clinic', 'doctor_experience'
    ];

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(User $user)
    {
        return [
            'id' => $user->id,
            'user_type' => $user->user_type,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'dial_code' => $user->dial_code,
            'mobile' => $user->mobile,
            'status' => $user->status,
            'language' => $user->language,
            'steps' => $user->steps,
            'avatar' => $user->avatar_url ?? "",
            'created_at' => $user->created_at->timestamp,
            'speciality' => $this->getDoctorSpeciality($user->speciality)
        ];
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function getDoctorSpeciality($speciality)
    {

        $specialityData = collect();
        $speciality->each(function ($special, $key) use ($specialityData) {
            $specialityData->push([
                'id' => $special->id,
                'specialist' => $special->specialist,
                'symptoms' => $special->symptom->symptoms_name
            ]);
        });
        return $specialityData;
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function includeDoctorExperience(User $user)
    {
        if (!empty($user->doctorExperience)) {
            return $this->collection($user->doctorExperience, new DoctorExperienceTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Item
     */
    public function includeDoctorDetail(User $user)
    {
        if (!empty($user->doctorDetail)) {
            return $this->item($user->doctorDetail, new DoctorDetailTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Collection
     */
    public function includeDoctorDocument(User $user)
    {
        if (!empty($user->doctorDocument)) {
            return $this->collection($user->doctorDocument, new DoctorDocumentTransformer());
        }
    }

    /**
     * @param User $user
     * @return \League\Fractal\Resource\Item
     */
    public function includeDoctorClinic(User $user)
    {
        if (!empty($user->doctorClinic)) {
            return $this->item($user->doctorClinic, new DoctorClinicTransformer());
        }
    }
}
