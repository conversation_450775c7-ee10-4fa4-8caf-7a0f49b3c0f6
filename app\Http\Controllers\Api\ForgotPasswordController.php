<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserForgotPasswordRequest;
use App\Repositories\Repositories\UsersRepositoryEloquent;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\UserRepositoryEloquent;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class ForgotPasswordController extends Controller
{
     /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    protected $apiResponse = '';

    protected $userRepo = '';
    /**
     * ForgotPasswordController constructor.
     * @param ApiFormattedResponse $apiResponse
     * @param UsersRepositoryEloquent $userRepositoryEloquent
     */
    public function __construct(ApiFormattedResponse $apiResponse, UserRepositoryEloquent $usersRepositoryEloquent)
    {
        $this->apiResponse = $apiResponse;
        $this->userRepo = $usersRepositoryEloquent;
    }

    /**
     * Send a reset link to the given user.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function sendResetLinkEmail(UserForgotPasswordRequest $request)
    {
        $jsonResponse = '';
        try {
            User::where('email', $request->email)->firstOrFail();
            $response = Password::sendResetLink(
                $request->only('email')
            );
            if ($response == Password::RESET_LINK_SENT) {
                $jsonResponse = $this->apiResponse->respondWithMessage(trans('messages.forgot_password_link'));
            } else {
                $jsonResponse = $this->apiResponse->respondValidationError(trans('messages.forgot_password_link_not_sent'));
            }
        } catch (ModelNotFoundException $exception) {
            $jsonResponse = $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            $jsonResponse = $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }

        return $jsonResponse;
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $jsonResponse = '';
        try {
            $update = DB::table('password_resets')->where(['email' => $request->email, 'token' => Hash::check('plain-text', $request->token)])->first();
            if(!$update){
                return $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
            }
            User::where('email', $request->email)->update(['password' => bcrypt($request->password)]);
            DB::table('password_resets')->where(['email'=> $request->email])->delete();
            $jsonResponse = $this->apiResponse->respondWithMessage(trans('messages.user_password_updated'));
        } catch (ModelNotFoundException $exception) {
            $jsonResponse = $this->apiResponse->respondNotFound(trans('messages.record_not_found'));
        } catch (Exception $exception) {
            $jsonResponse = $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
        return $jsonResponse;
    }

    /**
     * Get the broker to be used during password reset.
     *
     * @return mixed
     */
    public function broker()
    {
        return Password::broker('users');
    }

    /**
     * Get the needed authentication credentials from the request.
     *
     * @param Request $request
     * @return array
     */
    protected function credentials(Request $request)
    {
        return $request->only('email');
    }
}
