<?php

namespace App\Transformers;

use App\Models\Notification;
use League\Fractal\TransformerAbstract;

class NotificationAllDetailTransformer extends TransformerAbstract
{

    /**
     * A Fractal transformer.
     *
     * @return array
     */
    public function transform(Notification $notification)
    {
        return [
            'id' => $notification->id,
            'sender_id' => $notification->sender_id,
            'sender_name' => $notification->sender->name ?? 'Deleted User',
            'receiver_id' => $notification->receiver_id,
            'receiver_name' => $notification->receiver->name ?? 'Deleted User',
            'title' => $notification->title,
            'description' => $notification->description,
            'booking_id' => !empty($notification->doctorAppointment) ? $notification->doctorAppointment->unique_id : '-',
            'ref_id' => $notification->ref_id,
            'ref_type' => $notification->ref_type,
            'notification_type' => $notification->notification_type,
        ];
    }
}
