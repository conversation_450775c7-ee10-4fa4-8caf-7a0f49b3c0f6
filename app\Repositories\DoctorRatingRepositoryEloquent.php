<?php

namespace App\Repositories;

use App\Enums\NotificationType;
use App\Http\Responses\Transformers\CustomArraySerializer;
use App\Http\Responses\Transformers\PaginationArraySerializer;
use App\Jobs\Notification;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Criteria\RequestCriteria;
use App\Repositories\DoctorRatingRepository;
use App\Models\DoctorRating;
use App\Models\User;
use App\Transformers\DoctorRatingTransformer;
use App\Validators\DoctorRatingValidator;
use Illuminate\Support\Facades\Auth;

/**
 * Class DoctorRatingRepositoryEloquent.
 *
 * @package namespace App\Repositories;
 */
class DoctorRatingRepositoryEloquent extends BaseRepository implements DoctorRatingRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return DoctorRating::class;
    }

    /**
     * Set Doctor Rating
     *
     * @return string
     */
    public function createRating($request)
    {
        $data = $request->all();
        $data['patient_id'] = Auth::guard('api')->user()->id;
        $rating = DoctorRating::create($data);

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($rating->doctor_id);
        $extra = [
            'notification_type' => NotificationType::DoctorRating,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Rating',
            'message' => $sender->name." has given you rating.",
            'ref_id' => $rating->id,
            'ref_type' => 'DoctorRating'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return fractal($rating, new DoctorRatingTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Set Doctor Rating
     *
     * @return string
     */
    public function editRating($request, $id)
    {
        $rating = DoctorRating::findOrFail($id);
        $rating->update(['rating' => $request->rating, 'notes' => $request->notes ?? null]);

        //Send Notification to Doctor
        $sender = auth()->user();
        $receiver = User::findOrFail($rating->doctor_id);
        $extra = [
            'notification_type' => NotificationType::DoctorRating,
            'sender_name' => $sender->name,
            'sender_avatar' => $sender->avatar_url,
            'patient_id' => $sender->id,
            'doctor_id' => $receiver->id,
            'title' => 'Rating',
            'message' => $sender->name." has given you rating.",
            'ref_id' => $rating->id,
            'ref_type' => 'DoctorRating'
        ];

        dispatch(new Notification($sender, $receiver, $extra));

        $receiver->update(['notification_indicator' => true]);

        return fractal($rating, new DoctorRatingTransformer())->serializeWith(new CustomArraySerializer());
    }

    /**
     * Set Doctor Rating
     *
     * @return string
     */
    public function deleteRating($request, $id)
    {
        $rating = DoctorRating::findOrFail($id);
        return  $rating->delete();
    }

    /**
     * get Doctor Rating
     *
     * @return string
     */
    public function viewRating($request, $id)
    {
        $rating = DoctorRating::with('patient:id,first_name,last_name,avatar', 'doctor:id,first_name,last_name,avatar')->where('doctor_id', $id);
        $averageRating = $rating->avg('rating');
        $totalRating = $rating->count();
        $rating = $rating->orderBy('created_at', 'DESC')->paginate(10);
        return ['averageRating' => $averageRating, 'totalRating' => $totalRating, 'ratings' => fractal($rating, new DoctorRatingTransformer())->serializeWith(new PaginationArraySerializer())];
    }

    /**
     * get Doctor Rating
     *
     * @return string
     */
    public function myRating($request)
    {
        $rating = DoctorRating::with('patient:id,first_name,last_name,avatar', 'doctor:id,first_name,last_name,avatar')->where('patient_id', Auth::guard('api')->user()->id)->orderBy('created_at', 'DESC')->paginate(10);
        return fractal($rating, new DoctorRatingTransformer())->serializeWith(new PaginationArraySerializer());
    }

    /**
     * get Doctor Rating
     *
     * @return string
     */
    public function doctorViewRating($request)
    {
        $rating = DoctorRating::with('patient:id,first_name,last_name,avatar', 'doctor:id,first_name,last_name,avatar')->where('doctor_id', Auth::guard('api')->user()->id);
        $averageRating = $rating->avg('rating');
        $totalRating = $rating->count();
        $rating = $rating->orderBy('created_at', 'DESC')->paginate(10);
        return ['averageRating' => $averageRating, 'totalRating' => $totalRating, 'ratings' => fractal($rating, new DoctorRatingTransformer())->serializeWith(new PaginationArraySerializer())];
    }

    /**
     * get detail Doctor Rating
     *
     * @return string
     */
    public function detailRating($request)
    {
        $rating = DoctorRating::with('patient:id,first_name,last_name,avatar', 'doctor:id,first_name,last_name,avatar')
        ->where('patient_id', Auth::guard('api')->user()->id)
        ->where('doctor_id', $request->doctor_id)->first();
        return fractal($rating, new DoctorRatingTransformer())->serializeWith(new PaginationArraySerializer());
    }


    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }
}
