<?php

namespace App\Http\Controllers\Admin;

use App\Exports\LabTestExport;
use App\Http\Requests\LabTestRequest;
use App\Repositories\LabTestRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class LabTestController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "lab-test";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var LabTestRepositoryEloquent|string
     */
    public $labTestRepositoryEloquent = "";

    public function __construct(LabTestRepositoryEloquent $labTestRepositoryEloquent)
    {
        parent::__construct();
        $this->labTestRepositoryEloquent = $labTestRepositoryEloquent;
    }

    /**
     * Return List of LabTest
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        config(['globalconfig.APP_TITLE' => 'LabTest']);
        return view('admin.lab-test.index');
    }

    /**
     * Import lab test
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function import(Request $request)
    {
        try {
            $this->labTestRepositoryEloquent->import($request);
            $request->session()->flash('success', config("messages.file_import_success"));
            return redirect()->route('admin.medicine.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Return lab-test list.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->labTestRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * create a new lab-tests
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add LabTest']);
        return view('admin.lab-test.labtest');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LabTestRequest $request)
    {
        try {
            $labtest = $this->labTestRepositoryEloquent->store($request);
            $request->session()->flash('success', config("messages.lab-test_created"));
            return redirect()->route('admin.lab-test.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit lab-test list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit LabTest']);
        try {
            $labtest = $this->labTestRepositoryEloquent->edit($request, $id);
            return view('admin.lab-test.labtest', compact('labtest'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.lab-test.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * Update lab-test list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(LabTestRequest $request, $id)
    {
        try {
            $labtest = $this->labTestRepositoryEloquent->update($request, $id);
            $request->session()->flash('success', config("messages.lab-test_updated"));
            return redirect()->route('admin.lab-test.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.lab-test.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusActive(Request $request)
    {
        try {
            $this->labTestRepositoryEloquent->changeStatusActive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeactive(Request $request)
    {
        try {
            $this->labTestRepositoryEloquent->changeStatusDeactive($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeApproveStatus(Request $request)
    {
        try {
            $this->labTestRepositoryEloquent->changeApproveStatus($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * Delete player list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        try {
            $this->labTestRepositoryEloquent->destroy($request);
            $request->session()->flash('success', config("messages.lab-test_delete"));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.lab-test.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.lab-test.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "lab_test_" . time() . ".xlsx";
        return Excel::download(new LabTestExport($request->all()), $fileName);
    }
}
