<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateAdminPasswordRequest;
use App\Http\Requests\UpdateAdminProfileRequest;
use App\Repositories\AdminRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public $repository = '';

    /**
     * UserController constructor.
     * @param AdminRepositoryEloquent $repositoryEloquent
     */
    public function __construct(AdminRepositoryEloquent $repositoryEloquent)
    {
        $this->repository = $repositoryEloquent;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function show()
    {
        config(['globalconfig.APP_TITLE' => 'My Profile']);
        $admin = Auth::guard('admin')->user();

        return view('admin.account.index', compact('admin'));
    }


    /**
     * Update admin profile details.
     *
     * @param UpdateAdminProfileRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateAdminProfileRequest $request)
    {
        try {
            $this->repository->updateProfile($request);
            $request->session()->flash('success', config("messages.admin_profile_updated"));

            return redirect()->route('admin.my-profile');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.my-profile');
        }
    }

    /**
     * Changes password.
     *
     * @param UpdateAdminPasswordRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changePassword(UpdateAdminPasswordRequest $request)
    {
        try {
            if (Hash::check($request->current_password, Auth::guard('admin')->user()->password)) {
                $this->repository->changePassword($request);
                $request->session()->flash('success', config("messages.admin_password_updated"));
            } else {
                $request->session()->flash('error', config("messages.current_password_invalid"));
            }

            return redirect()->route('admin.my-profile');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.my-profile');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function checkPassword(Request $request)
    {
        return json_encode(Hash::check($request->current_password, Auth::guard('admin')->user()->password) ? true : false);
    }
}
