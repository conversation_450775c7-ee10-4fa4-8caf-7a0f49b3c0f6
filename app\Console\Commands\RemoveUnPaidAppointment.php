<?php

namespace App\Console\Commands;

use App\Enums\PaymentStatus;
use App\Enums\TransactionPaymentStatus;
use App\Models\DoctorAppointment;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RemoveUnPaidAppointment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:appointment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command will remove book appointment which payment is remaining.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info("remove:appointment");

        $doctorAppointment = DoctorAppointment::where('payment_status', PaymentStatus::Incomplete)
        ->where('created_at', '<', Carbon::now()->subMinutes(10))->get();

        $doctorAppointmentId = [];
        foreach($doctorAppointment as $key=>$appointment){
            $doctorAppointmentId[$key] = $appointment->id;
        }
        Log::alert($doctorAppointmentId);

        $transaction = Transaction::whereIn('appointment_id', $doctorAppointmentId)->update(['payment_status' => TransactionPaymentStatus::TimeOut, 'appointment_id' => null]);

        $doctorAppointment = DoctorAppointment::where('payment_status', PaymentStatus::Incomplete)
        ->where('created_at', '<', Carbon::now()->subMinutes(10))->delete();

        return $transaction;
    }
}
