<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\SymptomRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class SymptomController extends Controller
{
    /**
     * @var SymptomRepositoryEloquent
     */
    protected $symptomRepository;

    /**
     * @var ApiFormattedResponse
     */
    protected $apiResponse;

    /**
     * SymptomController constructor.
     * @param SymptomRepositoryEloquent $symptomRepository
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(SymptomRepositoryEloquent $symptomRepository, ApiFormattedResponse $apiResponse)
    {
        $this->symptomRepository = $symptomRepository;
        $this->apiResponse = $apiResponse;
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function viewSymptom(Request $request)
    {
        try {
            $speciality = $this->symptomRepository->viewSymptom($request);
            return $this->apiResponse->respondWithMessageAndPayload($speciality, trans('messages.medical_history_detail'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
