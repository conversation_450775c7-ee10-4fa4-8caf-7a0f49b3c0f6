<?php

namespace App\Http\Requests;

use App\Http\CommonTraits\ApiValidationMessageFormat;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRegisterRequest extends FormRequest
{
    use ApiValidationMessageFormat;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_type' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            // 'email' => ['required', 'email', Rule::unique('users')->whereNotNull('email_verified_at')->whereNotNull('mobile_verified_at')],
            // 'mobile' => ['required', 'numeric', 'min:10', Rule::unique('users')->whereNotNull('email_verified_at')->whereNotNull('mobile_verified_at')],
            'email' => ['required', 'email', Rule::unique('users')->whereNotNull('email_verified_at')],
            'mobile' => ['required', 'numeric', 'min:10', Rule::unique('users')->whereNotNull('email_verified_at')],
        ];
    }
}
