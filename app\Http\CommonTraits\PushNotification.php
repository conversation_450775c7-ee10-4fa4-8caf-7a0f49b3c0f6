<?php


namespace App\Http\CommonTraits;

use App\Entities\User;
use App\Entities\UserDevice;
use LaravelFCM\Facades\FCM;
use LaravelFCM\Message\OptionsBuilder;
use LaravelFCM\Message\PayloadDataBuilder;
use LaravelFCM\Message\PayloadNotificationBuilder;
use Log;


trait PushNotification
{
    /**
     * @param $tokens
     * @param $title
     * @param $message
     * @param $user
     * @throws \LaravelFCM\Message\Exceptions\InvalidOptionsException
     */
    public static function sendNotification($tokens, $title, $message, $user, $dataBuilder)
    {
        $optionBuilder = new OptionsBuilder();
        $optionBuilder->setPriority('normal');

        $notificationBuilder = new PayloadNotificationBuilder($title);
        $notificationBuilder->setBody($message)->setSound('default')->setChannelId('molema_app_notification');

        $data = $dataBuilder->build();
        \Log::info($data);
        \Log::info($message);
        \Log::info($tokens);
        $option = $optionBuilder->build();
        $notification = $notificationBuilder->build();
        $downstreamResponse = FCM::sendTo($tokens, $option, $notification, $data);
        \Log::info($downstreamResponse->numberSuccess());
        \Log::info($downstreamResponse->numberFailure());
        \Log::info($downstreamResponse->numberModification());

        // return Array - you must remove all this tokens in your database
        // self::deleteTokens($downstreamResponse);
    }

    /**
     * @param $tokens
     * @param $title
     * @param $message
     * @param $user
     * @throws \LaravelFCM\Message\Exceptions\InvalidOptionsException
     */
    public static function sendPushNotification($tokens, $title, $message, $user)
    {
        Log::error("======================started in  sendPushNotification=======================");
        try {

            if (is_array($tokens)) {
                foreach ($tokens as $key => $token) {
                    $test_data = [
                        "title" => $title,
                        "body" => $message,
                    ];

                    $data['notification'] =  $test_data;
                    $data['token'] = $token;
                    Log::info("data => " . json_encode($data));

                    $payload['message'] = $data;
                    $res = self::sendFCMNotification($payload);
                    Log::info('Response 1 => ' . $res);
                    if ($res) {
                        Log::info("Notification Sent: ");
                        Log::info('Response => ' . $res);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("======================Error in sendPushNotification=======================");
            Log::error("Error => " . $e->getMessage());
            Log::error("File => " . $e->getFile());
            Log::error("Line => " . $e->getLine());
            Log::error("======================Error in sendPushNotification End=======================");
        }
    }

    /**
     * @param $tokens
     * @throws \LaravelFCM\Message\Exceptions\InvalidOptionsException
     */
    public static function sendSilentNotification($tokens, $extra)
    {
        try {
            if (is_array($tokens)) {
                foreach ($tokens as $key => $token) {
                    $payload = [
                        'message' => [
                            'token' => $token,
                            'android' => [
                                'priority' => 'high',
                                'ttl' => '35s',
                            ],
                            'data' => $extra,
                        ],
                    ];
                    $response = self::sendFCMNotification($payload);

                    Log::info("Silent Notification Response", [
                        'response' => $response,
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error("Error sending silent notification", [
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    public static function getFirebaseToken()
    {
        try {
            $credentialsFilePath = str_replace("/public/", "/", public_path("firebase-credentials.json"));
            $serviceAccount = json_decode(file_get_contents($credentialsFilePath), true);
            $client = new \Google_Client();
            $client->setAuthConfig($serviceAccount);
            $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
            $token = $client->fetchAccessTokenWithAssertion();
            $accessToken = $token['access_token'];
            return $accessToken;
        } catch (\Exception $e) {
            Log::error("======================Error in getFirebaseToken=======================");
            Log::error("Error => " . $e->getMessage());
            Log::error("File => " . $e->getFile());
            Log::error("Line => " . $e->getLine());
            Log::error("======================Error in getFirebaseToken End=======================");
            throw new \Exception($e);
        }
    }

    public static function sendFCMNotification($payload)
    {
        Log::info("|| ==================== sendFCMNotification start====================||");
        Log::info("|| payload: " . json_encode($payload));

        $apiurl = 'https://fcm.googleapis.com/v1/projects/molema-salud/messages:send';
        $accessToken = self::getFirebaseToken();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiurl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        Log::info("|| response: " . $response);

        if ($httpCode >= 400) {
            throw new \Exception("Error sending FCM notification: " . $response);
        }

        return $response;
    }

    /**
     * @param $userIds
     * @return mixed
     */
    public static function getUsersToken($userIds)
    {
        $notificationEnabledUserOnly = User::whereIn('id', $userIds)->get()->pluck('id')->toArray();
        return UserDevice::whereIn('user_id', $notificationEnabledUserOnly)->get()->pluck('push_token')->toArray();
    }

    /**
     * @param $userIds
     * @return mixed
     */
    public static function getUsersSilentToken($userIds)
    {
        $notificationUser = User::whereIn('id', [$userIds])->get()->pluck('id')->toArray();
        return UserDevice::whereIn('user_id', [$userIds])->get()->pluck('push_token')->toArray();
    }

    /**
     * @param $downstreamResponse
     * @return mixed
     */
    public static function deleteTokens($downstreamResponse)
    {
        if ($downstreamResponse->tokensToDelete()) {
            return UserDevice::whereIn('push_token', $downstreamResponse->tokensToDelete())->delete();
        }
    }
}
