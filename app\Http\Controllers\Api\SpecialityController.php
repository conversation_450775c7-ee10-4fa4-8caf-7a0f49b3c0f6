<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiFormattedResponse;
use App\Repositories\SpecialityRepositoryEloquent;
use Exception;
use Illuminate\Http\Request;

class SpecialityController extends Controller
{
    /**
     * @var SpecialityRepositoryEloquent
     */
    protected $specialityRepository;

    /**
     * @var ApiFormattedResponse
     */
    protected $apiResponse;

    /**
     * FaqController constructor.
     * @param SpecialityRepositoryEloquent $specialityRepository
     *  @param ApiFormattedResponse $apiResponse
     */
    public function __construct(SpecialityRepositoryEloquent $specialityRepository, ApiFormattedResponse $apiResponse)
    {
        $this->specialityRepository = $specialityRepository;
        $this->apiResponse = $apiResponse;
    }

    /**
     *
     * @param Request $request
     * @return mixed
     */
    public function viewSpeciality(Request $request)
    {
        try {
            $speciality = $this->specialityRepository->viewSpeciality($request);
            return $this->apiResponse->respondWithMessageAndPayload($speciality, trans('messages.medical_history_detail'));
        } catch (Exception $exception) {
            return $this->apiResponse->respondInternalError(trans('messages.something_went_wrong'), $exception);
        }
    }
}
