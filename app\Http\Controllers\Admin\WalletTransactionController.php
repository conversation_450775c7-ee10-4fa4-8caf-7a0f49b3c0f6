<?php

namespace App\Http\Controllers\Admin;

use App\Exports\WalletTransactionExport;
use App\Models\DoctorWalletTransaction;
use App\Models\Transaction;
use App\Repositories\WalletTransactionRepositoryEloquent;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class WalletTransactionController extends Controller
{
    /**
     * Current active menu of side bar
     *
     * @var string
     */
    public $activeSidebarMenu = "wallet-transaction";

    /**
     * Current active sub-menu of side bar
     *
     * @var string
     */
    public $activeSidebarSubMenu = "";

    /**
     * @var WalletTransactionRepositoryEloquent|string
     */
    public $walletTransactionRepositoryEloquent = "";

    public function __construct(WalletTransactionRepositoryEloquent $walletTransactionRepositoryEloquent)
    {
        parent::__construct();
        $this->walletTransactionRepositoryEloquent = $walletTransactionRepositoryEloquent;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Doctor Wallet Transaction']);
        $transactionAmount = number_format(Transaction::sum('amount'), 2, '.', '');
        $commissionAmount = number_format(DoctorWalletTransaction::sum('wallet_amount'), 2, '.', '');
        $molemaCommissionAmount = number_format($transactionAmount - $commissionAmount, 2, '.', '');
        return view('admin.wallet-transaction.index', compact('molemaCommissionAmount', 'commissionAmount'));
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function countAmount(Request $request)
    {
        $transactionAmount = Transaction::whereHas('doctorWalletTransaction');
        $commissionAmount = DoctorWalletTransaction::query();
        if(!empty($request->start_date)){
            $transactionAmount =  $transactionAmount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
            $commissionAmount =  $commissionAmount->whereBetween(DB::raw("DATE_FORMAT(created_at,'%Y/%m/%d')"), [$request->start_date, $request->end_date]);
        }
        $data['transactionAmount'] = number_format($transactionAmount->sum('amount'), 2, '.', '');
        $data['commissionAmount'] = number_format($commissionAmount->sum('wallet_amount'), 2, '.', '');
        $data['molemaCommissionAmount'] = number_format($data['transactionAmount'] - $data['commissionAmount'], 2, '.', '');
        return $data;
    }

    /**
     * Return Doctor Wallet Transaction.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function getList(Request $request)
    {
        try {
            return $this->walletTransactionRepositoryEloquent->getList($request);
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        config(['globalconfig.APP_TITLE' => 'Add Reference']);
        return view('admin.user.refer.refer');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $doctor = $this->walletTransactionRepositoryEloquent->store($request);
            $request->session()->flash('success', config("messages.doctor_created"));
            return redirect()->route('admin.wallet-transaction.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->back();
        }
    }

    /**
     * Edit doctor list.
     *
     * @return mixed
     */
    public function edit(Request $request, $id)
    {
        config(['globalconfig.APP_TITLE' => 'Edit doctor']);
        try {
            $doctor = $this->walletTransactionRepositoryEloquent->edit($request, $id);
            return view('admin.user.doctor.doctor', compact('doctor'));
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.wallet-transaction.index');
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.wallet-transaction.index');
        }
    }

    /**
     * Update doctor list.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $doctor = $this->walletTransactionRepositoryEloquent->update($request, $id);
            $request->session()->flash('success', config("messages.doctor_updated"));
            return redirect()->route('admin.wallet-transaction.index');
        } catch (ModelNotFoundException $exception) {
            $request->session()->flash('error', config("messages.record_not_found"));
            return redirect()->route('admin.wallet-transaction.index');
        } catch (Exception $exception) {dd($exception);
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.wallet-transaction.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusApproved(Request $request)
    {
        try {
            $this->walletTransactionRepositoryEloquent->changeStatusApproved($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.wallet-transaction.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeStatusDeclined(Request $request)
    {
        try {
            $this->walletTransactionRepositoryEloquent->changeStatusDeclined($request);
            $request->session()->flash('success', config("messages.user_change_status"));
        } catch (Exception $exception) {
            $request->session()->flash('error', config("messages.something_went_wrong"));
            return redirect()->route('admin.wallet-transaction.index');
        }
    }

    /**
     * @return Response
     */
    public static function export(Request $request){
        $fileName = "wallet_transaction_" . time() . ".xlsx";
        return Excel::download(new WalletTransactionExport($request->all()), $fileName);
    }
}
